package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.*;

/**
 * @Description 多值匹配
 * @<PERSON> zhangguoli
 * @Date 14:12 2021/08/17
 **/
public class TermsQueryBuilder extends AbstractQueryBuilder<TermsQueryBuilder> {
    public static final String NAME = "terms";
    private String fieldName = "";
    private List<String> values = new ArrayList<>();

    public TermsQueryBuilder() {
    }

    public TermsQueryBuilder(String fieldName, String... values) {
        this(fieldName, values != null ? Arrays.asList(values) : null);

    }

    public TermsQueryBuilder(String fieldName, List<String> values) {
        this.fieldName = fieldName;
        this.values = values;
    }

    @Override
    public JSONObject toJson() {
        JSONObject termsObject = new JSONObject();
        JSONArray termsArray = new JSONArray();
        for (int i = 0; i < values.size(); i++) {
            termsArray.add(values.get(i));
        }
        JSONObject termsJson = new JSONObject();
        termsJson.set(fieldName, termsArray);
        termsObject.set(NAME, termsJson);
        return termsObject;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) throws RuntimeException{
        boolean rt = false;
        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object zb = scaleIndexMap.get(key);
            if (null == zb) {
                throw new RuntimeException("计算参数获取超时或不存在：" + key);
            }
            JSONArray arr = JSONUtil.parseArray(entry.getValue());
            if (null == arr || arr.size() == 0) {
                return false;
            }
            String[] ar = new String[arr.size()];
            for (int i = 0; i < arr.size(); i++) {
                ar[i] = String.valueOf(arr.get(i));
            }
            if (isContainEqual(zb, ar)) {
                return true;
            }
        }

        return rt;
    }

}
