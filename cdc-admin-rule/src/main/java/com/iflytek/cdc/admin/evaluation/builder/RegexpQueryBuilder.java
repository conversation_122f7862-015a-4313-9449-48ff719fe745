package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;
import java.util.Set;

/**
 * @Description 正则匹配
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class RegexpQueryBuilder extends AbstractQueryBuilder<RegexpQueryBuilder> {
    public static final String NAME = "regexp";
    private String fieldName = "";
    private String regexp = "";

    public RegexpQueryBuilder() {
    }

    public RegexpQueryBuilder(String fieldName, String regexp) {
        this.fieldName = fieldName;
        this.regexp = regexp;
    }

    @Override
    public JSONObject toJson() {
        JSONObject regexpObject = new JSONObject();
        JSONObject jsonObject = new JSONObject();
        jsonObject.set(fieldName,  this.regexp);
        regexpObject.set(NAME, jsonObject);
        return regexpObject;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {
        if (content instanceof JSONArray) {
            JSONArray v = (JSONArray) content;
            for (int i = 0; i < v.size(); i++) {
                if (!regexp(v.get(i), scaleIndexMap)) {
                    return false;
                }
            }
            return true;
        } else {
            return regexp(content, scaleIndexMap);
        }
    }

    public boolean regexp(Object content, Map<String, Object> scaleIndexMap) {

        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object zb = scaleIndexMap.get(key);
            if (null == zb) {
                return false;
            }
            Object value = entry.getValue();
            if (value instanceof JSONArray) {
                JSONArray ar = (JSONArray) value;
                for (int i = 0; i < ar.size(); i++) {
                    if (isMatcher(zb, String.valueOf(ar.get(i)))) {
                        return true;
                    }
                }
            } else {
                return isMatcher(zb, String.valueOf(value));
            }


        }

        return false;
    }

}
