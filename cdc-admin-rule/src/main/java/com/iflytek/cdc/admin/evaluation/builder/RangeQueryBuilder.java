package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnit;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnitItem;
import com.iflytek.cdc.admin.evaluation.utils.ParameterBind;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Description 范围判断
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class RangeQueryBuilder extends AbstractQueryBuilder<RangeQueryBuilder> {
    public static final String NAME = "range";

    private static final String LTE_FIELD = "lte";
    private static final String GTE_FIELD = "gte";
    private static final String GT_FIELD = "gt";
    private static final String LT_FIELD = "lt";
    public static final boolean DEFAULT_INCLUDE_UPPER = true;
    public static final boolean DEFAULT_INCLUDE_LOWER = true;

    private String fieldName = "";
    private Object from = null;
    private Object to = null;
    private boolean includeLower = DEFAULT_INCLUDE_LOWER;
    private boolean includeUpper = DEFAULT_INCLUDE_UPPER;

    public RangeQueryBuilder() {
    }

    public RangeQueryBuilder(String fieldName) {
        this.fieldName = fieldName;

    }

    public RangeQueryBuilder from(Object from, boolean includeLower) {
        this.from = from;
        this.includeLower = includeLower;
        return this;
    }

    public RangeQueryBuilder from(Object from) {
        this.from = from;
        return this;
    }

    public RangeQueryBuilder to(Object to, boolean includeUpper) {
        this.to = to;
        this.includeUpper = includeUpper;
        return this;
    }

    public RangeQueryBuilder to(Object to) {
        this.to = to;
        return this;
    }

    @Override
    public JSONObject toJson() {
        JSONObject rangeObject = new JSONObject();
        JSONObject rangeParm = new JSONObject();
        if (!Objects.isNull(this.from)) {
            Number fromObject = (Number) this.from;
            if (this.includeLower) {
                rangeParm.set(GTE_FIELD, fromObject);
            } else {
                rangeParm.set(GT_FIELD, fromObject);
            }
        }
        if (!Objects.isNull(this.to)) {
            Number toObject = (Number) this.to;
            if (this.includeUpper) {
                rangeParm.set(LTE_FIELD, toObject);
            } else {
                rangeParm.set(LT_FIELD, toObject);
            }
        }
        JSONObject rangeJson = new JSONObject();
        rangeJson.set(fieldName, rangeParm);
        rangeObject.set(NAME, rangeJson);
        return rangeObject;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) throws RuntimeException {
        List<Boolean> rangeClauses = new ArrayList<>();
        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object indexTemp = scaleIndexMap.get(key);
            if (null == indexTemp) {
                throw new RuntimeException("计算参数获取超时或不存在：" + key);
            }
            if (indexTemp instanceof EvaluationUnit) {
                EvaluationUnit unit = (EvaluationUnit) indexTemp;
                if (unit.getUnitValues().isEmpty()) {
                    return false;
                }
                rangeClauses.add(rangeListCheck(entry, unit.getUnitValues(), scaleIndexMap));
            } else {
                EvaluationUnitItem item = new EvaluationUnitItem();
                item.setValue(String.valueOf(indexTemp));
                rangeClauses.add(rangeCheck(entry, item, scaleIndexMap));
            }
        }
        if (rangeClauses.isEmpty()) {
            return false;
        }
        for (Boolean v : rangeClauses) {
            if (!v) {
                return false;
            }
        }
        return true;
    }

    /**
     * list range check
     *
     * @param entry
     * @param zbList
     * @return
     */
    private boolean rangeListCheck(Map.Entry<String, Object> entry, List<EvaluationUnitItem> zbList, Map<String, Object> scaleIndexMap) {
        List<Boolean> rangeClauses = new ArrayList<>();
        for (EvaluationUnitItem item : zbList) {
            if (rangeCheck(entry, item, scaleIndexMap)) {
                rangeClauses.add(true);
                break;
            }
        }
        for (Boolean v : rangeClauses) {
            if (v) {
                return true;
            }
        }
        return false;
    }

    /**
     * range check
     *
     * @param entry
     * @param item
     * @return
     */
    private boolean rangeCheck(Map.Entry<String, Object> entry, EvaluationUnitItem item, Map<String, Object> scaleIndexMap) {
        float indexValue = Float.parseFloat(item.getValue());
        List<Boolean> rangeClauses = new ArrayList<>();
        JSONObject fObject = JSONUtil.parseObj(entry.getValue());
        Set<Map.Entry<String, Object>> fEntries = fObject.entrySet();
        for (Map.Entry<String, Object> fEntry : fEntries) {
            String fkey = fEntry.getKey();
            String fvStr = String.valueOf(fEntry.getValue());

            if (StringUtils.isNotEmpty(fvStr) && fvStr.startsWith(":")) {
                fvStr = ParameterBind.bind(fvStr, item.getInsideMap(), "");
            }
            if (StringUtils.isNotEmpty(fvStr) && fvStr.startsWith(":")) {
                fvStr = ParameterBind.bind(fvStr, scaleIndexMap, "");
            }

            double fv = Double.parseDouble(fvStr);
            switch (fkey) {
                case LTE_FIELD:
                    if (indexValue <= fv) {
                        rangeClauses.add(true);
                        item.addMap(item.getValue(), LTE_FIELD + ":" + fvStr);
                    } else {
                        rangeClauses.add(false);
                    }
                    break;
                case GTE_FIELD:
                    if (indexValue >= fv) {
                        rangeClauses.add(true);
                        item.addMap(item.getValue(), GTE_FIELD + ":" + fvStr);
                    } else {
                        rangeClauses.add(false);
                    }
                    break;
                case GT_FIELD:
                    if (indexValue > fv) {
                        rangeClauses.add(true);
                        item.addMap(item.getValue(), GT_FIELD + ":" + fvStr);
                    } else {
                        rangeClauses.add(false);
                    }
                    break;
                case LT_FIELD:
                    if (indexValue < fv) {
                        rangeClauses.add(true);
                        item.addMap(item.getValue(), LT_FIELD + ":" + fvStr);
                    } else {
                        rangeClauses.add(false);
                    }
                    break;
                default: {
                    throw new RuntimeException("range规则参数非法");
                }

            }
        }
        if (rangeClauses.isEmpty()) {
            return false;
        }
        for (Boolean v : rangeClauses) {
            if (!v) {
                item.getTempMap().clear();
                return false;
            }
        }
        return true;
    }

}
