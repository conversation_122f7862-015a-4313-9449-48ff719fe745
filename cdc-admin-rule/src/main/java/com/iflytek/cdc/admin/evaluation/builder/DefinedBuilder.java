package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnit;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnitItem;
import parsii.eval.Expression;
import parsii.eval.Parser;
import parsii.eval.Scope;
import parsii.tokenizer.ParseException;

import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description 根据入参值计算新的值
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class DefinedBuilder extends AbstractQueryBuilder<DefinedBuilder> {
    public static final String NAME = "defined";

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {
        JSONObject jsonObject = JSONUtil.parseObj(content);

        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String exp = String.valueOf(entry.getValue());
            exp = expBind(exp, scaleIndexMap);
            if (exp.contains(":")) {
                return false;
            }
            try {
                Scope scope = new Scope();
                Expression parserExpr = Parser.parse(exp, scope);
                double result = parserExpr.evaluate();

                EvaluationUnit evaluationUnit = new EvaluationUnit(entry.getKey(), String.valueOf(result));
                scaleIndexMap.put(entry.getKey(), evaluationUnit);
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RuntimeException("计算表达式有误", e);
            }

        }
        return true;
    }

    /**
     * 参数值替换
     *
     * @param exp
     * @param map
     * @return
     */
    public String expBind(String exp, Map<String, Object> map) {
        for (Map.Entry<String, Object> o : map.entrySet()) {
            if (o.getValue() instanceof EvaluationUnit) {
                EvaluationUnit evaluationUnit = (EvaluationUnit) o.getValue();
                if (evaluationUnit.getUnitValues().size() == 0) {
                    continue;
                }
                if (isMatch(exp, o.getKey())) {
                    EvaluationUnitItem item = evaluationUnit.getUnitValues().get(0);
                    item.addMap(item.getValue(), "");
                    exp = exp.replace(":" + o.getKey(), item.getValue());
                }
            }
        }
        return exp;
    }

    public boolean isMatch(String value, String regEx) {
        Pattern pattern = Pattern.compile(":" + regEx + "[^\\w]");
        Matcher matcher = pattern.matcher(value + " ");

        return matcher.find();
    }


}
