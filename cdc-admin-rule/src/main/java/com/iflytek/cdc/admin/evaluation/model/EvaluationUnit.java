package com.iflytek.cdc.admin.evaluation.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description 计算单元
 * @<PERSON> zhangguoli
 * @Date 22:29 2021/08/17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationUnit implements Serializable {
    /**
     *   计算内容编码
     */
    private String indexCode;
    /**
     * 计算内容
     */
    private List<EvaluationUnitItem> unitValues = new ArrayList<>();
    /**
     * 是否返回依据
     */
    private boolean basis = false;
    /**
     * 耗时
     */
    private Long takeTime = 0L;

    /**
     * 通过值初始化计算单元
     *
     * @param indexCode
     * @param itemValue
     */
    public EvaluationUnit(String indexCode, String itemValue) {
        this.indexCode = indexCode;
        EvaluationUnitItem unitValue = new EvaluationUnitItem();
        unitValue.setValue(itemValue);
        this.unitValues.add(unitValue);
    }

    /**
     * 通过List结果，初始化计算单元
     *
     * @param indexCode
     * @param rt
     * @param startTime
     */
    public EvaluationUnit(String indexCode, List<Map<String, Object>> rt, LocalDateTime startTime) {
        EvaluationUnit evaluationUnit = new EvaluationUnit(indexCode, rt);
        java.time.Duration duration = java.time.Duration.between(startTime, LocalDateTime.now());
        evaluationUnit.setTakeTime(duration.toMillis());
    }

    /**
     * 通过List结果，初始化计算单元
     *
     * @param indexCode
     * @param rt
     */
    public EvaluationUnit(String indexCode, List<Map<String, Object>> rt) {
        this.indexCode = indexCode;
        if (null == rt) {
            rt = new ArrayList<>();
        }
        for (Map<String, Object> objectMap : rt) {
            EvaluationUnitItem unitValue = new EvaluationUnitItem();
            Object index = objectMap.get("contentIndex");
            if (!Objects.isNull(index)) {
                unitValue.setContentIndex(String.valueOf(index));
            }
            Object sortIndex = objectMap.get("sortIndex");
            if (!Objects.isNull(sortIndex)) {
                unitValue.setSortIndex(String.valueOf(sortIndex));
            }
            objectMap.entrySet().forEach(t -> {
                if ("contentIndex".equalsIgnoreCase(t.getKey()) || "basisContent".equalsIgnoreCase(t.getKey())){
                    return;
                }
                if (t.getKey().startsWith("inside")) {
                    unitValue.getInsideMap().put(t.getKey(), String.valueOf(t.getValue()));
                    return;
                }
                unitValue.setValue(String.valueOf(t.getValue()));
            });
            Object retValue = objectMap.get("basisContent");
            if (!Objects.isNull(retValue)) {
                unitValue.setRValue(String.valueOf(retValue));
            }
            this.unitValues.add(unitValue);
        }
        Collections.sort(this.unitValues);
    }

}
