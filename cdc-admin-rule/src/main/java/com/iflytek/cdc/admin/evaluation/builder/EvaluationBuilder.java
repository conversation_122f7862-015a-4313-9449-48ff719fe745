package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;
import java.util.Set;

/**
 * @Description 规则计算入口
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class EvaluationBuilder extends AbstractQueryBuilder<EvaluationBuilder> {
    private static final String FILTER = "filter";
    private static final String DEFINED = "defined";

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {

        JSONObject jsonObject = JSONUtil.parseObj(content);

        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            if (entry.getKey().equals(DEFINED)) {
                DefinedBuilder definedBuilder = new DefinedBuilder();
                Boolean def = definedBuilder.evaluation(entry.getValue(), scaleIndexMap);
                if (!def) {
                    return false;
                }

            }
        }
        for (Map.Entry<String, Object> entry : entries) {
            if (entry.getKey().equals(DEFINED)) {
                continue;
            }
            if (entry.getKey().equals(FILTER)) {
                FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder();
                return filterQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);
            } else {
                FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder();
                return filterQueryBuilder.evaluation(content, scaleIndexMap);
            }
        }
        return false;
    }


}
