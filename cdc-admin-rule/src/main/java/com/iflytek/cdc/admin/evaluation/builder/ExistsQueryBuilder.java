package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnit;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnitItem;

import java.util.*;

/**
 * @Description 判断是否存在，使用否定词正则表达式判断
 * @<PERSON>oli
 * @Date 14:12 2021/08/17
 **/
public class ExistsQueryBuilder extends AbstractQueryBuilder<ExistsQueryBuilder> {
    public static final String NAME = "exists";
    private String fieldName = "";
    private List<String> values = new ArrayList<>();

    public ExistsQueryBuilder() {
    }

    /**
     * 设置规则
     *
     * @param fieldName
     * @param values
     */
    public ExistsQueryBuilder(String fieldName, String... values) {
        this(fieldName, values != null ? Arrays.asList(values) : null);

    }

    /**
     * 设置规则
     *
     * @param fieldName
     * @param values
     */
    public ExistsQueryBuilder(String fieldName, List<String> values) {
        this.fieldName = fieldName;
        this.values = values;
    }


    @Override
    public JSONObject toJson() {
        JSONObject existsObject = new JSONObject();
        JSONArray existsArray = new JSONArray();
        for (int i = 0; i < values.size(); i++) {
            existsArray.add(values.get(i));
        }
        JSONObject existsJson = new JSONObject();
        existsJson.set(fieldName, existsArray);
        existsObject.set(NAME, existsJson);
        return existsObject;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {
        if (content instanceof JSONArray) {
            JSONArray v = JSONUtil.parseArray(content);
            for (int i = 0; i < v.size(); i++) {
                if (!exist(v.get(i), scaleIndexMap)) {
                    return false;
                }
            }
            return true;
        } else {
            return exist(content, scaleIndexMap);
        }
    }

    public boolean exist(Object content, Map<String, Object> scaleIndexMap) {
        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object zb = scaleIndexMap.get(key);
            if (null == zb) {
                return false;
            }
            Object value = entry.getValue();
            if (value instanceof JSONArray) {
                JSONArray jsonArray = JSONUtil.parseArray(value);

                String[] ar = new String[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    ar[i] = String.valueOf(jsonArray.get(i));
                }
                if (isContainLike(zb, ar)) {
                    return true;
                }
            } else {
                String v = String.valueOf(value);
                if (v.startsWith(":")) {
                    Object dic = scaleIndexMap.get(v.substring(1));
                    if (null == dic) {
                        return false;
                    }
                    if (dic instanceof EvaluationUnit) {
                        EvaluationUnit unit = (EvaluationUnit) dic;
                        List<String> arL = new ArrayList<>();
                        for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                            arL.add(zbv.getValue());
                        }
                        if (isContainLike(zb, arL.toArray(new String[arL.size()]))) {
                            return true;
                        }
                    }
                } else if (isContainLike(zb, v)) {
                    return true;
                }
            }
        }

        return false;
    }

}
