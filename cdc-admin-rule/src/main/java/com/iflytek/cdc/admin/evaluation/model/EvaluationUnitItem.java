package com.iflytek.cdc.admin.evaluation.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @Description 计算单元对应内容
 * <AUTHOR>
 * @Date 22:29 2021/08/17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationUnitItem implements Comparable<EvaluationUnitItem>, Serializable {
    /**
     * 内容索引说明
     */
    private String contentIndex = "";
    /**
     * 排序
     */
    private String sortIndex = "";
    /**
     * 内容
     */
    private String value = "";
    /**
     * 预设返回值，代替依据
     */
    private String rValue = "";
    /**
     * 主要范围(range)计算中和计算数据列判断
     */
    private transient Map<String, String> insideMap = new HashMap<>();
    /**
     * 判断依据内容
     */
    private transient Map<String, Set<String>> tempMap = new HashMap<>();

    /**
     * 添加判断依据
     *
     * @param key
     * @param value
     */
    public void addMap(String key, String value) {
        if (this.tempMap.containsKey(key)) {
            this.tempMap.get(key).add(value);
            return;
        }
        Set<String> values = new HashSet<>();
        values.add(value);
        this.tempMap.put(key, values);
    }

    /**
     * 排序
     * @param o
     * @return
     */
    @Override
    public int compareTo(EvaluationUnitItem o) {
        if (StringUtils.isNotEmpty(this.sortIndex) && StringUtils.isNotEmpty(o.sortIndex)) {
            return o.sortIndex.compareTo(this.sortIndex);
        }
        if (StringUtils.isEmpty(this.contentIndex)){
            return 1;
        }
        if (StringUtils.isEmpty(o.contentIndex)){
            return -1;
        }
        return o.contentIndex.compareTo(this.contentIndex);
    }
}
