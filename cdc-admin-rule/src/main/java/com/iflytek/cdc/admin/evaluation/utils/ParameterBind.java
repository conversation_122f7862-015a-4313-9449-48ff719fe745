package com.iflytek.cdc.admin.evaluation.utils;

import java.util.Iterator;
import java.util.Map;

/**
 * @Description 参数替换
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class ParameterBind {

    public static String bind(String content, Map map) {
        return ParameterBind.bind(content, map, "");
    }

    /**
     * 参考替换
     *
     * @param content
     * @param map
     * @param b
     * @return
     */
    public static String bind(String content, Map map, String b) {

        Iterator<String> keys = map.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            content = content.replace(":" + key, b + map.get(key)) + b;
        }
        return content;
    }

    private ParameterBind() {
        throw new IllegalStateException("Utility class");
    }

}
