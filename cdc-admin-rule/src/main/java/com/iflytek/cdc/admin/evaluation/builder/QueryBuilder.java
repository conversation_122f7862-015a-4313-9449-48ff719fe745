package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;

import java.util.Map;

/**
 * @Description 内容判断接口
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public interface QueryBuilder {
    /**
     * 规则计算
     *
     * @param content
     * @param scaleIndexMap
     * @return
     */
    boolean evaluation(Object content, Map<String, Object> scaleIndexMap) throws RuntimeException;

    /**
     * 规则名称
     *
     * @return
     */
    String queryName();

    /**
     * 生成规则json
     *
     * @return
     */
    JSONObject toJson();
}
