package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description bool判断
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/

public class BoolQueryBuilder extends AbstractQueryBuilder<BoolQueryBuilder> {
    public static final String NAME = "bool";

    private static final String MUST_NOT = "must_not";
    private static final String FILTER = "filter";
    private static final String SHOULD = "should";
    private static final String MUST = "must";

    private final List<JSONObject> mustClauses = new ArrayList<>();
    private final List<JSONObject> mustNotClauses = new ArrayList<>();
    private final List<JSONObject> filterClauses = new ArrayList<>();
    private final List<JSONObject> shouldClauses = new ArrayList<>();

    /**
     * 增加must判断规则json
     *
     * @param queryBuilderJson
     * @return
     */
    public BoolQueryBuilder addMust(JSONObject queryBuilderJson) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        jsonObjectList.add(queryBuilderJson);
        add(MUST, jsonObjectList);
        return this;
    }

    /**
     * 增加must not 判断规则json
     *
     * @param queryBuilderJson
     * @return
     */
    public BoolQueryBuilder addMustNot(JSONObject queryBuilderJson) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        jsonObjectList.add(queryBuilderJson);
        add(MUST_NOT, jsonObjectList);
        return this;
    }

    /**
     * 增加bool相关builder json
     *
     * @param key
     * @param queryBuilder
     * @return
     */
    private boolean add(String key, List<JSONObject> queryBuilder) {
        switch (key) {
            case MUST:
                mustClauses.addAll(queryBuilder);
                break;
            case SHOULD:
                shouldClauses.addAll(queryBuilder);
                break;
            case FILTER:
                filterClauses.addAll(queryBuilder);
                break;
            case MUST_NOT:
                mustNotClauses.addAll(queryBuilder);
                break;
            default: {
                throw new RuntimeException(" 规则解析异常，规则key:" + key);
            }
        }
        return true;
    }

    @Override
    public JSONObject toJson() {
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonClausesObject = new JSONObject();
        if (!mustClauses.isEmpty()) {
            jsonClausesObject.set(MUST, jsonClauses(mustClauses));
        }
        if (!shouldClauses.isEmpty()) {
            jsonClausesObject.set(SHOULD, jsonClauses(shouldClauses));
        }
        if (!filterClauses.isEmpty()) {
            jsonClausesObject.set(FILTER, jsonClauses(filterClauses));
        }
        if (!mustNotClauses.isEmpty()) {
            jsonClausesObject.set(MUST_NOT, jsonClauses(mustNotClauses));
        }
        jsonObject.set(NAME, jsonClausesObject);
        return jsonObject;
    }

    private JSONArray jsonClauses(List<JSONObject> jsonClauses) {
        JSONArray jsonArray = new JSONArray();
        jsonClauses.forEach(t -> {
            jsonArray.add(t);
        });
        return jsonArray;
    }


    public List<Boolean> parseInnerQueryBuilder(Object content, Map<String, Object> scaleIndexMap, String key) {
        List<Boolean> clauses = new ArrayList<>();
        FilterQueryBuilder filterQueryBuilder = new FilterQueryBuilder();
        if (content instanceof JSONArray) {
            if (key.equalsIgnoreCase(MUST)) {
                clauses.add(filterQueryBuilder.combine(content, scaleIndexMap));
                return clauses;
            }
            JSONArray v = JSONUtil.parseArray(content);
            for (int i = 0; i < v.size(); i++) {
                clauses.add(filterQueryBuilder.evaluation(v.get(i), scaleIndexMap));
            }
        } else {
            clauses.add(filterQueryBuilder.evaluation(content, scaleIndexMap));
        }

        return clauses;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {
        boolean rt = true;

        List<Boolean> mustClauses = new ArrayList<>();
        List<Boolean> mustNotClauses = new ArrayList<>();
        List<Boolean> shouldClauses = new ArrayList<>();
        List<Boolean> filterClauses = new ArrayList<>();

        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            switch (key) {
                case MUST:
                    mustClauses.addAll(parseInnerQueryBuilder(entry.getValue(), scaleIndexMap, key));
                    break;
                case SHOULD:
                    shouldClauses.addAll(parseInnerQueryBuilder(entry.getValue(), scaleIndexMap, key));
                    break;
                case FILTER:
                    filterClauses.addAll(parseInnerQueryBuilder(entry.getValue(), scaleIndexMap, key));
                    break;
                case MUST_NOT:
                    mustNotClauses.addAll(parseInnerQueryBuilder(entry.getValue(), scaleIndexMap, key));
                    break;
                default: {
                    throw new RuntimeException(" 规则解析异常，规则key:" + key);
                }
            }
        }
        for (Boolean v : mustClauses) {
            if (!v) {
                return false;
            }
        }
        for (Boolean v : mustNotClauses) {
            if (v) {
                return false;
            }
        }
        for (Boolean v : shouldClauses) {
            rt = false;
            if (v) {
                return true;
            }
        }
        return rt;
    }


}
