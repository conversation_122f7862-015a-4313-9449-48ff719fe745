package com.iflytek.cdc.admin.evaluation.builder;

import java.util.List;

/**
 * @Description 规则json构建
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public final class QueryBuilders {

    private QueryBuilders() {
    }

    /**
     * term构建
     *
     * @param name
     * @param value
     * @return
     */
    public static TermQueryBuilder termQuery(String name, String value) {
        return new TermQueryBuilder(name, value);
    }


    /**
     * range构建
     *
     * @param name
     * @return
     */
    public static RangeQueryBuilder rangeQuery(String name) {
        return new RangeQueryBuilder(name);
    }

    /**
     * range构建
     *
     * @param name
     * @param regexp
     * @return
     */
    public static RegexpQueryBuilder regexpQuery(String name, String regexp) {
        return new RegexpQueryBuilder(name, regexp);
    }

    /**
     * bool构建
     *
     * @return
     */
    public static BoolQueryBuilder boolQuery() {
        return new BoolQueryBuilder();
    }

    /**
     * terms构建
     *
     * @param name
     * @param values
     * @return
     */
    public static TermsQueryBuilder termsQuery(String name, String... values) {
        return new TermsQueryBuilder(name, values);
    }

    /**
     * terms构建
     *
     * @param name
     * @param values
     * @return
     */
    public static TermsQueryBuilder termsQuery(String name, List<String> values) {
        return new TermsQueryBuilder(name, values);
    }

    /**
     * exists构建
     *
     * @param name
     * @param values
     * @return
     */
    public static ExistsQueryBuilder existsQuery(String name, List<String> values) {
        return new ExistsQueryBuilder(name, values);
    }
}