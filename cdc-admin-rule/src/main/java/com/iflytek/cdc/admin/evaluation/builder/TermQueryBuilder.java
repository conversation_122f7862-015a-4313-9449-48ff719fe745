package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;
import java.util.Set;

/**
 * @Description 单值精确匹配
 * <AUTHOR>
 * @Date 14:12 2021/08/17
 **/
public class TermQueryBuilder extends AbstractQueryBuilder<TermQueryBuilder> {
    public static final String NAME = "term";
    private String fieldName = "";
    private String value = "";

    public TermQueryBuilder() {
    }

    public TermQueryBuilder(String fieldName, String value) {
        this.fieldName = fieldName;
        this.value = value;
    }

    @Override
    public JSONObject toJson() {
        JSONObject termObject = new JSONObject();
        JSONObject jsonObject = new JSONObject();
        jsonObject.set(fieldName, this.value);
        termObject.set(NAME, jsonObject);
        return termObject;
    }

    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) throws RuntimeException {
        boolean rt = false;

        JSONObject jsonObject = JSONUtil.parseObj(content);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object zb = scaleIndexMap.get(key);
            if (null == zb) {
                throw new RuntimeException("计算参数获取超时或不存在：" + key);
            }

            return isContainEqual(zb, String.valueOf(entry.getValue()));

        }

        return rt;
    }

}
