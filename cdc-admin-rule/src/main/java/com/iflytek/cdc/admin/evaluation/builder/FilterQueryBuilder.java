package com.iflytek.cdc.admin.evaluation.builder;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @Description filter组合判断
 * @<PERSON> zhangguoli
 * @Date 14:12 2021/08/17
 **/
public class FilterQueryBuilder extends AbstractQueryBuilder<FilterQueryBuilder> {
    public static final String NAME = "filter";

    private static final String TERM = "term";
    private static final String TERMS = "terms";
    private static final String RANGE = "range";
    private static final String EXISTS = "exists";
    private static final String MISSING = "missing";
    private static final String BOOL = "bool";
    private static final String REGEXP = "regexp";


    @Override
    public boolean evaluation(Object content, Map<String, Object> scaleIndexMap) {
        JSONObject jsonObject = JSONUtil.parseObj(content);


        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            if (entry.getKey().equals(BOOL)) {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                return boolQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);

            } else if (entry.getKey().equals(TERM)) {
                TermQueryBuilder termQueryBuilder = new TermQueryBuilder();
                return termQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);

            } else if (entry.getKey().equals(TERMS)) {
                TermsQueryBuilder termsQueryBuilder = new TermsQueryBuilder();
                return termsQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);

            } else if (entry.getKey().equals(RANGE)) {
                RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder();
                return rangeQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);

            } else if (entry.getKey().equals(EXISTS)) {
                ExistsQueryBuilder existsQueryBuilder = new ExistsQueryBuilder();
                return existsQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);

            } else if (entry.getKey().equals(MISSING)) {
                ExistsQueryBuilder existsQueryBuilder = new ExistsQueryBuilder();
                return !existsQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);
            } else if (entry.getKey().equals(REGEXP)) {
                RegexpQueryBuilder regexpQueryBuilder = new RegexpQueryBuilder();
                return regexpQueryBuilder.evaluation(entry.getValue(), scaleIndexMap);
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 合并相同计算单元计算逻辑,后计算是否满足
     *
     * @param content
     * @param scaleIndexMap
     * @return
     */
    public boolean combine(Object content, Map<String, Object> scaleIndexMap) {
        Map<String, Set<String>> existsMap = new HashMap<>(16);
        Map<String, Set<String>> missingMap = new HashMap<>(16);
        boolean noExist = true;
        if (content instanceof JSONArray) {
            JSONArray v = JSONUtil.parseArray(content);
            for (int i = 0; i < v.size(); i++) {
                JSONObject jsonObject = JSONUtil.parseObj(v.get(i));
                Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
                for (Map.Entry<String, Object> entry : entries) {
                    if (entry.getKey().equals(EXISTS)) {
                        noExist = false;
                        combine2(existsMap, entry.getValue());
                    } else if (entry.getKey().equals(MISSING)) {
                        combine2(missingMap, entry.getValue());
                    } else if (entry.getKey().equals(RANGE)) {
                        RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder();
                        if (!rangeQueryBuilder.evaluation(entry.getValue(), scaleIndexMap)) {
                            return false;
                        }
                    } else if (entry.getKey().equals(REGEXP)) {
                        RegexpQueryBuilder regexpQueryBuilder = new RegexpQueryBuilder();
                        if (!regexpQueryBuilder.evaluation(entry.getValue(), scaleIndexMap)) {
                            return false;
                        }
                    } else if (entry.getKey().equals(BOOL)) {
                        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                        if (!boolQueryBuilder.evaluation(entry.getValue(), scaleIndexMap)) {
                            return false;
                        }
                    } else if (entry.getKey().equals(TERM)) {
                        TermQueryBuilder termQueryBuilder = new TermQueryBuilder();
                        if (!termQueryBuilder.evaluation(entry.getValue(), scaleIndexMap)) {
                            return false;
                        }
                    } else if (entry.getKey().equals(TERMS)) {
                        TermsQueryBuilder termsQueryBuilder = new TermsQueryBuilder();
                        if (!termsQueryBuilder.evaluation(entry.getValue(), scaleIndexMap)) {
                            return false;
                        }
                    }

                }
            }
        }
        Set<Map.Entry<String, Set<String>>> entries = existsMap.entrySet();
        if (noExist && entries.isEmpty()) {
            return true;
        }
        if (entries.isEmpty()) {
            return false;
        }
        for (Map.Entry<String, Set<String>> entry : entries) {
            Object zb = scaleIndexMap.get(entry.getKey());
            if (null == zb) {
                return false;
            }
            if (!isCombineMatch(zb, entry.getValue(), missingMap.get(entry.getKey()))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析计算逻辑
     *
     * @param jsonSetMap
     * @param content
     * @return
     */

    public Map<String, Set<String>> combine2(Map<String, Set<String>> jsonSetMap, Object content) {
        if (content instanceof JSONArray) {
            JSONArray v = JSONUtil.parseArray(content);
            for (int j = 0; j < v.size(); j++) {
                getJsonMap(jsonSetMap, JSONUtil.parseObj(v.get(j)));
            }
        } else {
            getJsonMap(jsonSetMap, JSONUtil.parseObj(content));
        }
        return jsonSetMap;
    }

    /**
     * 合并相同计算单元计算逻辑
     *
     * @param jsonArrayMap
     * @param jsonObject
     */
    public void getJsonMap(Map<String, Set<String>> jsonArrayMap, JSONObject jsonObject) {
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();

        for (Map.Entry<String, Object> entry : entries) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (!jsonArrayMap.containsKey(key)) {
                jsonArrayMap.put(key, new HashSet<>());
            }

            Set<String> temp = jsonArrayMap.get(key);
            if (value instanceof JSONArray) {
                JSONArray v = JSONUtil.parseArray(value);
                String e = "";
                for (int j = 0; j < v.size(); j++) {
                    e += v.get(j) + " | ";
                }
                temp.add(e);
            } else {
                temp.add(String.valueOf(value));
            }
        }
    }


}
