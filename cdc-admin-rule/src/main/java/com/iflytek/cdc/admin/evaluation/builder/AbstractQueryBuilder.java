package com.iflytek.cdc.admin.evaluation.builder;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnit;
import com.iflytek.cdc.admin.evaluation.model.EvaluationUnitItem;
import com.iflytek.cdc.admin.evaluation.utils.ValidContentUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description 抽象构造者
 * <AUTHOR>
 * @Date 14:43 2021/08/17
 **/
public abstract class AbstractQueryBuilder<QB extends AbstractQueryBuilder<QB>> implements QueryBuilder {
    public static final String NAME = "filter";
    private final String SPLIT_REGEX = ",|，|。|；|;";
    public static final int SUB_CONTENT_L = 100;
    public static final int SUB_CONTENT_R = 100;

    @Override
    public String queryName() {
        return NAME;
    }

    @Override
    public JSONObject toJson() {
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    /**
     * 是否存在相等
     *
     * @param zb
     * @param ar
     * @return
     */
    public boolean isContainEqual(Object zb, String[] ar) {
        if (zb instanceof EvaluationUnit) {
            EvaluationUnit unit = (EvaluationUnit) zb;
            for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                for (int i = 0; i < ar.length; i++) {
                    if (ar[i].equalsIgnoreCase(zbv.getValue())) {
                        zbv.addMap(zbv.getValue(), ar[i]);
                        return true;
                    }
                }
            }
        } else {
            for (int i = 0; i < ar.length; i++) {
                if (zb.toString().equalsIgnoreCase(ar[i])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否存在相等
     *
     * @param zb
     * @param v
     * @return
     */
    public boolean isContainEqual(Object zb, String v) {
        if (zb instanceof EvaluationUnit) {
            EvaluationUnit unit = (EvaluationUnit) zb;
            for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                if (v.equalsIgnoreCase(zbv.getValue())) {
                    zbv.addMap(zbv.getValue(), v);
                    return true;
                }
            }
        } else {
            return zb.toString().equalsIgnoreCase(v);
        }
        return false;
    }

    /**
     * 是否存在包含
     *
     * @param zb
     * @param ar
     * @return
     */
    public boolean isContainLike(Object zb, String[] ar) {
        if (zb instanceof EvaluationUnit) {
            EvaluationUnit unit = (EvaluationUnit) zb;
            for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                for (int i = 0; i < ar.length; i++) {
                    if (regexContain(zbv, ar[i], true) > -1) {
                        return true;
                    }
                }
            }
        } else {
            for (int i = 0; i < ar.length; i++) {
                if (zb.toString().contains(ar[i])) {
                    return true;
                }
            }

        }
        return false;
    }

    /**
     * 是否存在包含
     *
     * @param zb
     * @param v
     * @return
     */
    public boolean isContainLike(Object zb, String v) {
        if (zb instanceof EvaluationUnit) {
            EvaluationUnit unit = (EvaluationUnit) zb;
            for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                if (StringUtils.isNotEmpty(zbv.getValue()) && StringUtils.isEmpty(v)) {
                    return true;
                }
                if (regexContain(zbv, v, true) > -1) {
                    return true;
                }

            }
        } else {
            return zb.toString().contains(v);
        }
        return false;
    }

    /**
     * 判断是否包括内容
     *
     * @param zbv
     * @param k
     * @return
     */
    public int regexContain(EvaluationUnitItem zbv, String k, boolean isAdd) {
        String str = zbv.getValue();
        int index = -1;
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(k)) {
            return -1;
        }

        if (!ValidContentUtil.checkContentRight(k)) {
            if (str.contains(k)) {
                index = str.indexOf(k);
                if (isAdd) {
                    zbv.addMap(subContent(str, index), k);
                }
                return index;
            }
            return -1;
        }
        String[] ar = str.split(SPLIT_REGEX);
        Pattern searchPattern = Pattern.compile("^((?!无|未闻及|未及|否认|未见).)*(" + k + ")((?!((：|:)?(\\s*)(无|否))|((：\\s*)$)).)*$");

        for (String s : ar) {
            index++;
            Matcher mt = searchPattern.matcher(s);
            if (mt.find()) {
                if (isAdd) {
                    if (ar.length > 1) {
                        zbv.addMap(subContent(str, index), s);
                    } else {
                        zbv.addMap(subContent(str, index), k);
                    }
                }
                return index;
            }
            index += s.length();
        }
        return -1;
    }

    /**
     * 是否符合条件
     *
     * @param zb
     * @param exists  必须包含
     * @param missing 不能包含
     * @return
     */
    public boolean isCombineMatch(Object zb, Set<String> exists, Set<String> missing) {
        boolean rt = false;
        if (!(zb instanceof EvaluationUnit)) {
            return false;
        }
        EvaluationUnit unit = (EvaluationUnit) zb;


        for (EvaluationUnitItem zbv : unit.getUnitValues()) {
            for (String exist : exists) {
                boolean et = false;
                String[] vs = exist.split(" \\| ");
                for (String v : vs) {
                    int indexTemp = regexContain(zbv, v, true);
                    if (indexTemp > -1) {
                        et = true;
                        break;
                    }
                }
                if (StringUtils.isEmpty(exist)) {
                    et = true;
                }
                //不满足exist,循环下一个exist
                if (!et) {
                    zbv.setTempMap(new HashMap<>(16));
                    rt = false;
                    break;
                } else {
                    rt = true;
                }
            }
            if (!rt) {
                continue;
            }

            //该条数据是否不包括missing内容
            if (CollectionUtil.isNotEmpty(missing)) {
                for (String m : missing) {
                    if (regexContain(zbv, m, false) > -1) {
                        zbv.setTempMap(new HashMap<>(16));
                        rt = false;
                        break;

                    }
                }
            }

            if (rt) {
                return true;
            }
        }
        return false;
    }

    /**
     * 截取满足条件部分内容
     *
     * @param zbvStr
     * @param index
     * @return
     */
    public String subContent(String zbvStr, int index) {
        String rt;
        if (StringUtils.isEmpty(zbvStr)) {
            return "";
        }
        int strLength = zbvStr.length();
        if (strLength > (SUB_CONTENT_L + SUB_CONTENT_R)) {
            int start = index - SUB_CONTENT_L < 0 ? 0 : (index - SUB_CONTENT_L);
            int end = index + SUB_CONTENT_R > strLength ? strLength : (index + SUB_CONTENT_R);
            rt = zbvStr.substring(start, end);
            if (start > 0) {
                rt = "..." + rt;
            }
            if (end < strLength) {
                rt = rt + "...";
            }
            return rt;
        }
        return zbvStr;
    }

    /**
     * 是否存在正则匹配
     *
     * @param zb
     * @param v
     * @return
     */
    public boolean isMatcher(Object zb, String v) {
        Pattern searchPattern = Pattern.compile(v);
        if (zb instanceof EvaluationUnit) {
            EvaluationUnit unit = (EvaluationUnit) zb;
            for (EvaluationUnitItem zbv : unit.getUnitValues()) {
                Matcher mt = searchPattern.matcher(zbv.getValue());
                if (mt.find()) {
                    int index = mt.start();
                    zbv.addMap(subContent(zbv.getValue(), index), v);
                    return true;
                }
            }
        } else {
            return zb.toString().contains(v);
        }
        return false;
    }
}
