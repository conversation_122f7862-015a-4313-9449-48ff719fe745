package com.iflytek.cdc.admin.expert.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.common.dto.workbench.CommonRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@ApiModel(value = "专家标签管理")
@Data
@TableName(value = "tb_cdc_expert_label",schema = "app")
@Accessors(chain = true)
public class TbCdcExpertLabel extends CommonRequestDTO implements Serializable {

    public static final String TB_NAME ="app.tb_cdc_expert_label";


    @ApiModelProperty("标签id")
    private String id;

    @ApiModelProperty("标签名称")
    private String labelName;
}
