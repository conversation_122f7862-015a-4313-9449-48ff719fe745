package com.iflytek.cdc.admin.expert.dto;

import com.iflytek.cdc.admin.common.dto.workbench.CommonRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ExpertSchemeDTO extends CommonRequestDTO {

    @ApiModelProperty("评审方案id")
    private String id;

    @ApiModelProperty("评估属性")
    private String assessAttribute;

    @ApiModelProperty("属性权重")
    private Float assessWeight;
}
