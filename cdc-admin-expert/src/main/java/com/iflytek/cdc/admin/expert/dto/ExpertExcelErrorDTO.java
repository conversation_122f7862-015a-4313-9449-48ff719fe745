package com.iflytek.cdc.admin.expert.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.iflytek.cdc.admin.common.util.easyexcel.ExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.LabelExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.NationExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.PoliticsFaceExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.SexExcelSelected;
import lombok.Data;

@Data
@ColumnWidth(22)
@HeadRowHeight(30)
public class ExpertExcelErrorDTO {

    @ExcelProperty(index = 0,value ="身份证")
    private String idCard;

    @ExcelProperty(index = 1,value ="联系电话")
    private String phone;

    @ExcelProperty(index = 2,value ="邮箱")
    private String email;

    @ExcelSelected(sourceClass = SexExcelSelected.class)
    @ExcelProperty(index = 3,value ="性别")
    private String sex;

    @ExcelSelected(sourceClass = NationExcelSelected.class)
    @ExcelProperty(index = 4,value ="民族")
    private String nation;

    @ExcelSelected(sourceClass = PoliticsFaceExcelSelected.class)
    @ExcelProperty(index = 5,value ="政治面貌")
    private String politicsFace;

    @ExcelSelected(sourceClass = LabelExcelSelected.class)
    @ExcelProperty(index = 6,value ="分类标签名称")
    private String labelNames;

    @ExcelProperty(index = 7,value ="失败原因")
    private String errorMsg;

}
