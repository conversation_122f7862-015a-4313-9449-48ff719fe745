package com.iflytek.cdc.admin.expert.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.iflytek.cdc.admin.common.util.easyexcel.ExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.LabelExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.NationExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.PoliticsFaceExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.SexExcelSelected;
import com.iflytek.cdc.admin.expert.excelselected.TitleLevelExcelSelected;
import lombok.Data;

import java.util.Date;

@Data
@ColumnWidth(22)
@HeadRowHeight(30)
public class ExportExcelDTO {

    @ExcelProperty(index = 0,value ="姓名*")
    private String name;

    @ExcelSelected(sourceClass = SexExcelSelected.class)
    @ExcelProperty(index = 1,value ="性别*")
    private String sex;

    @ExcelProperty(index = 2,value ="出生日期")
    private Date birthday;

    @ExcelProperty(index = 3,value ="身份证")
    private String idCard;

    @ExcelProperty(index = 4,value ="联系电话*")
    private String phone;

    @ExcelProperty(index = 5,value ="邮箱*")
    private String email;

    @ExcelSelected(sourceClass = NationExcelSelected.class)
    @ExcelProperty(index = 6,value ="民族")
    private String nation;

    @ExcelSelected(sourceClass = PoliticsFaceExcelSelected.class)
    @ExcelProperty(index = 7,value ="政治面貌")
    private String politicsFace;

    @ExcelProperty(index = 8,value ="机构名称*")
    private String orgName;

    @ExcelProperty(index = 9,value ="工作日期")
    private Date startWorkTime;

    @ExcelProperty(index = 10,value ="岗位类别*")
    private String jobCategory;

    @ExcelSelected(sourceClass = TitleLevelExcelSelected.class)
    @ExcelProperty(index = 11,value ="职级*")
    private String titleLevel;

    @ExcelProperty(index = 12,value ="学术背景")
    private String academicBackground;

    @ExcelProperty(index = 13,value ="工作经历")
    private String workExperience;

    @ExcelProperty(index = 14,value ="专业领域")
    private String jobMajor;

    @ExcelProperty(index = 15,value ="技能特长")
    private String skills;

    @ExcelSelected(sourceClass = LabelExcelSelected.class)
    @ExcelProperty(index = 16,value ="分类标签名称")
    private String labelNames;

}
