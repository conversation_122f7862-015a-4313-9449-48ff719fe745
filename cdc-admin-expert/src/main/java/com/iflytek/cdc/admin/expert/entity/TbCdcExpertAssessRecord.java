package com.iflytek.cdc.admin.expert.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.common.dto.workbench.CommonRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@ApiModel(value = "专家评估记录管理")
@Data
@TableName(value = "tb_cdc_expert_assess_record", schema = "app")
@Accessors(chain = true)
public class TbCdcExpertAssessRecord extends CommonRequestDTO implements Serializable {


    public static final String TB_NAME = "app.tb_cdc_expert_assess_record";

    @ApiModelProperty("评估记录id")
    private String id;

    @ApiModelProperty("评估方案id")
    private String assessSchemeId;

    @ApiModelProperty("专家id")
    private String expertId;

    @ApiModelProperty("评估属性")
    private String assessAttribute;

    @ApiModelProperty("评估属性权重")
    private Float assessWeight;

    @ApiModelProperty("得分")
    private Float score;

}
