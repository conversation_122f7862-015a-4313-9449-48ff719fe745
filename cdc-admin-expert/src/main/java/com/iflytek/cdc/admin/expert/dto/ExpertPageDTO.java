package com.iflytek.cdc.admin.expert.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class ExpertPageDTO {


    @ApiModelProperty(value = "pageIndex")
    private Integer pageIndex = 1;

    @ApiModelProperty(value = "pageSize")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "专家名称")
    private String name;

    @ApiModelProperty(value = "学术背景")
    private String academicBackground;

    @ApiModelProperty(value = "专业领域")
    private String jobMajor;

    @ApiModelProperty(value = "岗位类别")
    private String jobCategory;

    @ApiModelProperty(value = "职称")
    private String titleLevel;

    @ApiModelProperty(value = "技能特长")
    private String skills;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty("评估状态 0 待评估 1 待审核 2 已完成")
    private String assessStatus;


}
