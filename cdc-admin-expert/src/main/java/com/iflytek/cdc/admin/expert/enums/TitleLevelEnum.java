package com.iflytek.cdc.admin.expert.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 职级
 */
public enum TitleLevelEnum {
    PRIMARY("1", "初级"),
    INTERMEDIATE("2", "中级"),
    SENI<PERSON>("3", "高级"),
    deputy_senior("4", "副高级")
    ;


    private String code;
    private String name;

    TitleLevelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }
    
    
    public static List<String> getAllTitleLevelName() {
       return Arrays.stream(TitleLevelEnum.values())
               .map(TitleLevelEnum::getName)
               .collect(Collectors.toList());
    }
}
