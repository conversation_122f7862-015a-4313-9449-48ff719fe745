package com.iflytek.cdc.admin.expert.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum NationEnum {
    HAN("01", "汉族"),
    MEN_GU("02", "蒙古族"),
    HUI("03", "回族"),
    ZANG("04", "藏族"),
    WEI_WU_ER("05", "维吾尔族"),
    MIAO("06", "苗族"),
    YI("07", "彝族"),
    ZHUANG("08", "壮族"),
    BU_YI("09", "布依族"),
    CHAO_XIAN("10", "朝鲜族"),

    MAN("11", "满族"),
    TON("12", "侗族"),
    YAO("13", "瑶族"),
    BAI("14", "白族"),
    TU_JIA("15", "土家族"),
    HA_NI("16", "哈尼族"),
    HA_SA_KE("17", "哈萨克族"),
    DAI("18", "傣族"),
    LI("19", "黎族"),
    LI_SU("20", "傈僳族"),
    WA("21", "佤族"),
    SHE("22", "畲族"),
    GAO_SHAN("23", "高山族"),
    LA_HU("24", "拉祜族"),
    SHUI("25", "水族"),
    DON_XIANG("26", "东乡族"),
    NA_XI("27", "纳西族"),
    JING_PO("28", "景颇族"),
    KE_ER_KE_zi("29", "柯尔克孜族"),
    TU("30", "土族"),
    DA_XUAN_ER("31", "达斡尔族"),
    MU_LAO("32", "仫佬族"),
    QIANG("33", "羌族"),
    BU_LANG("34", "布朗族"),
    SA_LA("35", "撒拉族"),
    MAO_NAN("36", "毛难族"),
    QI_LAO("37", "仡佬族"),
    XI_BO("38", "锡伯族"),
    A_CHANG("39", "阿昌族"),
    PU_MI("40", "普米族"),
    TA_JI_KE("41", "塔吉克族"),
    NV("42", "怒族"),
    WU_ZI_BIE_KE("43", "乌孜别克族"),
    E_LUO_SI("44", "俄罗斯族"),
    E_WEN_KE("45", "鄂温克族"),
    BENG_LONG("46", "崩龙族"),
    BAO_AN("47", "保安族"),
    YU_GU("48", "裕固族"),
    JING("49", "京族"),
    TA_TA_ER("50", "塔塔尔族"),
    DU_LONG("51", "独龙族"),
    E_LUN_CHUN("52", "鄂伦春族"),
    HE_ZHE("53", "赫哲族"),
    MWEN_BA("54", "门巴族"),
    LUO_BA("55", "珞巴族"),
    JI_NUO("56", "基诺族"),
    FOREIGN("98", "外国血统中国籍人士"),
    OTHER("99", "其他（如外籍人士等）")
    ;


    private String code;
    private String name;

    NationEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }


    public String getName() {
        return name;
    }


    public static List<String> getAllNationName() {
        return Arrays.stream(NationEnum.values())
                .map(NationEnum::getName)
                .collect(Collectors.toList());
    }
}
