package com.iflytek.cdc.admin.expert.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExpertAssessRecordDTO {


    @ApiModelProperty("评估属性集合")
    List<ExpertAssessRecord> expertAssessRecordList;

    @ApiModelProperty("专家id")
    private String expertId;

    @ApiModelProperty("评估说明")
    private String assessDescribe;

    @ApiModelProperty("评估结果")
    private Float assessResult;


    @Data
    public static class ExpertAssessRecord {
        @ApiModelProperty("评估属性id")
        private String assessSchemeId;
        @ApiModelProperty("评估属性名称")
        private String assessAttribute;
        @ApiModelProperty("评估属性权重")
        private Float assessWeight;
        @ApiModelProperty("得分")
        private Float score;
    }


}
