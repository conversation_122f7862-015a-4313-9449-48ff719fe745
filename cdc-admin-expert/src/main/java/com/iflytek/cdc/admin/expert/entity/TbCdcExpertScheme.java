package com.iflytek.cdc.admin.expert.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.common.dto.workbench.CommonRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@ApiModel(value = "专家评审方案管理")
@Data
@TableName(value = "tb_cdc_expert_assess_scheme",schema = "app")
@Accessors(chain = true)
public class TbCdcExpertScheme extends CommonRequestDTO implements Serializable {

    public static final String TB_NAME ="app.tb_cdc_expert_assess_scheme";


    @ApiModelProperty("评审方案id")
    private String id;

    @ApiModelProperty("评估属性")
    private String assessAttribute;

    @ApiModelProperty("属性权重")
    private Float assessWeight;
}
