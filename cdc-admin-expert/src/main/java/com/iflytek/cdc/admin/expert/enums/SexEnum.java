package com.iflytek.cdc.admin.expert.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum SexEnum {
    UNKNOWN("0", "未知的性别"),
    MALE("1", "男性"),
    FEMALE("2", "女性"),
    FEMALE_TO_MALE("5", "女性改（变）为男性"),
    MALE_TO_FEMALE("6", "男性改（变）为女性"),
    UNSPECIFIED("9", "未说明的性别")
    ;


    private String code;
    private String name;
    
    SexEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }


    public static String getCodeByName(String name) {
        for (SexEnum enumValue : SexEnum.values()) {
            if (enumValue.getName().equals(name)) {
                return enumValue.getCode();
            }
        }
        throw new IllegalArgumentException("当前性别不存在：" + name);
    }


    public static List<String> getAllSexName() {
       return Arrays.stream(SexEnum.values())
               .map(SexEnum::getName)
               .collect(Collectors.toList());
    }
}
