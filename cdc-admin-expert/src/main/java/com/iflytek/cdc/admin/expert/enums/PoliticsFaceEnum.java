package com.iflytek.cdc.admin.expert.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum PoliticsFaceEnum {
    COMMUNIST_PARTY("01", "中共党员"),
    PROBATIONARY_PARTY_MEMBER("02", "中共预备党员"),
    COMMUNIST_YOUTH_LEAGUE("03", "共青团员"),
    MING_GE("04", "民革会员"),
    MING_MENG("05", "民盟盟员"),
    MING_JIAN("06", "民建会员"),
    MING_JING("07", "民进会员"),
    FARMER_LABOUR_PARTY("08", "农工党党员"),
    LABOUR_PARTY("09", "致工党党员"),
    JIUSAN_SOCIETY("10", "九三学社社员"),
    TAIWAN_LEAGUE("11", "台盟盟员"),
    WITHOUT_PARTY_AFFILIATION("12", "无党派民主人士"),
    MASSES("13", "群众"),
    OTHER("99", "其他")
    ;


    private String code;
    private String name;

    PoliticsFaceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }
    
    
    public static List<String> getAllPoliticsFaceName() {
       return Arrays.stream(PoliticsFaceEnum.values())
               .map(PoliticsFaceEnum::getName)
               .collect(Collectors.toList());
    }
}
