package com.iflytek.cdc.admin.expert.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.common.dto.workbench.CommonRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "专家库管理")
@Data
@TableName(value = "tb_cdc_expert", schema = "app")
@Accessors(chain = true)
public class TbCdcExpert extends CommonRequestDTO implements Serializable {

    public static final String TB_NAME = "app.tb_cdc_expert";


    @ApiModelProperty("专家id")
    private String id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("性别")
    private String sex;

    @ApiModelProperty("出生日期")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty("身份证")
    private String idCard;

    @ApiModelProperty("联系电话")
    private String phone;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("民族")
    private String nation;

    @ApiModelProperty("政治面貌")
    private String politicsFace;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("工作")
    private String job;

    @ApiModelProperty("岗位类别")
    private String jobCategory;

    @ApiModelProperty("职级")
    private String titleLevel;

    @ApiModelProperty("学术背景")
    private String academicBackground;

    @ApiModelProperty("工作经历")
    private String workExperience;

    @ApiModelProperty("专业领域")
    private String jobMajor;

    @ApiModelProperty("技能特长")
    private String skills;

    @ApiModelProperty("人员状态 1-启用，0-停用")
    private String status;

    @ApiModelProperty("标签，多个标签ID逗号分隔")
    private String label;

    @ApiModelProperty("标签名称，多个标签名称逗号分隔")
    @TableField(exist = false)
    private String labelNames;

    @ApiModelProperty("评分说明")
    private String ratingExplanation;

    @ApiModelProperty("评估人ID")
    private String assessUserId;
    
    @ApiModelProperty("评估状态 0 待评估 1 待审核 2 已完成 3已驳回")
    private String assessStatus;

    @ApiModelProperty("评估结果")
    private Float assessResult;

    @ApiModelProperty("评估时间")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date assessTime;

    @ApiModelProperty("评估人员")
    private String assessUser;

    @ApiModelProperty("审核人ID")
    private String examineUserId;
    
    @ApiModelProperty("审核人")
    private String examineUser;

    @ApiModelProperty("审核时间")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date examineTime;

    @ApiModelProperty("审核结果 1驳回 2通过")
    private String examineResult;

    @ApiModelProperty("审核说明")
    private String examineExplanation;

    @ApiModelProperty("工作开始时间")
    @JSONField(format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startWorkTime;

    @ApiModelProperty("uapId")
    private String uapUserId;


    @ApiModelProperty("评估记录")
    @TableField(exist = false)
    private List<ExpertAssessRecord> assessRecordList;

    @Data
    public static class ExpertAssessRecord {
        @ApiModelProperty("评估属性")
        private String assessAttribute;
        @ApiModelProperty("评估属性权重")
        private Float assessWeight;
        @ApiModelProperty("得分")
        private Float score;
    }


}
