package com.iflytek.cdc.admin.expert.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ExpertAccessZoneDTO {
    @ApiModelProperty("评估指标参数列表")
    List<AccessZoneParam> accessZoneParams;

    @ApiModelProperty("评估结果区间最小值")
    Float minResultScore;

    @ApiModelProperty("评估结果区间最大值")
    Float maxResultScore;

    @Data
    public static class AccessZoneParam {
        @ApiModelProperty("评估属性id")
        private String id;
        @ApiModelProperty("评估属性名称")
        private String assessAttribute;
        @ApiModelProperty("得分")
        private Float minScore;
        @ApiModelProperty("得分")
        private Float maxScore;
    }
}
