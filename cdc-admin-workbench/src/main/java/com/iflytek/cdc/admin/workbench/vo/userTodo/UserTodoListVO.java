package com.iflytek.cdc.admin.workbench.vo.userTodo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserTodoListVO {
    @ApiModelProperty(value = "任务类别名称")
    private String taskClassName;

    @ApiModelProperty(value = "任务类别编码")
    private String taskClassCode;

    @ApiModelProperty(value = "关键字查询 任务名称、信号id、病例id、事件id等")
    private String keyword;

    @ApiModelProperty("查询开始日期 yyyy-MM-dd HH:mm:ss")
    private String minStartDate;

    @ApiModelProperty("查询结束日期 yyyy-MM-dd HH:mm:ss")
    private String maxEndDate;


    @ApiModelProperty(value = "uap应用code")
    private String appCode;
    @ApiModelProperty(value = "请求参数",hidden = true)
    private String requestParam;
}
