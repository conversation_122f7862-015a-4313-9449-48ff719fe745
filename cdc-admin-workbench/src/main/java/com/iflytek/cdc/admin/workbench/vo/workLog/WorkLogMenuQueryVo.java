package com.iflytek.cdc.admin.workbench.vo.workLog;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class WorkLogMenuQueryVo {

    @ApiModelProperty(value = "系统来源编码")
    private String systemCode;

    @NotNull(message = "日志开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日志开始时间")
    private Date startDate;

    @NotNull(message = "日志结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "日志结束时间")
    private Date endDate;

    //智慧化预警系统参数
    @ApiModelProperty(value = "系统来源编码")
    private String appCode;
}
