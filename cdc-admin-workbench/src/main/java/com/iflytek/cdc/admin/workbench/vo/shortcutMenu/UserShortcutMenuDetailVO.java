package com.iflytek.cdc.admin.workbench.vo.shortcutMenu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UserShortcutMenuDetailVO {
    @NotBlank(message = "快捷菜单id不能为空")
    @Length(min = 0, max = 64, message = "快捷菜单id长度最大64")
    @ApiModelProperty(value = "快捷菜单id")
    private String shortcutMenuId;

    @NotNull(message = "排序号不能为空")
    @ApiModelProperty(value = "排序")
    private Integer orderNo;
}
