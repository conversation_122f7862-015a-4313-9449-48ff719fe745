ALTER TABLE app.tb_cdcdm_metadata_table_info if RENAME COLUMN create_datetime TO create_time;
ALTER TABLE app.tb_cdcdm_metadata_table_info RENAME COLUMN update_datetime TO update_time;

ALTER TABLE app.tb_cdcdm_metadata_table_info ADD creator_id varchar(100) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_info.creator_id IS '创建者id';
ALTER TABLE app.tb_cdcdm_metadata_table_info ADD updater_id varchar(100) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_info.updater_id IS '修改者id';


ALTER TABLE app.tb_cdcdm_metadata_table_column_info RENAME COLUMN create_datetime TO create_time;
ALTER TABLE app.tb_cdcdm_metadata_table_column_info RENAME COLUMN update_datetime TO update_time;

ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD creator_id varchar(100) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.creator_id IS '创建者id';
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD updater_id varchar(100) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.updater_id IS '修改者id';

ALTER TABLE app.tb_cdcdm_metadata_table_info DROP CONSTRAINT tb_cdcdm_metadata_table_info_un_table_code;
ALTER TABLE app.tb_cdcdm_metadata_table_info ADD CONSTRAINT tb_cdcdm_metadata_table_info_un UNIQUE (table_code,delete_flag);

ALTER TABLE app.tb_cdcdm_metadata_table_column_info DROP CONSTRAINT tb_cdcdm_metadata_table_column_info_un;
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD CONSTRAINT tb_cdcdm_metadata_table_column_info_un UNIQUE (table_id,column_name,delete_flag);
ALTER TABLE app.tb_cdcdm_metadata_table_column_info DROP CONSTRAINT tb_cdcdm_metadata_table_column_info_column_code;
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD CONSTRAINT tb_cdcdm_metadata_table_column_info_un_cd UNIQUE (column_code,delete_flag);


alter table tb_cdcmr_multichannel_topic_config add column if not exists data_parent_id varchar(256);
COMMENT ON COLUMN app.tb_cdcmr_multichannel_topic_config.data_parent_id IS '数据父id';

ALTER TABLE app.tb_cdcdm_metadata_table_info ADD ref_id varchar(100) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_info.ref_id IS '外键id';
ALTER TABLE app.tb_cdcdm_metadata_table_info ADD table_status varchar(50) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_info.table_status IS '实体表状态';

ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD column_length int NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.column_length IS '列长度';
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD column_precision int NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.column_precision IS '列精度';
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD is_required varchar(10) NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.is_required IS '是否必填';
ALTER TABLE app.tb_cdcdm_metadata_table_column_info ADD sort int NULL;
COMMENT ON COLUMN app.tb_cdcdm_metadata_table_column_info.sort IS '排序';



DROP TABLE IF EXISTS tb_cdcdm_metadata_table_sql_log;
CREATE TABLE tb_cdcdm_metadata_table_sql_log(
    id VARCHAR(255),
    table_id VARCHAR(255),
    sql TEXT,
    status VARCHAR(64),
    remark TEXT,
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)
);

COMMENT ON TABLE tb_cdcdm_metadata_table_sql_log IS '实体表同步sql';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.id IS '主键id';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.table_id IS '表id';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.sql IS 'sql内容';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.status IS '状态';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.remark IS '备注';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.creator_id IS '创建者id';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.creator IS '创建者';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.create_time IS '创建时间';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.updater_id IS '修改者id';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.updater IS '修改者';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.update_time IS '修改时间';
COMMENT ON COLUMN tb_cdcdm_metadata_table_sql_log.delete_flag IS '删除标记';

ALTER TABLE app.tb_cdccs_page_button_setting ADD app_show varchar(10) NULL;
COMMENT ON COLUMN app.tb_cdccs_page_button_setting.app_show IS 'app端是否显示';

ALTER TABLE app.tb_cdccs_page_view_col ADD app_show varchar(10) NULL;
COMMENT ON COLUMN app.tb_cdccs_page_view_col.app_show IS 'app端是否显示';
ALTER TABLE app.tb_cdccs_page_view_col ADD is_app_query varchar(10) NULL;
COMMENT ON COLUMN app.tb_cdccs_page_view_col.is_app_query IS 'app端是否是查询字段';

create table app.tb_cdcdm_metadata_table_info_test as select * from app.tb_cdcdm_metadata_table_info where 1=2;
create table app.tb_cdcdm_metadata_table_column_info_test as select * from app.tb_cdcdm_metadata_table_column_info where 1=2;

ALTER TABLE app.tb_cdcdm_metadata_table_column_info_test ADD CONSTRAINT tb_cdcdm_metadata_table_column_info_test_pk PRIMARY KEY (id);
ALTER TABLE app.tb_cdcdm_metadata_table_column_info_test ADD CONSTRAINT tb_cdcdm_metadata_table_column_info_test_un UNIQUE (column_code,delete_flag);

ALTER TABLE app.tb_cdcdm_metadata_table_info_test ADD CONSTRAINT tb_cdcdm_metadata_table_info_test_pk PRIMARY KEY (id);
ALTER TABLE app.tb_cdcdm_metadata_table_info_test ADD CONSTRAINT tb_cdcdm_metadata_table_info_test_un UNIQUE (table_code, delete_flag);


'163872192256278632',
'165193563640955056',
'166207564617351346',
'165198913022722224',
'165201250558673072',
'166195820029280434',
'166207585018446002',
'166210879258362034',
'163910940377481320',
'164990118556336237',
'165199752688828592',
'166207622599409842',
'166207649442955442',
'166409512905867442',
'166207535626322098'