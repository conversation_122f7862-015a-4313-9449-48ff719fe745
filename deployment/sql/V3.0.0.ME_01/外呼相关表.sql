ALTER TABLE app.tb_cdcmr_attachment ALTER COLUMN attachment_name TYPE varchar(255) USING attachment_name::varchar;

drop table if exists app.tb_cdcmr_outbound_template;
CREATE TABLE app.tb_cdcmr_outbound_template (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(100),
    template_content TEXT,
    "type" VARCHAR(255),
    "group" VARCHAR(255),
    speech_id VARCHAR(255),
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)

);

COMMENT ON TABLE app.tb_cdcmr_outbound_template IS '外呼模板维护';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.name IS '模板名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.template_content IS '模板内容';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.type IS '外呼类型';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.group IS '所属分组';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.speech_id IS '话术id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_template.delete_flag IS '删除标记，0表示未删除，1表示已删除';

drop table if exists app.tb_cdcmr_outbound_result_detail;
CREATE TABLE app.tb_cdcmr_outbound_result_detail (
    batch_id VARCHAR(255),
    record_id VARCHAR(255),
    dweller_name VARCHAR(255),
    telephone VARCHAR(255),
    call_time TIMESTAMP,
    task_time TIMESTAMP,
    plan_name VARCHAR(255),
    speech_name VARCHAR(255),
    call_time_length INTEGER,
    call_result VARCHAR(255),
    result_code VARCHAR(255),
    end_node VARCHAR(255),
    audio_url TEXT,
    local_url TEXT,
    sms_name VARCHAR(255),
    sms_content TEXT,
    actual_call_count INTEGER,
    connect_status VARCHAR(255),
    relation_id VARCHAR(255),
    id_card VARCHAR(255),
    wether_diarrhea VARCHAR(255),
    wether_diarrhea_convent VARCHAR(255),
    duration_time_convent VARCHAR(255),
    diarrhea_crowd VARCHAR(255),
    diarrhea_frequency_convent VARCHAR(255),
    extract_text1 TEXT,
    extract_text2 TEXT,
    extract_text3 TEXT,
    status VARCHAR(255),
    attachment_id VARCHAR(255),
    id VARCHAR(255) PRIMARY KEY,
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)
);

COMMENT ON TABLE app.tb_cdcmr_outbound_result_detail IS '外呼结果详情表';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.batch_id IS '批次';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.record_id IS '记录id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.dweller_name IS '姓名';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.telephone IS '手机号';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.call_time IS '电话拨打时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.task_time IS '任务开始时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.plan_name IS '方案名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.speech_name IS '话术模板名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.call_time_length IS '通话时长（秒）';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.call_result IS '电话任务状态描述';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.result_code IS '任务状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.end_node IS '接通状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.audio_url IS '转存后的音频地址';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.local_url IS '原始的音频地址';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.sms_name IS '短信模板名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.sms_content IS '短信内容';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.actual_call_count IS '拨打次数';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.connect_status IS '接通状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.relation_id IS '第三方ID';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.id_card IS '身份证';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.wether_diarrhea IS '当前话术返回状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.wether_diarrhea_convent IS '当前话术返回状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.duration_time_convent IS '持续时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.diarrhea_crowd IS '当前话术返回状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.diarrhea_frequency_convent IS '当前话术返回状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.extract_text1 IS '回答问题1';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.extract_text2 IS '回答问题2';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.extract_text3 IS '回答问题3';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.status IS '获取明细的状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.delete_flag IS '删除标记，0表示未删除，1表示已删除';
COMMENT ON COLUMN app.tb_cdcmr_outbound_result_detail.attachment_id IS '附件ID';

CREATE TABLE app.tb_cdcmr_outbound_record (
    speech_or_sms_id VARCHAR(255),
    type VARCHAR(255),
    batch_no VARCHAR(255),
    status VARCHAR(255),
    request_content TEXT,
    response_content TEXT,
    id VARCHAR(255) PRIMARY KEY,
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)
);

COMMENT ON COLUMN app.tb_cdcmr_outbound_record.speech_or_sms_id IS '话术或短信id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.type IS '外呼类型';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.batch_no IS '批次号';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.status IS '状态';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.request_content IS '请求内容';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.response_content IS '响应内容';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_record.delete_flag IS '删除标记，0表示未删除，1表示已删除';


CREATE TABLE app.tb_cdcmr_outbound_batch_exe_result (
    batch VARCHAR(255) NOT NULL,
    task_time BIGINT,
    speech_id VARCHAR(255),
    speech_name VARCHAR(255),
    plan_name VARCHAR(255),
    sms_id VARCHAR(255),
    sms_name VARCHAR(255),
    exec_status INTEGER,
    total_times INTEGER,
    complete_count INTEGER,
    call_connect_times INTEGER,
    dragging_times INTEGER,
    fail_task INTEGER,
    id VARCHAR(255) PRIMARY KEY,
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)
);

COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.batch IS '批次id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.task_time IS '任务开始时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.speech_id IS '话术id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.speech_name IS '话术名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.plan_name IS '任务名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.sms_id IS '短信id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.sms_name IS '短信名称';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.exec_status IS '执行状态: 1：未开始, 2：执行中, 3：已完成, 5：已取消, 6：审核未通过';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.total_times IS '批次总任务数';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.complete_count IS '完成任务数';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.call_connect_times IS '电话接通人次';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.dragging_times IS '执行中数量';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.fail_task IS '是否存在发送失败: 0：不存在, 1：存在';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.id IS '主键id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.creator_id IS '创建者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.creator IS '创建者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.create_time IS '创建时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.updater_id IS '修改者id';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.updater IS '修改者';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.update_time IS '修改时间';
COMMENT ON COLUMN app.tb_cdcmr_outbound_batch_exe_result.delete_flag IS '删除标记，0表示未删除，1表示已删除';


CREATE TABLE tb_cdcmr_outbound_person (
    outbound_record_id VARCHAR(255), -- 外呼记录id
    batch_no VARCHAR(255) NOT NULL, -- 批次号
    name VARCHAR(255) NOT NULL, -- 姓名（必须是汉字）
    telephone VARCHAR(255) NOT NULL, -- 号码
    id_card VARCHAR(255), -- 身份证
    relation_id VARCHAR(255), -- 第三方ID
    ext_property TEXT, -- 居民扩展信息
    parse_result TEXT, -- 解析之后的结果
    id VARCHAR(255) PRIMARY KEY,
    creator_id VARCHAR(255),
    creator VARCHAR(255),
    create_time TIMESTAMP,
    updater_id VARCHAR(255),
    updater VARCHAR(255),
    update_time TIMESTAMP,
    delete_flag VARCHAR(10)
);

COMMENT ON TABLE tb_cdcmr_outbound_person IS '外呼人员信息表';

-- 为特定字段添加注释
COMMENT ON COLUMN tb_cdcmr_outbound_person.outbound_record_id IS '外呼记录id';
COMMENT ON COLUMN tb_cdcmr_outbound_person.batch_no IS '批次号';
COMMENT ON COLUMN tb_cdcmr_outbound_person.name IS '姓名（必须是汉字）';
COMMENT ON COLUMN tb_cdcmr_outbound_person.telephone IS '号码';
COMMENT ON COLUMN tb_cdcmr_outbound_person.id_card IS '身份证';
COMMENT ON COLUMN tb_cdcmr_outbound_person.relation_id IS '第三方ID';
COMMENT ON COLUMN tb_cdcmr_outbound_person.ext_property IS '居民扩展信息';
COMMENT ON COLUMN tb_cdcmr_outbound_person.parse_result IS '解析之后的结果';