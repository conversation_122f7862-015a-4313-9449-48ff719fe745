
drop table if exists public.tb_cdcmr_syndrome_disease_warning_rule;
CREATE TABLE public.tb_cdcmr_syndrome_disease_warning_rule (
	id varchar(64) NOT NULL, -- 主键
	syndrome_disease_warning_id varchar(64) NULL, -- 症候群id
	syndrome_disease_warning_name varchar(64) NULL, -- 症候群名称
	rule_code varchar(64) NULL, -- 规则id
	rule_name varchar(64) NULL, -- 规则名称
	risk_level_id varchar(64) NULL, -- 风险等级id
	risk_level_detail_id varchar(64) NULL, -- 风险等级详情id
	warning_method varchar(64) NULL, -- 预警方法
	warning_indicator varchar(20) NULL, -- 指标 发病病例 MEDICAL_CASE 死亡病例 DEATH_CASE
	population_scope text NULL, -- 人群范围选项 ALL - 全部人群;  SPEC - 特定人群
	area_type varchar(32) NULL, -- 空间维度类型
	area_count int8 NULL, -- 空间数量
	area_level int8 NULL, -- 空间维度层级
	warning_threshold numeric(10, 2) NULL, -- 预警阈值
	model_param text NULL, -- 算法参数 大Json对象
	notes text NULL, -- 备注
	status varchar(10) NULL, -- 状态
	creator_id varchar(100) NULL, -- 创建人id
	creator varchar(100) NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(100) NULL, -- 更新人id
	updater varchar(100) NULL, -- 更新人
	update_time timestamp NULL, -- 更新时间
	delete_flag varchar(10) NULL, -- 删除标识: 0-未删除,1-已删除
	CONSTRAINT tb_cdcmr_syndrome_disease_warning_rule_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.id IS '主键';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.syndrome_disease_warning_id IS '症候群id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.syndrome_disease_warning_name IS '症候群名称';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.rule_code IS '规则id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.rule_name IS '规则名称';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.risk_level_id IS '风险等级id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.risk_level_detail_id IS '风险等级详情id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.warning_method IS '预警方法';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.warning_indicator IS '指标 发病病例 MEDICAL_CASE 死亡病例 DEATH_CASE';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.population_scope IS '人群范围选项 ALL - 全部人群;  SPEC - 特定人群';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.area_type IS '空间维度类型';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.area_count IS '空间数量';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.area_level IS '空间维度层级';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.warning_threshold IS '预警阈值';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.model_param IS '算法参数 大Json对象';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.notes IS '备注';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.status IS '状态';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.creator IS '创建人';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.updater_id IS '更新人id';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.updater IS '更新人';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.update_time IS '更新时间';
COMMENT ON COLUMN public.tb_cdcmr_syndrome_disease_warning_rule.delete_flag IS '删除标识: 0-未删除,1-已删除';