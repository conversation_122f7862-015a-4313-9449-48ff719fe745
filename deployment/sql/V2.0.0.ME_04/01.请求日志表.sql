-- public.tb_cdcmr_request_api_log definition

-- Drop table

-- DROP TABLE public.tb_cdcmr_request_api_log;

CREATE TABLE public.tb_cdcmr_request_api_log (
	id varchar(64) NULL, -- 主键id
	request_id varchar(64) NULL, -- 请求id
	request_url varchar(200) NULL, -- 请求路径
	request_param text NULL, -- 请求参数
	status varchar(64) NULL, -- 响应状态
	response text NULL, -- 响应值
	creator_id varchar(64) NULL, -- 创建人id
	creator varchar(64) NULL, -- 创建人
	create_time timestamp NULL, -- 创建时间
	updater_id varchar(64) NULL, -- 修改人id
	update_time timestamp NULL, -- 修改时间
	updater varchar(64) NULL -- 修改人
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN public.tb_cdcmr_request_api_log.id IS '主键id';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.request_id IS '请求id';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.request_url IS '请求路径';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.request_param IS '请求参数';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.status IS '响应状态';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.response IS '响应值';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.creator IS '创建人';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.updater_id IS '修改人id';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.update_time IS '修改时间';
COMMENT ON COLUMN public.tb_cdcmr_request_api_log.updater IS '修改人';