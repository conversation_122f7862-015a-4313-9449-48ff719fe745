-- 新建传染病维表
drop table if exists public.tb_cdcmr_infected_disease_info;
CREATE TABLE public.tb_cdcmr_infected_disease_info (
    id varchar(64) NOT NULL,
    infected_class_code varchar(64),
    infected_class_name varchar(64),
    disease_type_code varchar(64),
    disease_type_name varchar(64),
    parent_disease_id varchar(64),
    parent_disease_code varchar(64),
    parent_disease_name varchar(64),
    disease_code varchar(64),
    disease_name varchar(64),
    order_flag int4,
    transmission_type_code varchar(64),
    transmission_type_name varchar(64),
    management_type_code varchar(64),
    management_type_name varchar(64),
    alias varchar(64),
    notes text,
    status int2 NULL DEFAULT 1,
    delete_flag varchar(10) DEFAULT '0',
    create_time timestamp,
    update_time timestamp,
    creator_id varchar(100),
    creator varchar(100),
    updater_id varchar(100),
    updater varchar(100),
    CONSTRAINT tb_cdcmr_infected_disease_info_pkey PRIMARY KEY (id)
);COMMENT ON TABLE public.tb_cdcmr_infected_disease_info IS '传染病维表';

COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.id IS '传染病信息表主键ID';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.infected_class_code IS '传染病类型代码 01. 法定；  09.其他(包含自定义的)';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.infected_class_name IS '传染病类型名称';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.disease_type_code IS '传染病病种类型 甲乙丙其他';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.disease_type_name IS '传染病病种类型名称';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.parent_disease_id IS '父级病种id(构建树形结构使用id关联)';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.parent_disease_code IS '父级病种code';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.parent_disease_name IS '父级病种name';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.disease_code IS '病种code';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.disease_name IS '病种name';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.order_flag IS '排序字段';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.transmission_type_code IS '传播途径分类code';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.transmission_type_name IS '传播途径分类name';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.management_type_code IS '传染病类别管理代码';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.management_type_name IS '传染病类别管理名称';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.alias IS '别名';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.notes IS '备注';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.status IS '状态（启用状态 1启用；0未启用）';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.delete_flag IS '删除标识： 0-未删除，1-已删除';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.update_time IS '更新时间';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.creator IS '创建人姓名';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.updater_id IS '更新人id';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_info.updater IS '更新人name';


-- 新建传染病监测定义表
drop table if exists public.tb_cdcmr_infected_disease_monitor;
CREATE TABLE public.tb_cdcmr_infected_disease_monitor (
    id varchar(50) NOT NULL,
    infected_disease_info_id varchar(50),
    disease_code varchar(64),
    disease_name varchar(64),
    type varchar(50),
    title varchar(50),
    monitor_definition text,
    notes varchar(500),
    status int2 NULL DEFAULT 1,
    delete_flag varchar(10) DEFAULT '0',
    create_time timestamp,
    creator varchar(100),
    creator_id varchar(100),
    update_time timestamp,
    updater varchar(100),
    updater_id varchar(100),
    CONSTRAINT tb_cdcmr_infected_disease_monitor_pkey PRIMARY KEY (id)
);COMMENT ON TABLE public.tb_cdcmr_infected_disease_monitor IS '传染病监测定义';

COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.id IS '主键ID';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.infected_disease_info_id IS '传染病维表id（关联维表）';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.disease_code IS '病种code';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.disease_name IS '病种name';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.type IS '类型：diagnose_status:诊断状态/epidemic_level:疫情暴发分级';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.title IS '诊断状态/疫情暴发分级 名称';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.monitor_definition IS '监测定义';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.notes IS '备注';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.status IS '状态  0：关闭；1开启';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.delete_flag IS '删除标识  0：未删除；1：已删除';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.creator IS '创建人';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.update_time IS '更新时间';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.updater IS '更新人';
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_monitor.updater_id IS '更新人id';


-- 新建主数据修改记录表
drop table if exists public.tb_cdcmr_master_date_logs;
CREATE TABLE public.tb_cdcmr_master_date_logs (
    id varchar(50) NOT NULL,
    type varchar(50),
    origin_data text,
    new_data text,
    create_time timestamp,
    creator varchar(100),
    creator_id varchar(100),
    CONSTRAINT tb_cdcmr_master_date_logs_pkey PRIMARY KEY (id)
);COMMENT ON TABLE public.tb_cdcmr_master_date_logs IS '主数据操作日志表';

COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.id IS '主键id';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.type IS '主数据类型：传染病/症候群/症状/病原';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.origin_data IS '原始主数据基础信息';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.new_data IS '修改后主数据信息';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.creator IS '创建人';
COMMENT ON COLUMN public.tb_cdcmr_master_date_logs.creator_id IS '创建人id';


ALTER TABLE public.tb_cdcmr_infected_disease_warning ADD disease_id varchar(64) NULL;
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_warning.disease_id IS '疾病id（使用id构造树）';
ALTER TABLE public.tb_cdcmr_infected_disease_warning ADD parent_disease_id varchar(64) NULL;
COMMENT ON COLUMN public.tb_cdcmr_infected_disease_warning.parent_disease_id IS '父类疾病id（使用id构造树）';


ALTER TABLE public.tb_cdcmr_infected_disease_warning RENAME COLUMN infect_class_code TO infected_class_code;
ALTER TABLE public.tb_cdcmr_infected_disease_warning RENAME COLUMN infect_class_name TO infected_class_name;