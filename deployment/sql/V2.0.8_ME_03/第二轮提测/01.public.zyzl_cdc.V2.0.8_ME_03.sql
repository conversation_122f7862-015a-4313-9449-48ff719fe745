-- 新建值域字典表
drop table if exists public.tb_cdcdm_data_dict;
CREATE TABLE public.tb_cdcdm_data_dict (
    id varchar(64) NOT NULL,
    code varchar(64),
    name varchar(64),
    attr_count integer,
    attr_value_count integer,
    type varchar(10),
    notes text,
    status int2 DEFAULT 1,
    delete_flag varchar(10) DEFAULT '0',
    create_time timestamp,
    update_time timestamp,
    creator_id varchar(100),
    creator varchar(100),
    updater_id varchar(100),
    updater varchar(100),
    CONSTRAINT tb_cdcdm_data_dict_pkey PRIMARY KEY (id)
);

COMMENT ON COLUMN public.tb_cdcdm_data_dict.id IS '主键';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.code IS '编码（业务唯一键）';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.name IS '名称';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.attr_count IS '层级数';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.attr_value_count IS '值域数';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.type IS '1:值域;2:url;';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.notes IS '备注';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.status IS '状态（启用状态 1启用；0未启用）';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.delete_flag IS '删除标识： 0-未删除，1-已删除';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.update_time IS '更新时间';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.creator IS '创建人姓名';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.updater_id IS '更新人id';
COMMENT ON COLUMN public.tb_cdcdm_data_dict.updater IS '更新人name';


--新建值域 值表
drop table if exists public.tb_cdcdm_data_dict_value;
CREATE TABLE public.tb_cdcdm_data_dict_value (
    id varchar(64) NOT NULL,
    data_dict_id varchar(64),
    code varchar(64),
    name varchar(64),
    description varchar(64),
    parent_id varchar(64),
    notes text,
    status int2 DEFAULT 1,
    delete_flag varchar(10) DEFAULT '0',
    create_time timestamp,
    update_time timestamp,
    creator_id varchar(100),
    creator varchar(100),
    updater_id varchar(100),
    updater varchar(100),
    CONSTRAINT tb_cdcdm_data_dict_value_pkey PRIMARY KEY (id)
);

COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.id IS '主键';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.data_dict_id IS '值域ID';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.code IS '值编码';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.name IS '值名称';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.description IS '值描述';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.parent_id IS '父级ID';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.notes IS '备注';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.status IS '状态（启用状态 1启用；0未启用）';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.delete_flag IS '删除标识： 0-未删除，1-已删除';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.create_time IS '创建时间';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.update_time IS '更新时间';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.creator_id IS '创建人id';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.creator IS '创建人姓名';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.updater_id IS '更新人id';
COMMENT ON COLUMN public.tb_cdcdm_data_dict_value.updater IS '更新人name';


ALTER TABLE public.tb_cdcmr_infected_disease_warning DROP COLUMN if exists disease_id;
ALTER TABLE public.tb_cdcmr_infected_disease_warning DROP COLUMN if exists parent_disease_id;

ALTER TABLE public.tb_cdcmr_infected_disease_warning DROP CONSTRAINT if exists tb_cdcmr_infected_disease_warning_un;
ALTER TABLE public.tb_cdcmr_infected_disease_warning ADD CONSTRAINT tb_cdcmr_infected_disease_warning_un UNIQUE (disease_code);