package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.QueryWarnRuleInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.MedicalWarnRuleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Service
public class MedicalWarnApi {

    @Autowired
    private RestTemplate restTemplate;

    public List<MedicalWarnRuleVo> queryByDiseaseCodeAndRegionCode(QueryWarnRuleInfoFilter queryWarnRuleInfoFilter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/medicalWarnRule/queryByDiseaseCodeAndRegionCode",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(queryWarnRuleInfoFilter),
                new ParameterizedTypeReference<List<MedicalWarnRuleVo>>(){}
        ).getBody();
    }
}
