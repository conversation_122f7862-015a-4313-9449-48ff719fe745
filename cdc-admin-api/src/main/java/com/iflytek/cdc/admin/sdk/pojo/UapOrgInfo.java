package com.iflytek.cdc.admin.sdk.pojo;


import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/19 14:50
 **/
@Data
public class UapOrgInfo implements Serializable {

    /**
     * uap机构id
     **/
    @Alias(value = "sysOrgId")
    private  String  uapOrgId;

    /**
     * uap机构名称
     **/
    @Alias(value = "sysOrgName")
    private  String  uapOrgName;


}
