package com.iflytek.cdc.admin.sdk.pojo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName InfecDisTreeInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/1 11:13
 * @Version 1.0
 */
@Data
public class InfecDisTreeInfo implements Comparable<InfecDisTreeInfo>{

    private String code;

    private String value;

    private List<InfecDisTreeInfo> children;

    @Override
    public int compareTo(InfecDisTreeInfo o) {
        return this.getCode().compareTo(o.getCode());
    }
}
