package com.iflytek.cdc.admin.sdk.api;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.SyndromeInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.SyndInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 标准字典api
 * <AUTHOR>
 * @date 2021/8/23 10:54
 **/
public class NormCodeApi {

    @Autowired
    private   RestTemplate  restTemplate;


    public PageInfo<SyndInfo> syndPageInfo(SyndromeInfoFilter  filter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/syndrome/syndromeInfoPage",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(filter),
                new ParameterizedTypeReference<PageInfo<SyndInfo>>(){}
        ).getBody();
    }

    public SyndInfo  getSyndInfo(String  code){
        return restTemplate.exchange(String.format("http://%s/%s/pt/syndrome/syndInfoByCode?code=%s",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION,code),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<SyndInfo>(){}
        ).getBody();
    }
}
