package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.DictRelationFilter;
import com.iflytek.cdc.admin.sdk.pojo.DictValueInfo;
import com.iflytek.cdc.admin.sdk.pojo.InfectedValueInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/10 17:02
 **/
@Service
public class DictRelationApi {

    @Autowired
    private RestTemplate restTemplate;

    public List<DictValueInfo>  getDictValueInfo(DictRelationFilter filter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/dict/relation/relationDictValueInfo",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(filter),
                new ParameterizedTypeReference<List<DictValueInfo>>(){}
        ).getBody();
    }

    public List<InfectedValueInfo>  getInfectedValueDetail(DictRelationFilter filter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/dict/relation/infectedValueDetail",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(filter),
                new ParameterizedTypeReference<List<InfectedValueInfo>>(){}
        ).getBody();
    }
}
