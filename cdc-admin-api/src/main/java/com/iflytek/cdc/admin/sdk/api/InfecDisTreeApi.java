package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * @ClassName InfecDisTreeApi
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/2 15:23
 * @Version 1.0
 */
@Service
public class InfecDisTreeApi {

    @Autowired
    private  RestTemplate restTemplate;


    public List<InfecDisTreeInfo> queryInfecDisTree(){
        return restTemplate.exchange(String.format("http://%s/%s/pt/infectious/queryInfecDisTree",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<InfecDisTreeInfo>>(){}
        ).getBody();
    }
}
