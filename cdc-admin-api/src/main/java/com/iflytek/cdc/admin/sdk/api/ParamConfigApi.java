package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 参数管理api
 * <AUTHOR>
 * @date 2021/8/20 9:15
 **/
@Service
public class ParamConfigApi {

    @Autowired
    private  RestTemplate  restTemplate;


    /**
     * 查询参数值接口
     * @param filter
     * @return
     **/
    public ParamInfo  getParamInfo(ParamInfoFilter  filter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/param/config/paramInfoByCode",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(filter),
                new ParameterizedTypeReference<ParamInfo>(){}
        ).getBody();
    }

    /**
     *  批量查询参数值接口
     **/
    public List<ParamInfo>  getParamInfos(List<ParamInfoFilter> filters){
        return restTemplate.exchange(String.format("http://%s/%s/pt/param/config/paramListByCodeList",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(filters),
                new ParameterizedTypeReference<List<ParamInfo>>(){}
        ).getBody();
    }
}
