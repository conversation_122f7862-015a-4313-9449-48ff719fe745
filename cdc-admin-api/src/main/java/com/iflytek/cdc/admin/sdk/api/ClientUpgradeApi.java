package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionParam;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionVO;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName ClientUpgradeApi
 * @Description 客户端升级服务api
 * <AUTHOR>
 * @Date 2021/8/26 16:17
 * @Version 1.0
 */

@Service
public class ClientUpgradeApi {

    @Autowired
    private  RestTemplate restTemplate;



    public List<PackageVersionVO> queryClientUpgradeVersionInfo(ClientUpgradeFilter clientUpgradeFilter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/client/upgrade/queryClientUpgradeVersionInfo",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(clientUpgradeFilter),
                new ParameterizedTypeReference<List<PackageVersionVO>>(){}
        ).getBody();
    }
}
