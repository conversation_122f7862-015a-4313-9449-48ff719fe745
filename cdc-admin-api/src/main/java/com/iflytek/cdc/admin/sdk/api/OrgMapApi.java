package com.iflytek.cdc.admin.sdk.api;

import com.iflytek.cdc.admin.sdk.constants.SdkConstants;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 机构映射api
 * <AUTHOR>
 * @date 2021/8/19 15:19
 **/
@Service
public class OrgMapApi {

    @Autowired
    private  RestTemplate  restTemplate;



    /**
     * 根据his查询uap机构信息
     * @param hisInfoFilter
     * @return
     **/
    public UapOrgInfo  getUapByHisCode(HisInfoFilter  hisInfoFilter){
        return restTemplate.exchange(String.format("http://%s/%s/pt/org/mapping/uapOrgByHis",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION),
                HttpMethod.POST,
                new HttpEntity<>(hisInfoFilter),
                new ParameterizedTypeReference<UapOrgInfo>(){}
        ).getBody();
    }

    /**
     * 根据uap查询his机构信息
     * @param uapOrgId
     * @return
     **/
    public HisOrgInfo  getHisByUapId(String uapOrgId){
        return restTemplate.exchange(String.format("http://%s/%s/pt/org/mapping/hisOrgByUap?uapOrgId=%s",
                SdkConstants.SERVICE_NAME, SdkConstants.CURRENT_VERSION,uapOrgId),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<HisOrgInfo>(){}
        ).getBody();
    }
}
