package com.iflytek.cdc.admin.sdk.pojo;

import lombok.Data;

import javax.validation.constraints.Max;

/**
 * <AUTHOR>
 * @date 2021/8/23 10:42
 **/
@Data
public class SyndInfo {

    /**
     * 症候群编码
     */
    private String syndromeCode;
    /**
     * 症候群名称
     */
    private String syndromeName;
    /**
     * 症状规则
     */
    private String symptomsRule;
    /**
     * 增强规则
     */
    private String heightenRule;
    /**
     * 覆盖事件
     */
    private String coverEvent;
    /**
     * 启用状态;0-不启用 1-启用
     */
    private String useStatus;
    /**
     * 备注
     */
    private String remark;

}
