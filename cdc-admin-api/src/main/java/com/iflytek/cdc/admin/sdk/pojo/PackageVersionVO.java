package com.iflytek.cdc.admin.sdk.pojo;


import com.iflytek.cdc.admin.sdk.util.VersionCompareUtil;
import lombok.Data;

@Data
public class PackageVersionVO implements Comparable<PackageVersionVO> {


    private String version;

    private Integer subType;

    private String descriptionFile;

    private String resourceFile;


    @Override
    public int compareTo(PackageVersionVO packageVersionVO) {
        return VersionCompareUtil.compareVersionV2(this.getVersion(), packageVersionVO.getVersion());
    }
}
