import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.job.OutboundResultGetJobHandler;
import com.iflytek.cdc.admin.service.FileService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class OutboundResultTests {
    @Resource
    private OutboundResultGetJobHandler outboundResultGetJobHandler;

    @Test
    public void test(){
        outboundResultGetJobHandler.execute(null);
    }

    @Resource
    private FileService fileService;

    @Test
    public void test2(){
        fileService.upload("https://www.xfzyzl.com/yetavoice/ant/oss/aicc-bucket/fullAudio/2025/01/20/1867482819403838_325ea059-1f11-4010-8bb3-c5f76e11f5c4.wav", "20250120");
    }
}
