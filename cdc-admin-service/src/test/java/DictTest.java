import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.annotation.ExcelColumn;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.datamodel.service.ValueDomainService;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.util.ExcelUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.Data;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

import static com.iflytek.cdc.admin.datamodel.service.impl.ValueDomainServiceImpl.TB_CDCDM_DATA_DICT;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class DictTest {

    @Resource
    private ValueDomainService valueDomainService;

    @Resource
    private BatchUidService batchUidService;

    @Test
    public void test() throws FileNotFoundException {

        List<Value> test123 = ExcelUtils.readExcel(Value.class, "值域6.xlsx",
                new FileInputStream("C:\\Users\\<USER>\\Desktop\\流行性乙脑值域.xlsx"));
        test123.forEach(m -> {
            TbCdcdmDataDict tbCdcdmDataDict = new TbCdcdmDataDict();
            tbCdcdmDataDict.setId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_DICT)));
            tbCdcdmDataDict.setAttrCount(1);
            tbCdcdmDataDict.setName(m.getType());
            tbCdcdmDataDict.setType("1");
            valueDomainService.editValueDomain(tbCdcdmDataDict, new UapUserPo());
            List<TbCdcdmDataDictValue> dictValues = new ArrayList<>();
            String[] split = m.getValue().split("[；;]");
            for (String s : split) {
                TbCdcdmDataDictValue dataDictValue = new TbCdcdmDataDictValue();
                dataDictValue.setCode(s);
                dataDictValue.setName(s);
                dataDictValue.setDataDictId(tbCdcdmDataDict.getId());
                dictValues.add(dataDictValue);
            }
            valueDomainService.addValueDomainDetail(dictValues);
        });
    }

    @Data
    public static class Value {
        @ExcelColumn(name = "type", column = 0)
        private String type;

        @ExcelColumn(name = "value", column = 1)
        private String value;
    }
}
