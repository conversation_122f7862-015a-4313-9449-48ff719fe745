package com.iflytek.cdc.admin.datamodel.service;

import com.iflytek.cdc.admin.datamodel.listener.ValueDomainImportListener;
import com.iflytek.cdc.admin.datamodel.model.dto.ValueDomainImportDTO;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 值域导入功能测试
 */
//@RunWith(SpringRunner.class)
@SpringBootTest
public class ValueDomainImportTest {

    @Test
    public void testValueDomainImportDTO() {
        // 测试单层级
        ValueDomainImportDTO dto1 = new ValueDomainImportDTO();
        dto1.setLevel1("是");
        assertEquals(1, dto1.getMaxLevel());
        assertFalse(dto1.isEmptyRow());

        // 测试双层级
        ValueDomainImportDTO dto2 = new ValueDomainImportDTO();
        dto2.setLevel1("性别");
        dto2.setLevel2("男");
        assertEquals(2, dto2.getMaxLevel());
        assertFalse(dto2.isEmptyRow());

        // 测试三层级
        ValueDomainImportDTO dto3 = new ValueDomainImportDTO();
        dto3.setLevel1("内科");
        dto3.setLevel2("呼吸内科");
        dto3.setLevel3("肺炎");
        assertEquals(3, dto3.getMaxLevel());
        assertFalse(dto3.isEmptyRow());

        // 测试空行
        ValueDomainImportDTO dto4 = new ValueDomainImportDTO();
        assertEquals(0, dto4.getMaxLevel());
        assertTrue(dto4.isEmptyRow());
    }

    @Test
    public void testValueDomainImportListener() {
        String dataDictId = "test_dict_id";
        String dataDictName = "测试值域";

        ValueDomainImportListener listener = new ValueDomainImportListener(dataDictId, dataDictName);

        // 测试单层级数据
        ValueDomainImportDTO dto1 = new ValueDomainImportDTO();
        dto1.setLevel1("是");
        listener.invoke(dto1, null);

        // 测试双层级数据
        ValueDomainImportDTO dto2 = new ValueDomainImportDTO();
        dto2.setLevel1("性别");
        dto2.setLevel2("男");
        listener.invoke(dto2, null);

        // 测试三层级数据
        ValueDomainImportDTO dto3 = new ValueDomainImportDTO();
        dto3.setLevel1("内科");
        dto3.setLevel2("呼吸内科");
        dto3.setLevel3("肺炎");
        listener.invoke(dto3, null);

        List<TbCdcdmDataDictValue> result = listener.getDataDictValues();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);
        assertEquals(3, listener.getMaxLevel());

        // 验证数据内容
        boolean foundYes = false;
        boolean foundGender = false;
        boolean foundMale = false;
        boolean foundInternal = false;
        boolean foundRespiratory = false;
        boolean foundPneumonia = false;

        for (TbCdcdmDataDictValue value : result) {
            if ("是".equals(value.getName())) {
                foundYes = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertNull(value.getParentId()); // 根节点没有父级
            } else if ("性别".equals(value.getName())) {
                foundGender = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertNull(value.getParentId()); // 根节点没有父级
            } else if ("男".equals(value.getName())) {
                foundMale = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertEquals("性别", value.getParentId()); // 父级是"性别"
            } else if ("内科".equals(value.getName())) {
                foundInternal = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertNull(value.getParentId()); // 根节点没有父级
            } else if ("呼吸内科".equals(value.getName())) {
                foundRespiratory = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertEquals("内科", value.getParentId()); // 父级是"内科"
            } else if ("肺炎".equals(value.getName())) {
                foundPneumonia = true;
                assertEquals(dataDictId, value.getDataDictId());
                assertEquals("呼吸内科", value.getParentId()); // 父级是"呼吸内科"
            }
        }

        assertTrue("应该找到'是'节点", foundYes);
        assertTrue("应该找到'性别'节点", foundGender);
        assertTrue("应该找到'男'节点", foundMale);
        assertTrue("应该找到'内科'节点", foundInternal);
        assertTrue("应该找到'呼吸内科'节点", foundRespiratory);
        assertTrue("应该找到'肺炎'节点", foundPneumonia);
    }
} 