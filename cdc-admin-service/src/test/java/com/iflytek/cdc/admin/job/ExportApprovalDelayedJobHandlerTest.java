package com.iflytek.cdc.admin.job;

import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class ExportApprovalDelayedJobHandlerTest {

    @Resource
    private ExportApprovalDelayedJobHandler exportApprovalDelayedJobHandler;

    @Test
    public void testJob() {
        exportApprovalDelayedJobHandler.execute(null);
    }
}
