package com.iflytek.cdc.admin.service;


import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class StreetInfoServiceTest {

    @Resource
    private StreetInfoService streetInfoService;

    @Before
    public void setUp() {
        // 伪造用户权限
        UapUserPo user = new UapUserPo();
        user.setId("127893589446361218");
        user.setName("zhuangli5");
        user.setLoginName("17671433479");
        user.setOrgProvinceCode("340000");

        USER_INFO.set(user);
    }

    @After
    public void tearDown() {
        USER_INFO.remove();
    }

    @Test
    public void loadCurrUserArea() {
        // 测试环境账号 15102730412
//        String loginUserName = "15102730412";
        String loginUserName = USER_INFO.get().getLoginName();
        String cityName = "滁州市";
        String districtName = "天长市";

        LoginUserAreaVO areas = streetInfoService.loadCurrUserArea(loginUserName);
        System.out.println(cityName + "-市编码: " + areas.getCityCode(cityName));
        System.out.println(districtName + "-功能区编码: " + areas.getStandardDistrictByName(cityName, districtName));
        System.out.println(districtName + "-行政区编码: " + areas.getDistrictCode(cityName, districtName));
    }
}
