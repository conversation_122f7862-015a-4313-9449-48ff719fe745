package com.iflytek.cdc.admin.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.common.enums.AreaLevelEnum;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.TbCdcmrPopulationDataInfoQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo;
import com.iflytek.cdc.admin.vo.PopulationDataInfoVO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class TbCdcmrPopulationDataInfoServiceTest {

    @Resource
    private TbCdcmrPopulationDataInfoService populationDataInfoService;

    @Before
    public void setUp() {
        // 伪造用户权限
        UapUserPo user = new UapUserPo();
        user.setId("127893589446361218");
        user.setName("zhuangli5");
        user.setLoginName("17671433479");
        user.setOrgProvinceCode("340000");

        USER_INFO.set(user);
    }

    @After
    public void tearDown() {
        USER_INFO.remove();
    }

    @Test
    public void downloadTemplate() {
        String filePath = "D:\\人员信息模板-下载.xlsx";
        byte[] bytes = populationDataInfoService.downloadTemplate(USER_INFO.get().getLoginName());
        FileUtil.writeBytes(bytes, filePath);
    }

    @Test
    public void importByFile() {
        String filePath = "D:\\人员信息模板-导入.xlsx";
        populationDataInfoService.importByFile(FileUtil.getInputStream(filePath), USER_INFO.get().getId(), USER_INFO.get().getLoginName());
    }

    @Test
    public void pageList() {
        TbCdcmrPopulationDataInfoQueryDTO query = new TbCdcmrPopulationDataInfoQueryDTO();
        query.setPageIndex(1);
        query.setPageSize(20);
        query.setProvinceCode("340000");

        PageInfo<TbCdcmrPopulationDataInfo> pageInfo = populationDataInfoService.pageList(query, USER_INFO.get().getLoginName());
        System.out.println("总条数: " + pageInfo.getSize());
        pageInfo.getList().forEach(System.out::println);
    }

    @Test
    public void load() {
        System.out.println(populationDataInfoService.load("176512782445838513"));
    }

    @Test
    public void update() {
        TbCdcmrPopulationDataInfo entity = populationDataInfoService.load("176512782445838513");
        populationDataInfoService.update(entity, USER_INFO.get().getLoginName());
    }

    @Test
    public void listByAreaCodes() {
        TbCdcmrPopulationDataInfoQueryDTO query = new TbCdcmrPopulationDataInfoQueryDTO();
        query.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
        query.setAreaCodes(ListUtil.of("340000"));
        List<PopulationDataInfoVO> list = populationDataInfoService.listByAreaCodes(query);
        System.out.println("总条数: " + list.size());
        list.forEach(System.out::println);
    }

    @Test
    public void groupByAreaCodes() {
        TbCdcmrPopulationDataInfoQueryDTO query = new TbCdcmrPopulationDataInfoQueryDTO();
        query.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
        query.setAreaCodes(ListUtil.of("340000"));
        Map<String, PopulationDataInfoVO> map = populationDataInfoService.groupByAreaCodes(query);
        System.out.println("总条数: " + map.size());
        map.forEach((k, v) -> System.out.println(k + " : " + v));
    }

    @Test
    public void statByAreaCodes() {
        TbCdcmrPopulationDataInfoQueryDTO query = new TbCdcmrPopulationDataInfoQueryDTO();
        query.setProvinceCodes(ListUtil.of("340000"));
        query.setCityCodes(ListUtil.of("340200"));
        System.out.println(populationDataInfoService.statByAreaCodes(query));
    }

    @Test
    public void listByAreaCodesAndTime() {
        TbCdcmrPopulationDataInfoQueryDTO query = new TbCdcmrPopulationDataInfoQueryDTO();
        query.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
        query.setAreaCodes(ListUtil.of("340000"));
        query.setDateList(ListUtil.of(new Date()));
        System.out.println(populationDataInfoService.listByAreaCodesAndTime(query));
    }
}
