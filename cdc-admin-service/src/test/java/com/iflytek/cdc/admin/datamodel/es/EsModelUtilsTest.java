package com.iflytek.cdc.admin.datamodel.es;

import cn.hutool.core.collection.ListUtil;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class EsModelUtilsTest {

    @Resource
    private EsModelUtils esModelUtils;

    @Test
    public void buildEsIndexTemplateTest() {
        // 传染病(ads_fs_infect_process_info) 083de6da172833fe8e81e5342661f30b
        // 症候群(ads_fs_syndrome_process_info) b45a0b317ab2f0882eba1e5fc2293b46
        // EDR库(ads_fs_edr_info) 0376abbdb7ae6ca2dab695f26ffddbfe
        // 住院病历(ads_fs_inpat_medical_info) d4a557c8f9842f0e87c8d5385d72f069
        // 门诊病历(ads_fs_outpat_medical_info) 66f44eafd653704df3de7af51daf322a
        String modelId = "083de6da172833fe8e81e5342661f30b";
        String modelVersionId = "083de6da172833fe8e81e5342661f30b";
        String indexAlias = "cdc-model-" + modelId;
        System.out.println(esModelUtils.buildEsIndexTemplate(modelId, modelVersionId, indexAlias));
    }

    @Test
    public void batchBuild() {
        List<String> modelIds = ListUtil.of("083de6da172833fe8e81e5342661f30b",
                "b45a0b317ab2f0882eba1e5fc2293b46",
                "0376abbdb7ae6ca2dab695f26ffddbfe",
                "d4a557c8f9842f0e87c8d5385d72f069",
                "66f44eafd653704df3de7af51daf322a");
        modelIds.forEach(modelId -> {
            String indexAlias = "cdc-model-" + modelId;
            esModelUtils.buildEsIndexTemplate(modelId, modelId, indexAlias);
        });
    }

    @Test
    public void batchDelete() throws IOException {
        List<String> concreteIndexNames = ListUtil.of("cdc-model-083de6da172833fe8e81e5342661f30b-2025.02.26-000001",
                "cdc-model-083de6da172833fe8e81e5342661f30b-2025.02.27-000002");
        for (String indexName : concreteIndexNames) {
            esModelUtils.deleteConcreteIndex(indexName);
        }
    }
}
