package std;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.controller.old.AddressStandardizeController;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressFuzzyNormalizationDTO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressStandardVo;
import com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig;
import com.iflytek.cdc.admin.job.StdWordAutoJob;
import com.iflytek.cdc.admin.mapper.TbCdcmrStdTextConfigMapper;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.CsvUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class StdAddressCompanyTest {

    @Resource
    private AddressStandardizeController addressStandardizeController;
    @Resource
    private TbCdcmrStdTextConfigMapper tbCdcmrStdTextConfigMapper;
    @Resource
    private BatchUidService batchUidService;
    @Resource
    private StdWordAutoJob stdWordAutoJob;

    @Test
    public void testSTDWordAutoJob(){
        stdWordAutoJob.autoUpdateTextConfig();
    }

    @Test
    public void testCompanySTD(){
        AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyCompanyNormalization(AddressFuzzyNormalizationDTO.builder()
                .addressDetail("合肥很好很合适")
                .districtCode("340100").types("科教文化服务").build());
        System.out.println(addressStandardVo);
    }

    @Test
    public void testStdComapnyFromCsv(){
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\company-error.csv");
        System.out.println(csvRecords.size());

        List<Object[]> data = new ArrayList<>();
        csvRecords.forEach(e -> {
//            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            String newName = e.get(0);
            String types = null;
            if (StringUtils.isNotEmpty(e.get(2)) && "1".equals(e.get(2))) {
                types = "科教文化服务";
                newName = eduProcess(newName);
            }

//            String newName = preProcess(e.get(0));
            List<String> ignoreList = getIgnoreList();
            if (ignoreList.contains(newName)) {
                return;
            }
            AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyCompanyNormalization(AddressFuzzyNormalizationDTO.builder()
                    .addressDetail(newName)
                    .districtCode("340100").types(types).build());
            Object[] obj = new Object[8];
            obj[0] = e.get(0);
            obj[1] = e.get(1);
            obj[2] = addressStandardVo.getAddressAreaCode();
            obj[3] = addressStandardVo.getAddressAreaName();
            obj[4] = addressStandardVo.getAddressProvinceName()+addressStandardVo.getAddressCityName()+addressStandardVo.getAddressDistrictName();
            obj[5] = addressStandardVo.getAddressTownName();
            obj[6] = newName;
            obj[7] = e.get(2);
            data.add(obj);
        });

        Object[] headers = new Object[8];
        headers[0] = "原始名称";
        headers[1] = "原始名称报卡数量";
        headers[2] = "标化后名称";
        headers[3] = "标化后编码";
        headers[4] = "标化后省市区";
        headers[5] = "标化后街道(村)";
        headers[6] = "标化传参值";
        headers[7] = "是否教育机构";

        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, data, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\company-std-"+format+".csv");
    }

    @Test
    public void testStdConfig(){
        List<TbCdcmrStdTextConfig> configList = new ArrayList<>();
//
//        List<String> keys = new ArrayList<>();
//        keys.add("无");
//        keys.add("-");
//        keys.add("事业单位");
//        keys.add("无单位");
//        keys.add("不详");
//        keys.add("/");
//        keys.add("无工作");
//        keys.add("无工作单位");
//        keys.add("农民");
//        keys.add("退休");
//        keys.add("无职业");
//        keys.add("具体不详");
//        keys.add("自由职业");
//        keys.add("医保整理户2");
//        keys.add("灵活就业");
//        keys.add("个体");
//        keys.add("务农");
//        keys.add("--");
//        keys.add("个体户");
//        keys.add("无无无");
//        keys.add("无业");
//        keys.add("在家务农");
//        keys.add("目前无");
//        keys.add("暂不详");
//        keys.add("暂无");
//        keys.add("个体经营");
//        AtomicInteger ai = new AtomicInteger(1);
//        keys.forEach(e -> {
//            TbCdcmrStdTextConfig tbCdcmrStdTextConfig = new TbCdcmrStdTextConfig();
//            tbCdcmrStdTextConfig.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
//            tbCdcmrStdTextConfig.setStdType("单位标化");
//            tbCdcmrStdTextConfig.setItemType("通用");
//            tbCdcmrStdTextConfig.setItemValue(e);
//            tbCdcmrStdTextConfig.setRuleType("1");
//            tbCdcmrStdTextConfig.setNotes("单位通用规则-停用词");
//            tbCdcmrStdTextConfig.setCreateTime(new Date());
//            tbCdcmrStdTextConfig.setOrder(ai.longValue());
//            ai.addAndGet(1);
//            configList.add(tbCdcmrStdTextConfig);
//        });

        List<String> keys2 = new ArrayList<>();
        keys2.add("无");
        keys2.add("-");
        keys2.add("无地址");
        keys2.add("不详");
        keys2.add("/");
        keys2.add("具体不详");
        keys2.add("--");
        keys2.add("无无无");
        keys2.add("暂不详");
        keys2.add("暂无");
        AtomicInteger ai3 = new AtomicInteger(1);
        keys2.forEach(e -> {
            TbCdcmrStdTextConfig tbCdcmrStdTextConfig = new TbCdcmrStdTextConfig();
            tbCdcmrStdTextConfig.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
            tbCdcmrStdTextConfig.setStdType("地址标化");
            tbCdcmrStdTextConfig.setItemType("通用");
            tbCdcmrStdTextConfig.setItemValue(e);
            tbCdcmrStdTextConfig.setRuleType("1");
            tbCdcmrStdTextConfig.setNotes("单位通用规则-停用词");
            tbCdcmrStdTextConfig.setCreateTime(new Date());
            tbCdcmrStdTextConfig.setOrder(ai3.longValue());
            ai3.addAndGet(1);
            configList.add(tbCdcmrStdTextConfig);
        });

//        Map<String, String> ruleMap = new HashMap<>();
//        ruleMap.put("工地", "");
//        AtomicInteger ai1 = new AtomicInteger(1);
//        ruleMap.forEach((k, v) -> {
//            TbCdcmrStdTextConfig tbCdcmrStdTextConfig = new TbCdcmrStdTextConfig();
//            tbCdcmrStdTextConfig.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
//            tbCdcmrStdTextConfig.setStdType("单位标化");
//            tbCdcmrStdTextConfig.setItemType("通用");
//            tbCdcmrStdTextConfig.setItemValue(k);
//            tbCdcmrStdTextConfig.setReplaceValue(v);
//            tbCdcmrStdTextConfig.setRuleType("2");
//            tbCdcmrStdTextConfig.setNotes("单位通用规则-文本替换");
//            tbCdcmrStdTextConfig.setCreateTime(new Date());
//            tbCdcmrStdTextConfig.setOrder(ai1.longValue());
//            ai1.addAndGet(1);
//            configList.add(tbCdcmrStdTextConfig);
//        });
//
//        List<String> keys1 = new ArrayList<>();
//        keys1.add("院");
//        keys1.add("幼儿园");
//        keys1.add("学");
//        keys1.add("中");
//        AtomicInteger ai2 = new AtomicInteger(1);
//        keys1.forEach(e -> {
//            TbCdcmrStdTextConfig tbCdcmrStdTextConfig = new TbCdcmrStdTextConfig();
//            tbCdcmrStdTextConfig.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
//            tbCdcmrStdTextConfig.setStdType("单位标化");
//            tbCdcmrStdTextConfig.setItemType("科教文化服务");
//            tbCdcmrStdTextConfig.setItemValue(e);
//            tbCdcmrStdTextConfig.setRuleType("3");
//            tbCdcmrStdTextConfig.setNotes("教育单位-截取字符串");
//            tbCdcmrStdTextConfig.setCreateTime(new Date());
//            tbCdcmrStdTextConfig.setOrder(ai2.longValue());
//            ai2.addAndGet(1);
//            configList.add(tbCdcmrStdTextConfig);
//        });
        configList.forEach(tbCdcmrStdTextConfig -> {
            tbCdcmrStdTextConfigMapper.insert(tbCdcmrStdTextConfig);
        });
    }

    public static void main(String[] args) {
        System.out.println(preProcess("        肥东三中9（32）班\n"));
    }

    private static String replace(String name) {
        Map<String, String> ruleMap = new HashMap<>();
        ruleMap.put("工地", "");
        String changeName = name;
        Iterator<Map.Entry<String, String>> iterator = ruleMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            changeName = changeName.replace(next.getKey(), next.getValue());
        }
        return changeName;
    }
    private static String eduProcess(String name) {
        List<String> keys = new ArrayList<>();
        keys.add("院");
        keys.add("幼儿园");
        keys.add("学");
        keys.add("中");
        String changeName = name;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            int index = name.indexOf(key);
            if (index > 0) {
                changeName = name.substring(0, Math.min(index+key.length(), name.length()));
                break;
            }
        }
        return changeName;
    }

    private static String preProcess(String name) {
        String v1 = StringUtils.trim(name);
        String v2 = v1.replaceAll("[^\u4e00-\u9fa5a-zA-Z0-9]", "");
        return v2;
    }

    private List<String> getIgnoreList() {
        List<String> keys = new ArrayList<>();
        keys.add("无");
        keys.add("-");
        keys.add("事业单位");
        keys.add("无单位");
        keys.add("不详");
        keys.add("/");
        keys.add("无工作");
        keys.add("无工作单位");
        keys.add("农民");
        keys.add("退休");
        keys.add("无职业");
        keys.add("具体不详");
        keys.add("自由职业");
        keys.add("医保整理户2");
        keys.add("灵活就业");
        keys.add("个体");
        keys.add("务农");
        keys.add("--");
        keys.add("个体户");
        keys.add("无无无");
        keys.add("无业");
        keys.add("在家务农");
        keys.add("目前无");
        keys.add("暂不详");
        keys.add("暂无");
        keys.add("个体经营");
        return keys;
    }
}
