package std;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import org.apache.commons.csv.CSVRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.simmetrics.StringMetric;
import org.simmetrics.metrics.StringMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.CsvUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class TextSimilarityTest {

    @Test
    public void testSimilarity() {
        List<Object[]> data = new ArrayList<>();

        StringMetric smithWatermanGotoh = StringMetrics.smithWatermanGotoh();
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\hefei-report-address-similarity.csv");
        csvRecords.forEach(e -> {
            String ori = e.get(0);
            String std = e.get(1);
            double similarity = smithWatermanGotoh.compare(ori, std);

            Object[] obj = new Object[9];
            obj[0] = ori;
            obj[1] = std;
            obj[2] = similarity;
            data.add(obj);
        });

        Object[] headers = new Object[3];
        headers[0] = "源地址";
        headers[1] = "标化地址";
        headers[2] = "相似度%";

        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, data, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\hefei-report-address-similarity-"+format+".csv");

    }
}
