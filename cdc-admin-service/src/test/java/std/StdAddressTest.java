package std;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.controller.old.AddressStandardizeController;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressFuzzyNormalizationDTO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressStandardVo;
import com.iflytek.cdc.admin.dto.amap.inputtips.Tips;
import com.iflytek.cdc.admin.dto.amap.search.PoisV2;
import com.iflytek.cdc.admin.service.AmapService;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.CsvUtil;

import javax.annotation.Resource;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class StdAddressTest {

    @Resource
    private AddressStandardizeController addressStandardizeController;

    @Resource
    private AmapService amapService;

    @Test
    public void testSTD(){
        AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyAddressNormalization(AddressFuzzyNormalizationDTO.builder()
                .addressDetail("安徽省合肥市庐江县乐桥镇大化许瑶队")
                .refresh(true)
//                .types("科教文化服务")
                .districtCode("340100").build());
        System.out.println(addressStandardVo);
    }
    @Test
    public void testSTDFromCSV() {
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\hefei-report-address.csv");

        List<Object[]> data = new ArrayList<>();
        csvRecords.forEach(e -> {
            String ori = e.get(0);
            String std = e.get(1);
            double similarity = Double.parseDouble(e.get(2));

            if (similarity > 0.1 && similarity < 0.5 && data.size() < 100) {
                Object[] obj = new Object[3];
                obj[0] = ori;
                obj[1] = std;
                obj[2] = similarity;
                data.add(obj);
            }
        });

        List<Object[]> newData = new ArrayList<>();
        data.forEach(e -> {
            AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyAddressNormalization(AddressFuzzyNormalizationDTO.builder()
                    .addressDetail(e[0].toString())
                    .refresh(true)
                    .districtCode("340100").build());

            Object[] obj = new Object[3];
            obj[0] = e[0].toString();
            obj[1] = addressStandardVo.getAddressAreaName();
            obj[2] = StrUtil.similar(obj[0].toString(), obj[1].toString());
            newData.add(obj);
        });

        Object[] headers = new Object[3];
        headers[0] = "源地址";
        headers[1] = "标化地址";
        headers[2] = "相似度%";

        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, newData, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\hefei-report-address-"+format+".csv");

    }

    @Test
    public void testInputTips(){
        List<PoisV2> poisV2s = amapService.inputTips("合肥哈佛贝贝明珠湖畔幼儿园", "340100");
        System.out.println(poisV2s);
    }

    @Test
    public void testInputTipsFromCsv(){
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\address.csv");
        System.out.println(csvRecords.size());

        List<Object[]> data = new ArrayList<>();
        csvRecords.forEach(e -> {
            String address = e.get(0);
            String province = e.get(1);
            String city = e.get(2);
            String poiStdAddress = e.get(3);
            List<Tips> poisV2s = amapService.inputTipsV2(address, city);
            if (CollectionUtil.isNotEmpty(poisV2s)) {
                Object[] obj = new Object[9];
                obj[0] = address;
                obj[1] = province;
                obj[2] = city;
                obj[3] = poiStdAddress;
                obj[4] = poisV2s.get(0).getName();
                obj[5] = poisV2s.get(0).getAddress();
                obj[6] = poisV2s.get(0).getLocation();
                obj[7] = poisV2s.get(0).getId();
                obj[8] = poisV2s.get(0).getTypecode();
                data.add(obj);
            }
            }
        );
        Object[] headers = new Object[9];
        headers[0] = "源地址";
        headers[1] = "省编码";
        headers[2] = "市编码";
        headers[3] = "POI2标化地址";
        headers[4] = "输入提示-名称";
        headers[5] = "输入提示-地址";
        headers[6] = "输入提示-坐标";
        headers[7] = "输入提示-ID";
        headers[8] = "输入提示-POI类型";
        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, data, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\address-tips-std-"+format+".csv");
    }

    @Test
    public void testStdOrgFromCsv(){
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\org.csv");
        System.out.println(csvRecords.size());

        List<Object[]> data = new ArrayList<>();
        csvRecords.forEach(e -> {
            String cityCode = e.get(0);
            String newName = e.get(2);
            String sourceType = e.get(3);
            String types = null;
            if (StringUtils.isNotEmpty(sourceType) && "210".equals(sourceType)) {
                types = "科教文化服务";
            }

//            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyAddressNormalization(AddressFuzzyNormalizationDTO.builder()
                    .addressDetail(newName)
                    .districtCode(cityCode).types(types).build());

            Object[] obj = new Object[9];
            obj[0] = e.get(0);
            obj[1] = e.get(1);
            obj[2] = e.get(2);
            obj[3] = e.get(3);
            obj[4] = e.get(4);
            obj[5] = addressStandardVo.getAddressAreaCode();
            obj[6] = addressStandardVo.getAddressAreaName();
            obj[7] = addressStandardVo.getAddressProvinceName()+addressStandardVo.getAddressCityName()+addressStandardVo.getAddressDistrictName();
            obj[8] = addressStandardVo.getAddressTownName();
            data.add(obj);
        });

        Object[] headers = new Object[9];
        headers[0] = "城市编码";
        headers[1] = "区县编码";
        headers[2] = "机构名称";
        headers[3] = "机构类型";
        headers[4] = "源地址";
        headers[5] = "标化后编码";
        headers[6] = "标化后名称";
        headers[7] = "标化后省市区";
        headers[8] = "标化后街道(村)";
        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, data, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\org-std-"+format+".csv");
    }

}
