package std;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.controller.old.AddressStandardizeController;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressFuzzyNormalizationDTO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressStandardVo;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import util.CsvUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class StdAddressOrgTest {

    @Resource
    private AddressStandardizeController addressStandardizeController;

    @Test
    public void testOrgSTD(){
        AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyAddressNormalization(AddressFuzzyNormalizationDTO.builder()
                .addressDetail("合肥哈佛贝贝明珠湖畔幼儿园")
                .types("科教文化服务")
                .districtCode("340100").build());
        System.out.println(addressStandardVo);
    }

    @Test
    public void testStdOrgFromCsv(){
        List<CSVRecord> csvRecords = CsvUtil.readCsvParse("D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\org.csv");
        System.out.println(csvRecords.size());

        List<Object[]> data = new ArrayList<>();
        csvRecords.forEach(e -> {
            String cityCode = e.get(0);
            String newName = e.get(2);
            String sourceType = e.get(3);
            String types = null;
            if (StringUtils.isNotEmpty(sourceType) && "210".equals(sourceType)) {
                types = "科教文化服务";
            }

//            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            AddressStandardVo addressStandardVo = addressStandardizeController.fuzzyAddressNormalization(AddressFuzzyNormalizationDTO.builder()
                    .addressDetail(newName)
                    .districtCode(cityCode).types(types).build());

            Object[] obj = new Object[9];
            obj[0] = e.get(0);
            obj[1] = e.get(1);
            obj[2] = e.get(2);
            obj[3] = e.get(3);
            obj[4] = e.get(4);
            obj[5] = addressStandardVo.getAddressAreaCode();
            obj[6] = addressStandardVo.getAddressAreaName();
            obj[7] = addressStandardVo.getAddressProvinceName()+addressStandardVo.getAddressCityName()+addressStandardVo.getAddressDistrictName();
            obj[8] = addressStandardVo.getAddressTownName();
            data.add(obj);
        });

        Object[] headers = new Object[9];
        headers[0] = "城市编码";
        headers[1] = "区县编码";
        headers[2] = "机构名称";
        headers[3] = "机构类型";
        headers[4] = "源地址";
        headers[5] = "标化后编码";
        headers[6] = "标化后名称";
        headers[7] = "标化后省市区";
        headers[8] = "标化后街道(村)";
        String format = DateUtil.format(new Date(), "yyyy-MM-dd-HH-mm-ss");
        CsvUtil.writeCsvWithRecordSeparator(headers, data, "D:\\IdeaProjects\\cdc-admin\\cdc-admin-service\\src\\test\\resources\\org-std-"+format+".csv");
    }

}
