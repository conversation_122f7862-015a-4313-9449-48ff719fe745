import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class AesTest {
    private static final String KEY = "n1b&PwPPdPU7hl1z"; // 与 JS 中的 KEY 一致
    private static final String IV = "uQQH&cPz0!G3bihk"; // 与 JS 中的 IV 一致

    public static String decrypt(String data) {
        try {
            // 初始化密钥和 IV
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes("UTF-8"), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(IV.getBytes("UTF-8"));

            // 设置 Cipher 为解密模式
            Cipher cipher = Cipher.getInstance("Cipher.AES_128/ECB/NoPadding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // Base64 解码数据
            byte[] decodedData = Base64.getDecoder().decode(data);

            // 解密数据
            byte[] decryptedData = cipher.doFinal(decodedData);

            // 转换为字符串并返回
            return new String(decryptedData, "UTF-8").trim();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String encryptedData = "AVUe/emvS2VLypot8/pLofbAxb3ZwZ/4sU005tN/Q9V3BY+8jWFHe8xeRnGZMUGXCQ3iL4RGCF+ceLovnufsWrDOKtgJ7XsgUg+io4v41iMdcbQztVUsM92S2o1URsJjg8AFHOrT/sB+QZXG3970c8QjpmkCP9GzqHIAR9/LSBpEXwGF4IjvlVIe5VlKH6rItFWG8+ngzDJ6MQleZQld2saso8NLCYLKtZB0g+qEMaoYL4AwVlviynaOO9lDHkpvEv5qUdVYs/ab9aKCNv0p0M43CyNpJYmQkoRhEVu94b0CbzBP1NWEWfu8t2ufY7VTrnyPDxOFf+9S2bOxjaG/LiuUZc1F44sxKH4HVjIK9snCmwKUa/nh1qmWBgsIV7cdKguYMP6IduNS9LxkB+CsljOp+sU+McvtUMaTRGFvai39pbB8Zul/SB4pwzfV+Xlitnn6NHBbJXpoFe1DzuKes6X8dYHrf0sjd67x9+hNB8ZEJMal8keOdJaQkZWPvtRETi1an7vG7Y7UpcbhFBa9IA=="; // 替换为你的加密数据
        String decryptedData = decrypt(encryptedData);
        System.out.println("解密后的数据: " + decryptedData);
    }
}
