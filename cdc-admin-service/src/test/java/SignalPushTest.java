import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class SignalPushTest {

    @Resource
    private WarningChargePersonService warningChargePersonService;

    @Test
    public void test(){
        warningChargePersonService.scanSignalPushConfiguration();
    }
}
