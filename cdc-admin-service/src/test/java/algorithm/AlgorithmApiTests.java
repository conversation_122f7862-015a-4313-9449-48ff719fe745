package algorithm;

import com.google.gson.Gson;
import com.iflytek.cdc.admin.CdcAdminServiceApplication;
import com.iflytek.cdc.admin.capacity.api.algorithm.AlgorithmApi;
import com.iflytek.cdc.admin.capacity.api.algorithm.config.AlgorithmApiConfig;
import com.iflytek.cdc.admin.capacity.api.algorithm.request.*;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.*;
import com.iflytek.cdc.admin.capacity.controller.CapacityController;
import com.iflytek.cdc.admin.capacity.model.dto.AwarenessRequestDTO;
import com.iflytek.cdc.admin.util.UUIDUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcAdminServiceApplication.class)
public class AlgorithmApiTests {

    private AlgorithmApiConfig config;

    private String token;

    private AlgorithmApi algorithmApi;

    private Gson gson;

    @Resource
    AlgorithmApiConfig algorithmApiConfig;

    @Before
    public void configInit() {
//        testEnv();
//        config.setCdcPolicySimulationUrl("/v2.2/cdc/cdcPolicySimulation");
//        config.setAuthUrl("/v2.2/auth/oauth/token");
//        config.setQueryCdcAwarenessUrl("/v2.2/cdc/queryCdcAwareness");
//        config.setCdcDispositionAdviceUrl("/v2.2/cdc/cdcDispositionAdvice");
//        config.setCdcEventClassifyUrl("/v2.2/cdc/cdcEventClassify");
//        config.setCdcDiseaseTrendPredictionUrl("/v2.2/cdc/cdcDiseaseTrendPrediction");
//        config.setArimaUrl("http://172.29.229.73:5002/cdc_arima");
        testK8sDev();
        RestTemplate template = new RestTemplate();
        algorithmApi = new AlgorithmApi(template, config);
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(config.getClientId());
        authRequest.setClientSecret(config.getClientSecret());
        authRequest.setGrantType(config.getGrantType());
        token = algorithmApi.getToken(authRequest).getAccessToken();
        gson = new Gson();

    }

    @Test
    public void testNacosConfig() {
        config = algorithmApiConfig;
        RestTemplate template = new RestTemplate();
        algorithmApi = new AlgorithmApi(template, config);
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(config.getClientId());
        authRequest.setClientSecret(config.getClientSecret());
        authRequest.setGrantType(config.getGrantType());
        token = algorithmApi.getToken(authRequest).getAccessToken();
        gson = new Gson();
    }

    private void testK8sDev() {
        config = new AlgorithmApiConfig();
        config.setRootUrl("");
        config.setClientId("zscdc82783a35e11eb9df5fa163e7dscdc");
        config.setClientSecret("zscdc82783a35e11eb9df163e7dscdc0001");
        config.setGrantType("client_credentials");
        config.setCdcPolicySimulationUrl("http://172.30.9.82:32003/v2.2/cdc/cdcPolicySimulation");
        config.setAuthUrl("http://172.30.9.82:32003/v2.2/auth/oauth/token");
        config.setQueryCdcAwarenessUrl("http://172.30.9.82:32003/v2.2/cdc/queryCdcAwareness");
        config.setCdcDispositionAdviceUrl("http://172.30.9.82:32004/v2.2/cdc/cdcDispositionAdvice");
        config.setCdcEventClassifyUrl("http://172.30.9.82:32004/v2.2/cdc/cdcEventClassify");
        config.setCdcDiseaseTrendPredictionUrl("http://172.30.9.82:32004/v2.2/cdc/cdcDiseaseTrendPrediction");
        config.setArimaUrl("http://172.30.9.82:32004/cdc_arima");

//        config.setStgnnUrl("http://172.30.9.82:32005/cdc_stgnn");
//        config.setLstmUrl("http://172.30.9.82:32005/cdc_lstm");
//        config.setSeirUrl("http://172.30.9.82:32005/cdc_seir");
//        config.setEnsembleUrl("http://172.30.9.82:32005/cdc_ensemble");

        config.setStgnnUrl("http://172.29.234.75:32005/cdc_stgnn");
        config.setLstmUrl("http://172.29.234.75:32005/cdc_lstm");
        config.setSeirUrl("http://172.29.234.75:32005/cdc_seir");
        config.setEnsembleUrl("http://172.29.234.75:32005/cdc_ensemble");

        config.setFeatureSelectionUrl("http://172.29.229.73:2015/cdc_feature_selection");
    }

    /**
     * 开发环境
     */
    private void dev() {
        config = new AlgorithmApiConfig();
        config.setRootUrl("http://172.31.223.128:19002");
        config.setClientId("zscdc82783a35e11eb9df5fa163e7dscdc");
        config.setClientSecret("zscdc82783a35e11eb9df163e7dscdc0001");
        config.setGrantType("client_credentials");

    }

    /**
     * 集成环境
     */
    private void integration() {
        config = new AlgorithmApiConfig();
        config.setRootUrl("http://172.29.229.73:19002");
        config.setClientId("zscdc82783a35e11eb9df5fa163e7dscdc");
        config.setClientSecret("zscdc82783a35e11eb9df163e7dscdc0001");
        config.setGrantType("client_credentials");
    }

    /**
     * 测试环境
     */
    private void testEnv() {
        config = new AlgorithmApiConfig();
        config.setRootUrl("http://172.31.129.253:19003");
        config.setClientId("zscdc82783a35e11eb9df5fa163e7dscdc");
        config.setClientSecret("zscdc82783a35e11eb9df163e7dscdc0001");
        config.setGrantType("client_credentials");
    }


    /**
     * token获取
     */
    @Test
    public void cdcPolicySimulation() throws InterruptedException {
        AwarenessRequest request = new AwarenessRequest();
        request.setTaskID(UUIDUtil.generateUUID());
        request.setStartDay("2024-07-05");
        request.setEndDay("2024-08-11");
        request.setPopSize(1000000);
        request.setPopScale(1);

        request.setPopInfected(100);
        request.setBeta(0.016d);
        request.setAsympFactor(1d);
        request.setIfCalibrate(false);
        request.setIfContour(false);
        request.setPredDay("2024-08-15");

        request.setLatency(5d);
        request.setMySympProb(0.5);
        request.setMySevereProb(0.3);
        request.setQuarPeriod(3);
        request.setMyDeathProb(0.1);

        PlatformResponseBase<String> stringPlatformResponseBase = algorithmApi.cdcPolicySimulation(token, request);
        System.out.println(stringPlatformResponseBase.getResult());

        String taskId = stringPlatformResponseBase.getResult();

        Thread.sleep(2000);
        AwarenessResultRequest resultRequest = new AwarenessResultRequest();
        resultRequest.setTaskId(taskId);
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = algorithmApi.queryCdcAwareness(token, resultRequest);
        Gson gson = new Gson();
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(awarenessResponsePlatformResponseBase));
    }

    @Test
    public void testWithCheck() throws InterruptedException {
        test();
//        Thread thread1 = new Thread(() -> {
//            try {
//                test();
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        thread1.start();
//        Thread thread = new Thread(() -> {
//            try {
//                test();
//            } catch (InterruptedException e) {
//                throw new RuntimeException(e);
//            }
//        });
//        thread.start();
//        Thread.sleep(300 * 1000);
    }

    @Test
    public void testBinf() throws InterruptedException {
        Thread thread1 = new Thread(() -> {
            try {
                cdcPolicySimulation();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        Thread thread2 = new Thread(() -> {
            try {
                cdcPolicySimulation();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        thread1.start();
        thread2.start();
        Thread.sleep(2000);
    }

    @Test
    public void testddd() {
    }


    /**
     * 性能测试
     */
    @Test
    public void testPer() throws InterruptedException {
        Map<String, Object> results = new HashMap<>();
        Map<Integer, String> resultMap = new HashMap<>();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("发送请求");
        for (int i = 0; i < 10; i++) {
            try {
                results.put(awareness(), new HashMap<>());
            } catch (Exception e) {
                System.out.println(String.format("第%s次失败：%s", i, e.getLocalizedMessage()));
            }
        }
        stopWatch.stop();
        long startTime = new Date().getTime();
        long nowTime = new Date().getTime();
        long endTime = nowTime + 60 * 1000 * 5 * 4;
        stopWatch.start("获取结果");
        while (nowTime <= endTime && resultMap.size() < 10) {
            ArrayList<String> integers1 = new ArrayList<>(results.keySet());
            for (int i = 0; i < integers1.size(); i++) {
                try {
                    AwarenessResultRequest resultRequest = new AwarenessResultRequest();
                    resultRequest.setTaskId(integers1.get(i));
                    PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = algorithmApi
                            .queryCdcAwareness(token, resultRequest);
                    if (awarenessResponsePlatformResponseBase.getCode().equals(PlatformResponseBase.SUCCESS_CODE) ||
                            !awarenessResponsePlatformResponseBase.getCode().equals(PlatformResponseBase.RUNNING_CODE)) {
                        System.out.println(i + ":" + gson.toJson(awarenessResponsePlatformResponseBase));
                        resultMap.put(i, gson.toJson(awarenessResponsePlatformResponseBase));
                    }
                } catch (Exception e) {
                    resultMap.put(i, e.getLocalizedMessage());
                }
            }
            Thread.sleep(1000);
            nowTime = new Date().getTime();
        }
        resultMap.forEach((k, v) -> {
            System.out.println(k + ":" + v);
        });
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint());
        System.out.println((new Date().getTime() - startTime));
    }

    private String awareness() {
        AwarenessRequest request = new AwarenessRequest();
        request.setTaskID(UUIDUtil.generateUUID());
        request.setStartDay("2024-07-05");
        request.setEndDay("2024-08-11");
        request.setPopSize(1000000);
        request.setPopScale(1);

        request.setPopInfected(100);
        request.setBeta(0.016d);
        request.setAsympFactor(1d);
        request.setIfCalibrate(false);
        request.setIfContour(false);
        request.setPredDay("2024-08-11");

        request.setLatency(5d);
        request.setMySympProb(0.5);
        request.setMySevereProb(0.3);
        request.setQuarPeriod(3);
        request.setMyDeathProb(0.1);

        List<String> dates = Arrays.asList("2024-07-05", "2024-07-06", "2024-07-07", "2024-07-08");
        List<Integer> infectious = Arrays.asList(11, 12, 14, 15);
        List<Integer> cumInfectious = Arrays.asList(20, 32, 46, 51);
        List<Integer> deaths = Arrays.asList(1, 2, 3, 4);
        List<Integer> cumDeaths = Arrays.asList(11, 13, 16, 20);

        AwarenessRequest.Datafile datafile = new AwarenessRequest.Datafile();
        datafile.setDate(dates);
        datafile.setNew_infectious(infectious);
        datafile.setCum_infectious(cumInfectious);
        datafile.setNew_deaths(deaths);
        datafile.setCum_deaths(cumDeaths);
        request.setDatafile(datafile);

        request.setHouseDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
        request.setHouseChangesPre(Arrays.asList(0.5d, 1d));

        request.setSchoolDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
        request.setSchoolChangesPre(Arrays.asList(0.6d, 1d));

        request.setWorkDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
        request.setWorkChangesPre(Arrays.asList(0.5d, 1d));

        request.setCommunityDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
        request.setCommunityChangesPre(Arrays.asList(0.6d, 1d));

        request.setSimpleVaccinePre(true);
        request.setVaccineProbPre(0.6d);
        request.setVaccineDaysPre(Collections.singletonList(0));
        request.setVaccineRelSympPre(1d);
        request.setVaccineRelSusPre(1d);

        PlatformResponseBase<String> stringPlatformResponseBase = algorithmApi.cdcPolicySimulation(token, request);
        System.out.println("request=========================================" + gson.toJson(request));
        return stringPlatformResponseBase.getResult();
    }

    private void test() throws InterruptedException {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始");
        String taskId = awareness();
        System.out.println("stringPlatformResponseBase=========================================" + taskId);

        AwarenessResultRequest resultRequest = new AwarenessResultRequest();
        resultRequest.setTaskId(taskId);
        Thread.sleep(60 * 1000);
        long startTime = new Date().getTime();
        long nowTime = new Date().getTime();
        long endTime = nowTime + 60 * 1000 * 5;
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = null;
        while (nowTime < endTime && (awarenessResponsePlatformResponseBase == null || awarenessResponsePlatformResponseBase.getCode().equals(PlatformResponseBase.RUNNING_CODE))) {
            awarenessResponsePlatformResponseBase = algorithmApi.queryCdcAwareness(token, resultRequest);
            Thread.sleep(1000);
            nowTime = new Date().getTime();
        }
        stopWatch.stop();

        Gson gson = new Gson();
        System.out.println("awarenessResponsePlatformResponseBase=========================================" + gson.toJson(awarenessResponsePlatformResponseBase));
        System.out.println("awarenessResponsePlatformResponseBase=========================================时长" + stopWatch.prettyPrint());
    }

    @Test
    public void testWithPolicy() throws InterruptedException {

        AwarenessRequest request = new AwarenessRequest();
        request.setTaskID(UUIDUtil.generateUUID());
        request.setStartDay("2024-07-05");
        request.setEndDay("2024-08-11");
        request.setPopSize(100000);
        request.setPopInfected(100);
        request.setBeta(0.016d);
        request.setAsympFactor(1d);
        request.setIfCalibrate(false);
        request.setIfContour(false);

//        request.setHouseDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
//        request.setHouseChangesPre(Arrays.asList(0.5d, 1d));
//
//        request.setSchoolDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
//        request.setSchoolChangesPre(Arrays.asList(0.6d, 1d));
//
//        request.setWorkDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
//        request.setWorkChangesPre(Arrays.asList(0.5d, 1d));
//
//        request.setCommunityDaysPre(Arrays.asList("2024-07-12", "2024-07-30"));
//        request.setCommunityChangesPre(Arrays.asList(0.6d, 1d));

        request.setSimpleVaccinePre(true);
        request.setVaccineProbPre(0.6d);
        request.setVaccineDaysPre(Collections.singletonList(0));
        request.setVaccineRelSympPre(1d);
        request.setVaccineRelSusPre(1d);

        PlatformResponseBase<String> stringPlatformResponseBase = algorithmApi.cdcPolicySimulation(token, request);
        System.out.println(stringPlatformResponseBase.getResult());

        String taskId = stringPlatformResponseBase.getResult();

        Thread.sleep(2000);
        AwarenessResultRequest resultRequest = new AwarenessResultRequest();
        resultRequest.setTaskId(taskId);
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = algorithmApi.queryCdcAwareness(token, resultRequest);
        Gson gson = new Gson();
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(awarenessResponsePlatformResponseBase));

    }

    @Test
    public void queryCdcAwareness() {
        String taskId = "1234";
        AwarenessResultRequest resultRequest = new AwarenessResultRequest();
        resultRequest.setTaskId(taskId);
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = algorithmApi.queryCdcAwareness(token, resultRequest);
        Gson gson = new Gson();
        System.out.println(gson.toJson(awarenessResponsePlatformResponseBase));
    }

    @Test
    public void tesCdcEventClassify() {
        EventClassifyRequest request = new EventClassifyRequest();
        request.setKeyword("登革热");
        request.setJiekouType(0);
        request.setAddressCode("县区");
        request.setDeathNumber(10);
        request.setDiseaseNumber(100000);
        System.out.println(gson.toJson(request));
        PlatformResponseBase<EventClassifyResponse> eventClassifyResponsePlatformResponseBase = algorithmApi.cdcEventClassify(token, request);
        System.out.println(gson.toJson(eventClassifyResponsePlatformResponseBase));
    }

    @Test
    public void cdcDispositionAdvice() {
        DispositionAdviceRequest request = new DispositionAdviceRequest();
        request.setDisease("流行性感冒");
        request.setEventClass("二级响应");
        PlatformResponseBase<AlgorithmResponseBase<DispositionAdviceResponse>> dispositionAdvice = algorithmApi.cdcDispositionAdvice(token, request);
        System.out.println(gson.toJson(dispositionAdvice));
    }

    @Test
    public void cdcDiseaseTrendPrediction() {
        DiseaseTrendRequest request = new DiseaseTrendRequest();
        request.setHistoryData(Arrays.asList(1, 2, 3, 4, 5));
        request.setDisease("肺结核");
        request.setPredictStep(3);
        request.setHistoryDataAges(Arrays.asList(0, 1, 2, 3, 4, 5));
        request.setHistoryDataGenders(Arrays.asList(9, 6));
        request.setHistoryDataMonth(7);

        PlatformResponseBase<AlgorithmResponseBase<List<Integer>>> algorithmResponseBasePlatformResponseBase = algorithmApi.cdcDiseaseTrendPrediction(token, request);
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(algorithmResponseBasePlatformResponseBase));
    }

    @Test
    public void arima() {
        ArimaRequest request = new ArimaRequest();
        ArimaRequest.HistoryData historyData = new ArimaRequest.HistoryData();
        historyData.setTimestamp(Arrays.asList("2024-09-06", "2024-09-07", "2024-09-08", "2024-09-09",
                "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13",
                "2024-09-14", "2024-09-15", "2024-09-16", "2024-09-17", "2024-09-18"));
        historyData.setQuantity(Arrays.asList(102, 104, 200, 400,
                500, 642, 723, 844,
                935, 1100, 1235, 1368, 1485));
        request.setHistoryData(historyData);
        request.setStep(15);
        System.out.println(gson.toJson(request));
        AlgorithmResponseBase<List<Integer>> arima = algorithmApi.arima(request);
        System.out.println(gson.toJson(arima));
    }

    @Test
    public void arima2() {
        LocalDate startDate = LocalDate.parse("2022-02-18");
        LocalDate endDate = LocalDate.parse("2025-02-18");
        List<String> timestamps = new ArrayList<>();
        List<Integer> result = new ArrayList<>();
        int i = 1;
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            timestamps.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            result.add(i * 5 + 10);
            i++;
        }
        ArimaRequest request = new ArimaRequest();
        ArimaRequest.HistoryData historyData = new ArimaRequest.HistoryData();
        historyData.setQuantity(result);
        historyData.setTimestamp(timestamps);
        request.setHistoryData(historyData);
        request.setStep(365);
        System.out.println(gson.toJson(request));
        AlgorithmResponseBase<List<Integer>> arima = algorithmApi.arima(request);
        System.out.println(gson.toJson(arima));
    }

    @Test
    public void lstm() {
        LSTMRequest request = new LSTMRequest();
        Map<String, Integer> recordMap = new LinkedHashMap<>();
        recordMap.put("2025-02-01", 10);
        recordMap.put("2025-02-02", 10);
        recordMap.put("2025-02-03", 10);
        recordMap.put("2025-02-04", 10);
        recordMap.put("2025-02-05", 10);
        recordMap.put("2025-02-06", 10);
        recordMap.put("2025-02-07", 10);
        recordMap.put("2025-02-08", 10);
        recordMap.put("2025-02-09", 10);
        recordMap.put("2025-02-10", 10);
        recordMap.put("2025-02-11", 10);
        recordMap.put("2025-02-12", 10);
        recordMap.put("2025-02-13", 10);
        recordMap.put("2025-02-14", 10);
        recordMap.put("2025-02-15", 10);
        request.setRecordsCount(recordMap);
        request.setFutureDays(12);
        System.out.println(config.getLstmUrl());
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(algorithmApi.lstm(request)));
    }

    @Test
    public void stgnn() {
        STGNNRequest request = new STGNNRequest();
        Map<String, Integer> recordMap = new LinkedHashMap<>();
        recordMap.put("2025-02-01", 10);
        recordMap.put("2025-02-02", 10);
        recordMap.put("2025-02-03", 10);
        recordMap.put("2025-02-04", 10);
        recordMap.put("2025-02-05", 10);
        recordMap.put("2025-02-06", 10);
        recordMap.put("2025-02-07", 10);
        recordMap.put("2025-02-08", 10);
        recordMap.put("2025-02-09", 10);
        recordMap.put("2025-02-10", 10);
        recordMap.put("2025-02-11", 10);
        recordMap.put("2025-02-12", 10);
        recordMap.put("2025-02-13", 10);
        recordMap.put("2025-02-14", 10);
        recordMap.put("2025-02-15", 10);
        request.setRecordsCount(recordMap);
        request.setFutureDays(12);
        System.out.println(config.getStgnnUrl());
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(algorithmApi.stgnn(request)));
    }

    @Test
    public void ensemble() {
        EnsembleRequest request = new EnsembleRequest();
        request.setStartDay("2024-07-05");
        request.setEndDay("2024-08-11");
        request.setPopSize(100000);
        request.setPopInfected(100);
        request.setBeta(0.016d);
        request.setAsympFactor(1d);
        System.out.println(config.getEnsembleUrl());
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(algorithmApi.ensemble(request)));
    }

    @Test
    public void seir() {
        SEIRRequest request = new SEIRRequest();
        request.setStartDay("2024-07-05");
        request.setEndDay("2024-08-11");
        request.setPopSize(100000);
        request.setPopInfected(100);
        request.setBeta(0.016d);
        request.setAsympFactor(1d);
        System.out.println(gson.toJson(algorithmApi.seir(request)));
    }

    @Resource
    private CapacityController capacityController;

    @Test
    public void awarenessController() {
        String request = "{\"startDay\":\"2024-10-09\",\"endDay\":\"2024-10-24\",\"popSize\":457890,\"popInfected\":2,\"beta\":0.016,\"asympFactor\":1.0,\"ifCalibrate\":false,\"calibrationData\":[],\"ifContour\":false,\"schoolPolicy\":{\"startDate\":\"2024-12-05\",\"endDate\":\"2024-12-18\",\"rate\":0.4},\"simpleVaccinePre\":false}";
        AwarenessRequestDTO request1 = gson.fromJson(request, AwarenessRequestDTO.class);
        capacityController.awareness(request1, "100001");
    }

    @Test
    public void testPolicy() throws InterruptedException {
        String requestStr = "{\"taskID\":\"12073eda1bd144038ee46e8172c0e8f4\",\"startDay\":\"2024-10-09\",\"endDay\":\"2024-10-24\",\"popSize\":100000,\"popInfected\":100,\"beta\":0.016,\"asympFactor\":1.0,\"ifCalibrate\":false,\"ifContour\":false,\"schoolDaysPre\":[\"2024-12-05\",\"2024-12-18\"],\"schoolChangesPre\":[0.8,1.0],\"simpleVaccinePre\":false}";
        AwarenessRequest request = gson.fromJson(requestStr, AwarenessRequest.class);
        PlatformResponseBase<String> stringPlatformResponseBase = algorithmApi.cdcPolicySimulation(token, request);
        System.out.println(stringPlatformResponseBase.getResult());

        String taskId = stringPlatformResponseBase.getResult();

        Thread.sleep(2000);
        AwarenessResultRequest resultRequest = new AwarenessResultRequest();
        resultRequest.setTaskId(taskId);
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = algorithmApi.queryCdcAwareness(token, resultRequest);
        Gson gson = new Gson();
        System.out.println(gson.toJson(request));
        System.out.println(gson.toJson(awarenessResponsePlatformResponseBase));
    }
}
