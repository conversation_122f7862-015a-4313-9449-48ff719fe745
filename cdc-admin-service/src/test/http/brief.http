
###
# 获取简报模板列表
POST /pt/v1/brief/template/list
Content-Type: application/json

{
    "pageIndex": 1,
    "pageSize": 10
}

?? status == 200


###
# 保存简报规则
POST /pt/v1/brief/rule/saveOrUpdate?loginUserId={{loginUserId}}
Content-Type: application/json

{
    "templateId": "183206184033976511",
    "area": [
        "340202"
    ],
    "date": [
        "2025-05-28",
        "2025-06-27"
    ],
    "customTime": "",
    "repeatFlag": "1",
    "id": "183206302145577151",
    "status": "1",
    "provinceName": "",
    "cityName": "",
    "districtCode": "340202",
    "districtName": "镜湖区",
    "creatorId": "138124365827932277",
    "creatorName": "测试人员二",
    "createTime": "2025-05-28T11:36:05.155+00:00",
    "updatorId": "138124365827932277",
    "updatorName": "测试人员二",
    "updateTime": "2025-05-28T11:50:42.920+00:00",
    "deleteFlag": "0",
    "statisticsCycle": "day",
    "startTime": "2025-05-28",
    "endTime": "2025-06-27",
    "businessType": "emergency"
}


###
# 删除简报模板
GET /pt/v1/brief/template/delete/1?loginUserId={{loginUserId}}&loginUserName={{loginUserName}}

?? status == 200


###
# 执行简报生成任务
POST /pt/v1/brief/view/execute?param=2025-05-29
Content-Type: application/json

?? status == 200


###
# 获取简报列表
POST /pt/v1/brief/view/list?loginUserId={{loginUserId}}
Content-Type: application/json

{
    "pageIndex": 1,
    "pageSize": 20,
    "businessType": "infected",
    "provinceCode": "340000",
    "statisticsCycle": ""
}

?? status == 200


###
# 获取简报详情
GET /pt/v1/brief/view/info/182523985387323519
Content-Type: application/json

?? status == 200


###
# 获取推送记录
POST /pt/v1/brief/view/pushList
Content-Type: application/json

{
    "pageIndex": 1,
    "pageSize": 20,
    "businessType": "infected",
    "provinceCode": "340000",
    "pushUserName": "zhuangli5"
}

?? status == 200
