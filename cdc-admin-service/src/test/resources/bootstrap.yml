spring:
    application:
        name: cdc-admin-service
    cloud:
        nacos:
            config:
                server-addr: 172.31.185.162:8848
                namespace: dev-demo2
                username: nacos
                password: afeTW1123_id@#ad
#                server-addr: 172.31.186.222:8848
#                namespace: cdc-test-integration
#                username: nacos
#                password: Config@Nacos2090

#               server-addr: 172.30.8.207:8848
#               namespace: cdc-test2-5-0
#               username: nacos
#               password: Config@Nacos2090
                shared-dataids: application.yml,cdc-admin-service.yml
            discovery:
                enabled: true
                register-enabled: false

server:
    port: 18200

logging:
    level:
        com:
            iflytek:
                cdc: info