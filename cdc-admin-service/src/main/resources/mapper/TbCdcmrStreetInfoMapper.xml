<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrStreetInfoMapper">

    <update id="updateOldStreetCode">
        with mid as (select a.address_province_code province_code -- 省份编码
                          , a.address_province_name province_name -- 省份名称
                          , a.address_city_code     city_code     -- 城市编码
                          , a.address_city_name     city_name     -- 城市名称
                          , a.address_district_code standard_district_code -- 区县编码
                          , a.address_district_name standard_district_name -- 区县名称
                          , a.address_town_code     street_code   -- 街道编码
                          , a.address_town_name     street_name   -- 街道名称
                          --高德返回的街道编码发生过变更（半汤街道340181006000->340181009000)，取最新编码
                          , row_number()            over(partition by a.address_province_name, a.address_city_name, a.address_district_name, a.address_town_name order by a.create_datetime desc) rn
                     from tb_cdcmr_address_standard a
                     where length(a.address_town_name) >= 2       -- 剔除街道为空的
                       and (length(a.address_city_name)) >= 2     -- 剔除城市为空的
                       and (length(a.address_district_name)) >= 2 -- 剔除区划为空的
                       and a.address_town_code != 'unknown'
            and a.update_datetime between #{startTime} and #{endTime}
            )
        update tb_cdcmr_street_info t
        set delete_flag = '1', update_datetime = localtimestamp
        from mid
        where t.province_name = mid.province_name
          and t.city_name = mid.city_name
          and t.standard_district_name = mid.standard_district_name
          and t.street_name = mid.street_name
          and t.street_code <![CDATA[<>]]> mid.street_code -- 同样地址, 旧街道编码废弃

    </update>

    <insert id="mergeStreetInfo">
        insert into tb_cdcmr_street_info ( street_id
                                                , province_code
                                                , province_name
                                                , city_code
                                                , city_name
                                                , district_code  -- 功能区
                                                , district_name
                                                , street_code
                                                , street_name
                                                , street_longitude
                                                , street_latitude
            --, street_source
                                                , memo
                                                , create_datetime
                                                , update_datetime
            --, etl_create_datetime
            --, etl_update_datetime
                                                , standard_district_code    -- 行政区
                                                , standard_district_name)
        select a.street_code   street_id              -- 街道id
             , a.province_code province_code          -- 省份编码
             , a.province_name province_name          -- 省份名称
             , a.city_code     city_code              -- 城市编码
             , a.city_name     city_name              -- 城市名称
             , a.district_code district_code          -- 区县编码
             , a.district_name district_name          -- 区县名称
             , a.street_code   street_code            -- 街道编码
             , a.street_name   street_name            -- 街道名称
             , null            street_longitude       -- 街道中心点经度  -- 经纬度调用高德API处理
             , null            street_latitude        -- 街道中心点纬度
             --, 'AMAP'          street_source          -- 街道来源
             , '地址标化'      memo                   -- 备注
             , localtimestamp  create_datetime        -- 创建时间
             , localtimestamp  update_datetime        -- 更新时间
             --, localtimestamp  etl_create_datetime    -- ETL创建时间
             --, localtimestamp  etl_update_datetime    -- ETL更新时间
             , a.district_code standard_district_code -- 行政区划区县编码
             , a.district_name standard_district_name -- 行政区划区县名称
        from (select a.address_province_code province_code -- 省份编码
                   , a.address_province_name province_name -- 省份名称
                   , a.address_city_code     city_code     -- 城市编码
                   , a.address_city_name     city_name     -- 城市名称
                   , a.address_district_code district_code -- 区县编码
                   , a.address_district_name district_name -- 区县名称
                   , a.address_town_code     street_code   -- 街道编码
                   , a.address_town_name     street_name   -- 街道名称
                   --高德返回的街道编码发生过变更（半汤街道340181006000->340181009000)，取最新编码
                   , row_number()            over(partition by a.address_town_code order by a.create_datetime desc) rn
              from tb_cdcmr_address_standard a
                   --from dw.dim_mr_address_standard a
                   --iflydata版本参数兼容,根据项目需要注释
              where length(a.address_town_name) >= 2       -- 剔除街道为空的
                and (length(a.address_city_name)) >= 2     -- 剔除城市为空的
                and (length(a.address_district_name)) >= 2 -- 剔除区划为空的
                and a.address_town_code != 'unknown'
                and a.update_datetime between #{startTime} and #{endTime}) a
        where a.rn = 1 -- 同一个街道的编码不同，导致重复
            on conflict(street_id)
        do
        update set
            province_code = EXCLUDED.province_code
            ,province_name = EXCLUDED.province_name
            ,city_code = EXCLUDED.city_code
            ,city_name = EXCLUDED.city_name
        --    ,district_code = EXCLUDED.district_code       -- 功能区不从高德数据更新
        --    ,district_name = EXCLUDED.district_name
            ,street_code = EXCLUDED.street_code
            ,street_name = EXCLUDED.street_name
        --    ,street_longitude = EXCLUDED.street_longitude     -- 经纬度调用高德API处理, 此处不更新
        --    ,street_latitude = EXCLUDED.street_latitude
            --,street_source = EXCLUDED.street_source
            ,memo = EXCLUDED.memo
            ,update_datetime = EXCLUDED.update_datetime
        ,standard_district_code = EXCLUDED.standard_district_code
        ,standard_district_name = EXCLUDED.standard_district_name
        where tb_cdcmr_street_info.street_name <![CDATA[<>]]> excluded.street_name   -- 信息发生变化时更新
    </insert>

    <select id="getAdministrativeRegionDetail" resultType="com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo">
        select *
        from tb_cdcmr_street_info
        where delete_flag = '0'
        <if test="region != null ">
            and city_name = #{region}
        </if>
        <if test="splitCityCodeList != null and splitCityCodeList.size() &gt; 0">
            and city_code in
            <foreach close=")" collection="splitCityCodeList" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="splitDistrictCodeList != null and splitDistrictCodeList.size() &gt; 0">
            and district_code in
            <foreach close=")" collection="splitDistrictCodeList" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="isNameChanged != null">
            and is_name_changed = #{isNameChanged}
        </if>
        <choose>
            <when test="streetCondition == 'nowStreetName'">
                <if test="streetName != null">
                    and street_name like concat('%', #{streetName}, '%')
                </if>
            </when>
            <when test="streetCondition == 'originalStreetName'">
                <if test="streetName != null">
                    and original_street_name like concat('%', #{streetName}, '%')
                </if>
            </when>
            <when test="streetCondition == 'aliasStreetName'">
                <if test="streetName != null">
                    and alias_street_name like concat('%', #{streetName}, '%')
                </if>
            </when>
        </choose>
    </select>

    <select id="selectByStreetIds" resultType="com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo">
        select *
        from tb_cdcmr_street_info
        where delete_flag = '0'
        <choose>
            <when test="list != null and list.size() != 0">
                and street_id in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>

    </select>

    <select id="selectCursorByUpdateTime" resultType="com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo">
        select *
        from tb_cdcmr_street_info
        where delete_flag = '0'
        and update_datetime between #{startTime} and #{endTime}
    </select>

    <select id="selectUpdatedStreetCodes" resultType="java.lang.String">
        select distinct street_code
        from (select a.address_province_code province_code -- 省份编码
                   , a.address_province_name province_name -- 省份名称
                   , a.address_city_code     city_code     -- 城市编码
                   , a.address_city_name     city_name     -- 城市名称
                   , a.address_district_code district_code -- 区县编码
                   , a.address_district_name district_name -- 区县名称
                   , a.address_town_code     street_code   -- 街道编码
                   , a.address_town_name     street_name   -- 街道名称
                   --高德返回的街道编码发生过变更（半汤街道340181006000->340181009000)，取最新编码
                   , row_number()            over(partition by a.address_town_code order by a.create_datetime desc) rn
              from tb_cdcmr_address_standard a
                   --from dw.dim_mr_address_standard a
                   --iflydata版本参数兼容,根据项目需要注释
              where length(a.address_town_name) >= 2       -- 剔除街道为空的
                and (length(a.address_city_name)) >= 2     -- 剔除城市为空的
                and (length(a.address_district_name)) >= 2 -- 剔除区划为空的
                and a.address_town_code != 'unknown'
        and a.update_datetime between #{startTime}
                and #{endTime}) a
        where a.rn = 1 -- 同一个街道的编码不同，导致重复
    </select>

    <select id="selectByAreaCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo">
        select *
        from tb_cdcmr_street_info
        where delete_flag = '0'
        <if test="areaLevel == 2">
            and province_code = #{areaCode}
        </if>
        <if test="areaLevel == 1">
            and city_code = #{areaCode}
        </if>
        <if test="areaLevel == 0">
            and district_code = #{areaCode}
        </if>
    </select>

    <select id="listBy" resultType="com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo">
        select *
        from tb_cdcmr_street_info
        where delete_flag = '0'
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by province_code, city_code, district_code
    </select>

    <select id="listByAreaCodes" resultType="com.iflytek.cdc.admin.common.vo.AreaMappingVO">
        select
            province_code as provinceCode,
            province_name as provinceName,
            city_code as cityCode,
            city_name as cityName,
            district_code as districtCode,
            district_name as districtName,
            standard_district_code as standardDistrictCode,
            standard_district_name as standardDistrictName,
            street_code as streetCode,
            street_name as streetName
        from tb_cdcmr_street_info
        where delete_flag = '0'
        <if test="areaLevel == 1">
            and  province_code in
            <foreach collection="areaCodes" open="(" separator="," close=")"  item="code">
                #{code}
            </foreach>
        </if>
        <if test="areaLevel == 2">
            and city_code in
            <foreach collection="areaCodes" open="(" separator="," close=")"  item="code">
                #{code}
            </foreach>
        </if>
        <if test="areaLevel == 3">
            and district_code in
            <foreach collection="areaCodes" open="(" separator="," close=")"  item="code">
                #{code}
            </foreach>
        </if>

    </select>

    <update id="upsertStreetGeoCodes">
        insert into tb_cdcmr_street_info ( street_id, street_longitude, street_latitude, update_datetime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.streetId,jdbcType=VARCHAR},
            #{item.streetLongitude,jdbcType=DECIMAL},
            #{item.streetLatitude,jdbcType=DECIMAL},
            #{item.updateDatetime,jdbcType=TIMESTAMP})
        </foreach>
        on conflict(street_id) do update  set
            street_longitude = EXCLUDED.street_longitude,
            street_latitude = EXCLUDED.street_latitude,
            update_datetime = localtimestamp
    </update>
</mapper>