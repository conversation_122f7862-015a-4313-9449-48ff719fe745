<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarnMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="monitor_data_source" jdbcType="VARCHAR" property="monitorDataSource"/>
        <result column="warning_type" jdbcType="VARCHAR" property="warningType"/>
        <result column="warning_type_name" jdbcType="VARCHAR" property="warningTypeName"/>
        <result column="warning_correlation" jdbcType="VARCHAR" property="warningCorrelation"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , code, "name", monitor_data_source, warning_type, warning_type_name, warning_correlation,
    "status", memo, delete_flag, create_time, creator, update_time, updater
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultType="com.iflytek.cdc.admin.dto.CustomizedWarnVO">
        select *
        from "tb_cdcmr_customized_warn"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warn"
        where delete_flag = '0'
        <if test="name != null and name != ''">
            and name like concat('%',#{name},'%')
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="warningType != null and warningType != ''">
            and warning_type = #{warningType}
        </if>
        order by update_time desc
    </select>
    <select id="getByName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        select *
        from "tb_cdcmr_customized_warn"
        where delete_flag = '0'
          and name = #{name}
    </select>
    <select id="getEnabledWarn" resultType="com.iflytek.cdc.admin.dto.CustomizedWarnVO">
        select *
        from "tb_cdcmr_customized_warn"
        where delete_flag = '0'
          and status = 1
        <if test="warningType != null and warningType != ''">
            and warning_type = #{warningType}
        </if>
    </select>

    <select id="queryCustomizedInfoByGrade" resultType="com.iflytek.cdc.admin.dto.CustomizedGradeConfigVO">
        SELECT
        t2.remark AS remark,
        COALESCE ( t2.status, 0 ) AS status,
        t1.*,
        (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
        t3.is_deleted = 0)as ruleCount
        FROM tb_cdcmr_customized_warn t1
        LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'customized' AND
        t1.code = t2.disease_code
        where t1.delete_flag = '0'
        <if test="customizedName!= null and customizedName !=''">
            and t1."name" like concat('%',#{customizedName},'%')
        </if>
        <if test="warningType != null and warningType != ''">
            and t1.warning_type = #{warningType}
        </if>
        <if test="status != null ">
            and COALESCE ( t2.status,0) = #{status}
        </if>
        order by id
    </select>

    <select id="listLogicFieldBy" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedLogicField">
        SELECT
            *
        FROM tb_cdcmr_customized_logic_field
        where 1=1
        <if test="warningType != null and warningType != ''">
           and warning_type = #{warningType}
        </if>
        <if test="dataSource != null and dataSource != ''">
            and data_source = #{dataSource}
        </if>
        <if test="groupFlag != null">
            and group_flag = #{groupFlag}
        </if>
    </select>

    <select id="listEventTypeConfigByTypeAndSource" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedEventTypeConfig">
        SELECT
        *
        FROM tb_cdcmr_customized_event_type_config
        where 1 =1
        <if test="warningType != null">
           and warning_type = #{warningType}
        </if>
        <if test="dataSource != null">
            and monitor_data_source = #{dataSource}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_customized_warn"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        insert into "tb_cdcmr_customized_warn" (id, code, "name",
                                                monitor_data_source, warning_type, warning_type_name,
                                                warning_correlation, "status", memo,
                                                delete_flag, create_time, creator,
                                                update_time, updater)
        values (#{id,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{monitorDataSource,jdbcType=VARCHAR}, #{warningType,jdbcType=VARCHAR},
                #{warningTypeName,jdbcType=VARCHAR},
                #{warningCorrelation,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{memo,jdbcType=VARCHAR},
                #{deleteFlag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        insert into "tb_cdcmr_customized_warn"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                "name",
            </if>
            <if test="monitorDataSource != null">
                monitor_data_source,
            </if>
            <if test="warningType != null">
                warning_type,
            </if>
            <if test="warningTypeName != null">
                warning_type_name,
            </if>
            <if test="warningCorrelation != null">
                warning_correlation,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="monitorDataSource != null">
                #{monitorDataSource,jdbcType=VARCHAR},
            </if>
            <if test="warningType != null">
                #{warningType,jdbcType=VARCHAR},
            </if>
            <if test="warningTypeName != null">
                #{warningTypeName,jdbcType=VARCHAR},
            </if>
            <if test="warningCorrelation != null">
                #{warningCorrelation,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        update "tb_cdcmr_customized_warn"
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                "name" = #{name,jdbcType=VARCHAR},
            </if>
            <if test="monitorDataSource != null">
                monitor_data_source = #{monitorDataSource,jdbcType=VARCHAR},
            </if>
            <if test="warningType != null">
                warning_type = #{warningType,jdbcType=VARCHAR},
            </if>
            <if test="warningTypeName != null">
                warning_type_name = #{warningTypeName,jdbcType=VARCHAR},
            </if>
            <if test="warningCorrelation != null">
                warning_correlation = #{warningCorrelation,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn">
        update "tb_cdcmr_customized_warn"
        set code                = #{code,jdbcType=VARCHAR},
            "name"              = #{name,jdbcType=VARCHAR},
            monitor_data_source = #{monitorDataSource,jdbcType=VARCHAR},
            warning_type        = #{warningType,jdbcType=VARCHAR},
            warning_type_name   = #{warningTypeName,jdbcType=VARCHAR},
            warning_correlation = #{warningCorrelation,jdbcType=VARCHAR},
            "status"            = #{status,jdbcType=SMALLINT},
            memo                = #{memo,jdbcType=VARCHAR},
            delete_flag         = #{deleteFlag,jdbcType=VARCHAR},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            creator             = #{creator,jdbcType=VARCHAR},
            update_time         = #{updateTime,jdbcType=TIMESTAMP},
            updater             = #{updater,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>