<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientWarnMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outpatient_type_code" jdbcType="VARCHAR" property="outpatientTypeCode" />
    <result column="outpatient_type_name" jdbcType="VARCHAR" property="outpatientTypeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="observation" jdbcType="SMALLINT" property="observation" />
    <result column="max_life_cycle" jdbcType="SMALLINT" property="maxLifeCycle" />
    <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode" />
    <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc" />
    <result column="growth_rate" jdbcType="SMALLINT" property="growthRate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, outpatient_type_code, outpatient_type_name, remark, "status", is_deleted, update_time, 
    updater, observation, max_life_cycle, sms_send_type_code, sms_send_type_desc, growth_rate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_outpatient_warn"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_outpatient_warn"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    insert into "tb_cdcmr_outpatient_warn" (id, outpatient_type_code, outpatient_type_name,
                                            remark, "status", is_deleted,
                                            update_time, updater, observation,
                                            max_life_cycle, sms_send_type_code, sms_send_type_desc,
                                            growth_rate)
    values (#{id,jdbcType=VARCHAR}, #{outpatientTypeCode,jdbcType=VARCHAR}, #{outpatientTypeName,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT},
            #{updateTime,jdbcType=DATE}, #{updater,jdbcType=VARCHAR}, #{observation,jdbcType=SMALLINT},
            #{maxLifeCycle,jdbcType=SMALLINT}, #{smsSendTypeCode,jdbcType=VARCHAR}, #{smsSendTypeDesc,jdbcType=VARCHAR},
            #{growthRate,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    insert into "tb_cdcmr_outpatient_warn"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="outpatientTypeCode != null">
        outpatient_type_code,
      </if>
      <if test="outpatientTypeName != null">
        outpatient_type_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="observation != null">
        observation,
      </if>
      <if test="maxLifeCycle != null">
        max_life_cycle,
      </if>
      <if test="smsSendTypeCode != null">
        sms_send_type_code,
      </if>
      <if test="smsSendTypeDesc != null">
        sms_send_type_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeCode != null">
        #{outpatientTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeName != null">
        #{outpatientTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="observation != null">
        #{observation,jdbcType=SMALLINT},
      </if>
      <if test="maxLifeCycle != null">
        #{maxLifeCycle,jdbcType=SMALLINT},
      </if>
      <if test="smsSendTypeCode != null">
        #{smsSendTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smsSendTypeDesc != null">
        #{smsSendTypeDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    update "tb_cdcmr_outpatient_warn"
    <set>
      <if test="outpatientTypeCode != null">
        outpatient_type_code = #{outpatientTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeName != null">
        outpatient_type_name = #{outpatientTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
        observation = #{observation,jdbcType=SMALLINT},
        max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
      <if test="smsSendTypeCode != null">
        sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smsSendTypeDesc != null">
        sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
      </if>
        growth_rate = #{growthRate,jdbcType=SMALLINT},
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    update "tb_cdcmr_outpatient_warn"
    set outpatient_type_code = #{outpatientTypeCode,jdbcType=VARCHAR},
      outpatient_type_name = #{outpatientTypeName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      update_time = #{updateTime,jdbcType=DATE},
      updater = #{updater,jdbcType=VARCHAR},
      observation = #{observation,jdbcType=SMALLINT},
      max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
      sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
      sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getByOutpatientTypeCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    select *
    from tb_cdcmr_outpatient_warn
    where outpatient_type_code = #{outpatientTypeCode,jdbcType=VARCHAR}
      and is_deleted = 0
  </select>
  <select id="getByOutpatientTypeName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarn">
    select *
    from tb_cdcmr_outpatient_warn
    where outpatient_type_name = #{outpatientTypeName,jdbcType=VARCHAR}
      and is_deleted = 0
  </select>
  <select id="getList" resultType="com.iflytek.cdc.admin.dto.OutpatientWarnDto">
    select * ,(select count(b.id) from tb_cdcmr_outpatient_warn_rule b where a.id = b.warn_id and
    b.is_deleted = 0) as ruleCount from "tb_cdcmr_outpatient_warn" a
    where is_deleted = 0
    <if test="outpatientTypeName!= null and outpatientTypeName !=''">
      and outpatient_type_name like concat('%',#{outpatientTypeName},'%')
    </if>
    <if test="outpatientTypeCode!= null and outpatientTypeCode !=''">
      and outpatient_type_code  = #{outpatientTypeCode}
    </if>
    <if test="status != null">
      and "status" = #{status,jdbcType=SMALLINT}
    </if>
    order by id
  </select>
  <select id="getAllList" resultType="com.iflytek.cdc.admin.dto.OutpatientWarnDto">
    select * from "tb_cdcmr_outpatient_warn"
    where is_deleted = 0 and "status" = 1
    <if test="outpatientTypeCode!= null ">
      and outpatient_type_code = #{outpatientTypeCode}
    </if>
    order by id
  </select>
  <update id="updateStatusByPrimaryKey">
    update "tb_cdcmr_outpatient_warn"
    set
      "status" = #{status,jdbcType=SMALLINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>

</mapper>