<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.AuthCommonMapper">


    <select id="getViewAuthRecords" resultType="com.iflytek.cdc.admin.vo.AuthCommonVo">
        SELECT
            id,
            auth_code,
            status,
            valid_start_time,
            valid_end_time
        FROM
            <include refid="chooseTable"/>
        WHERE
            auth_code = #{query.authCode}
            AND case_id = #{query.caseId}
            AND creator_id = #{loginUserId}
            <if test = "query.moduleType != null and query.moduleType != ''">
                AND module_type = #{query.moduleType}
            </if>
            AND delete_flag = '0'
            AND (
            (
            status = '1'
            AND valid_start_time <![CDATA[<=]]> #{query.currentTime}
            AND valid_end_time <![CDATA[>=]]> #{query.currentTime}
            )
            OR status = '0'
            )
    </select>

    <sql id="chooseTable">
        <choose>
            <when test="query.systemType == 'caseManagement'">
                app.tb_cdcmr_case_management_permission_record
            </when>
        </choose>
    </sql>
</mapper>