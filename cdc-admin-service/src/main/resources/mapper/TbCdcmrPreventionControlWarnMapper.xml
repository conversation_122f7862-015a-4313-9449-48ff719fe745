<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPreventionControlWarnMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarn">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="prevention_control_code" jdbcType="VARCHAR" property="preventionControlCode" />
    <result column="prevention_control_name" jdbcType="VARCHAR" property="preventionControlName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="max_life_cycle" jdbcType="SMALLINT" property="maxLifeCycle" />
    <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode" />
    <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    id, prevention_control_code, prevention_control_name, remark, "status", is_deleted, 
    update_time, updater, max_life_cycle, sms_send_type_code, sms_send_type_desc
  </sql>
  <update id="updateByPrimaryKeySelective">
    update "tb_cdcmr_prevention_control_warn"
    <set>
      <if test="preventionControlCode != null">
        prevention_control_code = #{preventionControlCode,jdbcType=VARCHAR},
      </if>
      <if test="preventionControlName != null">
        prevention_control_name = #{preventionControlName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
        max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
      <if test="smsSendTypeCode != null">
        sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smsSendTypeDesc != null">
        sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateStatusByPrimaryKey">
    update "tb_cdcmr_prevention_control_warn"
    set
      "status" = #{status,jdbcType=SMALLINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getList" resultType="com.iflytek.cdc.admin.dto.PreventionControlWarnDto">
    select * ,(select count(b.id) from tb_cdcmr_prevention_control_warn_rule b where a.id = b.warn_id and
    b.is_deleted = 0) as ruleCount from "tb_cdcmr_prevention_control_warn" a
    where is_deleted = 0
      <if test="status != null">
          and "status" = #{status,jdbcType=SMALLINT}
      </if>
      order by id
  </select>
    <select id="selectByPrimaryKey" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarn">
        select *
        from tb_cdcmr_prevention_control_warn
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getAllList" resultType="com.iflytek.cdc.admin.dto.PreventionControlWarnDto">
        select * from "tb_cdcmr_prevention_control_warn"
        where is_deleted = 0 and "status" = 1
        <if test="preventionControlCode!= null and preventionControlCode !=''">
            and prevention_control_code = #{preventionControlCode}
        </if>
        order by id
    </select>

    <select id="queryPreventionControlInfoByGrade"
            resultType="com.iflytek.cdc.admin.dto.PreventionControlGradeConfigVO">
        SELECT
        t2.remark AS remark,
        COALESCE ( t2.status, 0 ) AS status,
        t1.*,
        (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
        t3.is_deleted = 0)as ruleCount
        FROM tb_cdcmr_prevention_control_warn t1
        LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'preventionControl' AND
        t1.prevention_control_code = t2.disease_code
        where t1.is_deleted = 0
        <if test="preventionControlName!= null and preventionControlName !=''">
            and t1.prevention_control_name like concat('%',#{preventionControlName},'%')
        </if>
        <if test="status != null ">
            and COALESCE ( t2.status,0) = #{status}
        </if>
        order by id
    </select>

</mapper>