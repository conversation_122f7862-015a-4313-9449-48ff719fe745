<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrUserDataAuthMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="login_user_id" jdbcType="VARCHAR" property="loginUserId" />
    <result column="data_type" jdbcType="SMALLINT" property="dataType" />
    <result column="auth_id" jdbcType="VARCHAR" property="authId" />
    <result column="auth_name" jdbcType="VARCHAR" property="authName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, login_user_id, data_type, auth_id, auth_name
  </sql>
  <select id="findByLoginUserId" resultType="com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_user_data_auth"
    where login_user_id = #{loginUserId}
  </select>

  <insert id="batchInsert" parameterType="com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth">
    insert into "tb_cdcmr_user_data_auth"

    (<include refid="Base_Column_List"/>)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},
      #{item.loginUserId,jdbcType=VARCHAR},
      #{item.dataType,jdbcType=SMALLINT},
      #{item.authId,jdbcType=VARCHAR},
      #{item.authName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <delete id="deleteByLoginUserId">
    delete
    from tb_cdcmr_user_data_auth
    where login_user_id = #{loginUserId}
  </delete>

  <select id="findByLoginUserIds" resultType="com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_user_data_auth"
    where login_user_id in
    <foreach item="item" index="index" collection="loginUserIds"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
  <select id="getDataAuthByLoginUserIdAndDataType" resultType="com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_user_data_auth"
    where login_user_id = #{loginUserId} and data_type=#{dataType}
  </select>
</mapper>