<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrInspectionRuleMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="mapping_id" jdbcType="VARCHAR" property="mappingId"/>
        <result column="mapping_name" jdbcType="VARCHAR" property="mappingName"/>
        <result column="inspection_item" jdbcType="VARCHAR" property="inspectionItem"/>
        <result column="inspection_item_code" jdbcType="VARCHAR" property="inspectionItemCode"/>
        <result column="sample_name" jdbcType="VARCHAR" property="sampleName"/>
        <result column="sample_code" jdbcType="VARCHAR" property="sampleCode"/>
        <result column="inspection_result" jdbcType="VARCHAR" property="inspectionResult"/>
        <result column="inspection_result_code" jdbcType="VARCHAR" property="inspectionResultCode"/>
        <result column="sex_desc" jdbcType="VARCHAR" property="sexDesc"/>
        <result column="sex_code" jdbcType="VARCHAR" property="sexCode"/>
        <result column="min_age" jdbcType="VARCHAR" property="minAge"/>
        <result column="max_age" jdbcType="VARCHAR" property="maxAge"/>
        <result column="age_unit" jdbcType="VARCHAR" property="ageUnit"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , mapping_id, mapping_name, inspection_item, inspection_item_code, sample_name,
    sample_code, inspection_result, inspection_result_code, sex_desc, sex_code, min_age, 
    max_age, age_unit, "status", update_user, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_inspection_rule"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_inspection_rule"
        where mapping_id = #{mappingId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_inspection_rule"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByMappingId">
        delete
        from "tb_cdcmr_inspection_rule"
        where mapping_id = #{mappingId,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByInfectedCode">
        delete
        from "tb_cdcmr_inspection_rule"
        where mapping_id in (select id
                             from "tb_cdcmr_inspection_mapping"
                             where infected_code = #{infectedCode,jdbcType=VARCHAR})
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        insert into "tb_cdcmr_inspection_rule" (id, mapping_id, mapping_name,
                                                inspection_item, inspection_item_code, sample_name,
                                                sample_code, inspection_result, inspection_result_code,
                                                sex_desc, sex_code, min_age,
                                                max_age, age_unit, "status",
                                                update_user, update_time)
        values (#{id,jdbcType=VARCHAR}, #{mappingId,jdbcType=VARCHAR}, #{mappingName,jdbcType=VARCHAR},
                #{inspectionItem,jdbcType=VARCHAR}, #{inspectionItemCode,jdbcType=VARCHAR},
                #{sampleName,jdbcType=VARCHAR},
                #{sampleCode,jdbcType=VARCHAR}, #{inspectionResult,jdbcType=VARCHAR},
                #{inspectionResultCode,jdbcType=VARCHAR},
                #{sexDesc,jdbcType=VARCHAR}, #{sexCode,jdbcType=VARCHAR}, #{minAge,jdbcType=VARCHAR},
                #{maxAge,jdbcType=VARCHAR}, #{ageUnit,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT},
                #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        insert into "tb_cdcmr_inspection_rule"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="mappingId != null">
                mapping_id,
            </if>
            <if test="mappingName != null">
                mapping_name,
            </if>
            <if test="inspectionItem != null">
                inspection_item,
            </if>
            <if test="inspectionItemCode != null">
                inspection_item_code,
            </if>
            <if test="sampleName != null">
                sample_name,
            </if>
            <if test="sampleCode != null">
                sample_code,
            </if>
            <if test="inspectionResult != null">
                inspection_result,
            </if>
            <if test="inspectionResultCode != null">
                inspection_result_code,
            </if>
            <if test="sexDesc != null">
                sex_desc,
            </if>
            <if test="sexCode != null">
                sex_code,
            </if>
            <if test="minAge != null">
                min_age,
            </if>
            <if test="maxAge != null">
                max_age,
            </if>
            <if test="ageUnit != null">
                age_unit,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="mappingId != null">
                #{mappingId,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="inspectionItem != null">
                #{inspectionItem,jdbcType=VARCHAR},
            </if>
            <if test="inspectionItemCode != null">
                #{inspectionItemCode,jdbcType=VARCHAR},
            </if>
            <if test="sampleName != null">
                #{sampleName,jdbcType=VARCHAR},
            </if>
            <if test="sampleCode != null">
                #{sampleCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectionResult != null">
                #{inspectionResult,jdbcType=VARCHAR},
            </if>
            <if test="inspectionResultCode != null">
                #{inspectionResultCode,jdbcType=VARCHAR},
            </if>
            <if test="sexDesc != null">
                #{sexDesc,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="minAge != null">
                #{minAge,jdbcType=VARCHAR},
            </if>
            <if test="maxAge != null">
                #{maxAge,jdbcType=VARCHAR},
            </if>
            <if test="ageUnit != null">
                #{ageUnit,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into "tb_cdcmr_inspection_rule" (id, mapping_id, mapping_name,
        inspection_item, inspection_item_code, sample_name,
        sample_code, inspection_result, inspection_result_code,
        sex_desc, sex_code, min_age,
        max_age, age_unit, "status",
        update_user, update_time)
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id,jdbcType=VARCHAR}, #{item.mappingId,jdbcType=VARCHAR}, #{item.mappingName,jdbcType=VARCHAR},
            #{item.inspectionItem,jdbcType=VARCHAR}, #{item.inspectionItemCode,jdbcType=VARCHAR},
            #{item.sampleName,jdbcType=VARCHAR},
            #{item.sampleCode,jdbcType=VARCHAR}, #{item.inspectionResult,jdbcType=VARCHAR},
            #{item.inspectionResultCode,jdbcType=VARCHAR},
            #{item.sexDesc,jdbcType=VARCHAR}, #{item.sexCode,jdbcType=VARCHAR}, #{item.minAge,jdbcType=VARCHAR},
            #{item.maxAge,jdbcType=VARCHAR}, #{item.ageUnit,jdbcType=VARCHAR}, #{item.status,jdbcType=SMALLINT},
            #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        update "tb_cdcmr_inspection_rule"
        <set>
            <if test="mappingId != null">
                mapping_id = #{mappingId,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                mapping_name = #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="inspectionItem != null">
                inspection_item = #{inspectionItem,jdbcType=VARCHAR},
            </if>
            <if test="inspectionItemCode != null">
                inspection_item_code = #{inspectionItemCode,jdbcType=VARCHAR},
            </if>
            <if test="sampleName != null">
                sample_name = #{sampleName,jdbcType=VARCHAR},
            </if>
            <if test="sampleCode != null">
                sample_code = #{sampleCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectionResult != null">
                inspection_result = #{inspectionResult,jdbcType=VARCHAR},
            </if>
            <if test="inspectionResultCode != null">
                inspection_result_code = #{inspectionResultCode,jdbcType=VARCHAR},
            </if>
            <if test="sexDesc != null">
                sex_desc = #{sexDesc,jdbcType=VARCHAR},
            </if>
            <if test="sexCode != null">
                sex_code = #{sexCode,jdbcType=VARCHAR},
            </if>
            <if test="minAge != null">
                min_age = #{minAge,jdbcType=VARCHAR},
            </if>
            <if test="maxAge != null">
                max_age = #{maxAge,jdbcType=VARCHAR},
            </if>
            <if test="ageUnit != null">
                age_unit = #{ageUnit,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule">
        update "tb_cdcmr_inspection_rule"
        set mapping_id             = #{mappingId,jdbcType=VARCHAR},
            mapping_name           = #{mappingName,jdbcType=VARCHAR},
            inspection_item        = #{inspectionItem,jdbcType=VARCHAR},
            inspection_item_code   = #{inspectionItemCode,jdbcType=VARCHAR},
            sample_name            = #{sampleName,jdbcType=VARCHAR},
            sample_code            = #{sampleCode,jdbcType=VARCHAR},
            inspection_result      = #{inspectionResult,jdbcType=VARCHAR},
            inspection_result_code = #{inspectionResultCode,jdbcType=VARCHAR},
            sex_desc               = #{sexDesc,jdbcType=VARCHAR},
            sex_code               = #{sexCode,jdbcType=VARCHAR},
            min_age                = #{minAge,jdbcType=VARCHAR},
            max_age                = #{maxAge,jdbcType=VARCHAR},
            age_unit               = #{ageUnit,jdbcType=VARCHAR},
            "status"               = #{status,jdbcType=SMALLINT},
            update_user            = #{updateUser,jdbcType=VARCHAR},
            update_time            = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>