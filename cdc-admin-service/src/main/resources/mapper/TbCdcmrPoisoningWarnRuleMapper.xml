<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningWarnRuleMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="time_range" jdbcType="SMALLINT" property="timeRange" />
    <result column="time_range_unit" jdbcType="SMALLINT" property="timeRangeUnit" />
    <result column="monitoring_object" jdbcType="SMALLINT" property="monitoringObject" />
    <result column="medical_count" jdbcType="SMALLINT" property="medicalCount" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="warn_id" jdbcType="VARCHAR" property="warnId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, time_range, time_range_unit, monitoring_object, medical_count, is_deleted, "status", 
    update_time, updater, warn_id, type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_poisoning_warn_rule"
    where id = #{id,jdbcType=VARCHAR}
  </select>
    <select id="getRuleListByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
      select *
      from tb_cdcmr_poisoning_warn_rule
      where is_deleted = 0
        and warn_id = #{warnId}

    </select>
  <select id="getAllRuleList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    select  *   from tb_cdcmr_poisoning_warn_rule
    where is_deleted = 0 and status = 1
  </select>
  <select id="getExportData" resultType="com.iflytek.cdc.admin.dto.PoisoningWarnRuleExportDataDto">
    SELECT
          tcsw.poisoning_code as poisoningCode,
          tcsw.poisoning_name as poisoningName,
           tcsw.max_Life_Cycle as maxLifeCycle,
           tcsw.poisoning_type_name as poisoningTypeName,
           tcswr.monitoring_object as  monitorObject,
           tcswr.medical_count as medicalCount,
           tcswr.time_range as timeRange,
           tcswr.time_range_unit as timeRangeUnit,
           tcswr.type as type
    FROM tb_cdcmr_poisoning_warn_rule  tcswr
           LEFT JOIN tb_cdcmr_poisoning_warn  tcsw ON tcswr.warn_id = tcsw.ID
    WHERE  tcswr.is_deleted = '0'
      and tcsw.is_deleted = '0'
    order by warn_id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_poisoning_warn_rule"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <update id="deleteOtherByIds">
    update "tb_cdcmr_poisoning_warn_rule" set is_deleted = 1
    where warn_id = #{warnId,jdbcType=VARCHAR}
    <if test="idList.size() != 0">
      and id not in
      <foreach collection="idList" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
  </update>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    insert into "tb_cdcmr_poisoning_warn_rule" (id, time_range, time_range_unit, 
      monitoring_object, medical_count, is_deleted, 
      "status", update_time, updater, 
      warn_id, type)
    values (#{id,jdbcType=VARCHAR}, #{timeRange,jdbcType=SMALLINT}, #{timeRangeUnit,jdbcType=SMALLINT}, 
      #{monitoringObject,jdbcType=SMALLINT}, #{medicalCount,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, 
      #{status,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE}, #{updater,jdbcType=VARCHAR}, 
      #{warnId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    insert into "tb_cdcmr_poisoning_warn_rule"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="timeRange != null">
        time_range,
      </if>
      <if test="timeRangeUnit != null">
        time_range_unit,
      </if>
      <if test="monitoringObject != null">
        monitoring_object,
      </if>
      <if test="medicalCount != null">
        medical_count,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="warnId != null">
        warn_id,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="timeRange != null">
        #{timeRange,jdbcType=SMALLINT},
      </if>
      <if test="timeRangeUnit != null">
        #{timeRangeUnit,jdbcType=SMALLINT},
      </if>
      <if test="monitoringObject != null">
        #{monitoringObject,jdbcType=SMALLINT},
      </if>
      <if test="medicalCount != null">
        #{medicalCount,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        #{warnId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsertRules">
    <foreach collection="recordList" item="item" separator=";">
    insert into "tb_cdcmr_poisoning_warn_rule"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="item.id != null">
        id,
      </if>
      <if test="item.timeRange != null">
        time_range,
      </if>
      <if test="item.timeRangeUnit != null">
        time_range_unit,
      </if>
      <if test="item.monitoringObject != null">
        monitoring_object,
      </if>
      <if test="item.medicalCount != null">
        medical_count,
      </if>
      <if test="item.isDeleted != null">
        is_deleted,
      </if>
      <if test="item.status != null">
        "status",
      </if>
      <if test="item.updateTime != null">
        update_time,
      </if>
      <if test="item.updater != null">
        updater,
      </if>
      <if test="item.warnId != null">
        warn_id,
      </if>
      <if test="item.type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="item.id != null">
        #{item.id,jdbcType=VARCHAR},
      </if>
      <if test="item.timeRange != null">
        #{item.timeRange,jdbcType=SMALLINT},
      </if>
      <if test="item.timeRangeUnit != null">
        #{item.timeRangeUnit,jdbcType=SMALLINT},
      </if>
      <if test="item.monitoringObject != null">
        #{item.monitoringObject,jdbcType=SMALLINT},
      </if>
      <if test="item.medicalCount != null">
        #{item.medicalCount,jdbcType=SMALLINT},
      </if>
      <if test="item.isDeleted != null">
        #{item.isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="item.status != null">
        #{item.status,jdbcType=SMALLINT},
      </if>
      <if test="item.updateTime != null">
        #{item.updateTime,jdbcType=DATE},
      </if>
      <if test="item.updater != null">
        #{item.updater,jdbcType=VARCHAR},
      </if>
      <if test="item.warnId != null">
        #{item.warnId,jdbcType=VARCHAR},
      </if>
      <if test="item.type != null">
        #{item.type,jdbcType=VARCHAR},
      </if>
    </trim>
    on conflict(id) do
    update
    set time_range      = excluded.time_range,
    time_range_unit     = excluded.time_range_unit,
    monitoring_object   = excluded.monitoring_object,
    medical_count       = excluded.medical_count,
    is_deleted          = excluded.is_deleted,
    "status"            = excluded.status,
    update_time         = excluded.update_time,
    updater             = excluded.updater,
    warn_id             = excluded.warn_id,
    type                = excluded.type
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    update "tb_cdcmr_poisoning_warn_rule"
    <set>
      <if test="timeRange != null">
        time_range = #{timeRange,jdbcType=SMALLINT},
      </if>
      <if test="timeRangeUnit != null">
        time_range_unit = #{timeRangeUnit,jdbcType=SMALLINT},
      </if>
      <if test="monitoringObject != null">
        monitoring_object = #{monitoringObject,jdbcType=SMALLINT},
      </if>
      <if test="medicalCount != null">
        medical_count = #{medicalCount,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        warn_id = #{warnId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarnRule">
    update "tb_cdcmr_poisoning_warn_rule"
    set time_range = #{timeRange,jdbcType=SMALLINT},
      time_range_unit = #{timeRangeUnit,jdbcType=SMALLINT},
      monitoring_object = #{monitoringObject,jdbcType=SMALLINT},
      medical_count = #{medicalCount,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      "status" = #{status,jdbcType=SMALLINT},
      update_time = #{updateTime,jdbcType=DATE},
      updater = #{updater,jdbcType=VARCHAR},
      warn_id = #{warnId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>