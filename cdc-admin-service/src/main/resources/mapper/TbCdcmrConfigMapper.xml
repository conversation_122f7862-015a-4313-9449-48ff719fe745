<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrConfigMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrConfig">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="config_type" jdbcType="SMALLINT" property="configType" />
    <result column="config_type_name" jdbcType="VARCHAR" property="configTypeName" />
    <result column="config_key" jdbcType="VARCHAR" property="configKey" />
    <result column="config_value" jdbcType="VARCHAR" property="configValue" />
    <result column="config_key_desc" jdbcType="VARCHAR" property="configKeyDesc" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, config_type, config_type_name, config_key, config_value, config_key_desc, updater_name
  </sql>
  <select id="selectByConfigTypeAndConfigKey"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_config"
    where config_type = #{configType} and config_key = #{configKey}
  </select>

  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrConfig">
    update "tb_cdcmr_config"
    set config_value = #{configValue,jdbcType=VARCHAR}
        where config_type = #{configType} and config_key = #{configKey}
  </update>
</mapper>