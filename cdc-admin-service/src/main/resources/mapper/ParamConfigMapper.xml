<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.ParamConfigMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.ParamConfig">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_param_config-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="config_code" jdbcType="VARCHAR" property="configCode" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="config_value" jdbcType="VARCHAR" property="configValue" />
    <result column="config_remark" jdbcType="VARCHAR" property="configRemark" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="queryConfigInfo" parameterType="com.iflytek.cdc.admin.dto.SearchParamDTO" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue, pc.config_group as configGroup,
    pc.config_remark as configRemark, pc.create_user as createUser, pc.update_user as updateUser,pc.create_time as createTime, pc.update_time as updateTime,
    pc.update_user_name from tb_cdcmr_param_config as pc
    where 1=1
    <if test="configGroup != null and configGroup != ''">
      and pc.config_group=#{configGroup}
    </if>
    <if test="configName != null and configName != ''">
      and pc.config_name like concat('%',#{configName},'%')
    </if>
    <if test="configCode != null and configCode != ''">
      and pc.config_code like concat('%',#{configCode},'%')
    </if>
    <if test="isDelete != null and isDelete != ''">
        and pc.is_delete=#{isDelete}
    </if>
    order by pc.update_time desc
  </select>

  <select id="queryConfigOrgInfo" parameterType="com.iflytek.cdc.admin.dto.SearchParamDTO" resultType="com.iflytek.cdc.admin.entity.ParamOrg">
     select org_code as orgCode,org_name as orgName,count(org_code) as paramNum,org_id as orgId from tb_cdcmr_param_org
     where 1=1
     <if test="orgIds !=null ">
       and  org_id  in (
      <foreach collection="orgIds" separator="," item="item" index="index">
        #{item}
      </foreach>
       )
    </if>
    <if test="orgCode != null and orgCode != ''">
      and  org_code =#{orgCode}
    </if>
    <if test="orgName != null and orgName != ''">
      and  org_name  like concat('%',#{orgName},'%')
    </if>
    <if test="isDelete != null and isDelete != ''">
      and  is_delete=#{isDelete}
    </if>
    group by org_code,org_name,org_id
  </select>

  <select id="queryConfigOrgByCode" parameterType="com.iflytek.cdc.admin.dto.SearchParamDTO" resultType="com.iflytek.cdc.admin.entity.ParamOrg">
    select id  as id, config_code as configCode,org_id as orgId,org_code as orgCode,org_name as orgName,
    dept_id as deptId,dept_name as deptName,org_config_value as orgConfigValue,create_user as createUser,
    update_user as updateUser, create_time as createTime,update_time as updateTime,update_user_name as updateUserName
    from tb_cdcmr_param_org where 1=1
    <if test="isDelete != null and isDelete != ''">
      and is_delete=#{isDelete,jdbcType=VARCHAR}
    </if>
    <if test="configCode != null and configCode != ''">
      and config_code=#{configCode,jdbcType=VARCHAR}
    </if>
    <if test="orgIds != null and orgIds.size() != 0 ">
      and  org_id  in (
      <foreach collection="orgIds" separator="," item="item" index="index">
        #{
      item}
      </foreach>
      )
    </if>
  </select>


  <select id="queryConfigInfoByOrg" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue,pc.config_group as configGroup,
    pc.config_remark as configRemark,po.org_config_value as orgConfigValue, pc.create_user as createUser, pc.update_user as updateUser,po.update_user_name as updateUserName,
    pc.create_time as createTime, pc.update_time as updateTime from tb_cdcmr_param_org as po join tb_cdcmr_param_config as pc on po.config_code=pc.config_code where 1=1
    <if test="OrgId != null and OrgId != ''">
      and po.org_id=#{OrgId}
    </if>
    <if test="isDelete != null and isDelete != ''">
       and pc.is_delete=#{isDelete} and po.is_delete=#{isDelete}
    </if>
    order by pc.update_time desc
  </select>

  <insert id="insertParamConfig" parameterType="com.iflytek.cdc.admin.dto.ParamConfigDTO">
    insert into tb_cdcmr_param_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="configGroup != null and configGroup != ''">
        config_group,
      </if>
      <if test="configCode != null and configCode != ''">
        config_code,
      </if>
      <if test="configName != null and configName != ''">
        config_name,
      </if>
      <if test="configValue != null and configValue != ''">
        config_value,
      </if>
      <if test="configRemark != null and configRemark != ''">
        config_remark,
      </if>
      <if test="createUser != null and createUser != ''">
        create_user,
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user,
      </if>
        create_time,
        update_time,
      <if test="isDelete != null and isDelete != ''">
        is_delete,
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        update_user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="configGroup != null and configGroup != ''">
        #{configGroup,jdbcType=VARCHAR},
      </if>
      <if test="configCode != null and configCode != ''">
        #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="configName != null and configName != ''">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null and configValue != ''">
        #{configValue,jdbcType=VARCHAR},
      </if>
      <if test="configRemark != null and configRemark != ''">
        #{configRemark,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null and createUser != ''">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        #{updateUser,jdbcType=VARCHAR},
      </if>
        now(),
        now(),
      <if test="isDelete != null and isDelete != ''">
        #{isDelete},
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        #{updateUserName,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>

  <insert id="insertParamOrg" parameterType="java.util.List">
    insert into tb_cdcmr_param_org (id, config_code, org_id, org_name,org_config_value,
     dept_id, dept_name, create_user, update_user, create_time, update_time,is_delete,org_code,update_user_name) values
    <foreach collection="list" item="org" separator="," index="index">
      (#{org.id,jdbcType=VARCHAR}, #{org.configCode,jdbcType=VARCHAR},  #{org.orgId,jdbcType=VARCHAR}, #{org.orgName,jdbcType=VARCHAR},
      #{org.orgConfigValue,jdbcType=VARCHAR},#{org.deptId,jdbcType=VARCHAR}, #{org.deptName,jdbcType=VARCHAR}, #{org.createUser,jdbcType=VARCHAR},
      #{org.updateUser,jdbcType=VARCHAR}, now(),now(), #{org.isDelete,jdbcType=VARCHAR},#{org.orgCode},#{org.updateUserName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="updateParamOrg" parameterType="com.iflytek.cdc.admin.dto.UpdateOrgInfoDTO">
     update tb_cdcmr_param_org
     <set>
       <if test="configCode != null and configCode != ''">
          config_code=#{configCode},
       </if>
       <if test="updateUser != null and updateUser != ''">
          update_user=#{updateUser,jdbcType=VARCHAR},
       </if>
       update_time=now()
     </set>
    where config_code=#{oldConfigCode}
    <if test="orgIds != null and orgIds.size() != 0">
      and  org_id  not in (
      <foreach collection="orgIds" separator="," item="item" index="index">
        #{item}
      </foreach>
      )
    </if>
  </update>

  <select id="queryParamConfigByCode" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue,
    pc.config_remark as configRemark from tb_cdcmr_param_config as pc where 1=1
    <if test="configCode != null and configCode != ''">
      and pc.config_code=#{configCode}
    </if>
    <if test="isDelete != null and isDelete != ''">
      and pc.is_delete=#{isDelete}
    </if>
  </select>

  <select id="queryOrgInfoByCodeAndId" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamOrg">
    select id as id, config_code as configCode, org_id as orgId,
    org_name as orgName,org_code as orgCode,dept_id as deptId, dept_name as deptName,org_config_value from tb_cdcmr_param_org
    where 1=1
    <if test="configCode != null and configCode != ''">
      and config_code=#{configCode}
    </if>
    <if test="orgId != null and orgId != ''">
      and org_id=#{orgId}
    </if>
    <if test="isDelete != null and isDelete != ''">
      and is_delete=#{isDelete,jdbcType=VARCHAR}
    </if>
  </select>

  <update id="deleteByCodeAndId" parameterType="java.lang.String">
    update tb_cdcmr_param_org
    <set>
      <if test="isDelete != null and isDelete != ''">
        is_delete=#{isDelete},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        update_user_name=#{updateUserName,jdbcType=VARCHAR},
      </if>
      update_time=now()
    </set>
    where org_id=#{orgId} and config_code=#{configCode}
  </update>

  <select id="queryOneParamConfigByCode" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue,
    pc.config_remark as configRemark,config_group as configGroup from tb_cdcmr_param_config as pc where 1=1
    <if test="configCode != null and configCode != ''">
      and config_code=#{configCode}
    </if>
    <if test="isDelete != null and isDelete != ''">
      and is_delete=#{isDelete}
    </if>
  </select>

  <update id="updateParamConfig" parameterType="com.iflytek.cdc.admin.dto.ParamConfigDTO">
    update tb_cdcmr_param_config
    <set>
      <if test="configCode != null and configCode != ''">
        config_code = #{configCode,jdbcType=VARCHAR},
      </if>
      <if test="configName != null and configName != ''">
        config_name = #{configName,jdbcType=VARCHAR},
      </if>
      <if  test="configGroup != null and configGroup != ''">
        config_group=#{configGroup,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null and configValue != ''">
        config_value = #{configValue,jdbcType=VARCHAR},
      </if>
        config_remark = #{configRemark,jdbcType=VARCHAR},
      <if test="createUser != null and createUser != ''">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
       update_time = now(),
      <if test="isDelete != null and isDelete != ''">
        is_delete=#{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        update_user_name=#{updateUserName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <update id="deleteOrgByCode" parameterType="com.iflytek.cdc.admin.entity.UpdateOrgParam">
   update tb_cdcmr_param_org
    <set>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      <if test="deleteFlag != null and deleteFlag != '' != null">
        is_delete=#{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUserName != null and updateUserName != ''">
        update_user_name=#{updateUserName,jdbcType=VARCHAR},
      </if>
      update_time=now()
    </set>
    where config_code=#{configCode}
    <if test="orgIds != null and orgIds.size() != 0">
      and  org_id  in (
      <foreach collection="orgIds" separator="," item="item" index="index">
        #{item.orgId,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </update>

  <select id="queryById" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select id as id, config_code as configCode, config_name as configName, config_value as configValue,
    config_remark as configRemark from tb_cdcmr_param_config where id=#{id}
  </select>

  <update id="deleteConfigById" parameterType="java.lang.String">
    update tb_cdcmr_param_config
    <set>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      <if test="isDelete != null and isDelete != ''">
        is_delete=#{isDelete},
      </if>
      update_time=now()
    </set>
    where id=#{id}
  </update>

  <select id="paramConfigInfo" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue,
    pc.config_remark as configRemark,config_group as configGroup from tb_cdcmr_param_config as pc where 1=1
      and config_code=#{configCode}
      and is_delete=#{isDelete}
    <if test="configGroup != null and configGroup != ''">
      and config_group=#{configGroup,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="orgInfo" resultType="com.iflytek.cdc.admin.entity.ParamOrg" parameterType="java.lang.String">
    select id as id, config_code as configCode, org_id as orgId,
    org_name as orgName,org_code as orgCode,dept_id as deptId, dept_name as deptName,org_config_value from tb_cdcmr_param_org
    where 1=1
      and config_code=#{configCode}
      and org_id=#{orgId}
      and is_delete=#{isDelete,jdbcType=VARCHAR}
  </select>

  <select id="queryParamByGroup" resultType="com.iflytek.cdc.admin.sdk.pojo.ParamInfo" parameterType="java.lang.String">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue,
    pc.config_remark as configRemark,config_group as configGroup from tb_cdcmr_param_config as pc where 1=1
    and pc.config_group=#{configGroup,jdbcType=VARCHAR}
    and pc.is_delete=#{isDelete,jdbcType=VARCHAR}
  </select>
  <select id="queryParamByCode" resultType="com.iflytek.cdc.admin.entity.ParamConfig">
    select pc.id as id, pc.config_code as configCode, pc.config_name as configName, pc.config_value as configValue, pc.config_group as configGroup,
    pc.config_remark as configRemark, pc.create_user as createUser, pc.update_user as updateUser,pc.create_time as createTime, pc.update_time as updateTime,
    pc.update_user_name from tb_cdcmr_param_config as pc
    where is_delete= '1'
    <if test="keyword!= null and keyword != ''">
      and pc.config_code like concat('%',#{keyword},'%')
    </if>
    order by pc.update_time desc
  </select>

  <select id="getStatusByCodeAndGroup" resultType="java.lang.String">
    select config_value
    from tb_cdcmr_param_config
    where config_code = #{configCode} and config_group = #{configGroup}
    and is_delete = '1'  -- 反人类设定, is_delete = 1 是未删除数据
  </select>
</mapper>