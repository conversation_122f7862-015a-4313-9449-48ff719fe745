<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrDataSourceConfigMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="signal_type" jdbcType="VARCHAR" property="signalType" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="medical_delay_days" jdbcType="INTEGER" property="medicalDelayDays" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_type, signal_type, update_name, update_id, update_time, data_source
  </sql>

  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig">
    update "tb_cdcmr_data_source_config"
    set
      update_name = #{updateName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=VARCHAR},
      update_time = #{updateTime},
      data_source = #{dataSource,jdbcType=VARCHAR},
      medical_delay_days = #{medicalDelayDays, jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByBusinessTypeAndSignalType"
          resultType="com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig">
    select *
    from tb_cdcmr_data_source_config
    where business_type = #{businessType,jdbcType=VARCHAR}
        and signal_type = #{signalType,jdbcType=VARCHAR}
    limit 1
  </select>

  <select id="listByBusinessType"
          resultType="com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig">
    select *
    from tb_cdcmr_data_source_config
    where business_type = #{businessType,jdbcType=VARCHAR}
  </select>

  <select id="getList" resultType="com.iflytek.cdc.admin.dto.DataSourceConfigVO">
    select *
    from tb_cdcmr_data_source_config
    order by business_type,id
  </select>
</mapper>