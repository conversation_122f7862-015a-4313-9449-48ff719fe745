<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    <result column="grade_code" jdbcType="VARCHAR" property="gradeCode" />
    <result column="grade_name" jdbcType="VARCHAR" property="gradeName" />
    <result column="color" jdbcType="VARCHAR" property="color"/>
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    grade_code, grade_name, color, "status", remark, update_time, update_user, is_deleted
  </sql>
  <select id="getPageList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
      select
      <include refid="Base_Column_List"/>
      from "tb_cdcmr_warning_grade"
      where is_deleted != '1'
      <if test="gradeName != null">
          and grade_name like concat('%',#{gradeName},'%')
      </if>
      <if test="status != null">
          and status = #{status}
      </if>
    order by grade_code
  </select>


  <select id="getByGradeName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_warning_grade"
    where is_deleted != '1'
    and grade_name = #{gradeName}
  </select>
  <select id="getByGradeCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_warning_grade"
    where is_deleted != '1'
    and grade_code = #{gradeCode}
  </select>
  <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_warning_grade"
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    update "tb_cdcmr_warning_grade"
    set is_deleted = 1
    where grade_code = #{gradeCode}  </delete>
  <delete id="deleteByGradeName">
    update "tb_cdcmr_warning_grade"
    set is_deleted = 1
    where grade_name = #{gradeName}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    insert into "tb_cdcmr_warning_grade" (grade_code, grade_name,
                                          color, "status", remark,
                                          update_time, update_user, is_deleted)
    values (#{gradeCode,jdbcType=VARCHAR}, #{gradeName,jdbcType=VARCHAR},
            #{color,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{remark,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    insert into "tb_cdcmr_warning_grade"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gradeCode != null">
        grade_code,
      </if>
      <if test="gradeName != null">
        grade_name,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gradeCode != null">
        #{gradeCode,jdbcType=VARCHAR},
      </if>
      <if test="gradeName != null">
        #{gradeName,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    update "tb_cdcmr_warning_grade"
    <set>
      <if test="gradeName != null">
        grade_name = #{gradeName,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </set>
    where grade_code = #{gradeCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade">
    update "tb_cdcmr_warning_grade"
    set grade_name  = #{gradeName,jdbcType=VARCHAR},
        color       = #{color,jdbcType=VARCHAR},
        "status"    = #{status,jdbcType=SMALLINT},
        remark      = #{remark,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        is_deleted  = #{isDeleted,jdbcType=SMALLINT}
    where grade_code = #{gradeCode,jdbcType=VARCHAR}
  </update>
</mapper>