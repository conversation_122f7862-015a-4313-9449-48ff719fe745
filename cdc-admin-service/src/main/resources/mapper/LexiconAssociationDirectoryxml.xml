<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.LexiconAssociationDirectoryMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.LexiconAssociationDirectory">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_lexicon_association_directory-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="association_type" jdbcType="VARCHAR" property="associationType" />
    <result column="directory_name" jdbcType="VARCHAR" property="directoryName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, association_type, directory_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_lexicon_association_directory
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.LexiconAssociationDirectory">
    <!--@mbg.generated-->
    insert into tb_cdcmr_lexicon_association_directory (id, association_type, directory_name
      )
    values (#{id,jdbcType=VARCHAR}, #{associationType,jdbcType=VARCHAR}, #{directoryName,jdbcType=VARCHAR}
      )
  </insert>

  <select id="selectAll" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
        from tb_cdcmr_lexicon_association_directory
  </select>
</mapper>