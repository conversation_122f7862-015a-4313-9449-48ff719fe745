<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.LexiconAssociationMapper">

    <select id="isDiseaseAssociated" resultType="boolean" parameterType="com.iflytek.cdc.admin.entity.LexiconAssociation">
        SELECT COUNT(1) > 0
        FROM tb_cdcmr_lexicon_association
        WHERE (left_lexicon_id = #{leftLexiconId}
        AND right_lexicon_id = #{rightLexiconId})
        or (left_lexicon_id = #{rightLexiconId}
        AND right_lexicon_id = #{leftLexiconId})
        AND association_type = #{associationType}
        AND is_association = '1'
    </select>

    <select id="findByLeftLexiconId" resultType="com.iflytek.cdc.admin.entity.LexiconAssociation">
        SELECT * FROM tb_cdcmr_lexicon_association
                WHERE left_lexicon_id = #{leftId}
        AND association_type = #{associationType}
        AND is_association = '1'
    </select>
</mapper>