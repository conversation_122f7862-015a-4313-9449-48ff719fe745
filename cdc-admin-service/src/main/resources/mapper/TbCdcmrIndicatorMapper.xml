<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrIndicator">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_indicator-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="indicator_name" jdbcType="VARCHAR" property="indicatorName" />
    <result column="indicator_definition" jdbcType="VARCHAR" property="indicatorDefinition" />
    <result column="business_process" jdbcType="VARCHAR" property="businessProcess" />
    <result column="indicator_type" jdbcType="VARCHAR" property="indicatorType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="calculation_formula" jdbcType="VARCHAR" property="calculationFormula" />
    <result column="indicator_label" jdbcType="VARCHAR" property="indicatorLabel" />
    <result column="indicator_alias_name" jdbcType="VARCHAR" property="indicatorAliasName" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="updater_name" jdbcType="VARCHAR" property="updaterName" />
    <result column="analysis_target" jdbcType="VARCHAR" property="analysisTarget" />
    <result column="applicable_disease" jdbcType="VARCHAR" property="applicableDisease" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, indicator_name, indicator_definition, business_process, indicator_type, create_time, 
    update_time, creator, updater, delete_flag, num, calculation_formula, indicator_label, 
    indicator_alias_name, creator_name, updater_name, analysis_target, applicable_disease
  </sql>


    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrIndicator">
        INSERT INTO tb_cdcmr_indicator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="null != indicatorName and '' != indicatorName">
                indicator_name,
            </if>
            <if test="null != indicatorAliasName and '' != indicatorAliasName">
                indicator_alias_name,
            </if>
            <if test="null != indicatorDefinition and '' != indicatorDefinition">
                indicator_definition,
            </if>
            <if test="null != businessProcess and '' != businessProcess">
                business_process,
            </if>
            <if test="null != indicatorType and '' != indicatorType">
                indicator_type,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updateTime ">
                update_time,
            </if>
            <if test="null != creator and '' != creator">
                creator,
            </if>
            <if test="null != creatorName and '' != creatorName">
                creator_name,
            </if>
            <if test="null != updater and '' != updater">
                updater,
            </if>
            <if test="null != updaterName and '' != updaterName">
                updater_name,
            </if>
            <if test="null != deleteFlag ">
                delete_flag,
            </if>
            <if test="null != num ">
                num,
            </if>
            <if test="null != calculationFormula and '' != calculationFormula">
                calculation_formula,
            </if>
            <if test="null != indicatorLabel and '' != indicatorLabel">
                indicator_label,
            </if>
            <if test="null != analysisTarget and '' != analysisTarget">
                analysis_target,
            </if>
            <if test="null != applicableDisease and '' != applicableDisease">
                applicable_disease
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="null != indicatorName and '' != indicatorName">
                #{indicatorName},
            </if>
            <if test="null != indicatorAliasName and '' != indicatorAliasName">
                #{indicatorAliasName},
            </if>
            <if test="null != indicatorDefinition and '' != indicatorDefinition">
                #{indicatorDefinition},
            </if>
            <if test="null != businessProcess and '' != businessProcess">
                #{businessProcess},
            </if>
            <if test="null != indicatorType and '' != indicatorType">
                #{indicatorType},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime ">
                #{updateTime},
            </if>
            <if test="null != creator and '' != creator">
                #{creator},
            </if>
            <if test="null != creatorName and '' != creatorName">
                #{creatorName},
            </if>
            <if test="null != updater and '' != updater">
                #{updater},
            </if>
            <if test="null != updaterName and '' != updaterName">
                #{updaterName},
            </if>
            <if test="null != deleteFlag ">
                #{deleteFlag},
            </if>
            <if test="null != num ">
                #{num},
            </if>
            <if test="null != calculationFormula and '' != calculationFormula">
                #{calculationFormula},
            </if>
            <if test="null != indicatorLabel and '' != indicatorLabel">
                #{indicatorLabel},
            </if>
            <if test="null != analysisTarget and '' != analysisTarget">
                #{analysisTarget},
            </if>
            <if test="null != applicableDisease and '' != applicableDisease">
                #{applicableDisease}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        update tb_cdcmr_indicator set delete_flag = '1'
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrIndicator">
        UPDATE tb_cdcmr_indicator
        <set>
            <if test="null != indicatorName and '' != indicatorName">
                indicator_name = #{indicatorName},
            </if>
            <if test="null != indicatorAliasName and '' != indicatorAliasName">
                indicator_alias_name = #{indicatorAliasName},
            </if>
            <if test="null != indicatorDefinition and '' != indicatorDefinition">
                indicator_definition = #{indicatorDefinition},
            </if>
            <if test="null != businessProcess and '' != businessProcess">
                business_process = #{businessProcess},
            </if>
            <if test="null != indicatorType and '' != indicatorType">
                indicator_type = #{indicatorType},
            </if>
            <if test="null != createTime ">
                create_time = #{createTime},
            </if>
            <if test="null != updateTime ">
                update_time = #{updateTime},
            </if>
            <if test="null != creator and '' != creator">
                creator = #{creator},
            </if>
            <if test="null != creatorName and '' != creatorName">
                creator_name = #{creatorName},
            </if>
            <if test="null != updater and '' != updater">
                updater = #{updater},
            </if>
            <if test="null != updaterName and '' != updaterName">
                updater_name = #{updaterName},
            </if>
            <if test="null != deleteFlag ">
                delete_flag = #{deleteFlag},
            </if>
            <if test="null != num ">
                num = #{num},
            </if>
            <if test="null != calculationFormula and '' != calculationFormula">
                calculation_formula = #{calculationFormula},
            </if>
            <if test="null != indicatorLabel and '' != indicatorLabel">
                indicator_label = #{indicatorLabel},
            </if>
            <if test="null != analysisTarget and '' != analysisTarget">
                analysis_target = #{analysisTarget},
            </if>
            <if test="null != applicableDisease and '' != applicableDisease">
                applicable_disease = #{applicableDisease}
            </if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_indicator
        WHERE id = #{id}
        and delete_flag = '0'
    </select>

    <select id="loadByLabel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_indicator
        WHERE indicator_label = #{label}
        and delete_flag = '0'
    </select>

    <select id="countByNameNorId" resultType="int">
        SELECT count(1)
        FROM tb_cdcmr_indicator
        WHERE indicator_name = #{name}
        <if test="null != excludeId and '' != excludeId">
            and id != #{excludeId}
        </if>
        and delete_flag = '0'
    </select>

    <select id="listByLabels" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_indicator
        WHERE indicator_label in
        <foreach collection="labels" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and delete_flag = '0'
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_indicator
        where delete_flag = '0'
        <if test="indicatorLabel != null and indicatorLabel != ''">
            and indicator_label = #{indicatorLabel}
        </if>
        <if test="indicatorName != null and indicatorName != ''">
            and indicator_name = #{indicatorName}
        </if>
        <if test="null != businessProcess and '' != businessProcess">
            and business_process = #{businessProcess}
        </if>
        <if test="null != analysisTargetCodes and '' != analysisTargetCodes">
            and EXISTS (
            SELECT 1
            FROM json_array_elements(analysis_target::json) AS elem
            WHERE elem->>'code' = ANY(string_to_array(#{analysisTargetCodes}, ','))
            )
        </if>
        <if test="null != indicatorType and '' != indicatorType">
            and indicator_type = #{indicatorType}
        </if>
        <if test="null != keyword and '' != keyword">
            and (indicator_label ilike concat('%',#{keyword},'%') or indicator_name ilike concat('%',#{keyword},'%'))
        </if>
        <if test="null != applicableDiseaseQuery">
            <if test="null != applicableDiseaseQuery.type and '' != applicableDiseaseQuery.type">
                and applicable_disease::json->>'type' = #{applicableDiseaseQuery.type}
            </if>
            <if test="null != applicableDiseaseQuery.diseaseId and '' != applicableDiseaseQuery.diseaseId">
                and applicable_disease::json->>'diseaseId' = #{applicableDiseaseQuery.diseaseId}
            </if>
        </if>
        order by update_time desc, create_time desc
    </select>

    <select id="loadLastNum" resultType="java.lang.Integer">
        SELECT max(num)
        FROM tb_cdcmr_indicator
        where delete_flag = '0'
    </select>
    

</mapper>