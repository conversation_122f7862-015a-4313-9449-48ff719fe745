<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimRollupRelMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimRollupRel">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_analysis_dim_rollup_rel-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="analysis_dim_id" jdbcType="VARCHAR" property="analysisDimId" />
    <result column="rollup_dim_id" jdbcType="VARCHAR" property="rollupDimId" />
    <result column="seq_num" jdbcType="INTEGER" property="seqNum" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, analysis_dim_id, rollup_dim_id, seq_num, delete_flag, create_time, update_time
  </sql>

  <delete id="softDelByDimId">
    update tb_cdcmr_analysis_dim_rollup_rel
    set delete_flag = '1'
    where analysis_dim_id = #{dimId,jdbcType=VARCHAR}
  </delete>

  <delete id="softDelById">
    update tb_cdcmr_analysis_dim_rollup_rel
    set delete_flag = '1'
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <select id="selectByDimIds" resultType="com.iflytek.cdc.admin.vo.AnalysisDimRollupRelVO">
    select r.id,
           r.analysis_dim_id,
           r.rollup_dim_id,
           r.seq_num,
           r.delete_flag,
           r.create_time,
           r.update_time,
           d.dimension_name as rollup_dim_name
    from tb_cdcmr_analysis_dim_rollup_rel r
           join tb_cdcmr_analysis_dimension d on r.rollup_dim_id = d.id
    where r.delete_flag = '0'
    <if test="dimIds != null and dimIds.size() != 0">
      and r.analysis_dim_id in (
      <foreach collection="dimIds" item="item" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </select>

  <select id="loadLastNum" resultType="java.lang.Integer">
    SELECT max(seq_num)
    FROM tb_cdcmr_analysis_dim_rollup_rel
    where delete_flag = '0'
      and analysis_dim_id = #{dimId,jdbcType=VARCHAR}
  </select>
</mapper>
