<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrExportTaskMapper">



    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_name, task_url, task_param, total_count, attachment_id, attachment_url, attachment_size,
        export_result_info, notes, status, delete_flag, create_time, update_time, creator_id, creator,error_desc,export_type,app_code
    </sql>
    <select id="getProceedCount" resultType="java.lang.Integer">
        select count(id) from  tb_cdcmr_export_task where status=1 and delete_flag ='0'
    </select>
    <select id="getProceedDoneTask" resultType="com.iflytek.cdc.admin.entity.TbCdcmrExportTask">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_export_task where status = 1 and delete_flag = '0'
        and task_url =#{taskUrl}  and task_param=#{taskParam} and creator_id=#{userId} order by create_time limit 1
    </select>
    <select id="queryList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrExportTask">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_export_task
        where delete_flag = '0'
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="startDate != null and endDate != null ">
            and create_time between #{startDate} and #{endDate}
        </if>
        <if test="creator != null and creator != ''">
            and creator like concat('%',#{creator},'%')
        </if>
        <if test="exportType != null and exportType != ''">
            and export_type = #{exportType}
        </if>
        <if test="loginUserId != null and loginUserId != ''">
            and creator_id = #{loginUserId}
        </if>
        <if test="appCode != null and appCode != ''">
            and app_code = #{appCode}
        </if>
        <if test="taskName != null and taskName != ''">
            and task_name like concat('%', #{taskName}, '%')
        </if>
         order by create_time desc
    </select>

    <select id="queryExportRecordList" resultType="com.iflytek.cdc.admin.dto.ExportApplicationListDTO">
        select
        b.id,
        a.id as approval_id,
        a.task_id,
        b.task_name,
        b.export_type,
        b.status,
        a.creator_time,
        coalesce(a.approval_status, 'NO_APPROVAL_REQUIRED') as approval_status,
        b.attachment_size as file_size,
        b.attachment_id,
        b.attachment_url,
        coalesce(b.export_name, c.export_name) as export_name
        from tb_cdcmr_export_task b
        left join tb_cdcmr_export_approval a on a.task_id = b.id
        left join tb_cdcmr_export_permission_config c on a.export_code = c.export_code
        where b.delete_flag = '0'

        <if test="status != null">
            and b.status = #{status}
        </if>

        <if test="taskName != null and taskName != ''">
            and b.task_name like concat('%',#{taskName},'%')
        </if>

        <if test="startDate != null and endDate != null ">
            and b.create_time between #{startDate} and #{endDate}
        </if>

        <if test="creator != null and creator != ''">
            and b.creator like concat('%',#{creator},'%')
        </if>

        <if test="loginUserId != null and loginUserId != ''">
            and b.creator_id = #{loginUserId}
        </if>

        <if test="exportType != null and exportType != ''">
            and b.export_type = any (string_to_array(#{exportType}, ','))
        </if>

        <if test="appCode != null and appCode != ''">
            and b.app_code = #{appCode}
        </if>
        <if test="approvalStatus != null and approvalStatus == 'PENDING_APPROVAL'">
            and (a.approval_status in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL'))
        </if>

        <if test="approvalStatus != null and approvalStatus != '' and approvalStatus != 'PENDING_APPROVAL'">
            and coalesce(a.approval_status, 'NO_APPROVAL_REQUIRED') = #{approvalStatus}
        </if>

        <if test="exportName != null and exportName != ''">
            and coalesce(b.export_name, c.export_name) = concat('%', #{exportName}, '%')
        </if>


        order by b.create_time desc
    </select>




</mapper>
