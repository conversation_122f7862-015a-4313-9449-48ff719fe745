<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.AddressStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.AddressStandard">
        <id column="id" property="id"/>
        <result column="address_area_name" property="addressAreaName"/>
        <result column="address_area_code" property="addressAreaCode"/>
        <result column="address_longitude" property="addressLongitude"/>
        <result column="address_latitude" property="addressLatitude"/>
        <result column="address_province_name" property="addressProvinceName"/>
        <result column="address_province_code" property="addressProvinceCode"/>
        <result column="address_city_name" property="addressCityName"/>
        <result column="address_city_code" property="addressCityCode"/>
        <result column="address_district_name" property="addressDistrictName"/>
        <result column="address_district_code" property="addressDistrictCode"/>
        <result column="address_town_name" property="addressTownName"/>
        <result column="address_town_code" property="addressTownCode"/>
        <result column="poi_type" property="poiType"/>
        <result column="coordinates_type" property="coordinatesType"/>
        <result column="address_name" property="addressName"/>
        <result column="address_detail_desc" property="addressDetailDesc"/>
        <result column="creator" property="creator"/>
        <result column="create_datetime" property="createDatetime"/>
        <result column="updator" property="updator"/>
        <result column="update_datetime" property="updateDatetime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, address_area_name, address_area_code, address_longitude, address_latitude, address_province_name, address_province_code, address_city_name, address_city_code, address_district_name, address_district_code, address_town_name, address_town_code, poi_type, coordinates_type, address_name, address_detail_desc, creator, create_datetime, updator, update_datetime
    </sql>

    <update id="saveOrUpdateAddressStandard">
        INSERT INTO tb_cdcmr_address_standard (id, address_area_name, address_area_code,
        address_longitude, address_latitude, address_province_name,
        address_province_code, address_city_name, address_city_code,
        address_district_name, address_district_code,
        address_town_name, address_town_code, poi_type,
        coordinates_type, address_name, address_detail_desc,
        creator, create_datetime, updator,
        update_datetime)
        VALUES (#{id,jdbcType=VARCHAR}, #{addressAreaName,jdbcType=VARCHAR}, #{addressAreaCode,jdbcType=VARCHAR},
        #{addressLongitude,jdbcType=VARCHAR}, #{addressLatitude,jdbcType=VARCHAR}, #{addressProvinceName,jdbcType=VARCHAR},
        #{addressProvinceCode,jdbcType=VARCHAR}, #{addressCityName,jdbcType=VARCHAR}, #{addressCityCode,jdbcType=VARCHAR},
        #{addressDistrictName,jdbcType=VARCHAR}, #{addressDistrictCode,jdbcType=VARCHAR},
        #{addressTownName,jdbcType=VARCHAR}, #{addressTownCode,jdbcType=VARCHAR}, #{poiType,jdbcType=VARCHAR},
        #{coordinatesType,jdbcType=VARCHAR}, #{addressName,jdbcType=VARCHAR}, #{addressDetailDesc,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, now(), #{updator,jdbcType=VARCHAR},
        now())
        ON CONFLICT (id)
        DO UPDATE SET address_area_name      = excluded.address_area_name    ,
                      address_area_code      = excluded.address_area_code    ,
                      address_longitude      = excluded.address_longitude    ,
                      address_latitude       = excluded.address_latitude     ,
                      address_province_name  = excluded.address_province_name,
                      address_province_code  = excluded.address_province_code,
                      address_city_name      = excluded.address_city_name    ,
                      address_city_code      = excluded.address_city_code    ,
                      address_district_name  = excluded.address_district_name,
                      address_district_code  = excluded.address_district_code,
                      address_town_name      = excluded.address_town_name    ,
                      address_town_code      = excluded.address_town_code    ,
                      poi_type               = excluded.poi_type             ,
                      coordinates_type       = excluded.coordinates_type     ,
                      address_name           = excluded.address_name         ,
                      address_detail_desc    = excluded.address_detail_desc  ,
                      updator                = excluded.updator              ,
                      update_datetime        = now()
    </update>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_address_standard
        <where>
            <choose>
                <when test="list != null and list.size() >0 ">
                    id in
                    <foreach collection="list" item="item" open="(" close=")" separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>

    </select>

</mapper>
