<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.CommonDataSyncMapper">
    <select id="queryLocalInfo" statementType="STATEMENT" parameterType="java.lang.String"
            resultType="com.iflytek.cdc.admin.entity.CommonMdmDictInfo">
        select id as id, dict_code as dictCode, dict_name as dictName
        from ${tableName}
    </select>

    <insert id="insertLocalInfo">
        insert into ${tableName}(id, dict_code, dict_name,
        is_enable, create_user,create_time, update_user, update_time,sync_time)
        values
        <foreach collection="params" item="sn" separator="," index="index">
            (#{sn.id,jdbcType=VARCHAR}, #{sn.dictCode,jdbcType=VARCHAR}, #{sn.dictName,jdbcType=VARCHAR},
            #{sn.isEnabled,jdbcType=VARCHAR}, #{sn.createUser,jdbcType=VARCHAR},
            now(), #{sn.updateUser,jdbcType=VARCHAR}, now(),
            now())
        </foreach>
    </insert>

    <update id="updateMasterDataStatus" parameterType="com.iflytek.cdc.admin.dto.UpdateMdmDataDTO">
        update ${tableName}
        <set>
            <if test="useStatus != null and useStatus != ''">
                is_enable=#{useStatus,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user=#{updateUser},
            </if>
            update_time=now(),
            sync_time=now(),
        </set>
        where id in (
        <foreach collection="ids" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </update>

    <update id="updateCodeName" parameterType="java.lang.String">
        update ${tableName}
        <set>
            <if test="updateCodeName != null and updateCodeName != ''">
                dict_name=#{updateCodeName,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_time =now(),
        </set>
        where dict_code=#{updateCode,jdbcType=VARCHAR}
    </update>

    <select id="queryDictInfoById" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.CommonMdmDictInfo">
        select id as id, dict_code as dictCode, dict_name as dictName
        from ${tableName} where 1=1
        <if test="id != null and id != ''">
            and id=#{id}
        </if>
    </select>
</mapper>