<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrFzUapOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_fz_uap_organization-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="org_type_code" jdbcType="VARCHAR" property="orgTypeCode" />
    <result column="higher_org" jdbcType="CHAR" property="higherOrg" />
    <result column="higher_name" jdbcType="VARCHAR" property="higherName" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="higher_org_code" jdbcType="VARCHAR" property="higherOrgCode" />
    <result column="street" jdbcType="VARCHAR" property="street" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="flag" jdbcType="INTEGER" property="flag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", code, province, province_code, city, city_code, district, district_code, 
    short_name, org_type, org_type_code, higher_org, higher_name, "level", "status", 
    sort, remark, create_time, update_time, higher_org_code, street, street_code, flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_fz_uap_organization
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_fz_uap_organization
    where id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_uap_organization (id, "name", code, 
      province, province_code, city, 
      city_code, district, district_code, 
      short_name, org_type, org_type_code, 
      higher_org, higher_name, "level", 
      "status", sort, remark, 
      create_time, update_time, higher_org_code, 
      street, street_code, flag
      )
    values (#{id,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{district,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, 
      #{shortName,jdbcType=VARCHAR}, #{orgType,jdbcType=VARCHAR}, #{orgTypeCode,jdbcType=VARCHAR}, 
      #{higherOrg,jdbcType=CHAR}, #{higherName,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{higherOrgCode,jdbcType=VARCHAR}, 
      #{street,jdbcType=VARCHAR}, #{streetCode,jdbcType=VARCHAR}, #{flag,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_uap_organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="district != null">
        district,
      </if>
      <if test="districtCode != null">
        district_code,
      </if>
      <if test="shortName != null">
        short_name,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="orgTypeCode != null">
        org_type_code,
      </if>
      <if test="higherOrg != null">
        higher_org,
      </if>
      <if test="higherName != null">
        higher_name,
      </if>
      <if test="level != null">
        "level",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="higherOrgCode != null">
        higher_org_code,
      </if>
      <if test="street != null">
        street,
      </if>
      <if test="streetCode != null">
        street_code,
      </if>
      <if test="flag != null">
        flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgTypeCode != null">
        #{orgTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="higherOrg != null">
        #{higherOrg,jdbcType=CHAR},
      </if>
      <if test="higherName != null">
        #{higherName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="higherOrgCode != null">
        #{higherOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="street != null">
        #{street,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null">
        #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_uap_organization
    <set>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        district_code = #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        short_name = #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgTypeCode != null">
        org_type_code = #{orgTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="higherOrg != null">
        higher_org = #{higherOrg,jdbcType=CHAR},
      </if>
      <if test="higherName != null">
        higher_name = #{higherName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        "level" = #{level,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="higherOrgCode != null">
        higher_org_code = #{higherOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="street != null">
        street = #{street,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null">
        street_code = #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzUapOrganization">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_uap_organization
    set "name" = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      district = #{district,jdbcType=VARCHAR},
      district_code = #{districtCode,jdbcType=VARCHAR},
      short_name = #{shortName,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=VARCHAR},
      org_type_code = #{orgTypeCode,jdbcType=VARCHAR},
      higher_org = #{higherOrg,jdbcType=CHAR},
      higher_name = #{higherName,jdbcType=VARCHAR},
      "level" = #{level,jdbcType=INTEGER},
      "status" = #{status,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      higher_org_code = #{higherOrgCode,jdbcType=VARCHAR},
      street = #{street,jdbcType=VARCHAR},
      street_code = #{streetCode,jdbcType=VARCHAR},
      flag = #{flag,jdbcType=INTEGER}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>