<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrInspectionStandardMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        <id column="inspection_item_code" jdbcType="VARCHAR" property="inspectionItemCode"/>
        <result column="inspection_item" jdbcType="VARCHAR" property="inspectionItem"/>
        <result column="item_type" jdbcType="VARCHAR" property="itemType"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        inspection_item_code
        , inspection_item, item_type, business_type, "status", remark,
    update_user, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_inspection_standard"
        where inspection_item_code = #{inspectionItemCode,jdbcType=VARCHAR}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        select *
        from tb_cdcmr_inspection_standard
        where status = 1
    </select>
    <select id="getListByParam" resultType="com.iflytek.cdc.admin.dto.InspectionStandardVO">
        select * from (SELECT t1.*,
        CASE
        WHEN
        (SELECT COUNT
        (t2.ID)
        FROM tb_cdcmr_inspection_rule t2
        WHERE t2.inspection_item_code = t1.inspection_item_code
        OR t2.inspection_item_code LIKE concat(t1.inspection_item_code, '|%')
        OR t2.inspection_item_code LIKE concat('%|', t1.inspection_item_code, '|%')
        OR t2.inspection_item_code LIKE concat('%|', t1.inspection_item_code)) > 0 THEN 1
        ELSE 0 END AS isMapping
        FROM tb_cdcmr_inspection_standard t1) as r
        where 1 = 1
        <if test="inspectionItemCode != null and inspectionItemCode != '' ">
            and inspection_item_code like concat( '%', #{inspectionItemCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="inspectionItem != null and inspectionItem != ''">
            and inspection_item like concat( '%', #{inspectionItem,jdbcType=VARCHAR},'%')
        </if>
        <if test="businessType != null and businessType != ''">
            and business_type like concat( '%',#{businessType,jdbcType=VARCHAR},'%')
        </if>
        <if test="isMapping != null">
            and isMapping = #{isMapping}
        </if>
        order by inspection_item_code
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_inspection_standard"
        where inspection_item_code = #{inspectionItemCode,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByPrimaryKeyList">
        delete from "tb_cdcmr_inspection_standard"
        where inspection_item_code in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        insert into "tb_cdcmr_inspection_standard" (inspection_item_code, inspection_item,
                                                    item_type, business_type, "status",
                                                    remark, update_user, update_time)
        values (#{inspectionItemCode,jdbcType=VARCHAR}, #{inspectionItem,jdbcType=VARCHAR},
                #{itemType,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT},
                #{remark,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        insert into "tb_cdcmr_inspection_standard"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inspectionItemCode != null">
                inspection_item_code,
            </if>
            <if test="inspectionItem != null">
                inspection_item,
            </if>
            <if test="itemType != null">
                item_type,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inspectionItemCode != null">
                #{inspectionItemCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectionItem != null">
                #{inspectionItem,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                #{itemType,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="upsertList">
        <foreach collection="recordList" item="item" separator=";">
            insert into "tb_cdcmr_inspection_standard"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.inspectionItemCode != null">
                    inspection_item_code,
                </if>
                <if test="item.inspectionItem != null">
                    inspection_item,
                </if>
                <if test="item.itemType != null">
                    item_type,
                </if>
                <if test="item.businessType != null">
                    business_type,
                </if>
                <if test="item.status != null">
                    "status",
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.updateUser != null">
                    update_user,
                </if>
                <if test="item.updateTime != null">
                    update_time,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.inspectionItemCode != null">
                    #{item.inspectionItemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.inspectionItem != null">
                    #{item.inspectionItem,jdbcType=VARCHAR},
                </if>
                <if test="item.itemType != null">
                    #{item.itemType,jdbcType=VARCHAR},
                </if>
                <if test="item.businessType != null">
                    #{item.businessType,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    #{item.status,jdbcType=SMALLINT},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.updateUser != null">
                    #{item.updateUser,jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
            </trim>
            on conflict(inspection_item_code) do
            update
            set inspection_item = excluded.inspection_item
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        update "tb_cdcmr_inspection_standard"
        <set>
            <if test="inspectionItem != null">
                inspection_item = #{inspectionItem,jdbcType=VARCHAR},
            </if>
            <if test="itemType != null">
                item_type = #{itemType,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where inspection_item_code = #{inspectionItemCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard">
        update "tb_cdcmr_inspection_standard"
        set inspection_item = #{inspectionItem,jdbcType=VARCHAR},
            item_type       = #{itemType,jdbcType=VARCHAR},
            business_type   = #{businessType,jdbcType=VARCHAR},
            "status"        = #{status,jdbcType=SMALLINT},
            remark          = #{remark,jdbcType=VARCHAR},
            update_user     = #{updateUser,jdbcType=VARCHAR},
            update_time     = #{updateTime,jdbcType=TIMESTAMP}
        where inspection_item_code = #{inspectionItemCode,jdbcType=VARCHAR}
    </update>
</mapper>