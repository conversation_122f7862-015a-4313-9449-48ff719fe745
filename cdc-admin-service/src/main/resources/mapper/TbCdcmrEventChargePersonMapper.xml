<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrEventChargePersonMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrEventChargePerson">
        <!--@mbg.generated-->
        <!--@Table tb_cdcmr_event_charge_person-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
        <result column="deal_person_info" jdbcType="VARCHAR" property="dealPersonInfo" />
        <result column="deal_person_type" jdbcType="VARCHAR" property="dealPersonType" />
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
        <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
        <result column="disease_id" jdbcType="VARCHAR" property="diseaseId" />
        <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
        <result column="event_level_id" jdbcType="VARCHAR" property="eventLevelId" />
        <result column="event_type" jdbcType="VARCHAR" property="eventType" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, creator, creator_id, deal_person_info, deal_person_type, delete_flag,
        disease_code, disease_id, disease_name, event_level_id, event_type, "status", update_time,
        updater, updater_id
    </sql>

    <select id="loadById" resultType="com.iflytek.cdc.admin.entity.TbCdcmrEventChargePerson">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_event_charge_person
        where id = #{id}
    </select>

    <select id="bulkLoadByIds" resultType="com.iflytek.cdc.admin.entity.TbCdcmrEventChargePerson">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_event_charge_person
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <insert id="mergeInto">
        insert into tb_cdcmr_event_charge_person (id, create_time, creator, creator_id, deal_person_info, deal_person_type, delete_flag,
        disease_code, disease_id, disease_name, event_level_id, event_type, status, update_time,
        updater, updater_id)
        values
        <foreach collection="entities" item="entity" separator="," >
            (
            #{entity.id}, #{entity.createTime}, #{entity.creator}, #{entity.creatorId}, #{entity.dealPersonInfo}, #{entity.dealPersonType}, #{entity.deleteFlag},
            #{entity.diseaseCode}, #{entity.diseaseId}, #{entity.diseaseName}, #{entity.eventLevelId}, #{entity.eventType}, #{entity.status}, #{entity.updateTime},
            #{entity.updater}, #{entity.updaterId}
            )
        </foreach>
        on conflict (id)
        do update set
        deal_person_info = EXCLUDED.deal_person_info,
        deal_person_type = EXCLUDED.deal_person_type,
        delete_flag = EXCLUDED.delete_flag,
        disease_code = EXCLUDED.disease_code,
        disease_id = EXCLUDED.disease_id,
        disease_name = EXCLUDED.disease_name,
        event_level_id = EXCLUDED.event_level_id,
        event_type = EXCLUDED.event_type,
        status = EXCLUDED.status,
        update_time = EXCLUDED.update_time,
        updater = EXCLUDED.updater,
        updater_id = EXCLUDED.updater_id
    </insert>

    <select id="listDiseaseConfig" resultType="com.iflytek.cdc.admin.vo.EventChargePersonVO">

        select
        p.id,
        p.deal_person_type,
        dw.warning_type as event_type,
        p.deal_person_info,
        dw.disease_code,
        dw.disease_name,
        l.event_level,
        l.id as event_level_id,
        p.status as is_enable
        from tb_cdcmr_disease_warning dw
        join tb_cdcmr_event_level l on l.delete_flag = '0' and dw.delete_flag = '0'
        left join tb_cdcmr_event_charge_person p
        on p.disease_code = dw.disease_code
        and p.event_level_id = l.id
        and p.delete_flag = '0'
        where 1=1
        <if test="eventType != null and eventType != ''">
            and dw.warning_type = #{eventType}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and dw.disease_code = #{diseaseCode}
        </if>
        <if test="diseaseCodes != null and diseaseCodes.size() > 0">
            and dw.disease_code in
            <foreach collection="diseaseCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="eventLevelId != null and eventLevelId != ''">
            and l.id = #{eventLevelId}
        </if>
        ORDER BY event_type, disease_name, event_level_id
    </select>

    <select id="getDealPersonInfoBy" resultType="com.iflytek.cdc.admin.vo.EventChargePersonVO">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_event_charge_person
        where status = 1
        and (
        <foreach collection="dtoList" index="index" item="item" separator="or">
            (
            <if test="item.initDiagnose != null and item.initDiagnose != ''">
                disease_name = #{item.initDiagnose}
            </if>
            <if test="item.initDiagnose != null and item.initDiagnose != '' and
                 (item.eventLevelId != null or item.eventType != null)">
                and
            </if>
            <if test="item.eventLevelId != null and item.eventLevelId != ''">
                event_level_id = #{item.eventLevelId}
            </if>
            <if test="item.eventLevelId != null and item.eventLevelId != '' and
                 item.eventType != null and item.eventType != ''">
                and
            </if>
            <if test="item.eventType != null and item.eventType != ''">
                event_type = #{item.eventType}
            </if>
            )
        </foreach>
        )
    </select>

    <select id="findByDiseaseCodeAndEventLevel" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_event_charge_person
        where disease_code = #{diseaseCode}
        and event_level_id = #{eventLevelId}
        and delete_flag = '0'
    </select>


    <select id="findByDiseaseCodeLevelPairs" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        FROM tb_cdcmr_event_charge_person
        WHERE
        <foreach collection="pairs" item="pair" separator="OR">
            (disease_code = #{pair.diseaseCode} AND event_level_id = #{pair.eventLevelId})
        </foreach>
    </select>
</mapper>