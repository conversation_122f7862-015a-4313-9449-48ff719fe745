<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrLogExportMapper">

    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrLogExport">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="queryUrl" column="query_url" jdbcType="VARCHAR"/>
            <result property="queryParam" column="query_param" jdbcType="OTHER"/>
            <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,query_url,query_param,
        file_size,status,create_time,
        creator
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_log_export
        where  id = #{id,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from tb_cdcmr_log_export
        where  id = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrLogExport" useGeneratedKeys="true">
        insert into tb_cdcmr_log_export
        ( id,query_url,query_param
        ,file_size,status,create_time
        ,creator)
        values (#{id,jdbcType=VARCHAR},#{queryUrl,jdbcType=VARCHAR},#{queryParam, jdbcType=VARCHAR}
        ,#{fileSize,jdbcType=BIGINT},#{status,jdbcType=SMALLINT},#{createTime,jdbcType=TIMESTAMP}
        ,#{creator,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrLogExport" useGeneratedKeys="true">
        insert into tb_cdcmr_log_export
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="queryUrl != null">query_url,</if>
                <if test="queryParam != null">query_param,</if>
                <if test="fileSize != null">file_size,</if>
                <if test="status != null">status,</if>
                <if test="createTime != null">create_time,</if>
                <if test="creator != null">creator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=VARCHAR},</if>
                <if test="queryUrl != null">#{queryUrl,jdbcType=VARCHAR},</if>
                <if test="queryParam != null">#{queryParam,jdbcType=VARCHAR},</if>
                <if test="fileSize != null">#{fileSize,jdbcType=BIGINT},</if>
                <if test="status != null">#{status,jdbcType=SMALLINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrLogExport">
        update tb_cdcmr_log_export
        <set>
                <if test="queryUrl != null">
                    query_url = #{queryUrl,jdbcType=VARCHAR},
                </if>
                <if test="queryParam != null">
                    query_param = #{queryParam,jdbcType=VARCHAR},
                </if>
                <if test="fileSize != null">
                    file_size = #{fileSize,jdbcType=BIGINT},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=SMALLINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="creator != null">
                    creator = #{creator,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrLogExport">
        update tb_cdcmr_log_export
        set 
            query_url =  #{queryUrl,jdbcType=VARCHAR},
            query_param =  #{queryParam,jdbcType=VARCHAR},
            file_size =  #{fileSize,jdbcType=BIGINT},
            status =  #{status,jdbcType=SMALLINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            creator =  #{creator,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=VARCHAR} 
    </update>
</mapper>
