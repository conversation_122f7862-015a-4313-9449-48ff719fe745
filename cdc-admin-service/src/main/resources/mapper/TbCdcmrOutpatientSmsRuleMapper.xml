<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientSmsRuleMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="rule_num" jdbcType="VARCHAR" property="ruleNum" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="outpatient_type_name" jdbcType="VARCHAR" property="outpatientTypeName" />
    <result column="outpatient_type_code" jdbcType="VARCHAR" property="outpatientTypeCode" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="SMALLINT" property="deleted" />
    <result column="login_user_id" jdbcType="VARCHAR" property="loginUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, "name", user_type, phone, rule_type, rule_num, province_name, province_code, 
    city_name, city_code, district_name, district_code, outpatient_type_name, outpatient_type_code, 
    creator, create_time, updater, update_time, deleted, login_user_id
  </sql>

  <insert id="insert" >
    insert into "tb_cdcmr_outpatient_sms_rule" (id, "name", user_type,
    phone, rule_type, rule_num,
    province_name, province_code, city_name,
    city_code, district_name, district_code,
    outpatient_type_name, outpatient_type_code,
    creator, create_time, updater,
    update_time, deleted, login_user_id)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.userType,jdbcType=VARCHAR},
      #{item.phone,jdbcType=VARCHAR}, #{item.ruleType,jdbcType=VARCHAR}, #{item.ruleNum,jdbcType=VARCHAR},
      #{item.provinceName,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR},
      #{item.cityCode,jdbcType=VARCHAR}, #{item.districtName,jdbcType=VARCHAR}, #{item.districtCode,jdbcType=VARCHAR},
      #{item.outpatientTypeName,jdbcType=VARCHAR}, #{item.outpatientTypeCode,jdbcType=VARCHAR},
      #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=VARCHAR},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleted,jdbcType=SMALLINT}, #{item.loginUserId,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective">
    <foreach collection="recordList" item="item" index="index" open="" close="" separator=";">
      update "tb_cdcmr_outpatient_sms_rule"
      <set>
        <if test="item.name != null">
          "name" = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.userType != null">
          user_type = #{item.userType,jdbcType=VARCHAR},
        </if>
        <if test="item.phone != null">
          phone = #{item.phone,jdbcType=VARCHAR},
        </if>
        <if test="item.ruleType != null">
          rule_type = #{item.ruleType,jdbcType=VARCHAR},
        </if>
        <if test="item.ruleNum != null">
          rule_num = #{item.ruleNum,jdbcType=VARCHAR},
        </if>
        <if test="item.provinceName != null">
          province_name = #{item.provinceName,jdbcType=VARCHAR},
        </if>
        <if test="item.provinceCode != null">
          province_code = #{item.provinceCode,jdbcType=VARCHAR},
        </if>
        <if test="item.cityName != null">
          city_name = #{item.cityName,jdbcType=VARCHAR},
        </if>
        <if test="item.cityCode != null">
          city_code = #{item.cityCode,jdbcType=VARCHAR},
        </if>
        district_name = #{item.districtName,jdbcType=VARCHAR},
        district_code = #{item.districtCode,jdbcType=VARCHAR},
        <if test="item.creator != null">
          creator = #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updater != null">
          updater = #{item.updater,jdbcType=VARCHAR},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted,jdbcType=SMALLINT},
        </if>

      </set>
      where outpatient_type_code = #{item.outpatientTypeCode,jdbcType=VARCHAR} and login_user_id =
      #{item.loginUserId,jdbcType=VARCHAR}
    </foreach>
  </update>

  <delete id="deleteByOutpatientCodeList">
    update "tb_cdcmr_outpatient_sms_rule"
    <set>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      deleted = 1
    </set>
    where login_user_id = #{loginUserId}
    and outpatient_type_code in
    <foreach item="item" index="index" collection="outpatientCodeList"
             open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="getListByLoginUserId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule">
    select *
    from tb_cdcmr_outpatient_sms_rule
    where login_user_id = #{loginUserId}
      and deleted = 0
  </select>
  <select id="getListByCodeList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule">
    select *
    from tb_cdcmr_outpatient_sms_rule
    where deleted = 0
    <if test="codeList!= null and codeList.size() &gt; 0">
      and outpatient_type_code in
      <foreach item="item" index="index" collection="codeList"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </select>
  <delete id="deleteByLoginUserId">
    update "tb_cdcmr_outpatient_sms_rule"
    <set>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      deleted = 1
    </set>
    where login_user_id = #{loginUserId}
  </delete>


</mapper>