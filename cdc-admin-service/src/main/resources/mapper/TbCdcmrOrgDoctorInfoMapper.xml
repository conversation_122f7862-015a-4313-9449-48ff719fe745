<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOrgDoctorInfoMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrOrgDoctorInfo">
        <!--@mbg.generated-->
        <!--@Table tb_cdcmr_org_doctor_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="doc_id" jdbcType="VARCHAR" property="docId"/>
        <result column="doc_name" jdbcType="VARCHAR" property="docName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="forbidden" jdbcType="BOOLEAN" property="forbidden"/>
        <result column="deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, org_name, dept_id, dept_name, doc_id, doc_name, phone, forbidden, deleted,
        create_time, creator, update_time, updater
    </sql>

    <!--auto generated by MybatisCodeHelper on 2022-04-21-->
    <select id="selectAllByOrgId" resultMap="BaseResultMap">
        select doc_name,phone
        from tb_cdcmr_org_doctor_info
        where org_id = #{orgId,jdbcType=VARCHAR}
          and forbidden = false
          and deleted = false
    </select>
</mapper>