<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientWarnRuleMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="time_range" jdbcType="SMALLINT" property="timeRange" />
    <result column="growth_rate" jdbcType="SMALLINT" property="growthRate" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="warn_id" jdbcType="VARCHAR" property="warnId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, time_range, growth_rate, is_deleted, "status", update_time, updater, warn_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_outpatient_warn_rule"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_outpatient_warn_rule"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule" useGeneratedKeys="true">
    insert into "tb_cdcmr_outpatient_warn_rule" (time_range, growth_rate, is_deleted, 
      "status", update_time, updater, 
      warn_id)
    values (#{timeRange,jdbcType=SMALLINT}, #{growthRate,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, 
      #{status,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE}, #{updater,jdbcType=VARCHAR}, 
      #{warnId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule" useGeneratedKeys="true">
    insert into "tb_cdcmr_outpatient_warn_rule"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="timeRange != null">
        time_range,
      </if>
      <if test="growthRate != null">
        growth_rate,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="warnId != null">
        warn_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="timeRange != null">
        #{timeRange,jdbcType=SMALLINT},
      </if>
      <if test="growthRate != null">
        #{growthRate,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        #{warnId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule">
    update "tb_cdcmr_outpatient_warn_rule"
    <set>
      <if test="timeRange != null">
        time_range = #{timeRange,jdbcType=SMALLINT},
      </if>
      <if test="growthRate != null">
        growth_rate = #{growthRate,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        warn_id = #{warnId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule">
    update "tb_cdcmr_outpatient_warn_rule"
    set time_range = #{timeRange,jdbcType=SMALLINT},
      growth_rate = #{growthRate,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      "status" = #{status,jdbcType=SMALLINT},
      update_time = #{updateTime,jdbcType=DATE},
      updater = #{updater,jdbcType=VARCHAR},
      warn_id = #{warnId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getRuleListByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule">
    select *
    from tb_cdcmr_outpatient_warn_rule
    where is_deleted = 0
      and warn_id = #{warnId}
  </select>
  <select id="getAllRuleList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientWarnRule">
    select  *   from tb_cdcmr_outpatient_warn_rule
    where is_deleted = 0 and status = 1
  </select>
  <select id="getExportData" resultType="com.iflytek.cdc.admin.dto.OutpatientWarnRuleExportDataDto">
    SELECT tcsw.outpatient_type_name as outpatientTypeName,
           tcsw.observation          as observation,
           tcsw.max_life_cycle       as maxLifeCycle,
           tcswr.time_range          as timeRange,
           tcswr.growth_rate         as growthRate
    FROM tb_cdcmr_outpatient_warn_rule tcswr
           LEFT JOIN tb_cdcmr_outpatient_warn tcsw ON tcswr.warn_id = tcsw.ID
    WHERE tcswr.is_deleted = '0'
      and tcsw.is_deleted = '0'
    order by warn_id
  </select>
  <delete id="deleteOtherByIds">
    update "tb_cdcmr_outpatient_warn_rule" set is_deleted = 1
    where warn_id = #{warnId,jdbcType=VARCHAR}
    <if test="idList.size() != 0">
      and id not in
      <foreach collection="idList" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
  </delete>
  <insert id="upsertRules">
    <foreach collection="recordList" item="item" separator=";">
      insert into "tb_cdcmr_outpatient_warn_rule"
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.timeRange != null">
          time_range,
        </if>
        <if test="item.growthRate != null">
          growth_rate,
        </if>
        <if test="item.isDeleted != null">
          is_deleted,
        </if>
        <if test="item.status != null">
          "status",
        </if>
        <if test="item.updateTime != null">
          update_time,
        </if>
        <if test="item.updater != null">
          updater,
        </if>
        <if test="item.warnId != null">
          warn_id,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=VARCHAR},
        </if>
        <if test="item.timeRange != null">
          #{item.timeRange,jdbcType=SMALLINT},
        </if>
        <if test="item.growthRate != null">
           #{item.growthRate,jdbcType=SMALLINT},
        </if>
        <if test="item.isDeleted != null">
          #{item.isDeleted,jdbcType=SMALLINT},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=SMALLINT},
        </if>
        <if test="item.updateTime != null">
           #{item.updateTime,jdbcType=DATE},
        </if>
        <if test="item.updater != null">
           #{item.updater,jdbcType=VARCHAR},
        </if>
        <if test="item.warnId != null">
          #{item.warnId,jdbcType=VARCHAR},
        </if>

      </trim>
      on conflict(id) do
      update
      set time_range = excluded.time_range,
      growth_rate = excluded.growth_rate,
      is_deleted = excluded.is_deleted,
      "status" = excluded.status,
      update_time = excluded.update_time,
      updater = excluded.updater,
      warn_id = excluded.warn_id
    </foreach>
  </insert>
</mapper>