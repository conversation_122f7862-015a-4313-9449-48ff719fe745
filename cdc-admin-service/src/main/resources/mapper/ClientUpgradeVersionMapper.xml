<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.ClientUpgradeVersionMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.ClientUpgradeVersion">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_client_upgrade_version-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="version_type" jdbcType="VARCHAR" property="versionType" />
    <result column="version_sub_type" jdbcType="VARCHAR" property="versionSubType" />
    <result column="description_file_url" jdbcType="VARCHAR" property="descriptionFileUrl" />
    <result column="resource_file_url" jdbcType="VARCHAR" property="resourceFileUrl" />
    <result column="is_upgrade_version" jdbcType="VARCHAR" property="isUpgradeVersion" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="upgrade_note" jdbcType="VARCHAR" property="upgradeNote" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, area_code, area_name, org_code, version_code, version_type, version_sub_type, description_file_url,
    resource_file_url, is_upgrade_version, create_user, create_time, update_user, update_time, 
    upgrade_note, org_name, is_delete
  </sql>

  <sql id="version_count">
    <!--@mbg.generated-->
    version_code,count(*) as version_count
  </sql>

  <select id="queryClientUpgradeDtoByVersionCode" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto"
          resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <!--@mbg.generated-->
    select a.id,a.version_code,a.version_sub_type,a.version_type,a.description_file_url,
    a.resource_file_url,a.update_time, b.version_id,count(b.version_id) as version_count from
    tb_cdcmr_client_upgrade_version as a join
    tb_cdcmr_client_upgrade_district
    as b on a.id=b.version_id
    where
    b.is_delete='1'
    <if test="versionType != null and versionType != ''">
      and version_type = #{versionType,jdbcType=VARCHAR}
    </if>
    <if test="versionCode != null and versionCode != '' ">
      and version_code  like concat('%',#{versionCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    <if test="orgName != null  and orgName != ''">
      and b.org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
    </if>
     group by a.id,a.version_code, a.version_type, a.version_sub_type, a.description_file_url, a.resource_file_url,a.update_time, b.version_id
     order by a.update_time desc
  </select>


  <select id="queryClientUpgradeDtoById" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto"
          resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    select a.id,a.version_code,a.version_sub_type,a.version_type,a.description_file_url,
    a.resource_file_url,a.upgrade_note,a.update_time, b.org_code,b.org_name,b.area_code,b.area_name from tb_cdcmr_client_upgrade_version as a join
    tb_cdcmr_client_upgrade_district
    as b on a.id=b.version_id  where a.id = #{id,jdbcType=VARCHAR}
    <if test="orgName != null  and orgName != ''">
    and b.org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
    </if>
    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    and b.is_delete='1'
  </select>


  <select id="queryClientUpgradeVersionCountDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <!--@mbg.generated-->
    select
    <include refid="version_count" />
    from tb_cdcmr_client_upgrade_version
    where
    is_delete='1'
    <if test="versionCode != null and versionCode != '' ">
      and version_code = #{versionCode,jdbcType=VARCHAR}
    </if>
    group by  version_code
  </select>

  <select id="queryCurrentVersionByOrgId" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    SELECT org_code,org_name, max((update_time) ) max_day FROM tb_cdcmr_client_upgrade_district  b
    where
    is_delete='1'
    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    <if test="orgName != null  and orgName != ''">
      and b.org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
    </if>
     GROUP BY org_code,org_name  order by max_day desc
  </select>

  <select id="queryPreVersionByOrgId" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    select b.org_code, c.version_code,c.description_file_url,c.resource_file_url,b.update_time from tb_cdcmr_client_upgrade_version c,

    (SELECT org_code,update_time,version_id FROM tb_cdcmr_client_upgrade_district  where  is_delete ='1' group  by org_code,update_time,version_id order by  org_code,update_time desc) b where c.id=b.version_id

    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    order by b.update_time   desc
  </select>

  <select id="queryVersionCountByOrgId" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    select count(b.version_id) as version_count,b.org_code,b.org_name,b.area_code,b.area_name from
    tb_cdcmr_client_upgrade_district
    as b  where b.is_delete='1'
    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    <if test="orgName != null  and orgName != ''">
      and org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
    </if>
    group by
    b.org_code,b.org_name,b.area_code,b.area_name
  </select>

  <select id="queryClientUpgradeVersionDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    select a.version_code,a.version_sub_type,a.version_type,a.description_file_url,
    a.resource_file_url,a.update_time,b.org_code,b.org_name from
    tb_cdcmr_client_upgrade_version as a join
    tb_cdcmr_client_upgrade_district
    as b on a.id=b.version_id
    where
    b.is_delete='1'
    <if test="orgCodes != null">
      and b.org_code in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
    <if test="versionType != null and versionType != ''">
      and version_type = #{versionType,jdbcType=VARCHAR}
    </if>
    <if test="versionCode != null and versionCode != '' ">
      and version_code = #{versionCode,jdbcType=VARCHAR}
    </if>
    <if test="orgName != null  and orgName != ''">
      and org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
    </if>
     order by a.update_time desc
</select>


  <delete id="deleteClientUpgradeVersion" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto">
    <!--@mbg.generated-->
    delete from tb_cdcmr_client_upgrade_version
    where org_code in
    <foreach collection="orgCodes" open="(" separator="," close=")" item="orgCode" index="index">
      #{orgCode}
    </foreach>
    <if test="versionCode != null">
      and version_code = #{versionCode,jdbcType=VARCHAR},
    </if>
  </delete>

  <insert id="addBatchClientUpgradeVersionDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <!--@mbg.generated-->
    insert into tb_cdcmr_client_upgrade_version (id, area_code, area_name,
    org_code, version_code, version_type,
      version_sub_type, description_file_url, resource_file_url, 
      is_upgrade_version, create_user, create_time, 
      update_user, update_time, upgrade_note, 
      org_name, is_delete)
    values
    <foreach collection="clientUpgradeVersionDtos" item="clientUpgradeVersionDto" separator=",">
      (#{clientUpgradeVersionDto.id,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.areaCode,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.areaName,jdbcType=VARCHAR},
      #{clientUpgradeVersionDto.orgCode,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.versionCode,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.versionType,jdbcType=VARCHAR},
      #{clientUpgradeVersionDto.versionSubType,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.descriptionFileUrl,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.resourceFileUrl,jdbcType=VARCHAR},
      #{clientUpgradeVersionDto.isUpgradeVersion,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.createUser,jdbcType=VARCHAR}, now(),
      #{clientUpgradeVersionDto.updateUser,jdbcType=VARCHAR}, now(), #{clientUpgradeVersionDto.upgradeNote,jdbcType=VARCHAR},
      #{clientUpgradeVersionDto.orgName,jdbcType=VARCHAR}, #{clientUpgradeVersionDto.isDelete,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="addClientUpgradeVersionDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <!--@mbg.generated-->
    insert into tb_cdcmr_client_upgrade_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="versionCode != null">
        version_code,
      </if>
      <if test="versionType != null">
        version_type,
      </if>
      <if test="versionSubType != null">
        version_sub_type,
      </if>
      <if test="descriptionFileUrl != null">
        description_file_url,
      </if>
      <if test="resourceFileUrl != null">
        resource_file_url,
      </if>
      <if test="isUpgradeVersion != null">
        is_upgrade_version,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      create_time,
      update_time,
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="upgradeNote != null">
        upgrade_note,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="versionCode != null">
        #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="versionType != null">
        #{versionType,jdbcType=VARCHAR},
      </if>
      <if test="versionSubType != null">
        #{versionSubType,jdbcType=VARCHAR},
      </if>
      <if test="descriptionFileUrl != null">
        #{descriptionFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceFileUrl != null">
        #{resourceFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="isUpgradeVersion != null">
        #{isUpgradeVersion,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      now(),
      now(),
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="upgradeNote != null">
        #{upgradeNote,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateClientUpgradeVersionDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <!--@mbg.generated-->
    update tb_cdcmr_client_upgrade_version
    <set>
      <if test="versionCode != null">
        version_code = #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="versionType != null">
        version_type = #{versionType,jdbcType=VARCHAR},
      </if>
      <if test="versionSubType != null">
        version_sub_type = #{versionSubType,jdbcType=VARCHAR},
      </if>
      <if test="descriptionFileUrl != null">
        description_file_url = #{descriptionFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceFileUrl != null">
        resource_file_url = #{resourceFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="isUpgradeVersion != null">
        is_upgrade_version = #{isUpgradeVersion,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      UPDATE_TIME = now(),
      <if test="upgradeNote != null">
        upgrade_note = #{upgradeNote,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateClientIsUpgradeVersion" parameterType="String">
  <!--@mbg.generated-->
  update tb_cdcmr_client_upgrade_version
  <set>
    is_upgrade_version ='0'
  </set>
  where
   org_code in
  <foreach collection="array" open="(" separator="," close=")" item="item" index="index">
    #{item}
  </foreach>
  and is_delete = '1'
</update>

  <update id="updateClientUpgradeVersionIsDelete" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto">
    <!--@mbg.generated-->
    update tb_cdcmr_client_upgrade_version
    <set>
      is_delete = '0'
    </set>
    where
     org_code in
    <foreach collection="orgCodes" open="(" separator="," close=")" item="orgCode" index="index">
      #{orgCode}
    </foreach>
    <if test="versionCode != null">
      and version_code = #{versionCode,jdbcType=VARCHAR},
    </if>
    and is_delete = '1'
  </update>

  <update id="updateBatchClientUpgradeVersionDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeVersionDto">
    <foreach collection="clientUpgradeVersionDtos" item="item" index="index" open="" close="" separator=";">
      update tb_cdcmr_client_upgrade_version
      <set>
        <if test="item.versionType != null">
          version_type = #{item.versionType,jdbcType=VARCHAR},
        </if>
        <if test="item.versionSubType != null">
          version_sub_type = #{item.versionSubType,jdbcType=VARCHAR},
        </if>

        <if test="item.descriptionFileUrl != null">
          description_file_url = #{item.descriptionFileUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.resourceFileUrl != null">
          resource_file_url = #{item.resourceFileUrl,jdbcType=VARCHAR},
        </if>
        UPDATE_TIME = now(),
        <if test="item.upgradeNote != null">
          upgrade_note = #{item.upgradeNote,jdbcType=VARCHAR},
        </if>
      </set>
      where org_code = #{item.orgCode,jdbcType=VARCHAR} and version_code = #{item.versionCode,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="deleteVersionById" parameterType="java.lang.String">
    update tb_cdcmr_client_upgrade_version
    <set>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      <if test="isDelete != null and isDelete != ''">
        is_delete=#{isDelete},
      </if>
      update_time=now()
    </set>
    where id=#{id}
  </update>


  <select id="getAllClientVersion" resultType="com.iflytek.cdc.admin.entity.ClientUpgradeVersion">
    select a.* from tb_cdcmr_client_upgrade_version a
      inner join tb_cdcmr_client_upgrade_district b on a.id = b.version_id
      where a.is_delete='1' and b.is_delete = '1'
      <if test="param.areaCode != null and param.areaCode != ''">
        and b.area_code = #{param.areaCode}
      </if>
      <if test="param.clientType != null and param.clientType != ''">
        and a.client_type = #{param.clientType}
      </if>
      <if test="param.assembly != null and param.assembly != ''">
        and a.assembly = #{param.assembly}
      </if>
  </select>

  <select id="getParamClientVersion" resultType="com.iflytek.cdc.admin.entity.ClientUpgradeVersion">
    select a.* from tb_cdcmr_client_upgrade_version a
      inner join tb_cdcmr_client_upgrade_district b on a.id = b.version_id
      where a.is_delete='1' and b.is_delete = '1'
      <if test="param.currentVersion != null and param.currentVersion != ''">
        and a.version_code = #{param.currentVersion}
      </if>
      <if test="param.areaCode != null and param.areaCode != ''">
        and b.area_code = #{param.areaCode}
      </if>
      <if test="param.clientType != null and param.clientType != ''">
        and a.client_type = #{param.clientType}
      </if>
      <if test="param.assembly != null and param.assembly != ''">
        and a.assembly = #{param.assembly}
      </if>
    order by a.update_time desc limit 1 offset 0
  </select>

</mapper>