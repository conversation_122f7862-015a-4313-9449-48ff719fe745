<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrSignalPushConfigMessageRelationMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfigMessageRelation">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_signal_push_config_message_relation-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="app_code" jdbcType="VARCHAR" property="appCode" />
    <result column="message_content" jdbcType="VARCHAR" property="messageContent" />
    <result column="message_name" jdbcType="VARCHAR" property="messageName" />
    <result column="message_type" jdbcType="INTEGER" property="messageType" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="receiver_id" jdbcType="VARCHAR" property="receiverId" />
    <result column="receiver_login_name" jdbcType="VARCHAR" property="receiverLoginName" />
    <result column="request_param" jdbcType="VARCHAR" property="requestParam" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="sender_id" jdbcType="VARCHAR" property="senderId" />
    <result column="sender_login_name" jdbcType="VARCHAR" property="senderLoginName" />
    <result column="signal_push_config_id" jdbcType="VARCHAR" property="signalPushConfigId" />
    <result column="source_system_code" jdbcType="VARCHAR" property="sourceSystemCode" />
    <result column="source_system_name" jdbcType="VARCHAR" property="sourceSystemName" />
    <result column="system_relative_path" jdbcType="VARCHAR" property="systemRelativePath" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="message_config_id" jdbcType="VARCHAR" property="messageConfigId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_code, message_content, message_name, message_type, receiver,
    receiver_id, receiver_login_name, request_param, sender, sender_id, sender_login_name, 
    signal_push_config_id, source_system_code, source_system_name, system_relative_path, task_id, create_time, status, message_config_id
  </sql>

  <select id="listBySignalPushConfigurationId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    tb_cdcmr_signal_push_config_message_relation
    WHERE
    signal_push_config_id = #{signalPushConfigId}
  </select>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into tb_cdcmr_signal_push_config_message_relation (
    id, app_code, message_content, message_name, message_type,
    receiver, receiver_id, receiver_login_name, request_param,
    sender, sender_id, sender_login_name,
    signal_push_config_id, source_system_code, source_system_name,
    system_relative_path, task_id, create_time, status, message_config_id
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id},
      #{item.appCode},
      #{item.messageContent},
      #{item.messageName},
      #{item.messageType},
      #{item.receiver},
      #{item.receiverId},
      #{item.receiverLoginName},
      #{item.requestParam},
      #{item.sender},
      #{item.senderId},
      #{item.senderLoginName},
      #{item.signalPushConfigId},
      #{item.sourceSystemCode},
      #{item.sourceSystemName},
      #{item.systemRelativePath},
      #{item.taskId},
      #{item.createTime,jdbcType=TIMESTAMP},
      #{item.status},
      #{item.messageConfigId}
      )
    </foreach>
  </insert>

  <select id="listByTaskAndRecervierId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    tb_cdcmr_signal_push_config_message_relation
    WHERE
    task_id = #{taskId} AND receiver_id = #{receiverId}
  </select>

  <update id="updateStatusOffByReceiverAndTaskIdAndConfig">
    UPDATE tb_cdcmr_signal_push_config_message_relation
    <set>
      status = 0
    </set>
    WHERE 1=1
    AND (
    <foreach collection="dtos" item="dto" separator=" OR ">
      (1=1
      <if test="dto.taskId != null and dto.taskId != ''">
        AND task_id = #{dto.taskId}
      </if>
      <if test="dto.senderId != null and dto.senderId != ''">
        AND receiver_id = #{dto.senderId}
      </if>
      <if test="dto.messageConfigId != null and dto.messageConfigId != ''">
        AND message_config_id = #{dto.messageConfigId}
      </if>
      )
    </foreach>
    )
  </update>
</mapper>