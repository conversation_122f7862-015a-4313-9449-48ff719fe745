<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningRuleItemMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="warning_id" jdbcType="VARCHAR" property="warningId"/>
        <result column="time_range" jdbcType="SMALLINT" property="timeRange"/>
        <result column="time_range_unit" jdbcType="SMALLINT" property="timeRangeUnit"/>
        <result column="monitor_object" jdbcType="VARCHAR" property="monitorObject"/>
        <result column="medical_operate" jdbcType="VARCHAR" property="medicalOperate"/>
        <result column="medical_count" jdbcType="SMALLINT" property="medicalCount"/>
        <result column="medical_attribute" jdbcType="VARCHAR" property="medicalAttribute"/>
        <result column="warn_type" jdbcType="VARCHAR" property="warnType"/>
        <result column="rule_json" jdbcType="OTHER" property="ruleJson"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , warning_id, time_range, time_range_unit, monitor_object, medical_operate, medical_count,
        medical_attribute, warn_type, rule_json, "status", delete_flag, create_time, creator,
        update_time, updater, address_monitor_type,type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_rule_item"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByRuleId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_rule_item"
        where warning_id = #{ruleId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_customized_warning_rule_item"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByRuleId">
        delete
        from "tb_cdcmr_customized_warning_rule_item"
        where warning_id = #{ruleId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        insert into "tb_cdcmr_customized_warning_rule_item" (id, warning_id, time_range,
                                                             time_range_unit, monitor_object, medical_operate,
                                                             medical_count, medical_attribute, warn_type,
                                                             rule_json, "status", delete_flag,
                                                             create_time, creator, update_time,
                                                             updater)
        values (#{id,jdbcType=VARCHAR}, #{warningId,jdbcType=VARCHAR}, #{timeRange,jdbcType=SMALLINT},
                #{timeRangeUnit,jdbcType=SMALLINT}, #{monitorObject,jdbcType=VARCHAR},
                #{medicalOperate,jdbcType=VARCHAR},
                #{medicalCount,jdbcType=SMALLINT}, #{medicalAttribute,jdbcType=VARCHAR}, #{warnType,jdbcType=VARCHAR},
                #{ruleJson,jdbcType=OTHER}, #{status,jdbcType=SMALLINT}, #{deleteFlag,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        insert into "tb_cdcmr_customized_warning_rule_item"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="warningId != null">
                warning_id,
            </if>
            <if test="timeRange != null">
                time_range,
            </if>
            <if test="timeRangeUnit != null">
                time_range_unit,
            </if>
            <if test="monitorObject != null">
                monitor_object,
            </if>
            <if test="medicalOperate != null">
                medical_operate,
            </if>
            <if test="medicalCount != null">
                medical_count,
            </if>
            <if test="medicalAttribute != null">
                medical_attribute,
            </if>
            <if test="warnType != null">
                warn_type,
            </if>
            <if test="ruleJson != null">
                rule_json,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="warningId != null">
                #{warningId,jdbcType=VARCHAR},
            </if>
            <if test="timeRange != null">
                #{timeRange,jdbcType=SMALLINT},
            </if>
            <if test="timeRangeUnit != null">
                #{timeRangeUnit,jdbcType=SMALLINT},
            </if>
            <if test="monitorObject != null">
                #{monitorObject,jdbcType=VARCHAR},
            </if>
            <if test="medicalOperate != null">
                #{medicalOperate,jdbcType=VARCHAR},
            </if>
            <if test="medicalCount != null">
                #{medicalCount,jdbcType=SMALLINT},
            </if>
            <if test="medicalAttribute != null">
                #{medicalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="warnType != null">
                #{warnType,jdbcType=VARCHAR},
            </if>
            <if test="ruleJson != null">
                #{ruleJson,jdbcType=OTHER},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        <foreach collection="recordList" item="record" separator=";">
            insert into "tb_cdcmr_customized_warning_rule_item"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    id,
                </if>
                <if test="record.warningId != null">
                    warning_id,
                </if>
                <if test="record.timeRange != null">
                    time_range,
                </if>
                <if test="record.timeRangeUnit != null">
                    time_range_unit,
                </if>
                <if test="record.monitorObject != null">
                    monitor_object,
                </if>
                <if test="record.medicalOperate != null">
                    medical_operate,
                </if>
                <if test="record.medicalCount != null">
                    medical_count,
                </if>
                <if test="record.medicalAttribute != null">
                    medical_attribute,
                </if>
                <if test="record.warnType != null">
                    warn_type,
                </if>
                <if test="record.ruleJson != null">
                    rule_json,
                </if>
                <if test="record.status != null">
                    "status",
                </if>
                <if test="record.deleteFlag != null">
                    delete_flag,
                </if>
                <if test="record.createTime != null">
                    create_time,
                </if>
                <if test="record.creator != null">
                    creator,
                </if>
                <if test="record.updateTime != null">
                    update_time,
                </if>
                <if test="record.updater != null">
                    updater,
                </if>
                <if test="record.addressMonitorType != null">
                    address_monitor_type,
                </if>
                <if test="record.type != null">
                    type,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    #{record.id,jdbcType=VARCHAR},
                </if>
                <if test="record.warningId != null">
                    #{record.warningId,jdbcType=VARCHAR},
                </if>
                <if test="record.timeRange != null">
                    #{record.timeRange,jdbcType=SMALLINT},
                </if>
                <if test="record.timeRangeUnit != null">
                    #{record.timeRangeUnit,jdbcType=SMALLINT},
                </if>
                <if test="record.monitorObject != null">
                    #{record.monitorObject,jdbcType=VARCHAR},
                </if>
                <if test="record.medicalOperate != null">
                    #{record.medicalOperate,jdbcType=VARCHAR},
                </if>
                <if test="record.medicalCount != null">
                    #{record.medicalCount,jdbcType=SMALLINT},
                </if>
                <if test="record.medicalAttribute != null">
                    #{record.medicalAttribute,jdbcType=VARCHAR},
                </if>
                <if test="record.warnType != null">
                    #{record.warnType,jdbcType=VARCHAR},
                </if>
                <if test="record.ruleJson != null">
                    #{record.ruleJson,jdbcType=OTHER},
                </if>
                <if test="record.status != null">
                    #{record.status,jdbcType=SMALLINT},
                </if>
                <if test="record.deleteFlag != null">
                    #{record.deleteFlag,jdbcType=VARCHAR},
                </if>
                <if test="record.createTime != null">
                    #{record.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="record.creator != null">
                    #{record.creator,jdbcType=VARCHAR},
                </if>
                <if test="record.updateTime != null">
                    #{record.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="record.updater != null">
                    #{record.updater,jdbcType=VARCHAR},
                </if>
                <if test="record.addressMonitorType != null">
                    #{record.addressMonitorType,jdbcType=VARCHAR},
                </if>
                <if test="record.type != null">
                    #{record.type,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        update "tb_cdcmr_customized_warning_rule_item"
        <set>
            <if test="warningId != null">
                warning_id = #{warningId,jdbcType=VARCHAR},
            </if>
            <if test="timeRange != null">
                time_range = #{timeRange,jdbcType=SMALLINT},
            </if>
            <if test="timeRangeUnit != null">
                time_range_unit = #{timeRangeUnit,jdbcType=SMALLINT},
            </if>
            <if test="monitorObject != null">
                monitor_object = #{monitorObject,jdbcType=VARCHAR},
            </if>
            <if test="medicalOperate != null">
                medical_operate = #{medicalOperate,jdbcType=VARCHAR},
            </if>
            <if test="medicalCount != null">
                medical_count = #{medicalCount,jdbcType=SMALLINT},
            </if>
            <if test="medicalAttribute != null">
                medical_attribute = #{medicalAttribute,jdbcType=VARCHAR},
            </if>
            <if test="warnType != null">
                warn_type = #{warnType,jdbcType=VARCHAR},
            </if>
            <if test="ruleJson != null">
                rule_json = #{ruleJson,jdbcType=OTHER},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRuleItem">
        update "tb_cdcmr_customized_warning_rule_item"
        set warning_id        = #{warningId,jdbcType=VARCHAR},
            time_range        = #{timeRange,jdbcType=SMALLINT},
            time_range_unit   = #{timeRangeUnit,jdbcType=SMALLINT},
            monitor_object    = #{monitorObject,jdbcType=VARCHAR},
            medical_operate   = #{medicalOperate,jdbcType=VARCHAR},
            medical_count     = #{medicalCount,jdbcType=SMALLINT},
            medical_attribute = #{medicalAttribute,jdbcType=VARCHAR},
            warn_type         = #{warnType,jdbcType=VARCHAR},
            rule_json         = #{ruleJson,jdbcType=OTHER},
            "status"          = #{status,jdbcType=SMALLINT},
            delete_flag       = #{deleteFlag,jdbcType=VARCHAR},
            create_time       = #{createTime,jdbcType=TIMESTAMP},
            creator           = #{creator,jdbcType=VARCHAR},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            updater           = #{updater,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>