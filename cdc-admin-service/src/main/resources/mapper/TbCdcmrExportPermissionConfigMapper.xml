<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionConfigMapper">
    <select id="queryExportConfigs" resultType="com.iflytek.cdc.admin.dto.ExportConfigDTO">

        SELECT
            c.id as id,
            c.export_name as exportName,
            c.export_type as exportType,
            o.org_id as orgId,
            o.approval_required as approvalRequired,
            o.approval_time_limit as approvalTimeLimit,
            o.approval_levels as approvalLevels,
            string_agg(a.user_name, ',') AS approve,
            o.updater_time as updaterTime,
            o.id as permissionOrgId
        FROM
            tb_cdcmr_export_permission_org o
        JOIN
            tb_cdcmr_export_permission_config c ON c.id = o.export_id
        LEFT JOIN
            tb_cdcmr_org_approve a ON a.permission_org_id = o.id
        WHERE
        o.org_id = #{orgId}

            <if test="exportType != null and exportType != ''">
                AND c.export_type = #{exportType}
            </if>
            <if test="exportName != null and exportName != ''">
                AND c.export_name = #{exportName}
            </if>
            <if test="approvalRequired != null and approvalRequired != ''">
                AND b.approval_required = #{approvalRequired}
            </if>
            <if test="startDate != null and endDate != null">
                AND b.updater_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="moduleType != null and moduleType != ''">
                <choose>
                    <when test="moduleType == 'cdc-disease-report'">
                        AND c.id IN ('1', '2')
                    </when>
                    <when test="moduleType == 'cdc-syndrome-monitor'">
                        AND c.id IN ('3', '4')
                    </when>
                    <when test="moduleType == 'cdc-edr-manager'">
                        AND c.id IN ('5', '6','2','4')
                    </when>
                    <when test="moduleType == 'cdc-situation-warning'">
                        AND c.id IN ('9','10','11','12')
                    </when>
                    <when test="moduleType == 'cdc-syndrome-warning'">
                        AND c.id IN ('9')
                    </when>
                    <when test="moduleType == 'cdc-emergency-manage'">
                        AND c.id IN ('9')
                    </when>
                </choose>
            </if>

        GROUP BY
        c.id, c.export_name, o.org_id, o.approval_required, o.approval_time_limit, o.updater_time, o.id
    </select>
    <update id="updateApprovalRequired">
        UPDATE tb_cdcmr_export_permission_org
        SET approval_required = #{approvalRequired}, updater_time = now()
        WHERE id = #{id}
    </update>
    <select id="selectNotExportConfig" resultType="java.lang.String">
        select
            c.id as export_id
        from
            tb_cdcmr_export_permission_config c
        left join tb_cdcmr_export_permission_org o
        on c.id = o.export_id and o.org_id = #{orgId}
        where
        o.export_id is null
        <if test="moduleType != null and moduleType != ''">
            <choose>
                <when test="moduleType == 'cdc-disease-report'">
                    AND c.id IN ('1', '2')
                </when>
                <when test="moduleType == 'cdc-syndrome-monitor'">
                    AND c.id IN ('3', '4')
                </when>
                <when test="moduleType == 'cdc-edr-manager'">
                    AND c.id IN ('5', '6','2','4','7','8')
                </when>
                <when test="moduleType == 'cdc-situation-warning'">
                    AND c.id IN ('9','10','11','12')
                </when>
                <when test="moduleType == 'cdc-syndrome-warning'">
                    AND c.id IN ('9')
                </when>
                <when test="moduleType == 'cdc-emergency-manage'">
                    AND c.id IN ('9')
                </when>
            </choose>
        </if>
    </select>
    <select id="selectByExportCodeAndOrgId" resultType="com.iflytek.cdc.admin.dto.ExportConfigDTO">
        SELECT
        c.id as id,
        c.export_name as exportName,
        c.export_type as exportType,
        o.org_id as orgId,
        o.approval_required as approvalRequired,
        o.approval_time_limit as approvalTimeLimit,
        o.approval_levels as approvalLevels,
        o.updater_time as updaterTime,
        o.id as permissionOrgId
        FROM
        tb_cdcmr_export_permission_org o
        JOIN
        tb_cdcmr_export_permission_config c ON c.id = o.export_id
        where o.org_id = #{orgId} and c.export_code = #{exportCode}
    </select>
</mapper>