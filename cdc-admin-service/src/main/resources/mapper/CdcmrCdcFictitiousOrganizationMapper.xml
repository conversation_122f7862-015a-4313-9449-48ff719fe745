<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.CdcmrCdcFictitiousOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_cdc_fictitious_organization-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="org_type_code" jdbcType="VARCHAR" property="orgTypeCode" />
    <result column="higher_org" jdbcType="VARCHAR" property="higherOrg" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, province_code, city_code, district_code, org_id, org_name, org_code, org_type, 
    org_type_code, higher_org, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_cdc_fictitious_organization
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_cdc_fictitious_organization
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization">
    <!--@mbg.generated-->
    insert into tb_cdcmr_cdc_fictitious_organization (id, province_code, city_code, 
      district_code, org_id, org_name, 
      org_code, org_type, org_type_code, 
      higher_org, create_user, create_time, 
      update_user, update_time)
    values (#{id,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{districtCode,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{orgCode,jdbcType=VARCHAR}, #{orgType,jdbcType=VARCHAR}, #{orgTypeCode,jdbcType=VARCHAR}, 
      #{higherOrg,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization">
    <!--@mbg.generated-->
    insert into tb_cdcmr_cdc_fictitious_organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="districtCode != null">
        district_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="orgTypeCode != null">
        org_type_code,
      </if>
      <if test="higherOrg != null">
        higher_org,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgTypeCode != null">
        #{orgTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="higherOrg != null">
        #{higherOrg,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization">
    <!--@mbg.generated-->
    update tb_cdcmr_cdc_fictitious_organization
    <set>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        district_code = #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgTypeCode != null">
        org_type_code = #{orgTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="higherOrg != null">
        higher_org = #{higherOrg,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization">
    <!--@mbg.generated-->
    update tb_cdcmr_cdc_fictitious_organization
    set province_code = #{provinceCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      district_code = #{districtCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      org_code = #{orgCode,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=VARCHAR},
      org_type_code = #{orgTypeCode,jdbcType=VARCHAR},
      higher_org = #{higherOrg,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByProperty" parameterType="com.iflytek.cdc.admin.entity.CdcmrCdcFictitiousOrganization" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_cdc_fictitious_organization
    <where>
      <if test="provinceCode != null">
        and province_code = #{provinceCode,jdbcType=VARCHAR}
      </if>
      <if test="cityCode != null">
        and city_code = #{cityCode,jdbcType=VARCHAR}
      </if>
      <if test="districtCode != null">
        and district_code = #{districtCode,jdbcType=VARCHAR}
      </if>
      <if test="orgId != null">
        and org_id = #{orgId,jdbcType=VARCHAR}
      </if>
      <if test="orgName != null">
        and org_name = #{orgName,jdbcType=VARCHAR}
      </if>
      <if test="orgType != null">
        and org_type = #{orgType,jdbcType=VARCHAR}
      </if>
      <if test="orgTypeCode != null">
        and org_type_code = #{orgTypeCode,jdbcType=VARCHAR}
      </if>
      <if test="higherOrg != null">
        and higher_org = #{higherOrg,jdbcType=VARCHAR}
      </if>
      <if test="createUser != null">
        and create_user = #{createUser,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateUser != null">
        and update_user = #{updateUser,jdbcType=VARCHAR}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>
</mapper>