<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.ClientUpgradeDistrictMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.ClientUpgradeDistrict">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_client_upgrade_district-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="version_id" jdbcType="VARCHAR" property="versionId" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, version_id, area_code, area_name, org_code, org_name, create_user, create_time, 
    update_user, update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_client_upgrade_district
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_client_upgrade_district
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="addBatchClientUpgradeDistrictDto" parameterType="com.iflytek.cdc.admin.entity.ClientUpgradeDistrict">
    <!--@mbg.generated-->
    insert into tb_cdcmr_client_upgrade_district (id, version_id, area_code, 
      area_name, org_code, org_name, 
      create_user, create_time, update_user, 
      update_time, is_delete)
    values
    <foreach collection="clientUpgradeDistrictDtos" item="clientUpgradeDistrictDto" separator=",">
    (#{clientUpgradeDistrictDto.id,jdbcType=VARCHAR}, #{clientUpgradeDistrictDto.versionId,jdbcType=VARCHAR}, #{clientUpgradeDistrictDto.areaCode,jdbcType=VARCHAR},
      #{clientUpgradeDistrictDto.areaName,jdbcType=VARCHAR}, #{clientUpgradeDistrictDto.orgCode,jdbcType=VARCHAR}, #{clientUpgradeDistrictDto.orgName,jdbcType=VARCHAR},
      #{clientUpgradeDistrictDto.createUser,jdbcType=VARCHAR}, now(), #{clientUpgradeDistrictDto.updateUser,jdbcType=VARCHAR},
       now(), #{clientUpgradeDistrictDto.isDelete,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.ClientUpgradeDistrict">
    <!--@mbg.generated-->
    insert into tb_cdcmr_client_upgrade_district
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="versionId != null">
        version_id,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null">
        #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.ClientUpgradeDistrict">
    <!--@mbg.generated-->
    update tb_cdcmr_client_upgrade_district
    <set>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.ClientUpgradeDistrict">
    <!--@mbg.generated-->
    update tb_cdcmr_client_upgrade_district
    set version_id = #{versionId,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      area_name = #{areaName,jdbcType=VARCHAR},
      org_code = #{orgCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <update id="deleteOrgByCode" parameterType="com.iflytek.cdc.admin.dto.UpdateDistrictDto">
    update tb_cdcmr_client_upgrade_district
    <set>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      <if test="isDelete != null and isDelete != ''">
        is_delete=#{isDelete},
      </if>
      update_time=now()
    </set>
    where version_id=#{versionId}
    <if test="orgCodes != null ">
      and  org_code  in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
  </update>

  <select id="getOrgCodeById" parameterType="com.iflytek.cdc.admin.dto.UpdateDistrictDto" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto">
    <!--@mbg.generated-->
    select  org_code
    from tb_cdcmr_client_upgrade_district
    where is_delete ='1' and version_id = #{versionId,jdbcType=VARCHAR}
    <if test="orgCodes != null ">
      and  org_code  in
      <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
  </select>
</mapper>