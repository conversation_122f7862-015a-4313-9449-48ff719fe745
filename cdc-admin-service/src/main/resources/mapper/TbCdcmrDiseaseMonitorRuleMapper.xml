<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrDiseaseMonitorRuleMapper">

    <sql id="Base_Column_List">
        id, config_type, disease_name, disease_code, description,
        remark, abnormal_days, abnormal_mulriple, type,
        abnormal_num, status, delete_flag, create_datetime,
        update_datetime, creator, updator
    </sql>

    <update id="deleteByLogic">
        update tb_cdcmr_disease_monitor_rule
        set delete_flag = 1, status = 0,
        updator = #{loginUserId, jdbcType=VARCHAR},
        update_datetime = #{timeNow}
        where id = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getMonitorConfigMsg" resultType="com.iflytek.cdc.admin.entity.TbCdcmrDiseaseMonitorRule">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_disease_monitor_rule"
        where status = 1 and delete_flag = 0
        and config_type = #{configType, jdbcType=VARCHAR}
        <if test="diseaseCode != null and diseaseCode!=''">
            and disease_code = #{diseaseCode, jdbcType=VARCHAR}
        </if>
        <if test="diseaseName != null and diseaseName!=''">
            and disease_name like concat('%',#{diseaseName,jdbcType=VARCHAR},'%')
        </if>
        order by config_type ,disease_code
    </select>
</mapper>