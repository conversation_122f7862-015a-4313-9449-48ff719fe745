<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizeDistrictMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict">
    <id column="name" jdbcType="VARCHAR" property="name" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="parent_name" jdbcType="VARCHAR" property="parentName" />
    <result column="json_data" jdbcType="VARCHAR" property="jsonData" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    "name", district_code, parent_code, parent_name, json_data, is_deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "tb_cdcmr_customize_district"
    where  district_code = #{districtCode,jdbcType=VARCHAR} and is_deleted = 0
  </select>

  <select id="selectList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict">
    select *   from "tb_cdcmr_customize_district"
    where  is_deleted = 0
  </select>
  <select id="selectListWithoutData" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict">
    select name,district_code,parent_code,parent_name,is_deleted   from "tb_cdcmr_customize_district"
    where  is_deleted = 0
  </select>
  <delete id="deleteCustomizeDistrict" >
    update "tb_cdcmr_customize_district" set is_deleted = 1
    where
      district_code = #{districtCode,jdbcType=VARCHAR}
  </delete>
  
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict">
      INSERT INTO tb_cdcmr_customize_district (district_code,name, parent_code, parent_name, json_data, is_deleted)
      VALUES (#{districtCode,jdbcType=VARCHAR},
              #{name,jdbcType=VARCHAR},
              #{parentCode,jdbcType=VARCHAR},
              #{parentName,jdbcType=VARCHAR},
              #{jsonData,jdbcType=VARCHAR},
              #{isDeleted,jdbcType=SMALLINT}) ON CONFLICT (district_code) DO
      UPDATE SET
          name = EXCLUDED.name,
          parent_code = EXCLUDED.parent_code,
          parent_name = EXCLUDED.parent_name,
          json_data = EXCLUDED.json_data,
          is_deleted = EXCLUDED.is_deleted
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict">
      update "tb_cdcmr_customize_district"
      <set>
          <if test="districtCode != null">
              district_code = #{districtCode,jdbcType=VARCHAR},
          </if>
          <if test="parentCode != null">
              parent_code = #{parentCode,jdbcType=VARCHAR},
          </if>
          <if test="parentName != null">
              parent_name = #{parentName,jdbcType=VARCHAR},
          </if>
          <if test="name != null">
              name = #{name,jdbcType=VARCHAR},
          </if>
          <if test="jsonData != null">
              json_data = #{jsonData,jdbcType=VARCHAR},
          </if>
          <if test="isDeleted != null">
              is_deleted = #{isDeleted,jdbcType=SMALLINT},
          </if>
      </set>
      where district_code = #{districtCode,jdbcType=VARCHAR}
  </update>

</mapper>