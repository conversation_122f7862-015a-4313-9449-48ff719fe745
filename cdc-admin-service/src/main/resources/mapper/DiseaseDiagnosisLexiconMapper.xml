<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.DiseaseDiagnosisLexiconMapper">
    <select id="getDiagnosisLexiconPage" resultType="com.iflytek.cdc.admin.entity.DiseaseDiagnosisLexicon" parameterType="com.iflytek.cdc.admin.dto.SearchDiagnosisLexiconDTO">
        SELECT *
        FROM "tb_cdcmr_disease_diagnosis_lexicon"
        <where>
            is_delete = '0'
            <if test="dto.diagnosisName != null and dto.diagnosisName != ''">
                AND diagnosis_name LIKE CONCAT('%', #{dto.diagnosisName}, '%')
            </if>
            <if test="dto.diagnosisSource != null and dto.diagnosisSource != ''">
                AND diagnosis_source = #{dto.diagnosisSource}
            </if>
            <if test="dto.businessType != null and dto.businessType != ''">
                AND business_type = #{dto.businessType}
            </if>
            <if test="dto.diagnosisCode != null and dto.diagnosisCode != ''">
                AND diagnosis_code LIKE CONCAT('%', #{dto.diagnosisCode}, '%')
            </if>
        </where>
        ORDER BY diagnosis_code
    </select>
</mapper>