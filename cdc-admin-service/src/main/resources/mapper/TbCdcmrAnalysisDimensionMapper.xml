<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimensionMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimension">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_analysis_dimension-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="dim_code" jdbcType="VARCHAR" property="dimCode" />
    <result column="dimension_name" jdbcType="VARCHAR" property="dimensionName" />
    <result column="data_attr_id" jdbcType="VARCHAR" property="dataAttrId" />
    <result column="dim_definition" jdbcType="VARCHAR" property="dimDefinition" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="seq_num" jdbcType="INTEGER" property="seqNum" />
    <result column="analysis_target" jdbcType="VARCHAR" property="analysisTargetStr" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dim_code, dimension_name, data_attr_id, dim_definition, notes, "status", delete_flag, 
    create_time, update_time, creator_id, creator, updater_id, updater, seq_num
  </sql>

  <delete id="softDelete">
    update tb_cdcmr_analysis_dimension
    set delete_flag = '1'
    where id = #{id,jdbcType=VARCHAR}
    </delete>

  <select id="pageList" resultType="com.iflytek.cdc.admin.vo.AnalysisDimensionVO">
      SELECT d.*, a.attr_count as dimLevelCount
      FROM tb_cdcmr_analysis_dimension d
               join tb_cdcdm_data_dict a
                    on d.data_attr_id = a.id
      where d.delete_flag = '0'
      <if test="id != null and id != ''">
          and d.id = #{id,jdbcType=VARCHAR}
      </if>
      <if test="analysisDimCode != null and analysisDimCode != ''">
          and d.dim_code = #{analysisDimCode}
      </if>
      <if test="analysisDimName != null and analysisDimName != ''">
          and d.dimension_name = #{analysisDimName}
      </if>
      <if test='analysisDimType == "0"'>
          and a.attr_count = 1
      </if>
      <if test='analysisDimType == "1"'>
          and a.attr_count > 1
      </if>
      <if test="levelCount != null">
          and a.attr_count = #{levelCount,jdbcType=INTEGER}
      </if>
      <if test="null != keyword and '' != keyword">
          and (d.dim_code ilike concat('%',#{keyword},'%') or d.dimension_name ilike concat('%',#{keyword},'%'))
      </if>
      <if test="null != analysisTargetCodes and '' != analysisTargetCodes">
          and EXISTS (
          SELECT 1
          FROM json_array_elements(analysis_target::json) AS elem
          WHERE elem->>'code' = ANY(string_to_array(#{analysisTargetCodes}, ','))
          )
      </if>
      order by d.update_time desc, d.create_time desc
  </select>

  <select id="selectVoById" resultType="com.iflytek.cdc.admin.vo.AnalysisDimensionVO">
      SELECT d.*, a.attr_count as dimLevelCount
      FROM tb_cdcmr_analysis_dimension d
               join tb_cdcdm_data_dict a
                    on d.data_attr_id = a.id
      where d.delete_flag = '0'
        and d.id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="loadLastNum" resultType="java.lang.Integer">
      SELECT max(seq_num)
      FROM tb_cdcmr_analysis_dimension
      where delete_flag = '0'
  </select>
</mapper>
