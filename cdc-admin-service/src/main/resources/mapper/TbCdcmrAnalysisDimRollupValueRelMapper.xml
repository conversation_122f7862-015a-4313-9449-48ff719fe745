<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimRollupValueRelMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimRollupValueRel">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_analysis_dim_rollup_value_rel-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="analysis_dim_id" jdbcType="VARCHAR" property="analysisDimId" />
    <result column="dim_attr_value_id" jdbcType="VARCHAR" property="dimAttrValueId" />
    <result column="rollup_dim_id" jdbcType="VARCHAR" property="rollupDimId" />
    <result column="rollup_attr_value_id" jdbcType="VARCHAR" property="rollupAttrValueId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, analysis_dim_id, dim_attr_value_id, rollup_dim_id, rollup_attr_value_id, delete_flag, 
    create_time, update_time
  </sql>
</mapper>