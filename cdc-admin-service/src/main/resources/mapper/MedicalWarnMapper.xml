<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.MedicalWarnMapper">


    <sql id="all_columns">
        id
        , disease_code, disease_name, rule_desc, rule_status, create_datetime, creator,
    update_datetime, updator, delete_flag, event_generation_node, silence_duration, silence_duration_unit,
    ai_removed_state, ai_removed_duration, ai_removed_duration_unit, is_single_case,    sms_send_type_code, sms_send_type_desc,
        report_logic_setting_json,medical_logic_setting_json

    </sql>

    <select id="selectAll" parameterType="java.lang.String"
            resultType="com.iflytek.cdc.admin.entity.MedicalWarn">
        select
        <include refid="all_columns"/>
        from "tb_cdcmr_medical_warn"
        where delete_flag = '0' and rule_status = '1'
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultType="com.iflytek.cdc.admin.entity.MedicalWarn">
        select
        <include refid="all_columns"/>
        from "tb_cdcmr_medical_warn"
        where id = #{id}
    </select>

    <update id="modify">
        update tb_cdcmr_medical_warn
        <set>
            <if test="eventGenerationNode != null">
                event_generation_node= #{eventGenerationNode,jdbcType=VARCHAR},
            </if>
            <if test="silenceDuration != null ">
                silence_duration=#{silenceDuration},
            </if>
            <if test="silenceDurationUnit != null">
                silence_duration_unit= #{silenceDurationUnit,jdbcType=VARCHAR},
            </if>
            <if test="aiRemovedState != null ">
                ai_removed_state=#{aiRemovedState},
            </if>
            <if test="aiRemovedDuration != null ">
                ai_removed_duration= #{aiRemovedDuration},
            </if>
            <if test="aiRemovedDurationUnit != null ">
                ai_removed_duration_unit=#{aiRemovedDurationUnit},
            </if>
            <if test="isSingleCase != null ">
                is_single_case=#{isSingleCase},
            </if>
            <if test="loginUserId != null and loginUserId != '' ">
                updator=#{loginUserId},
            </if>
            <if test="ruleDesc != null ">
                rule_desc = #{ruleDesc},
            </if>
            <if test="ruleStatus != null and ruleStatus != '' ">
                rule_status = #{ruleStatus},
            </if>
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="reportLogicSettingJson != null">
                report_logic_setting_json = #{reportLogicSettingJson,jdbcType=VARCHAR},
            </if>
            <if test="medicalLogicSettingJson != null">
                medical_logic_setting_json = #{medicalLogicSettingJson,jdbcType=VARCHAR},
            </if>
            update_datetime=now()
        </set>
        where id = #{id}
    </update>

    <select id="pageList" resultType="com.iflytek.cdc.admin.dto.MedicalWarnPageListResponseDto">
        select a.id,a.disease_code,a.disease_name,a.rule_desc,a.rule_status as status,c.diseases_type as
        diseaseTypeCode,'1' as businessType,a.is_single_case as isSingleCase,
        (select count(b.id) from tb_cdcmr_medical_warn_rule b where a.id = b.rule_id and
        b.delete_flag = '0') as ruleCount
        from tb_cdcmr_medical_warn a left join tb_cdcmr_infectious_diseases c on a.disease_code = c.diseases_code
        where 1=1
        AND A.disease_code IN ( SELECT diseases_code FROM tb_cdcmr_infectious_diseases where COALESCE (
        diseases_classify, '' ) = '' )
        <if test="param.diseaseType != null and param.diseaseType != ''">
            and c.diseases_type = #{param.diseaseType}
        </if>
        <if test="param.diseaseCode != null and param.diseaseCode != ''">
            and (a.disease_code = #{param.diseaseCode}
            or a.disease_code in (select diseases_classify from tb_cdcmr_infectious_diseases where diseases_code =
            #{param.diseaseCode}))
        </if>

        <if test="param.status != null and param.status != ''">
            and (a.rule_status = #{param.status}
            or a.disease_code in (SELECT diseases_classify FROM tb_cdcmr_infectious_diseases WHERE diseases_code IN (
            SELECT disease_code FROM tb_cdcmr_medical_warn WHERE rule_status = #{param.status} )))
        </if>
        and a.delete_flag = '0'
        order by a.disease_code asc
    </select>

    <update id="updateCodeName" parameterType="java.lang.String">
        update tb_cdcmr_medical_warn
        <set>
            <if test="updateCodeName != null and updateCodeName != ''">
                disease_name=#{updateCodeName,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                updator = #{updateUser,jdbcType=VARCHAR},
            </if>
            update_datetime =now()
        </set>
        where disease_code=#{updateCode,jdbcType=VARCHAR}
    </update>

    <insert id="insertMedicalWarns" parameterType="com.iflytek.cdc.admin.entity.MedicalWarn">
        insert into tb_cdcmr_medical_warn (id, disease_code, disease_name,
        create_datetime,
        creator, update_datetime, updator,
        delete_flag,rule_status)
        values
        <foreach collection="medicalWarns" item="sn" separator="," index="index">
            (#{sn.id,jdbcType=VARCHAR}, #{sn.diseaseCode,jdbcType=VARCHAR}, #{sn.diseaseName,jdbcType=VARCHAR},

            now(), #{sn.creator,jdbcType=VARCHAR},
            now(), #{sn.updator,jdbcType=VARCHAR},#{sn.deleteFlag,jdbcType=VARCHAR},#{sn.ruleStatus,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="updateInfecInfo" parameterType="com.iflytek.cdc.admin.dto.UpdateInfecDTO">
        <!--@mbg.generated-->
        update tb_cdcmr_medical_warn
        <set>
            <if test="isEnable != null and isEnable != ''">
                rule_status= #{isEnable,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                updator=#{updateUser},
            </if>
            update_datetime=now()
        </set>
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
    </update>
    <update id="editMedicalWarn" parameterType="com.iflytek.cdc.admin.dto.MedicalWarnEditDto">
        update tb_cdcmr_medical_warn
        <set>
            <if test="eventGenerationNode != null">
                event_generation_node= #{eventGenerationNode,jdbcType=VARCHAR},
            </if>
            <if test="silenceDuration != null ">
                silence_duration=#{silenceDuration},
            </if>
            <if test="silenceDurationUnit != null">
                silence_duration_unit= #{silenceDurationUnit,jdbcType=VARCHAR},
            </if>
            <if test="aiRemovedState != null ">
                ai_removed_state=#{aiRemovedState},
            </if>
            <if test="aiRemovedDuration != null ">
                ai_removed_duration= #{aiRemovedDuration},
            </if>
            <if test="aiRemovedDurationUnit != null ">
                ai_removed_duration_unit=#{aiRemovedDurationUnit},
            </if>
            <if test="isSingleCase != null ">
                is_single_case=#{isSingleCase},
            </if>
            <if test="updator != null and updator != '' ">
                updator=#{updator},
            </if>
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            update_datetime=now()
        </set>
        where id = #{id}
    </update>


    <select id="queryInfecInfo" parameterType="com.iflytek.cdc.admin.dto.SearchInfecInfoDTO"
            resultType="com.iflytek.cdc.admin.entity.MedicalWarn">
        <!--@mbg.generated-->
        select
        id as id , disease_code as diseaseCode, disease_name as diseaseName ,rule_desc as rueDesc, rule_status as
        ruleStatus,
        creator as creator, create_datetime as createDatetime, updator as updator, update_datetime as updateDatetime
        from tb_cdcmr_medical_warn
        where 1=1
        order by disease_code
    </select>

    <select id="getAllChooseButtonData" resultType="com.iflytek.cdc.admin.dto.WarnRuleExportDataDto">
        select a.disease_code,a.disease_name,a.rule_desc,a.rule_status as status,
        (select count(b.id) from tb_cdcmr_medical_warn_rule b where a.id = b.rule_id and b.region_id = #{areaCode} and
        b.delete_flag = '0') as ruleCount,c.diseases_type as diseaseTypeCode,COALESCE ( diseases_classify, '' ) = '' as
        businessType
        from tb_cdcmr_medical_warn a
        left join tb_cdcmr_infectious_diseases c on a.disease_code = c.diseases_code
        where 1=1
        <if test="param.diseaseCode != null and param.diseaseCode != ''">
            and a.disease_code = #{param.diseaseCode}
        </if>

        <if test="param.status != null and param.status != ''">
            and a.rule_status = #{param.status}
        </if>
        and a.delete_flag = '0'
        order by a.disease_code asc
    </select>

    <select id="queryChooseExportData" resultType="com.iflytek.cdc.admin.dto.WarnRuleExportDataDto">
        SELECT
        tcmwr.disease_name as diseaseName,
        tcid.diseases_type as diseaseTypeCode,
        COALESCE (tcid.diseases_classify, '' ) = '' as businessType,
        COALESCE (tcmw.is_single_case,'0') as isSingleCase,
        tcmw.event_generation_node  as eventGenerationNode,
        tcmw.silence_Duration as silenceDuration,
        tcmw.ai_Removed_Duration as aiRemovedDuration,
        tcmwr.monitor_object as monitorObject,
        tcmwr.time_scope as timeScope,
        tcmwr.time_unit as timeScopeUnit,
        tcmwr.medical_count as medicalCount,
        tcmwr.type as type,
        tcmwr.medical_Attribute as medicalAttribute,
        tcmwr.warn_Type as warnType
        FROM tb_cdcmr_medical_warn_rule  tcmwr
        LEFT JOIN tb_cdcmr_medical_warn  tcmw ON tcmwr.rule_id = tcmw.ID
        left join tb_cdcmr_infectious_diseases  tcid on tcmwr.disease_code = tcid.diseases_code
        WHERE tcmwr.region_id = #{areaCode}
        AND tcmwr.delete_flag = '0'
        and tcmw.delete_flag = '0'
        AND tcmw.rule_status = '1'
    </select>
    <select id="subPageList" resultType="com.iflytek.cdc.admin.dto.MedicalWarnPageListResponseDto">
        select a.id,a.disease_code,a.disease_name,a.rule_desc,a.rule_status as status,c.diseases_classify as
        diseaseParentCode,c.diseases_type as diseaseTypeCode,'2' as businessType,a.is_single_case as isSingleCase,
        (select count(b.id) from tb_cdcmr_medical_warn_rule b where a.id = b.rule_id and
        b.delete_flag = '0') as ruleCount
        from tb_cdcmr_medical_warn a left join tb_cdcmr_infectious_diseases c on a.disease_code = c.diseases_code
        where 1=1
        and a.disease_code not in (SELECT COALESCE ( diseases_classify, '' ) FROM tb_cdcmr_infectious_diseases )
        <if test="param.diseaseType != null and param.diseaseType != ''">
            and c.diseases_type = #{param.diseaseType}
        </if>
        <if test="param.diseaseCode != null and param.diseaseCode != ''">
            and a.disease_code = #{param.diseaseCode}
            or a.disease_code in (select diseases_code from tb_cdcmr_infectious_diseases where diseases_classify =
            #{param.diseaseCode})
        </if>

        <if test="param.status != null and param.status != ''">
            and a.rule_status = #{param.status}
        </if>
        and a.delete_flag = '0'
    </select>

</mapper>