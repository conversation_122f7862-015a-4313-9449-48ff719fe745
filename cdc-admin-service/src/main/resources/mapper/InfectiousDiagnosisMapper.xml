<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.InfectiousDiagnosisMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_infectious_diagnosis-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="diagnosis_code" jdbcType="VARCHAR" property="diagnosisCode" />
    <result column="diagnosis_name" jdbcType="VARCHAR" property="diagnosisName" />
    <result column="diagnosis_type" jdbcType="VARCHAR" property="diagnosisType" />
    <result column="diagnosis_type_name" jdbcType="VARCHAR" property="diagnosisTypeName" />
    <result column="diagnosis_classify" jdbcType="VARCHAR" property="diagnosisClassify" />
    <result column="diagnosis_classify_name" jdbcType="VARCHAR" property="diagnosisClassifyName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_enable" jdbcType="VARCHAR" property="isEnable" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, diagnosis_code, diagnosis_name, diagnosis_type, diagnosis_type_name, diagnosis_classify, 
    diagnosis_classify_name, remark, is_enable, create_user, create_time, update_user, 
    update_time, sync_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diagnosis
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_infectious_diagnosis
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    insert into tb_cdcmr_infectious_diagnosis (id, diagnosis_code, diagnosis_name, 
      diagnosis_type, diagnosis_type_name, diagnosis_classify, 
      diagnosis_classify_name, remark, is_enable, 
      create_user, create_time, update_user, 
      update_time, sync_time, is_delete
      )
    values (#{id,jdbcType=VARCHAR}, #{diagnosisCode,jdbcType=VARCHAR}, #{diagnosisName,jdbcType=VARCHAR}, 
      #{diagnosisType,jdbcType=VARCHAR}, #{diagnosisTypeName,jdbcType=VARCHAR}, #{diagnosisClassify,jdbcType=VARCHAR}, 
      #{diagnosisClassifyName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{syncTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    insert into tb_cdcmr_infectious_diagnosis
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="diagnosisCode != null">
        diagnosis_code,
      </if>
      <if test="diagnosisName != null">
        diagnosis_name,
      </if>
      <if test="diagnosisType != null">
        diagnosis_type,
      </if>
      <if test="diagnosisTypeName != null">
        diagnosis_type_name,
      </if>
      <if test="diagnosisClassify != null">
        diagnosis_classify,
      </if>
      <if test="diagnosisClassifyName != null">
        diagnosis_classify_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="syncTime != null">
        sync_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisType != null">
        #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisTypeName != null">
        #{diagnosisTypeName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassify != null">
        #{diagnosisClassify,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassifyName != null">
        #{diagnosisClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diagnosis
    <set>
      <if test="diagnosisCode != null">
        diagnosis_code = #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        diagnosis_name = #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisType != null">
        diagnosis_type = #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisTypeName != null">
        diagnosis_type_name = #{diagnosisTypeName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassify != null">
        diagnosis_classify = #{diagnosisClassify,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassifyName != null">
        diagnosis_classify_name = #{diagnosisClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncTime != null">
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diagnosis
    set diagnosis_code = #{diagnosisCode,jdbcType=VARCHAR},
      diagnosis_name = #{diagnosisName,jdbcType=VARCHAR},
      diagnosis_type = #{diagnosisType,jdbcType=VARCHAR},
      diagnosis_type_name = #{diagnosisTypeName,jdbcType=VARCHAR},
      diagnosis_classify = #{diagnosisClassify,jdbcType=VARCHAR},
      diagnosis_classify_name = #{diagnosisClassifyName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      sync_time = #{syncTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryInfecInfo" parameterType="com.iflytek.cdc.admin.dto.SearchInfecInfoDTO"
          resultType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    select
    id as id , diagnosis_code as diagnosisCode, diagnosis_name as diagnosisName , diagnosis_type as diagnosisType, diagnosis_type_name as diagnosisTypeName,
    diagnosis_classify as diagnosisClassify,diagnosis_classify_name as diagnosisClassifyName, remark as remark , is_enable as isEnable,
    create_user as createUser, create_time as createTime, update_user as updateUser, update_time  as updateTime
    from tb_cdcmr_infectious_diagnosis
    where 1=1
    <if test="diagnosisName != null and diagnosisName !='' ">
      and diagnosis_name like  concat('%',#{diagnosisName,jdbcType=VARCHAR},'%')
    </if>
    <if test="diagnosisType != null and diagnosisType !='' ">
      and diagnosis_type = #{diagnosisType,jdbcType=VARCHAR}
    </if>
    <if test="diagnosisClassify != null and diagnosisClassify !='' ">
      and diagnosis_classify = #{diagnosisClassify,jdbcType=VARCHAR}
    </if>
    order by diagnosis_code
  </select>

  <insert id="insertInfectiousDiagnosis" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    insert into tb_cdcmr_infectious_diagnosis (id, diagnosis_code, diagnosis_name,
    diagnosis_type, diagnosis_type_name, diagnosis_classify,
    diagnosis_classify_name, remark, is_enable,
    create_user, create_time, update_user,
    update_time, sync_time)
    values
    <foreach collection="infectiousDiagnosis" item="sn" separator="," index="index">
      (#{sn.id,jdbcType=VARCHAR}, #{sn.diagnosisCode,jdbcType=VARCHAR}, #{sn.diagnosisName,jdbcType=VARCHAR},
      #{sn.diagnosisType,jdbcType=VARCHAR}, #{sn.diagnosisTypeName,jdbcType=VARCHAR},
      #{sn.diagnosisClassify,jdbcType=VARCHAR}, #{sn.diagnosisClassifyName,jdbcType=VARCHAR},#{sn.remark,jdbcType=VARCHAR},
      #{sn.isEnable,jdbcType=VARCHAR}, #{sn.createUser,jdbcType=VARCHAR},
      now(), #{sn.updateUser,jdbcType=VARCHAR}, now(),now())
    </foreach>
  </insert>


  <update id="updateInfecInfo" parameterType="com.iflytek.cdc.admin.dto.UpdateInfecDTO">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diagnosis
    <set>
      <if test="isEnable != null and isEnable != ''">
        is_enable= #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      update_time=now(),
      sync_time=now()
    </set>
    where id in
    <foreach collection="ids" open="(" separator="," close=")" item="item" index="index">
      #{item}
    </foreach>
  </update>

  <update id="updateinfectiousDiagnosis" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiagnosis">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diagnosis
    <set>

      <if test="diagnosisType != null">
        diagnosis_type = #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisTypeName != null">
        diagnosis_type_name = #{diagnosisTypeName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassify != null">
        diagnosis_classify = #{diagnosisClassify,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClassifyName != null">
        diagnosis_classify_name = #{diagnosisClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      UPDATE_TIME = now()
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryInfoById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diagnosis
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <update id="updateCodeName" parameterType="java.lang.String">
    update tb_cdcmr_infectious_diagnosis
    <set>
      <if test="updateCodeName != null and updateCodeName != ''">
        diagnosis_name=#{updateCodeName,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      update_time =now()
    </set>
    where diagnosis_code=#{updateCode,jdbcType=VARCHAR}
  </update>
</mapper>