<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonDiseaseMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <insert id="insert">
      insert into "tb_cdcmr_unknown_reason_disease" (id, disease_code, disease_name, "status",
                                                     remark, create_user, create_time,
                                                     update_user, update_time, is_deleted)
      values (#{id,jdbcType=VARCHAR}, #{diseaseCode,jdbcType=VARCHAR}, #{diseaseName,jdbcType=VARCHAR},
              #{status,jdbcType=INTEGER},
              #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
              #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER})
  </insert>
    <update id="updateByPrimaryKeySelective">
        update "tb_cdcmr_unknown_reason_disease"
        <set>
            <if test="diseaseCode != null">
                disease_code = #{diseaseCode,jdbcType=VARCHAR},
            </if>
            <if test="diseaseName != null">
                disease_name = #{diseaseName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByPrimaryKey">
        update  "tb_cdcmr_unknown_reason_disease" set is_deleted = 1 where id = #{id,jdbcType=VARCHAR}
    </delete>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
      select * from tb_cdcmr_unknown_reason_disease
      where is_deleted = 0
      <if test="diseaseName!= null and diseaseName !=''">
        and disease_name like concat('%',#{diseaseName},'%')
      </if>
      <if test="status != null">
        and "status" = #{status,jdbcType=SMALLINT}
      </if>
      order by id
    </select>
    <select id="getByDiseaseCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
      select * from "tb_cdcmr_unknown_reason_disease"  where is_deleted = 0 and disease_code = #{diseaseCode,jdbcType=VARCHAR}
    </select>
  <select id="getByDiseaseName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
    select *
    from "tb_cdcmr_unknown_reason_disease"
    where is_deleted = 0
      and disease_name = #{diseaseName,jdbcType=VARCHAR}
  </select>
    <select id="selectByPrimaryKey" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
        select *
        from "tb_cdcmr_unknown_reason_disease"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="findAll" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease">
        select *
        from "tb_cdcmr_unknown_reason_disease"
        where is_deleted = 0
    </select>
    <select id="queryUnknownReasonInfoByGrade" resultType="com.iflytek.cdc.admin.dto.UnknownReasonGradeConfigVO">
        SELECT
        t2.remark AS remark,
        COALESCE ( t2.status, 0 ) AS status ,t1.* ,
        (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
        t3.is_deleted = 0)as ruleCount from tb_cdcmr_unknown_reason_disease t1 LEFT JOIN
        tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'unknownReason'
        AND t1.disease_code= t2.disease_code
        where t1.is_deleted = 0
        <if test="diseaseName!= null and diseaseName !=''">
            and t1.disease_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="status != null">
            and COALESCE ( t2.status, 0 ) = #{status,jdbcType=SMALLINT}
        </if>
        order by id
    </select>
</mapper>