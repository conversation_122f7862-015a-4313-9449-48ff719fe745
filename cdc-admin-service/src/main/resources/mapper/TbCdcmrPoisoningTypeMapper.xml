<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningTypeMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="poisoning_type_code" jdbcType="VARCHAR" property="poisoningTypeCode" />
    <result column="poisoning_type_name" jdbcType="VARCHAR" property="poisoningTypeName" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, poisoning_type_code, poisoning_type_name, is_deleted, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_poisoning_type"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    select     <include refid="Base_Column_List" />
    from "tb_cdcmr_poisoning_type"
    where is_deleted = 0
    order by id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_poisoning_type"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    insert into "tb_cdcmr_poisoning_type" (id, poisoning_type_code, poisoning_type_name, 
      is_deleted, update_time, updater
      )
    values (#{id,jdbcType=VARCHAR}, #{poisoningTypeCode,jdbcType=VARCHAR}, #{poisoningTypeName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=SMALLINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    insert into "tb_cdcmr_poisoning_type"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="poisoningTypeCode != null">
        poisoning_type_code,
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeCode != null">
        #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    update "tb_cdcmr_poisoning_type"
    <set>
      <if test="poisoningTypeCode != null">
        poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningType">
    update "tb_cdcmr_poisoning_type"
    set poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
      poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>