<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOrgApproveMapper">
    <insert id="saveOrgApprove">
        insert into tb_cdcmr_org_approve (id,permission_org_id,user_id, user_name,level,creator_id,creator_time,creator)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.permissionOrgId,jdbcType=VARCHAR},#{item.userId,jdbcType=VARCHAR},
            #{item.userName},#{item.level},#{item.creatorId},#{item.creatorTime},#{item.creator})
        </foreach>
    </insert>

    <select id="selectApproveByPermissionOrgId" resultType="com.iflytek.cdc.admin.dto.ApproverDTO">

        select
            user_id as id ,
            user_name as name,
            "level" as level
        from
            tb_cdcmr_org_approve
        where
            permission_org_id = #{permissionOrgId}
    </select>
</mapper>