<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.ScreenLayoutConfigMapper">

    <sql id="Base_Column_List">
        id, config_type, config_key, config_value, config_key_dec, create_time, update_time,
        creator, creator_id, updater, updater_id
    </sql>

    <insert id="insert">
        insert into "tb_cdcmr_screen_config" (id, config_type, config_key, config_value,
                                              config_key_dec, create_time, update_time,
                                              creator, creator_id, updater,
                                              updater_id)
        values (#{id,jdbcType=VARCHAR}, #{configType,jdbcType=SMALLINT}, #{configKey,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR},
                #{configKeyDec,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{creator,jdbcType=VARCHAR}, #{creatorId,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR},
                #{updaterId,jdbcType=VARCHAR})
    </insert>

    <select id="getScreenLayoutConfig" resultType="com.iflytek.cdc.admin.entity.TbCdcmrScreenConfig">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_screen_config
        where config_type = 0
        order by update_time desc
        limit 1
    </select>
    <select id="getDesensitization" resultType="com.iflytek.cdc.admin.entity.TbCdcmrScreenConfig">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_screen_config
        where config_type = 1
        order by update_time desc
        limit 1
    </select>

</mapper>