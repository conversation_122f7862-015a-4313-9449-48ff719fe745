<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrSignalPushConfigurationMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_signal_push_configuration-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
    <result column="is_repeat" jdbcType="VARCHAR" property="isRepeat" />
    <result column="repeat_end_time" jdbcType="TIMESTAMP" property="repeatEndTime" />
    <result column="repeat_frequency" jdbcType="VARCHAR" property="repeatFrequency" />
    <result column="repeat_start_time" jdbcType="TIMESTAMP" property="repeatStartTime" />
    <result column="risk_level_detail_id" jdbcType="VARCHAR" property="riskLevelDetailId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
    <result column="warning_charge_person_table_id" jdbcType="VARCHAR" property="warningChargePersonTableId" />
    <result column="message_config_id" jdbcType="VARCHAR" property="messageConfigId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator, creator_id, disease_code, disease_name, is_repeat, repeat_end_time,
    repeat_frequency, repeat_start_time, risk_level_detail_id, update_time, updater, updater_id,
    warning_charge_person_table_id,message_config_id
  </sql>

  <insert id="batchInsert" parameterType="list">
    insert into tb_cdcmr_signal_push_configuration (
    <include refid="Base_Column_List"/>
    ) values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.id},
      #{item.createTime},
      #{item.creator},
      #{item.creatorId},
      #{item.diseaseCode},
      #{item.diseaseName},
      #{item.isRepeat},
      #{item.repeatEndTime},
      #{item.repeatFrequency},
      #{item.repeatStartTime},
      #{item.riskLevelDetailId},
      #{item.updateTime},
      #{item.updater},
      #{item.updaterId},
      #{item.warningChargePersonTableId},
      #{item.messageConfigId}
      )
    </foreach>
  </insert>


  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      UPDATE tb_cdcmr_signal_push_configuration
      <set>
        <if test="item.createTime != null">
          create_time = #{item.createTime},
        </if>
        <if test="item.creator != null">
          creator = #{item.creator},
        </if>
        <if test="item.creatorId != null">
          creator_id = #{item.creatorId},
        </if>
        <if test="item.diseaseCode != null">
          disease_code = #{item.diseaseCode},
        </if>
        <if test="item.diseaseName != null">
          disease_name = #{item.diseaseName},
        </if>
        <if test="item.isRepeat != null">
          is_repeat = #{item.isRepeat},
        </if>
        <if test="item.repeatEndTime != null">
          repeat_end_time = #{item.repeatEndTime},
        </if>
        <if test="item.repeatFrequency != null">
          repeat_frequency = #{item.repeatFrequency},
        </if>
        <if test="item.repeatStartTime != null">
          repeat_start_time = #{item.repeatStartTime},
        </if>
        <if test="item.riskLevelDetailId != null">
          risk_level_detail_id = #{item.riskLevelDetailId},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime},
        </if>
        <if test="item.updater != null">
          updater = #{item.updater},
        </if>
        <if test="item.updaterId != null">
          updater_id = #{item.updaterId},
        </if>
        <if test="item.warningChargePersonTableId != null">
          warning_charge_person_table_id = #{item.warningChargePersonTableId},
        </if>
        <if test="item.messageConfigId != null">
          message_config_id = #{item.messageConfigId},
        </if>
      </set>
      WHERE id = #{item.id}
    </foreach>
  </update>

  <select id="loadSignPushConfigByDiseaseCode" resultType="com.iflytek.cdc.admin.vo.SignalPushConfigurationVO">
    SELECT
    id, create_time, creator, creator_id, disease_code, disease_name, is_repeat,
    TO_CHAR(repeat_start_time, 'HH24:MI:SS') as repeat_start_time,
    TO_CHAR(repeat_end_time, 'HH24:MI:SS') as repeat_end_time,
    repeat_frequency,  risk_level_detail_id, update_time, updater, updater_id,
    warning_charge_person_table_id,message_config_id
    FROM
    tb_cdcmr_signal_push_configuration
    WHERE
    disease_code = #{diseaseCode}
  </select>

  <select id="selectByDiseaceCodeAndRiskLevelDetailId" resultMap="BaseResultMap">
    SELECT
    spc.id, spc.create_time, spc.creator, spc.creator_id, spc.disease_code, spc.disease_name,
    spc.is_repeat, spc.repeat_end_time, spc.repeat_frequency, spc.repeat_start_time,
    spc.risk_level_detail_id, spc.update_time, spc.updater, spc.updater_id,
    spc.warning_charge_person_table_id, spc.message_config_id
    FROM
    tb_cdcmr_signal_push_configuration spc
    INNER JOIN tb_cdcmr_warning_charge_person wcp
    ON spc.warning_charge_person_table_id = wcp.id
    WHERE
    wcp.status = '1'
    AND (
    <foreach collection="list" item="item" separator=" OR ">
      <trim prefix="(" suffix=")" suffixOverrides="AND">
        <if test="item.diseaseCode != null and item.diseaseCode != ''">
          spc.disease_code = #{item.diseaseCode} AND
        </if>
        <if test="item.riskLevelDetailId != null and item.riskLevelDetailId != ''">
          spc.risk_level_detail_id = #{item.riskLevelDetailId} AND
        </if>
      </trim>
    </foreach>
    )
  </select>

  <select id="selectByDiseaseCodeAndRiskLevelDetailId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    tb_cdcmr_signal_push_configuration
    WHERE 1=1
    AND disease_code = #{diseaseCode}
    <if test="riskLevelDetailId != null">
      AND risk_level_detail_id = #{riskLevelDetailId}
    </if>
    order by create_time
    limit 1
  </select>

  <select id="selectByWarningChargePersonTableId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    tb_cdcmr_signal_push_configuration
    WHERE warning_charge_person_table_id = #{id}
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    SELECT
        spc.id, spc.create_time, spc.creator, spc.creator_id, spc.disease_code, spc.disease_name,
        spc.is_repeat, spc.repeat_end_time, spc.repeat_frequency, spc.repeat_start_time,
        spc.risk_level_detail_id, spc.update_time, spc.updater, spc.updater_id,
        spc.warning_charge_person_table_id, spc.message_config_id
    FROM
        tb_cdcmr_signal_push_configuration spc
    LEFT JOIN tb_cdcmr_warning_charge_person wcp
        ON spc.warning_charge_person_table_id = wcp.id
    WHERE
        wcp.status = '1' OR wcp.id IS NULL
</select>


</mapper>