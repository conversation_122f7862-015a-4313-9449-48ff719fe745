<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeEmergencyPlanMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="config_id" jdbcType="VARCHAR" property="configId"/>
  </resultMap>
  <sql id="Base_Column_List">
    id
    , attachment_id, config_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_warning_grade_emergency_plan"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="getByConfigId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    select
    <include refid="Base_Column_List"/>
    from "tb_cdcmr_warning_grade_emergency_plan"
    where config_id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="getAttachmentIds" resultType="java.lang.String">
    select attachment_id
    from tb_cdcmr_warning_grade_emergency_plan
    where config_id in (select id
                        from tb_cdcmr_warning_grade_config
                        where config_type = #{configType}
                          and disease_code = #{diseaseCode}
                          and status = '1')
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete
    from "tb_cdcmr_warning_grade_emergency_plan"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    insert into "tb_cdcmr_warning_grade_emergency_plan" (id, attachment_id, config_id)
    values (#{id,jdbcType=VARCHAR}, #{attachmentId,jdbcType=VARCHAR}, #{configId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    insert into "tb_cdcmr_warning_grade_emergency_plan"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    update "tb_cdcmr_warning_grade_emergency_plan"
    <set>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan">
    update "tb_cdcmr_warning_grade_emergency_plan"
    set attachment_id = #{attachmentId,jdbcType=VARCHAR},
        config_id     = #{configId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <delete id="deleterByConfigId">
    delete
    from "tb_cdcmr_warning_grade_emergency_plan"
    where config_id = #{configId}
  </delete>
  <insert id="insertPlans">
    <foreach collection="recordList" item="item" separator=";">
      insert into "tb_cdcmr_warning_grade_emergency_plan"
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.attachmentId != null">
          attachment_id,
        </if>
        <if test="item.configId != null">
          config_id,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=VARCHAR},
        </if>
        <if test="item.attachmentId != null">
          #{item.attachmentId,jdbcType=VARCHAR},
        </if>
        <if test="item.configId != null">
          #{item.configId,jdbcType=VARCHAR},
        </if>
      </trim>
    </foreach>

  </insert>

</mapper>