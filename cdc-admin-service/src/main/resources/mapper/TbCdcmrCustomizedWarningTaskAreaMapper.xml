<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningTaskAreaMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="district_code" jdbcType="VARCHAR" property="districtCode"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="street_code" jdbcType="VARCHAR" property="streetCode"/>
        <result column="street_name" jdbcType="VARCHAR" property="streetName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , task_id, province_code, province_name, city_code, city_name, district_code, district_name,
    street_code, street_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_task_area"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByTaskId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_task_area"
        where task_id = #{taskId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_customized_warning_task_area"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByTaskId">
        delete
        from "tb_cdcmr_customized_warning_task_area"
        where task_id = #{taskId}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        insert into "tb_cdcmr_customized_warning_task_area" (id, task_id, province_code,
                                                             province_name, city_code, city_name,
                                                             district_code, district_name, street_code,
                                                             street_name)
        values (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR},
                #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR},
                #{districtCode,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR}, #{streetCode,jdbcType=VARCHAR},
                #{streetName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        insert into "tb_cdcmr_customized_warning_task_area"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="provinceCode != null">
                province_code,
            </if>
            <if test="provinceName != null">
                province_name,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="districtCode != null">
                district_code,
            </if>
            <if test="districtName != null">
                district_name,
            </if>
            <if test="streetCode != null">
                street_code,
            </if>
            <if test="streetName != null">
                street_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="streetCode != null">
                #{streetCode,jdbcType=VARCHAR},
            </if>
            <if test="streetName != null">
                #{streetName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        <foreach collection="recordList" item="record" separator=";">
            insert into "tb_cdcmr_customized_warning_task_area"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    id,
                </if>
                <if test="record.taskId != null">
                    task_id,
                </if>
                <if test="record.provinceCode != null">
                    province_code,
                </if>
                <if test="record.provinceName != null">
                    province_name,
                </if>
                <if test="record.cityCode != null">
                    city_code,
                </if>
                <if test="record.cityName != null">
                    city_name,
                </if>
                <if test="record.districtCode != null">
                    district_code,
                </if>
                <if test="record.districtName != null">
                    district_name,
                </if>
                <if test="record.streetCode != null">
                    street_code,
                </if>
                <if test="record.streetName != null">
                    street_name,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    #{record.id,jdbcType=VARCHAR},
                </if>
                <if test="record.taskId != null">
                    #{record.taskId,jdbcType=VARCHAR},
                </if>
                <if test="record.provinceCode != null">
                    #{record.provinceCode,jdbcType=VARCHAR},
                </if>
                <if test="record.provinceName != null">
                    #{record.provinceName,jdbcType=VARCHAR},
                </if>
                <if test="record.cityCode != null">
                    #{record.cityCode,jdbcType=VARCHAR},
                </if>
                <if test="record.cityName != null">
                    #{record.cityName,jdbcType=VARCHAR},
                </if>
                <if test="record.districtCode != null">
                    #{record.districtCode,jdbcType=VARCHAR},
                </if>
                <if test="record.districtName != null">
                    #{record.districtName,jdbcType=VARCHAR},
                </if>
                <if test="record.streetCode != null">
                    #{record.streetCode,jdbcType=VARCHAR},
                </if>
                <if test="record.streetName != null">
                    #{record.streetName,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        update "tb_cdcmr_customized_warning_task_area"
        <set>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null">
                district_name = #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="streetCode != null">
                street_code = #{streetCode,jdbcType=VARCHAR},
            </if>
            <if test="streetName != null">
                street_name = #{streetName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTaskArea">
        update "tb_cdcmr_customized_warning_task_area"
        set task_id       = #{taskId,jdbcType=VARCHAR},
            province_code = #{provinceCode,jdbcType=VARCHAR},
            province_name = #{provinceName,jdbcType=VARCHAR},
            city_code     = #{cityCode,jdbcType=VARCHAR},
            city_name     = #{cityName,jdbcType=VARCHAR},
            district_code = #{districtCode,jdbcType=VARCHAR},
            district_name = #{districtName,jdbcType=VARCHAR},
            street_code   = #{streetCode,jdbcType=VARCHAR},
            street_name   = #{streetName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>