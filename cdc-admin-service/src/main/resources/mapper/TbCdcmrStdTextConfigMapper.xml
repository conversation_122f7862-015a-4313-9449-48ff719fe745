<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrStdTextConfigMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="std_type" jdbcType="VARCHAR" property="stdType" />
        <result column="item_type" jdbcType="VARCHAR" property="itemType" />
        <result column="item_value" jdbcType="VARCHAR" property="itemValue" />
        <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
        <result column="replace_value" jdbcType="VARCHAR" property="replaceValue" />
        <result column="notes" jdbcType="VARCHAR" property="notes" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
        <result column="order" jdbcType="BIGINT" property="order" />
    </resultMap>
    <sql id="Base_Column_List">
        id, std_type, item_type, item_value, rule_type, replace_value, notes, create_time,
    update_time, creator, updater, "order"
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_std_text_config
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_std_text_config
    </select>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig">
        insert into "tb_cdcmr_std_text_config" (id, std_type, item_type,
                                                item_value, rule_type, replace_value,
                                                notes, create_time, update_time,
                                                creator, updater, "order"
        )
        values (#{id,jdbcType=VARCHAR}, #{stdType,jdbcType=VARCHAR}, #{itemType,jdbcType=VARCHAR},
                #{itemValue,jdbcType=VARCHAR}, #{ruleType,jdbcType=VARCHAR}, #{replaceValue,jdbcType=VARCHAR},
                #{notes,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{order,jdbcType=BIGINT}
               )
    </insert>
    <insert id="batchInsert">
        insert into "tb_cdcmr_std_text_config" (id, std_type, item_type,
        item_value, rule_type, replace_value,
        notes, create_time, update_time,
        creator, updater, "order"
        )
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id,jdbcType=VARCHAR}, #{item.stdType,jdbcType=VARCHAR}, #{item.itemType,jdbcType=VARCHAR},
            #{item.itemValue,jdbcType=VARCHAR}, #{item.ruleType,jdbcType=VARCHAR}, #{item.replaceValue,jdbcType=VARCHAR},
            #{item.notes,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR}, #{item.order,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
    <insert id="mergeInfo">
        insert into "tb_cdcmr_std_text_config" (id, std_type, item_type,
        item_value, rule_type, replace_value,
        notes, create_time, update_time,
        creator, updater, "order"
        )
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id,jdbcType=VARCHAR}, #{item.stdType,jdbcType=VARCHAR}, #{item.itemType,jdbcType=VARCHAR},
            #{item.itemValue,jdbcType=VARCHAR}, #{item.ruleType,jdbcType=VARCHAR}, #{item.replaceValue,jdbcType=VARCHAR},
            #{item.notes,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR}, #{item.order,jdbcType=BIGINT}
            )
        </foreach>
        on conflict( std_type,item_type,item_value,rule_type ) do update
            set update_time = now()
    </insert>

</mapper>