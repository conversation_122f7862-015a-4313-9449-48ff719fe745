<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.PackageVersionMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.PackageVersion">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_package_version-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="version_type" jdbcType="VARCHAR" property="versionType" />
    <result column="version_sub_type" jdbcType="VARCHAR" property="versionSubType" />
    <result column="description_file_url" jdbcType="VARCHAR" property="descriptionFileUrl" />
    <result column="resource_file_url" jdbcType="VARCHAR" property="resourceFileUrl" />
    <result column="is_new_version" jdbcType="VARCHAR" property="isNewVersion" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, area_code, area_name, org_id, version_code, version_type, version_sub_type, description_file_url, 
    resource_file_url, is_new_version, creator, create_time, updator, update_time
  </sql>

  <select id="queryPackageUrl" resultType="com.iflytek.cdc.admin.entity.OrgPackge" parameterType="com.iflytek.cdc.admin.entity.OrgPackge">
    select org_id as orgId, org_name as orgName,package_file_url as packageFileUrl,
    to_timestamp(create_time,'yyyy-MM-dd HH24:mi:ss') as createTime,to_timestamp(update_time,'yyyy-MM-dd HH24:mi:ss') as updateTime
    from tb_cdcmr_org_packge where 1=1
    <if test="orgId != null and orgId != ''">
      and org_id=#{orgId}
    </if>
    <if test="orgName != null and orgName != ''">
      and org_name like concat('%',#{orgName},'%')
    </if>
  </select>

   <insert id="insertPackageUrl"  parameterType="com.iflytek.cdc.admin.entity.OrgPackge">
     insert into tb_cdcmr_org_packge
     <trim prefix="(" suffix=")" suffixOverrides=",">
       <if test="id != null and id != ''">
         id,
       </if>
       <if test="areaCode != null and areaCode != ''">
         area_code,
       </if>
       <if test="areaName != null and areaName != ''">
         area_name,
       </if>
       <if test="areaParentCode != null and areaParentCode != ''">
         area_parent_code,
       </if>
       <if test="areaParentName != null and areaParentName != ''">
         area_parent_name,
       </if>
       <if test="orgId != null and orgId != ''">
         org_id,
       </if>
       <if test="orgName != null and orgName != ''">
         org_name,
       </if>
       <if test="packageFileUrl != null and packageFileUrl != ''">
         package_file_url,
       </if>
       <if test="creator != null and creator != ''">
         creator,
       </if>
       <if test="createTime != null">
         create_time,
       </if>
       <if test="updator != null and updator != ''">
         updator,
       </if>
       <if test="updateTime != null">
         update_time,
       </if>
     </trim>
     <trim prefix="values (" suffix=")" suffixOverrides=",">
       <if test="id != null and id != ''">
         #{id,jdbcType=VARCHAR},
       </if>
       <if test="areaCode != null and areaCode != ''">
         #{areaCode,jdbcType=VARCHAR},
       </if>
       <if test="areaName != null and areaName != ''">
         #{areaName,jdbcType=VARCHAR},
       </if>
       <if test="areaParentCode != null and areaParentCode != ''">
         #{areaParentCode,jdbcType=VARCHAR},
       </if>
       <if test="areaParentName != null and areaParentName != ''">
         #{areaParentName,jdbcType=VARCHAR},
       </if>
       <if test="orgId != null and orgId != ''">
         #{orgId,jdbcType=VARCHAR},
       </if>
       <if test="orgName != null and orgName != ''">
         #{orgName,jdbcType=VARCHAR},
       </if>
       <if test="packageFileUrl != null and packageFileUrl != ''">
         #{packageFileUrl,jdbcType=VARCHAR},
       </if>
       <if test="creator != null and creator != ''">
         #{creator,jdbcType=VARCHAR},
       </if>
       <if test="createTime != null">
         now(),
       </if>
       <if test="updator != null and updator != ''">
         #{updator,jdbcType=VARCHAR},
       </if>
       <if test="updateTime != null">
         now(),
       </if>
     </trim>
   </insert>

  <update id="updatePackageUrl"  parameterType="com.iflytek.cdc.admin.entity.OrgPackge">
    update tb_cdcmr_org_packge
    <set>
    <if test="areaCode != null and areaCode != ''">
      area_code = #{areaCode,jdbcType=VARCHAR},
    </if>
    <if test="areaName != null and areaName != ''">
      area_name = #{areaName,jdbcType=VARCHAR},
    </if>
    <if test="areaParentCode != null and areaParentCode != ''">
      area_parent_code = #{areaParentCode,jdbcType=VARCHAR},
    </if>
    <if test="areaParentName != null and areaParentName != ''">
      area_parent_name = #{areaParentName,jdbcType=VARCHAR},
    </if>
    <if test="orgId != null and orgId != ''">
      org_id = #{orgId,jdbcType=VARCHAR},
    </if>
    <if test="orgName != null and orgName != ''">
      org_name = #{orgName,jdbcType=VARCHAR},
    </if>
    <if test="packageFileUrl != null and packageFileUrl != ''">
      package_file_url = #{packageFileUrl,jdbcType=VARCHAR},
    </if>
    <if test="updator != null and updator != ''">
      updator = #{updator,jdbcType=VARCHAR},
    </if>
    <if test="updateTime != null">
      update_time = now(),
    </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>


</mapper>