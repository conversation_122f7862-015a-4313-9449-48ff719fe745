<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.AddressDetailMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.AddressDetailMapping">
        <result column="address_detail" property="addressDetail"/>
        <result column="address_standard_id" property="addressStandardId"/>
        <result column="telephone_city_name" property="telephoneCityName"/>
        <result column="telephone_city_code" property="telephoneCityCode"/>
        <result column="id_card_city_name" property="idCardCityName"/>
        <result column="id_card_city_code" property="idCardCityCode"/>
        <result column="org_city_name" property="orgCityName"/>
        <result column="org_city_code" property="orgCityCode"/>
        <result column="poi_type" property="poiType"/>
        <result column="creator" property="creator"/>
        <result column="create_datetime" property="createDatetime"/>
        <result column="updator" property="updator"/>
        <result column="update_datetime" property="updateDatetime"/>
        <result column="id" property="id"/>
        <result column="similarity" property="similarity"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        address_detail, address_standard_id, telephone_city_name, telephone_city_code, id_card_city_name, id_card_city_code, org_city_name, org_city_code, poi_type, creator, create_datetime, updator, update_datetime, id, similarity, status
    </sql>
    <update id="saveOrUpdateAddressDetailMapping">

        INSERT INTO tb_cdcmr_address_detail_mapping (address_detail, address_standard_id,
        telephone_city_name, telephone_city_code, id_card_city_name,
        id_card_city_code, org_city_name, org_city_code,
        poi_type, creator, create_datetime,
        updator, update_datetime, id, similarity, status
        )
        VALUES (#{addressDetail,jdbcType=VARCHAR}, #{addressStandardId,jdbcType=VARCHAR},
        #{telephoneCityName,jdbcType=VARCHAR}, #{telephoneCityCode,jdbcType=VARCHAR}, #{idCardCityName,jdbcType=VARCHAR},
        #{idCardCityCode,jdbcType=VARCHAR}, #{orgCityName,jdbcType=VARCHAR}, #{orgCityCode,jdbcType=VARCHAR},
        #{poiType,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, now(),
        #{updator,jdbcType=VARCHAR}, now(), #{id,jdbcType=VARCHAR},
        #{similarity,jdbcType=DOUBLE}, #{status,jdbcType=INTEGER}
        )
        ON CONFLICT (address_detail,telephone_city_name, telephone_city_code, id_card_city_name,id_card_city_code, org_city_name, org_city_code)
        DO UPDATE SET
        address_standard_id = excluded.address_standard_id,
        poi_type = excluded.poi_type,
        updator = excluded.updator,
        update_datetime = now(),
        id = excluded.id,
        similarity = excluded.similarity,
        status = excluded.status
    </update>

    <update id="batchUpdateSimilarity">
        update tb_cdcmr_address_detail_mapping set
        status = case id
                <foreach collection="list" item="item">
                    when #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=INTEGER}
                </foreach>
                else status
            end,
        similarity = case id
                <foreach collection="list" item="item">
                    when #{item.id,jdbcType=VARCHAR} then #{item.similarity,jdbcType=DOUBLE}
                </foreach>
                else similarity
            end
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectByEtlTimeLimit" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tb_cdcmr_address_detail_mapping
        where update_datetime between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
        <if test="similarity != null">
            and similarity >= #{similarity,jdbcType=DOUBLE}
        </if>
        order by update_datetime, id
        <if test="pageIndex != null and pageSize != null">
            LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
        </if>
    </select>

</mapper>
