<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnRuleMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarnRule">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="time_range" jdbcType="SMALLINT" property="timeRange" />
    <result column="time_range_unit" jdbcType="SMALLINT" property="timeRangeUnit" />
    <result column="monitoring_object" jdbcType="SMALLINT" property="monitoringObject" />
    <result column="medical_count" jdbcType="SMALLINT" property="medicalCount" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="warn_id" jdbcType="VARCHAR" property="warnId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, time_range, time_range_unit, monitoring_object, medical_count, is_deleted, "status", 
    update_time, updater, warn_id
  </sql>
  <insert id="upsertRules">
    <foreach collection="recordList" item="item" separator=";">
      insert into "tb_cdcmr_unknown_reason_warn_rule"
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          id,
        </if>
        <if test="item.timeRange != null">
          time_range,
        </if>
        <if test="item.timeRangeUnit != null">
          time_range_unit,
        </if>
        <if test="item.monitoringObject != null">
          monitoring_object,
        </if>
        <if test="item.medicalCount != null">
          medical_count,
        </if>
        <if test="item.isDeleted != null">
          is_deleted,
        </if>
        <if test="item.status != null">
          "status",
        </if>
        <if test="item.updateTime != null">
          update_time,
        </if>
        <if test="item.updater != null">
          updater,
        </if>
        <if test="item.warnId != null">
          warn_id,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.id != null">
          #{item.id,jdbcType=VARCHAR},
        </if>
        <if test="item.timeRange != null">
          #{item.timeRange,jdbcType=SMALLINT},
        </if>
        <if test="item.timeRangeUnit != null">
          #{item.timeRangeUnit,jdbcType=SMALLINT},
        </if>
        <if test="item.monitoringObject != null">
          #{item.monitoringObject,jdbcType=SMALLINT},
        </if>
        <if test="item.medicalCount != null">
          #{item.medicalCount,jdbcType=SMALLINT},
        </if>
        <if test="item.isDeleted != null">
          #{item.isDeleted,jdbcType=SMALLINT},
        </if>
        <if test="item.status != null">
          #{item.status,jdbcType=SMALLINT},
        </if>
        <if test="item.updateTime != null">
          #{item.updateTime,jdbcType=DATE},
        </if>
        <if test="item.updater != null">
          #{item.updater,jdbcType=VARCHAR},
        </if>
        <if test="item.warnId != null">
          #{item.warnId,jdbcType=VARCHAR},
        </if>
      </trim>
      on conflict(id) do
      update
      set time_range = excluded.time_range,
      time_range_unit = excluded.time_range_unit,
      monitoring_object =excluded.monitoring_object,
      medical_count = excluded.medical_count,
      is_deleted = excluded.is_deleted,
      "status" = excluded.status,
      update_time = excluded.update_time,
      updater = excluded.updater,
      warn_id = excluded.warn_id
    </foreach>
  </insert>
  <delete id="deleteOtherByIds">
    update "tb_cdcmr_unknown_reason_warn_rule" set is_deleted = 1
    where warn_id = #{warnId,jdbcType=VARCHAR}
    <if test="idList.size() != 0">
      and id not in
      <foreach collection="idList" open="(" separator="," close=")" item="item" index="index">
        #{item}
      </foreach>
    </if>
  </delete>

  <select id="getRuleListByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarnRule">
    select * from tb_cdcmr_unknown_reason_warn_rule
    where warn_id = #{warnId}
    and is_deleted = 0
  </select>
  <select id="getAllRuleList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarnRule">
      select *
      from tb_cdcmr_unknown_reason_warn_rule
      where is_deleted = 0
        and status = 1
  </select>
  <select id="getExportData" resultType="com.iflytek.cdc.admin.dto.UnknownReasonWarnRuleExportDataDto">
    SELECT tcsw.disease_name as diseaseName,
           tcsw.incubation as incubation,
           tcswr.monitoring_object as  monitorObject,
           tcswr.medical_count as medicalCount,
           tcswr.time_range as timeRange,
           tcswr.time_range_unit as timeRangeUnit
    FROM tb_cdcmr_unknown_reason_warn_rule  tcswr
           LEFT JOIN tb_cdcmr_unknown_reason_warn  tcsw ON tcswr.warn_id = tcsw.ID
    WHERE  tcswr.is_deleted = '0'
      and tcsw.is_deleted = '0'
    order by warn_id
  </select>
</mapper>