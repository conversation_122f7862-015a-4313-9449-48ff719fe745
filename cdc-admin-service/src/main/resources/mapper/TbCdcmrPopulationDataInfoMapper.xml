<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPopulationDataInfoMapper">

    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo" >
        <result column="id" property="id" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="street_code" property="streetCode" />
        <result column="street_name" property="streetName" />
        <result column="resident_population" property="residentPopulation" />
        <result column="registered_population" property="registeredPopulation" />
        <result column="gdp" property="gdp" />
        <result column="urban_dpi" property="urbanDpi" />
        <result column="rural_dpi" property="ruralDpi" />
        <result column="notes" property="notes" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="updater" property="updater" />
        <result column="creator" property="creator" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="stat_date" property="statDate" />
        <result column="street_code" property="streetCode" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        province_code,
        province_name,
        city_code,
        city_name,
        district_code,
        district_name,
        street_code,
        street_name,
        resident_population,
        registered_population,
        gdp,
        urban_dpi,
        rural_dpi,
        notes,
        create_time,
        update_time,
        updater,
        creator,
        delete_flag,
        stat_date
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo">
        INSERT INTO tb_cdcmr_population_data_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="null != provinceCode and '' != provinceCode">
                province_code,
            </if>
            <if test="null != provinceName and '' != provinceName">
                province_name,
            </if>
            <if test="null != cityCode and '' != cityCode">
                city_code,
            </if>
            <if test="null != cityName and '' != cityName">
                city_name,
            </if>
            <if test="null != districtCode and '' != districtCode">
                district_code,
            </if>
            <if test="null != districtName and '' != districtName">
                district_name,
            </if>
            <if test="null != streetCode and '' != streetCode">
                street_code,
            </if>
            <if test="null != streetName and '' != streetName">
                street_name,
            </if>
            <if test="null != residentPopulation ">
                resident_population,
            </if>
            <if test="null != registeredPopulation ">
                registered_population,
            </if>
            <if test="null != gdp and '' != gdp">
                gdp,
            </if>
            <if test="null != urbanDpi and '' != urbanDpi">
                urban_dpi,
            </if>
            <if test="null != ruralDpi and '' != ruralDpi">
                rural_dpi,
            </if>
            <if test="null != notes and '' != notes">
                notes,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updateTime ">
                update_time,
            </if>
            <if test="null != updater and '' != updater">
                updater,
            </if>
            <if test="null != creator and '' != creator">
                creator,
            </if>
            <if test="null != deleteFlag ">
                delete_flag,
            </if>
            <if test="null != statDate ">
                stat_date
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="null != provinceCode and '' != provinceCode">
                #{provinceCode},
            </if>
            <if test="null != provinceName and '' != provinceName">
                #{provinceName},
            </if>
            <if test="null != cityCode and '' != cityCode">
                #{cityCode},
            </if>
            <if test="null != cityName and '' != cityName">
                #{cityName},
            </if>
            <if test="null != districtCode and '' != districtCode">
                #{districtCode},
            </if>
            <if test="null != districtName and '' != districtName">
                #{districtName},
            </if>
            <if test="null != streetCode and '' != streetCode">
                #{streetCode},
            </if>
            <if test="null != streetName and '' != streetName">
                #{streetName},
            </if>
            <if test="null != residentPopulation ">
                #{residentPopulation},
            </if>
            <if test="null != registeredPopulation ">
                #{registeredPopulation},
            </if>
            <if test="null != gdp and '' != gdp">
                #{gdp},
            </if>
            <if test="null != urbanDpi and '' != urbanDpi">
                #{urbanDpi},
            </if>
            <if test="null != ruralDpi and '' != ruralDpi">
                #{ruralDpi},
            </if>
            <if test="null != notes and '' != notes">
                #{notes},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime ">
                #{updateTime},
            </if>
            <if test="null != updater and '' != updater">
                #{updater},
            </if>
            <if test="null != creator and '' != creator">
                #{creator},
            </if>
            <if test="null != deleteFlag ">
                #{deleteFlag},
            </if>
            <if test="null != statDate ">
                #{statDate}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo">
        INSERT INTO tb_cdcmr_population_data_info
        (<include refid="Base_Column_List"/>)
        values
        <foreach collection="entities" separator="," item="item">
            (#{item.id},#{item.provinceCode},#{item.provinceName},#{item.cityCode},#{item.cityName},#{item.districtCode}, #{item.districtName},#{item.streetCode}, #{item.streetName},
            #{item.residentPopulation},#{item.registeredPopulation}, #{item.gdp},#{item.urbanDpi},#{item.ruralDpi},#{item.notes}, #{item.createTime}, #{item.updateTime},
            #{item.updater},#{item.creator},#{item.deleteFlag}, #{item.statDate}
            )
        </foreach>
    </insert>

    <delete id="delete" >
        DELETE FROM tb_cdcmr_population_data_info
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo">
        UPDATE tb_cdcmr_population_data_info
        <set>
            <if test="null != provinceCode and '' != provinceCode">province_code = #{provinceCode},</if>
            <if test="null != provinceName and '' != provinceName">province_name = #{provinceName},</if>
            <if test="null != cityCode and '' != cityCode">city_code = #{cityCode},</if>
            <if test="null != cityName and '' != cityName">city_name = #{cityName},</if>
            <if test="null != districtCode and '' != districtCode">district_code = #{districtCode},</if>
            <if test="null != districtName and '' != districtName">district_name = #{districtName},</if>
            <if test="null != streetCode and '' != streetCode">street_code = #{streetCode},</if>
            <if test="null != streetName and '' != streetName">street_name = #{streetName},</if>
            <if test="null != residentPopulation ">resident_population = #{residentPopulation},</if>
            <if test="null != registeredPopulation ">registered_population = #{registeredPopulation},</if>
            <if test="null != gdp and '' != gdp">gdp = #{gdp},</if>
            <if test="null != urbanDpi and '' != urbanDpi">urban_dpi = #{urbanDpi},</if>
            <if test="null != ruralDpi and '' != ruralDpi">rural_dpi = #{ruralDpi},</if>
            <if test="null != notes and '' != notes">notes = #{notes},</if>
            <if test="null != createTime ">create_time = #{createTime},</if>
            <if test="null != updateTime ">update_time = #{updateTime},</if>
            <if test="null != updater and '' != updater">updater = #{updater},</if>
            <if test="null != creator and '' != creator">creator = #{creator},</if>
            <if test="null != deleteFlag ">delete_flag = #{deleteFlag},</if>
            <if test="null != statDate ">stat_date = #{statDate}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_population_data_info
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        with ranked_data as (
            SELECT <include refid="Base_Column_List" />,
                ROW_NUMBER() OVER (PARTITION BY province_code, city_code, district_code, street_code ORDER BY stat_date DESC NULLS LAST) AS rn
            FROM tb_cdcmr_population_data_info
            where 1 = 1
            <if test="statDate != null">
                AND DATE(stat_date) = DATE(#{statDate})
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                and province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size() > 0">
                and province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size() > 0">
                and city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size() > 0">
                and district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="streetCodes != null and streetCodes.size() > 0">
                and street_code in
                <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        )
        select * from ranked_data where rn = 1

    </select>

    <select id="listByAreaCodes" resultType="com.iflytek.cdc.admin.vo.PopulationDataInfoVO">
        with ranked_data as (
            SELECT <include refid="Base_Column_List" />,
                ROW_NUMBER() OVER (PARTITION BY province_code, city_code, district_code, street_code ORDER BY stat_date DESC NULLS LAST) AS rn
            FROM tb_cdcmr_population_data_info
            where
                <if test="areaLevel == 1">
                    province_code in
                        <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                            #{item}
                        </foreach>
                    and city_code is null
                </if>
                <if test="areaLevel == 2">
                    city_code in
                    <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                    and district_code is null
                </if>
                <if test="areaLevel == 3">
                    district_code in
                    <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                    and street_code is null
                </if>
                <if test="areaLevel == 4">
                    street_code in
                    <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="statDate != null">
                    and stat_date &lt;= #{statDate}
                </if>
            )
        select
        <if test="areaLevel == 1">
            province_code as areaCode,
            province_name as areaName,
            '1' as areaLevel,
            sum(resident_population) as residentPopulation,
            sum(registered_population) as registeredPopulation,
            sum(gdp) as gdp,
            sum(urban_dpi) as urbanDpi,
            sum(rural_dpi) as ruralDpi
        </if>
        <if test="areaLevel == 2">
            city_code as areaCode,
            city_name as areaName,
            '2' as areaLevel,
            resident_population as residentPopulation,
            registered_population as registeredPopulation,
            gdp as gdp,
            urban_dpi as urbanDpi,
            rural_dpi as ruralDpi
        </if>
        <if test="areaLevel == 3">
            district_code as areaCode,
            district_name as areaName,
            '3' as areaLevel,
            resident_population as residentPopulation,
            registered_population as registeredPopulation,
            gdp as gdp,
            urban_dpi as urbanDpi,
            rural_dpi as ruralDpi
        </if>
        <if test="areaLevel == 4">
            street_code as areaCode,
            street_name as areaName,
            '4' as areaLevel,
            resident_population as residentPopulation,
            registered_population as registeredPopulation,
            gdp as gdp,
            urban_dpi as urbanDpi,
            rural_dpi as ruralDpi
        </if>
        from ranked_data where rn = 1
        <if test="areaLevel == 1">
            group by province_code ,province_name
        </if>
    </select>

    <select id="statByAreaCodes" resultType="com.iflytek.cdc.admin.vo.PopulationDataInfoVO">
        with ranked_data as (
        SELECT <include refid="Base_Column_List" />,
            ROW_NUMBER() OVER (PARTITION BY province_code, city_code, district_code, street_code ORDER BY stat_date DESC NULLS LAST) AS rn
        FROM tb_cdcmr_population_data_info
        where
        <if test="areaLevel == 1">
            province_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and city_code is null
        </if>
        <if test="areaLevel == 2">
            city_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and district_code is null
        </if>
        <if test="areaLevel == 3">
            district_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and street_code is null
        </if>
        <if test="areaLevel == 4">
            street_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="statDate != null">
            and stat_date &lt;= #{statDate}
        </if>
        )
        select
            sum(resident_population) as residentPopulation,
            sum(registered_population) as registeredPopulation,
            sum(gdp) as gdp,
            sum(urban_dpi) as urbanDpi,
            sum(rural_dpi) as ruralDpi
        from ranked_data where rn = 1
    </select>
    <select id="listByArea" resultType="com.iflytek.cdc.admin.vo.PopulationDataInfoVO">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_population_data_info
        where 1=1
        <if test="areaLevel == 1">
           and province_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and city_code is null
        </if>
        <if test="areaLevel == 2">
          and   city_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and district_code is null
        </if>
        <if test="areaLevel == 3">
         and   district_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and street_code is null
        </if>
        <if test="areaLevel == 4">
            and   street_code in
            <foreach collection="areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateHistoricalStatistics" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            UPDATE tb_cdcmr_population_data_info
            <set>
                <if test="item.residentPopulation != null">resident_population = #{item.residentPopulation},</if>
                <if test="item.registeredPopulation != null">registered_population = #{item.registeredPopulation},</if>
                <if test="item.gdp != null and item.gdp != ''">gdp = #{item.gdp},</if>
                <if test="item.urbanDpi != null and item.urbanDpi != ''">urban_dpi = #{item.urbanDpi},</if>
                <if test="item.ruralDpi != null and item.ruralDpi != ''">rural_dpi = #{item.ruralDpi},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.updater != null and item.updater != ''">updater = #{item.updater},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="listByAreaAndDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_cdcmr_population_data_info
        where 1= 1
        <if test="areaLevel == 1">
            <if test="provinceCode != null and provinceCode != ''">
                and province_code = #{provinceCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size() > 0">
                and province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and city_code is null
        </if>
        <if test="areaLevel == 2">
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="cityCodes != null and cityCodes.size() > 0">
                and city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and district_code is null
        </if>
        <if test="areaLevel == 3">
            <if test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </if>
            <if test="districtCodes != null and districtCodes.size() > 0">
                and district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and street_code is null
        </if>
        <if test="areaLevel == 4">
            <if test="streetCode != null and streetCode != ''">
                and street_code = #{streetCode}
            </if>
            <if test="streetCodes != null and streetCodes.size() > 0">
                and street_code in
                <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        order by stat_date desc nulls last
    </select>

    <select id="listByEachAreaCodes" resultType="com.iflytek.cdc.admin.vo.PopulationDataInfoVO">
        with ranked_data as (
        SELECT
        <include refid="Base_Column_List" />,
        ROW_NUMBER() OVER (PARTITION BY province_code, city_code, district_code, street_code ORDER BY stat_date DESC NULLS LAST) AS rn
        FROM tb_cdcmr_population_data_info
        <where>
            <trim prefix="(" suffix=")" prefixOverrides="or">
                <if test="provinceCodes != null and provinceCodes.size() > 0">
                    <foreach collection="provinceCodes" item="item" separator="or">
                        (province_code = #{item} and city_code is null)
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size() > 0">
                    or
                    <foreach collection="cityCodes" item="item" separator="or">
                        (city_code = #{item} and district_code is null)
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size() > 0">
                    or
                    <foreach collection="districtCodes" item="item" separator="or">
                        (district_code = #{item} and street_code is null)
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size() > 0">
                    or
                    <foreach collection="streetCodes" item="item" separator="or">
                        (street_code = #{item})
                    </foreach>
                </if>
            </trim>
            <if test="statDate != null">
                and stat_date &lt;= #{statDate}
            </if>
        </where>
        )
        select
        case
        when province_code is not null and city_code is null then province_code
        when city_code is not null and district_code is null then city_code
        when district_code is not null and street_code is null then district_code
        else street_code
        end as areaCode,
        case
        when province_code is not null and city_code is null then province_name
        when city_code is not null and district_code is null then city_name
        when district_code is not null and street_code is null then district_name
        else street_name
        end as areaName,
        case
        when province_code is not null and city_code is null then '1'
        when city_code is not null and district_code is null then '2'
        when district_code is not null and street_code is null then '3'
        else '4'
        end as areaLevel,
        resident_population as residentPopulation,
        registered_population as registeredPopulation,
        gdp as gdp,
        urban_dpi as urbanDpi,
        rural_dpi as ruralDpi
        from ranked_data
        where rn = 1
    </select>



</mapper>