<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningWarnMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="poisoning_code" jdbcType="VARCHAR" property="poisoningCode" />
    <result column="poisoning_name" jdbcType="VARCHAR" property="poisoningName" />
    <result column="poisoning_type_code" jdbcType="VARCHAR" property="poisoningTypeCode" />
    <result column="poisoning_type_name" jdbcType="VARCHAR" property="poisoningTypeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="max_life_cycle" jdbcType="SMALLINT" property="maxLifeCycle" />
    <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode" />
    <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc" />
    <result column="event_generation_node" jdbcType="INTEGER" property="eventGenerationNode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, poisoning_code, poisoning_name, poisoning_type_code, poisoning_type_name, remark, 
    "status", is_deleted, update_time, updater, max_life_cycle, sms_send_type_code, sms_send_type_desc, event_generation_node
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "tb_cdcmr_poisoning_warn"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    update "tb_cdcmr_poisoning_warn"
    set is_deleted = 1
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn">
    insert into "tb_cdcmr_poisoning_warn" (id, poisoning_code, poisoning_name,
                                           poisoning_type_code, poisoning_type_name, remark,
                                           "status", is_deleted, update_time,
                                           updater, max_life_cycle, sms_send_type_code,
                                           sms_send_type_desc, event_generation_node)
    values (#{id,jdbcType=VARCHAR}, #{poisoningCode,jdbcType=VARCHAR}, #{poisoningName,jdbcType=VARCHAR},
            #{poisoningTypeCode,jdbcType=VARCHAR}, #{poisoningTypeName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE},
            #{updater,jdbcType=VARCHAR}, #{maxLifeCycle,jdbcType=SMALLINT}, #{smsSendTypeCode,jdbcType=VARCHAR},
            #{smsSendTypeDesc,jdbcType=VARCHAR}, #{eventGenerationNode,jdbcType=INTEGER})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn">
    update "tb_cdcmr_poisoning_warn"
    <set>
      <if test="poisoningCode != null">
        poisoning_code = #{poisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningName != null">
        poisoning_name = #{poisoningName,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeCode != null">
        poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
        max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
      <if test="smsSendTypeCode != null">
        sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smsSendTypeDesc != null">
        sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventGenerationNode != null">
        event_generation_node = #{eventGenerationNode},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateStatusByPrimaryKey">
    update "tb_cdcmr_poisoning_warn"
            set
        "status" = #{status,jdbcType=SMALLINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPoisoning">
    update "tb_cdcmr_poisoning_warn"
    <set>
      <if test="poisoningCode != null">
        poisoning_code = #{poisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningName != null">
        poisoning_name = #{poisoningName,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeCode != null">
        poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="maxLifeCycle != null">
       max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
      </if>
      <if test="smsSendTypeCode != null">
        sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smsSendTypeDesc != null">
        sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventGenerationNode != null">
        event_generation_node = #{eventGenerationNode},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getByPoisoningCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn">
    select *
    from tb_cdcmr_poisoning_warn
    where poisoning_code = #{poisoningCode,jdbcType=VARCHAR}
      and is_deleted = 0
  </select>
  <select id="getByPoisoningName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoningWarn">
    select *
    from tb_cdcmr_poisoning_warn
    where poisoning_name = #{poisoningName,jdbcType=VARCHAR}
      and is_deleted = 0
  </select>
  <select id="getList" resultType="com.iflytek.cdc.admin.dto.PoisoningWarnDto">
    select * ,(select count(b.id) from tb_cdcmr_poisoning_warn_rule b where a.id = b.warn_id and
    b.is_deleted = 0) as ruleCount from "tb_cdcmr_poisoning_warn" a
    where is_deleted = 0
    <if test="poisoningName!= null and poisoningName !=''">
      and poisoning_name like concat('%',#{poisoningName},'%')
    </if>
    <if test="poisoningTypeCode!= null and poisoningTypeCode !=''">
      and poisoning_type_code  = #{poisoningTypeCode}
    </if>
    <if test="status != null">
      and "status" = #{status,jdbcType=SMALLINT}
    </if>
    order by id
  </select>
  <select id="getAllList" resultType="com.iflytek.cdc.admin.dto.PoisoningWarnDto">
    select * from "tb_cdcmr_poisoning_warn"
    where is_deleted = 0 and "status" = 1
    <if test="poisoningCode!= null and poisoningName !=''">
      and poisoning_code = #{poisoningCode}
    </if>
    order by id
  </select>
</mapper>