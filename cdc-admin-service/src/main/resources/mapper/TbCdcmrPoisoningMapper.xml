<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="poisoning_code" jdbcType="VARCHAR" property="poisoningCode" />
    <result column="poisoning_name" jdbcType="VARCHAR" property="poisoningName" />
    <result column="poisoning_type_code" jdbcType="VARCHAR" property="poisoningTypeCode" />
    <result column="poisoning_type_name" jdbcType="VARCHAR" property="poisoningTypeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, poisoning_code, poisoning_name, poisoning_type_code, poisoning_type_name, remark, 
    "status", is_deleted, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "tb_cdcmr_poisoning"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    update  "tb_cdcmr_poisoning" set is_deleted = 1 where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
    insert into "tb_cdcmr_poisoning" (id, poisoning_code, poisoning_name,
                                      poisoning_type_code, poisoning_type_name, remark,
                                      "status", is_deleted, update_time,
                                      updater, parent_code, parent_name)
    values (#{id,jdbcType=VARCHAR}, #{poisoningCode,jdbcType=VARCHAR}, #{poisoningName,jdbcType=VARCHAR},
            #{poisoningTypeCode,jdbcType=VARCHAR}, #{poisoningTypeName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE},
            #{updater,jdbcType=VARCHAR}, #{parentCode,jdbcType=VARCHAR},  #{parentName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
    insert into "tb_cdcmr_poisoning"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="poisoningCode != null">
        poisoning_code,
      </if>
      <if test="poisoningName != null">
        poisoning_name,
      </if>
      <if test="poisoningTypeCode != null">
        poisoning_type_code,
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="poisoningCode != null">
        #{poisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningName != null">
        #{poisoningName,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeCode != null">
        #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
    update "tb_cdcmr_poisoning"
    <set>
      <if test="poisoningCode != null">
        poisoning_code = #{poisoningCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningName != null">
        poisoning_name = #{poisoningName,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeCode != null">
        poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="poisoningTypeName != null">
        poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
      </if>
        <if test="parentCode != null">
            parent_code = #{parentCode,jdbcType=VARCHAR},
        </if>
        <if test="parentName != null">
            parent_name = #{parentName,jdbcType=VARCHAR},
        </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=DATE},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
    update "tb_cdcmr_poisoning"
    set poisoning_code = #{poisoningCode,jdbcType=VARCHAR},
        poisoning_name = #{poisoningName,jdbcType=VARCHAR},
        parent_code = #{parentCode,jdbcType=VARCHAR},
        parent_name = #{parentName,jdbcType=VARCHAR},
        poisoning_type_code = #{poisoningTypeCode,jdbcType=VARCHAR},
        poisoning_type_name = #{poisoningTypeName,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        "status" = #{status,jdbcType=SMALLINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        update_time = #{updateTime,jdbcType=DATE},
        updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
      select * from "tb_cdcmr_poisoning"
      where is_deleted = 0
      <if test="poisoningName!= null and poisoningName !=''">
          and poisoning_name like concat('%',#{poisoningName},'%')
      </if>
      <if test="poisoningTypeCode!= null and poisoningTypeCode !=''">
          and poisoning_type_code = #{poisoningTypeCode}
      </if>
      <if test="status != null">
          and "status" = #{status,jdbcType=SMALLINT}
      </if>
      order by id
  </select>
    <select id="queryPoisonInfoByGrade" resultType="com.iflytek.cdc.admin.dto.PoisonGradeConfigVO">
        SELECT
        t2.remark AS remark,
        COALESCE ( t2.status, 0 ) AS status,
        t1.*,
        (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
        t3.is_deleted = 0)as ruleCount
        FROM
        "tb_cdcmr_poisoning" t1
        LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'poison'
        AND t1.poisoning_code= t2.disease_code
        where t1.is_deleted = 0
        <if test="poisoningName!= null and poisoningName !=''">
            and t1.poisoning_name like concat('%',#{poisoningName},'%')
        </if>
        <if test="poisoningTypeCode!= null and poisoningTypeCode !=''">
            and t1.poisoning_type_code = #{poisoningTypeCode}
        </if>
        <if test="status != null ">
            and COALESCE ( t2.status,0) = #{status}
        </if>
        order by id
    </select>
    <select id="getByPoisoningCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
        select *
        from "tb_cdcmr_poisoning"
        where is_deleted = 0
          and poisoning_code = #{poisoningCode}
    </select>
    <select id="getByPoisoningName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
        select *
        from "tb_cdcmr_poisoning"
        where is_deleted = 0
          and poisoning_name = #{poisoningName}
    </select>
    <select id="findAll" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
        select *
        from "tb_cdcmr_poisoning"
        where is_deleted = 0
    </select>
    <select id="findAllParent" resultType="com.iflytek.cdc.admin.entity.TbCdcmrPoisoning">
        select *
        from "tb_cdcmr_poisoning"
        where is_deleted = 0 and  coalesce(parent_code,'')=''
    </select>

</mapper>