<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarnItemMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="customized_warn_id" jdbcType="VARCHAR" property="customizedWarnId"/>
        <result column="item_id" jdbcType="VARCHAR" property="itemId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="item" jdbcType="VARCHAR" property="item"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="parent_item_id" jdbcType="VARCHAR" property="parentItemId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , customized_warn_id, item_id, "type", item, "operator", code, "value", parent_item_id,
    create_time, update_time,unit
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warn_item"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warn_item"
        where customized_warn_id = #{warnId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_customized_warn_item"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByWarnId">
        delete
        from "tb_cdcmr_customized_warn_item"
        where customized_warn_id = #{warnId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        insert into "tb_cdcmr_customized_warn_item" (id, customized_warn_id, item_id,
                                                     "type", item, "operator",
                                                     code, "value", parent_item_id,
                                                     create_time, update_time)
        values (#{id,jdbcType=VARCHAR}, #{customizedWarnId,jdbcType=VARCHAR}, #{itemId,jdbcType=VARCHAR},
                #{type,jdbcType=VARCHAR}, #{item,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
                #{code,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}, #{parentItemId,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        insert into "tb_cdcmr_customized_warn_item"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="customizedWarnId != null">
                customized_warn_id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="type != null">
                "type",
            </if>
            <if test="item != null">
                item,
            </if>
            <if test="operator != null">
                "operator",
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="value != null">
                "value",
            </if>
            <if test="parentItemId != null">
                parent_item_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="unit != null">
                unit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="customizedWarnId != null">
                #{customizedWarnId,jdbcType=VARCHAR},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="item != null">
                #{item,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="parentItemId != null">
                #{parentItemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        <foreach collection="recordList" item="record" separator=";">
            insert into "tb_cdcmr_customized_warn_item"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    id,
                </if>
                <if test="record.customizedWarnId != null">
                    customized_warn_id,
                </if>
                <if test="record.itemId != null">
                    item_id,
                </if>
                <if test="record.type != null">
                    "type",
                </if>
                <if test="record.item != null">
                    item,
                </if>
                <if test="record.operator != null">
                    "operator",
                </if>
                <if test="record.code != null">
                    code,
                </if>
                <if test="record.value != null">
                    "value",
                </if>
                <if test="record.parentItemId != null">
                    parent_item_id,
                </if>
                <if test="record.createTime != null">
                    create_time,
                </if>
                <if test="record.updateTime != null">
                    update_time,
                </if>
                <if test="record.unit != null">
                    unit,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="record.id != null">
                    #{record.id,jdbcType=VARCHAR},
                </if>
                <if test="record.customizedWarnId != null">
                    #{record.customizedWarnId,jdbcType=VARCHAR},
                </if>
                <if test="record.itemId != null">
                    #{record.itemId,jdbcType=VARCHAR},
                </if>
                <if test="record.type != null">
                    #{record.type,jdbcType=VARCHAR},
                </if>
                <if test="record.item != null">
                    #{record.item,jdbcType=VARCHAR},
                </if>
                <if test="record.operator != null">
                    #{record.operator,jdbcType=VARCHAR},
                </if>
                <if test="record.code != null">
                    #{record.code,jdbcType=VARCHAR},
                </if>
                <if test="record.value != null">
                    #{record.value,jdbcType=VARCHAR},
                </if>
                <if test="record.parentItemId != null">
                    #{record.parentItemId,jdbcType=VARCHAR},
                </if>
                <if test="record.createTime != null">
                    #{record.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="record.updateTime != null">
                    #{record.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="record.unit != null">
                    #{record.unit,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        update "tb_cdcmr_customized_warn_item"
        <set>
            <if test="customizedWarnId != null">
                customized_warn_id = #{customizedWarnId,jdbcType=VARCHAR},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                "type" = #{type,jdbcType=VARCHAR},
            </if>
            <if test="item != null">
                item = #{item,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                "operator" = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                "value" = #{value,jdbcType=VARCHAR},
            </if>
            <if test="parentItemId != null">
                parent_item_id = #{parentItemId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarnItem">
        update "tb_cdcmr_customized_warn_item"
        set customized_warn_id = #{customizedWarnId,jdbcType=VARCHAR},
            item_id            = #{itemId,jdbcType=VARCHAR},
            "type"             = #{type,jdbcType=VARCHAR},
            item               = #{item,jdbcType=VARCHAR},
            "operator"         = #{operator,jdbcType=VARCHAR},
            code               = #{code,jdbcType=VARCHAR},
            "value"            = #{value,jdbcType=VARCHAR},
            parent_item_id     = #{parentItemId,jdbcType=VARCHAR},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            unit               = #{unit, jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>