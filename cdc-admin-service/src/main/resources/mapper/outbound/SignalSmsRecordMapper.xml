<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.outbound.mapper.SignalSmsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.outbound.entity.SignalSmsRecord">
        <id column="id" property="id" />
        <result column="signal_id" property="signalId" />
        <result column="warning_type" property="warningType" />
        <result column="phone" property="phone" />
        <result column="batch_id" property="batchId" />
        <result column="create_time" property="createTime" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="request_content" property="requestContent" />
        <result column="response_content" property="responseContent" />
        <result column="speech_or_sms_id" property="speechOrSmsId" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, signal_id, warning_type, phone, batch_id, create_time, user_id, user_name, 
        request_content, response_content, speech_or_sms_id, status
    </sql>


</mapper> 