<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.outbound.mapper.OutboundPersonMapper">
    <select id="listByRecordIds" resultType="com.iflytek.cdc.admin.outbound.model.vo.OutboundPersonVO">
        select
            p.id,
            p.outbound_record_id,
            p.batch_no,
            p.name,
            p.telephone,
            p.id_card,
            p.relation_id,
            p.ext_property,
            p.manual_result,
            d.dialog as parse_result,
            d.call_time,
            d.task_time,
            d.call_result,
            d.result_code,
            d.end_node,
            d.audio_url
        from app.tb_cdcmr_outbound_person p
        left join app.tb_cdcmr_outbound_result_detail d on d.relation_id = p.relation_id and d.batch_id  = p.batch_no
        where p.outbound_record_id in
        <foreach collection="recordIds" open="(" separator="," close=")" item="recordId">
            #{recordId}
        </foreach>
    </select>
</mapper>