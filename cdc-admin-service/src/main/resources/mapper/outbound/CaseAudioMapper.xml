<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.outbound.mapper.CaseAudioMapper">

    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.outbound.entity.AudioRecord">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="audio_path" jdbcType="VARCHAR" property="audioPath"/>
        <result column="audio_temp_text" jdbcType="VARCHAR" property="audioTempText"/>
        <result column="audio_final_text" jdbcType="VARCHAR" property="audioFinalText"/>
        <result column="business_id" jdbcType="VARCHAR" property="businessId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="updater_id" jdbcType="VARCHAR" property="updaterId"/>
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag"/>
        <result column="object_name" jdbcType="VARCHAR" property="objectName"/>
        <result column="transform_task_id" jdbcType="VARCHAR" property="transformTaskId"/>
        <result column="transform_finish_flag" jdbcType="INTEGER" property="transformFinishFlag"/>
        <result column="audio_name" jdbcType="VARCHAR" property="audioName"/>
        <result column="audio_index" jdbcType="INTEGER" property="audioIndex"/>
        <result column="audio_address" jdbcType="VARCHAR" property="audioAddress"/>
        <result column="audio_time" jdbcType="INTEGER" property="audioTime"/>
        <result column="is_call" jdbcType="VARCHAR" property="isCall"/>
        <result column="call_path" jdbcType="VARCHAR" property="callPath"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, audio_path, audio_temp_text, audio_final_text, business_id, create_time, creator, creator_id,
        update_time, updater_id, updater, delete_flag, object_name, transform_task_id, transform_finish_flag,
        audio_name, audio_index, audio_address, audio_time,is_call,call_path
    </sql>


    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_audio_record
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getCallAudio" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_audio_record
        where business_id = #{businessId,jdbcType=VARCHAR}  and audio_address = #{audioAddress}  and is_call = '1' order by create_time desc limit 1
    </select>

    <update id="updateAudioFinalTextById">
        update tb_cdcmr_audio_record
        set audio_final_text=#{updatedAudioFinalText,jdbcType=VARCHAR},
            update_time=now(),
            transform_finish_flag = 1
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="insertSelective">
        INSERT INTO tb_cdcmr_audio_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="audioPath != null">
                audio_path,
            </if>
            <if test="audioTempText != null">
                audio_temp_text,
            </if>
            <if test="audioFinalText != null">
                audio_final_text,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            create_time,
            update_time,
            <if test="objectName != null">
                object_name,
            </if>
            <if test="transformTaskId != null">
                transform_task_id,
            </if>
            <if test="transformFinishFlag != null">
                transform_finish_flag,
            </if>
            <if test="audioName != null">
                audio_name,
            </if>
            <if test="audioIndex != null">
                audio_index,
            </if>
            <if test="audioTime != null">
                audio_time,
            </if>
            <if test="audioAddressType != null">
                audio_address,
            </if>
            <if test="isCall != null">
                is_call,
            </if>
            <if test="callPath != null">
               call_path
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="audioPath != null">
                #{audioPath,jdbcType=VARCHAR},
            </if>
            <if test="audioTempText != null">
                #{audioTempText,jdbcType=VARCHAR},
            </if>
            <if test="audioFinalText != null">
                #{audioFinalText,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>

            now(),
            now(),

            <if test="objectName != null">
                #{objectName,jdbcType=VARCHAR},
            </if>
            <if test="transformTaskId != null">
                #{transformTaskId,jdbcType=VARCHAR},
            </if>
            <if test="transformFinishFlag != null">
                #{transformFinishFlag,jdbcType=INTEGER},
            </if>
            <if test="audioName != null">
                #{audioName,jdbcType=VARCHAR},
            </if>
            <if test="audioIndex != null">
                #{audioIndex,jdbcType=INTEGER},
            </if>
            <if test="audioTime != null">
                #{audioTime},
            </if>
            <if test="audioAddressType != null">
                #{audioAddressType},
            </if>
            <if test="isCall != null">
                #{isCall},
            </if>
            <if test="callPath != null">
                #{callPath}
            </if>
        </trim>
    </insert>

    <delete id="deleteByIdAndBusinessId">
        delete
        from tb_cdcmr_audio_record
        where id = #{id,jdbcType=VARCHAR}
          and business_id = #{businessId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByPathAndBusinessId">
        delete
        from tb_cdcmr_audio_record
        where audio_path = #{audioPath,jdbcType=VARCHAR}
          and business_id = #{businessId,jdbcType=VARCHAR}
    </delete>

    <select id="findByBusinessId" resultType="com.iflytek.cdc.admin.outbound.model.dto.AudioDTO">
        select id,
               audio_name    as name,
               create_time,
               audio_path    as url,
               audio_address as address,
               audio_time    as time
        from tb_cdcmr_audio_record
        where business_id = #{businessId,jdbcType=VARCHAR}
        order by create_time
    </select>

    <update id="updatefileName">
        update tb_cdcmr_audio_record
        set audio_name = #{name,jdbcType=VARCHAR},
            update_time = now()
        where id=#{id,jdbcType=VARCHAR}
    </update>

    <select id="findMaxIndex" resultType="java.lang.Integer">
        select max(audio_index)
        from tb_cdcmr_audio_record
        where business_id = #{businessId}
    </select>

    <select id="checkIsExistCaseAudio" resultType="java.lang.Integer">
        select count(1)
        from tb_cdcmr_audio_record
        where business_id = #{businessId} and is_call = '1' and (audio_path = #{audioPath} or call_path = #{audioPath})
    </select>

    <update id="updateAudioUrl">
        update tb_cdcmr_audio_record
        set audio_path = #{url,jdbcType=VARCHAR},
            update_time = now()
        where id in(
            select id from tb_cdcmr_audio_record where business_id = #{businessId,jdbcType=VARCHAR} and audio_address = 'call' and is_call = '1' and (audio_path is null or audio_path = '') order by create_time desc limit 1
            )
    </update>

    <update id="updateByPathAndBusinessId">
        update tb_cdcmr_audio_record
        set audio_path = '',
            update_time = now()
        where business_id = #{businessId,jdbcType=VARCHAR} and audio_path = #{url}
    </update>

    <select id="getTempTextByPathAndCaseId" resultType="java.lang.String">
        select audio_temp_text
        from tb_cdcmr_audio_record
        where business_id = #{businessId} and audio_path = #{url} order by create_time desc limit 1
    </select>
</mapper>