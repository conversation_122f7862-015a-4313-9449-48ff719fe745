<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.SyndromeInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.SyndromeInfo">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_syndrome_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="syndrome_code" jdbcType="VARCHAR" property="syndromeCode" />
    <result column="syndrome_name" jdbcType="VARCHAR" property="syndromeName" />
    <result column="symptoms_rule" jdbcType="VARCHAR" property="symptomsRule" />
    <result column="heighten_rule" jdbcType="VARCHAR" property="heightenRule" />
    <result column="cover_event" jdbcType="VARCHAR" property="coverEvent" />
    <result column="is_enable" jdbcType="VARCHAR" property="useStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="dataSynchronTime" />
    <result column="parent_syndrome_code" jdbcType="VARCHAR" property="parentSyndromeCode" />
    <result column="parent_syndrome_name" jdbcType="VARCHAR" property="parentSyndromeName" />
    <result column="incubation" jdbcType="INTEGER" property="incubation" />
    <result column="his_window" jdbcType="INTEGER" property="hisWindow" />
    <result column="forecast_window" jdbcType="INTEGER" property="forecastWindow" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, syndrome_code, syndrome_name, symptoms_rule, heighten_rule, cover_event, is_enable,
    remark, create_user, create_time, update_user, update_time, sync_time, parent_syndrome_code,
    parent_syndrome_name, incubation, his_window, forecast_window
  </sql>

  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.SyndromeInfo">
    <!--@mbg.generated-->
    insert into tb_cdcmr_syndrome_info (id, syndrome_code, syndrome_name,
                                        symptoms_rule, heighten_rule, cover_event,
                                        is_enable, remark, create_user,
                                        create_time, update_user, update_time,
                                        sync_time, parent_syndrome_code, parent_syndrome_name,
                                        incubation, his_window, forecast_window)
    values (#{id,jdbcType=VARCHAR}, #{syndromeCode,jdbcType=VARCHAR}, #{syndromeName,jdbcType=VARCHAR},
            #{symptomsRule,jdbcType=VARCHAR}, #{heightenRule,jdbcType=VARCHAR}, #{coverEvent,jdbcType=VARCHAR},
            #{useStatus,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR},
            localtimestamp, #{updateUser,jdbcType=VARCHAR}, localtimestamp, localtimestamp,
            #{parentSyndromeCode,jdbcType=VARCHAR}, #{parentSyndromeName,jdbcType=VARCHAR},
            #{incubation,jdbcType=INTEGER}, #{hisWindow,jdbcType=INTEGER}, #{forecastWindow,jdbcType=INTEGER})
  </insert>

  <!-- ==========================================================================-->
  <select id="querySyndromeInfo" parameterType="com.iflytek.cdc.admin.dto.SearchSyndromeInfoDTO" resultType="com.iflytek.cdc.admin.entity.SyndromeInfo">
    select id as id, syndrome_code as syndromeCode, syndrome_name as syndromeName,symptoms_rule as symptomsRule, heighten_rule as heightenRule,
    cover_event as coverEvent,is_enable as useStatus, remark as remark, create_user as createUser,create_time as createTime, update_user as updateUser,
    update_time as updateTime, sync_time as dataSynchronTime from tb_cdcmr_syndrome_info where 1=1
    <if test="syndromeName != null and syndromeName != ''">
      and syndrome_name like  concat('%',#{syndromeName,jdbcType=VARCHAR},'%')
    </if>
    <if test="symptomsRule != null and symptomsRule != ''">
      and symptoms_rule like  concat('%',#{symptomsRule,jdbcType=VARCHAR},'%')
    </if>
    <if test="heightenRule != null and heightenRule != ''">
      and heighten_rule like concat('%',#{heightenRule,jdbcType=VARCHAR},'%')
    </if>
    order by syndrome_code
  </select>

  <select id="syndromeInfo" parameterType="com.iflytek.cdc.admin.sdk.entity.SyndromeInfoFilter" resultType="com.iflytek.cdc.admin.sdk.pojo.SyndInfo">
    select id as id, syndrome_code as syndromeCode, syndrome_name as syndromeName,symptoms_rule as symptomsRule, heighten_rule as heightenRule,
    cover_event as coverEvent,is_enable as useStatus, remark as remark, create_user as createUser,create_time as createTime, update_user as updateUser,
    update_time as updateTime, sync_time as dataSynchronTime from tb_cdcmr_syndrome_info
    order by syndrome_code
  </select>

  <select id="syndromeInfoByCode" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.sdk.pojo.SyndInfo">
    select id            as id,
           syndrome_code as syndromeCode,
           syndrome_name as syndromeName,
           symptoms_rule as symptomsRule,
           heighten_rule as heightenRule,
           cover_event   as coverEvent,
           is_enable     as useStatus,
           remark        as remark,
           create_user   as createUser,
           create_time   as createTime,
           update_user   as updateUser,
           update_time   as updateTime,
           sync_time     as dataSynchronTime
    from tb_cdcmr_syndrome_info
    where syndrome_code = #{code,jdbcType=VARCHAR}
  </select>

  <update id="updateSyndromeInfo" parameterType="com.iflytek.cdc.admin.entity.SyndromeInfo">
      update tb_cdcmr_syndrome_info
      <set>
        syndrome_code = #{syndromeCode,jdbcType=VARCHAR},
        syndrome_name = #{syndromeName,jdbcType=VARCHAR},
        symptoms_rule = #{symptomsRule,jdbcType=VARCHAR},
        heighten_rule = #{heightenRule,jdbcType=VARCHAR},
        cover_event = #{coverEvent,jdbcType=VARCHAR},
        is_enable = #{useStatus,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        sync_time = #{dataSynchronTime,jdbcType=TIMESTAMP},
        parent_syndrome_code = #{parentSyndromeCode,jdbcType=VARCHAR},
        parent_syndrome_name = #{parentSyndromeName,jdbcType=VARCHAR},
        incubation = #{incubation,jdbcType=INTEGER},
        his_window = #{hisWindow,jdbcType=INTEGER},
        forecast_window = #{forecastWindow,jdbcType=INTEGER}
      </set>
      where id = #{id,jdbcType=VARCHAR}
  </update>

  <insert id="insertSyndromeInfo" parameterType="com.iflytek.cdc.admin.entity.SyndromeInfo">
    insert into tb_cdcmr_syndrome_info (id, syndrome_code, syndrome_name,
                                        is_enable, create_user, create_time, update_user, update_time, sync_time,
                                        parent_syndrome_code, parent_syndrome_name,
                                        incubation, his_window, forecast_window)
    values
    <foreach collection="list" item="sn" separator="," index="index">
      (#{sn.id,jdbcType=VARCHAR}, #{sn.syndromeCode,jdbcType=VARCHAR}, #{sn.syndromeName,jdbcType=VARCHAR},
       #{sn.useStatus,jdbcType=VARCHAR}, #{sn.createUser,jdbcType=VARCHAR},
       now(), #{sn.updateUser,jdbcType=VARCHAR}, now(),
       now(),
      #{sn.parentSyndromeCode,jdbcType=VARCHAR}, #{sn.parentSyndromeName,jdbcType=VARCHAR},
      #{sn.incubation,jdbcType=INTEGER}, #{sn.hisWindow,jdbcType=INTEGER}, #{sn.forecastWindow,jdbcType=INTEGER})
    </foreach>

  </insert>

  <select id="queryInfoById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_syndrome_info
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <update id="updateSyndromeStatus" parameterType="java.lang.String">
    update tb_cdcmr_syndrome_info
    <set>
      <if test="useStatus != null and useStatus != ''">
        is_enable = #{useStatus,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
        update_time =now(),
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateCodeName" parameterType="java.lang.String">
    update tb_cdcmr_syndrome_info
    <set>
      <if test="updateCodeName != null and updateCodeName != ''">
        syndrome_name=#{updateCodeName,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      update_time =now(),
    </set>
    where syndrome_code=#{updateCode,jdbcType=VARCHAR}
  </update>

  <update id="updateSyndromeInfoStatus" parameterType="com.iflytek.cdc.admin.dto.UpdateSyndDTO">
    <!--@mbg.generated-->
    update tb_cdcmr_syndrome_info
    <set>
      <if test="useStatus != null and useStatus != ''">
        is_enable= #{useStatus,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      update_time=now(),
      sync_time=now(),
    </set>
    where id in (
    <foreach collection="ids" separator="," item="item" index="index">
      #{item}
    </foreach>
    )
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_syndrome_info where is_enable = '1'
  </select>

  <select id="querySyndromeInfoByGrade" resultType="com.iflytek.cdc.admin.dto.SyndromeGradeConfigVO">
    SELECT
    t1.ID AS ID,
    syndrome_code AS syndromeCode,
    syndrome_name AS syndromeName,
    t2.remark AS remark,
    COALESCE ( t2.status,0) AS status,
    (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
    t3.is_deleted = 0)as ruleCount
    FROM
    tb_cdcmr_syndrome_info t1
    LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'syndrome'
    AND t1.syndrome_code = t2.disease_code where 1=1
    <if test="syndromeName != null and syndromeName != ''">
      and t1.syndrome_name like concat('%',#{syndromeName,jdbcType=VARCHAR},'%')
    </if>
    <if test="status != null ">
      and COALESCE ( t2.status,0) = #{status}
    </if>
  </select>
</mapper>