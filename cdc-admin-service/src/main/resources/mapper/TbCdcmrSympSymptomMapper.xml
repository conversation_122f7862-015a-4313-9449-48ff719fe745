<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrSympSymptomMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="symptom_code" jdbcType="VARCHAR" property="symptomCode" />
        <result column="symptom_name" jdbcType="VARCHAR" property="symptomName" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
        <result column="update_time" jdbcType="DATE" property="updateTime" />
        <result column="updater" jdbcType="VARCHAR" property="updater" />
    </resultMap>
    <sql id="Base_Column_List">
        id, symptom_code, symptom_name, remark, "status", is_deleted, update_time, updater
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "tb_cdcmr_symp_symptom"
        where id = #{id,jdbcType=VARCHAR} and is_deleted = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        update  "tb_cdcmr_symp_symptom" set is_deleted = 1
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom" useGeneratedKeys="true">
        insert into "tb_cdcmr_symp_symptom" (id,symptom_code, symptom_name, remark,
                                           "status", is_deleted, update_time,
                                           updater)
        values ( #{id,jdbcType=VARCHAR},#{symptomCode,jdbcType=VARCHAR}, #{symptomName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE},
                #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom" useGeneratedKeys="true">
        insert into "tb_cdcmr_symp_symptom"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="symptomCode != null">
                symptom_code,
            </if>
            <if test="symptomName != null">
                symptom_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="symptomCode != null">
                #{symptomCode,jdbcType=VARCHAR},
            </if>
            <if test="symptomName != null">
                #{symptomName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        update "tb_cdcmr_symp_symptom"
        <set>
            <if test="symptomCode != null">
                symptom_code = #{symptomCode,jdbcType=VARCHAR},
            </if>
            <if test="symptomName != null">
                symptom_name = #{symptomName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        update "tb_cdcmr_symp_symptom"
        set symptom_code = #{symptomCode,jdbcType=VARCHAR},
            symptom_name = #{symptomName,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            "status" = #{status,jdbcType=SMALLINT},
            is_deleted = #{isDeleted,jdbcType=SMALLINT},
            update_time = #{updateTime,jdbcType=DATE},
            updater = #{updater,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        select * from "tb_cdcmr_symp_symptom"
        where is_deleted = 0
        <if test="symptomName!= null and symptomName !=''">
            and symptom_name like concat('%',#{symptomName},'%')
        </if>
        <if test="status != null">
            and "status" = #{status,jdbcType=SMALLINT}
        </if>
        order by id
    </select>
    <select id="getBySymptomCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        select *
        from "tb_cdcmr_symp_symptom"
        where is_deleted = 0
          and symptom_code = #{symptomCode}
    </select>
    <select id="getBySymptomName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        select *
        from "tb_cdcmr_symp_symptom"
        where is_deleted = 0
          and symptom_name = #{symptomName}

    </select>
    <select id="findAll" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom">
        select *
        from tb_cdcmr_symp_symptom
        where is_deleted = 0
    </select>
    <select id="querySymptomInfoByGrade" resultType="com.iflytek.cdc.admin.dto.SymptomGradeConfigVO">
        SELECT
        t2.remark AS remark,
        COALESCE ( t2.status, 0 ) AS status ,t1.* ,
        (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
        t3.is_deleted = 0)as ruleCount from "tb_cdcmr_symp_symptom" t1 LEFT JOIN
        tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'symptom'
        AND t1.symptom_code= t2.disease_code
        where t1.is_deleted = 0
        <if test="symptomName!= null and symptomName !=''">
            and t1.symptom_name like concat('%',#{symptomName},'%')
        </if>
        <if test="status != null ">
            and COALESCE ( t2.status,0) = #{status}
        </if>
        order by id
    </select>
</mapper>