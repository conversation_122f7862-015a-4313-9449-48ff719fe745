<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrSympWarnMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrSympWarn">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="symptom_code" jdbcType="VARCHAR" property="symptomCode"/>
        <result column="symptom_name" jdbcType="VARCHAR" property="symptomName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="incubation" jdbcType="SMALLINT" property="incubation"/>
        <result column="max_life_cycle" jdbcType="SMALLINT" property="maxLifeCycle"/>
        <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode"/>
        <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , symptom_code, symptom_name, remark, "status", is_deleted, update_time, updater,
    incubation, max_life_cycle, sms_send_type_code, sms_send_type_desc
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        update "tb_cdcmr_symp_warn"
        set is_deleted = 1
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarn">
        insert into "tb_cdcmr_symp_warn" (id, symptom_code, symptom_name,
                                        remark, "status", is_deleted,
                                        update_time, updater, incubation,
                                        max_life_cycle,
                                        sms_send_type_code, sms_send_type_desc)
        values (#{id,jdbcType=VARCHAR}, #{symptomCode,jdbcType=VARCHAR}, #{symptomName,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT},
                #{updateTime,jdbcType=DATE}, #{updater,jdbcType=VARCHAR}, #{incubation,jdbcType=SMALLINT},
                #{maxLifeCycle,jdbcType=SMALLINT}, #{smsSendTypeCode,jdbcType=VARCHAR},
                #{smsSendTypeDesc,jdbcType=VARCHAR})
    </insert>
    <select id="getBySymptomCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarn">
        select *
        from tb_cdcmr_symp_warn
        where symptom_code = #{symptomCode,jdbcType=VARCHAR}
          and is_deleted = 0
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarn">
        update "tb_cdcmr_symp_warn"
        <set>
            <if test="symptomCode != null">
                symptom_code = #{symptomCode,jdbcType=VARCHAR},
            </if>
            <if test="symptomName != null">
                symptom_name = #{symptomName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            incubation = #{incubation,jdbcType=SMALLINT},
            max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateStatusByPrimaryKey">
        update "tb_cdcmr_symp_warn"
        set "status" = #{status,jdbcType=SMALLINT}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByWarn">
        update "tb_cdcmr_symp_warn"
        <set>
            <if test="symptomCode != null">
                symptom_code = #{symptomCode,jdbcType=VARCHAR},
            </if>
            <if test="symptomName != null">
                symptom_name = #{symptomName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="incubation != null">
            incubation = #{incubation,jdbcType=SMALLINT},
            </if>
            <if test="maxLifeCycle != null">
            max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>


    <select id="getList" resultType="com.iflytek.cdc.admin.dto.SympWarnDto">
        select * ,(select count(b.id) from tb_cdcmr_symp_warn_rule b where a.id = b.warn_id and
        b.is_deleted = 0) as ruleCount from "tb_cdcmr_symp_warn" a
        where is_deleted = 0
        <if test="symptomName!= null and symptomName !=''">
            and symptom_name like concat('%',#{symptomName},'%')
        </if>
        <if test="status != null">
            and "status" = #{status,jdbcType=SMALLINT}
        </if>
        order by id
    </select>

    <select id="getAllList" resultType="com.iflytek.cdc.admin.dto.SympWarnDto">
        select * from "tb_cdcmr_symp_warn"
        where is_deleted = 0 and "status" = 1
        <if test="symptomCode!= null and symptomName !=''">
            and symptom_code = #{symptomCode}
        </if>
        order by id
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_symp_warn"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getBySymptomName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarn">
        select *
        from tb_cdcmr_symp_warn
        where symptom_name = #{symptomName,jdbcType=VARCHAR}
          and is_deleted = 0
    </select>
</mapper>