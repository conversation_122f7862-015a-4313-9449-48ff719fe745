<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingDiseaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_emerging_disease_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
    <result column="disease_parent_id" jdbcType="VARCHAR" property="diseaseParentId" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="order_flag" jdbcType="INTEGER" property="orderFlag" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator, creator_id, delete_flag, disease_code, disease_name, disease_parent_id, 
    notes, order_flag, "status", update_time, updater, updater_id
  </sql>

  <select id="getEmergingTreeInfo" resultType="com.iflytek.cdc.admin.model.mr.vo.EmergingDiseaseInfoVO">
    select id, disease_code, disease_name, disease_parent_id, order_flag
        from tb_cdcmr_emerging_disease_info
        where delete_flag = '0'
  </select>

  <select id="getAllDiseaseCodeBy" resultType="java.lang.String">
    select disease_code
        from tb_cdcmr_emerging_disease_info
        where disease_code is not null and disease_code != ''
  </select>

  <insert id="insertOrUpdate">
    insert into tb_cdcmr_emerging_disease_info
    (id, disease_code, disease_name, disease_parent_id, notes, status, delete_flag, create_time, creator,
    creator_id, update_time, updater, updater_id, order_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.diseaseCode}, #{item.diseaseName}, #{item.diseaseParentId}, #{item.notes},
      #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator}, #{item.creatorId},
      #{item.updateTime}, #{item.updater}, #{item.updaterId}, #{item.orderFlag})
    </foreach>
    on conflict (id) do update set
    disease_code        = excluded.disease_code,
    disease_name        = excluded.disease_name,
    disease_parent_id   = excluded.disease_parent_id,
    notes               = excluded.notes,
    status              = excluded.status,
    delete_flag         = excluded.delete_flag,
    update_time         = excluded.update_time,
    updater             = excluded.updater,
    updater_id          = excluded.updater_id,
    order_flag          = excluded.order_flag;
  </insert>

  <update id="updateDeleteFlagById">
    update tb_cdcmr_emerging_disease_info
    set delete_flag = '1'
    where id = #{id}
  </update>

  <select id="getEmergingDiseaseCodeByName" resultType="java.lang.String">
    select disease_code
        from tb_cdcmr_emerging_disease_info
        where delete_flag = '0' and disease_name = #{diseaseName} and disease_code is not null
        limit 1
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_emerging_disease_info
    where delete_flag = '0'
  </select>

  <insert id="insertBatch">
    insert into tb_cdcmr_emerging_disease_info
    (id, disease_code, disease_name, disease_parent_id, notes, status, delete_flag, create_time, creator,
    creator_id, update_time, updater, updater_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.diseaseCode}, #{item.diseaseName}, #{item.diseaseParentId}, #{item.notes},
      #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator}, #{item.creatorId},
      #{item.updateTime}, #{item.updater}, #{item.updaterId})
    </foreach>
  </insert>

  <select id="getEmergingDiseaseInfoByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tb_cdcmr_emerging_disease_info
    where 1=1
    <if test="ids != null and ids.size() &gt; 0">
      and disease_parent_id in
      <foreach collection="ids" item="item" separator="," close=")" open="(" index="index">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getDiseaseInfoById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tb_cdcmr_emerging_disease_info
    where id = #{id}
  </select>

  <update id="updateSubDiseaseParent">
    update tb_cdcmr_emerging_disease_info
    set disease_parent_id = #{info.diseaseParentId},
        update_time     = now(),
        updater         = #{loginUserName},
        updater_id      = #{loginUserId}
    where disease_parent_id = #{info.id}
  </update>

  <update id="updateDeleteFlagByIds">
    update tb_cdcmr_emerging_disease_info
    set
    delete_flag = '1',
    update_time = now(),
    updater_id = #{loginUserId},
    updater = #{loginUserName}
    where id in
    <foreach collection="ids" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

  <select id="getDiseaseInfoByCode" resultType="com.iflytek.cdc.admin.model.mr.dto.CommonMasterData">
    select
    id as id,
    disease_code as masterDataCode,
    disease_name as masterDataName,
    disease_parent_id as parentDataId
    from tb_cdcmr_emerging_disease_info
    where delete_flag = '0' and disease_code = #{masterDataCode}
    limit 1
  </select>
</mapper>