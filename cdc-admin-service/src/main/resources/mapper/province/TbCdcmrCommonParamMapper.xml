<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrCommonParamMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCommonParam">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_common_param-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="param_class" jdbcType="VARCHAR" property="paramClass" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, param_class, param_type, param_key, param_value, notes, "status", delete_flag, 
    create_time, update_time
  </sql>
</mapper>