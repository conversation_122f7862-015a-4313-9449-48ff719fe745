<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseWarningRuleMapper">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, disease_info_id, risk_level, rule_name, warning_method, warning_period, warning_period_time, warning_indicator,
        case_scope, population_scope, population_scope_param, area_count, area_level, model_param, warning_threshold,
        event_level, event_report_type, notes, status, delete_flag, creator_id, creator, create_time, updater_id,
        updater, update_time, area_scope, area_scope_param, first_case_warning, first_case_area_level, follow_status, address_type
    </sql>

    <insert id="upsert">
        insert into tb_cdcmr_infected_disease_warning_rule
        (id, disease_info_id, risk_level, rule_name, warning_method, warning_period, warning_period_time, warning_indicator,
        case_scope, population_scope, population_scope_param, area_count, area_level, model_param, warning_threshold,
        event_level, event_report_type, notes, status, delete_flag, creator_id, creator, create_time, updater_id,
        updater, update_time, area_scope, area_scope_param, first_case_warning, first_case_area_level, follow_status, address_type)
        values
        (#{id}, #{diseaseInfoId}, #{riskLevel}, #{ruleName}, #{warningMethod}, #{warningPeriod}, #{warningPeriodTime},
        #{warningIndicator}, #{caseScope}, #{populationScope}, #{populationScopeParam}, #{areaCount}, #{areaLevel},
        #{modelParam}, #{warningThreshold}, #{eventLevel}, #{eventReportType}, #{notes}, #{status}, #{deleteFlag},
        #{creatorId}, #{creator}, #{createTime}, #{updaterId}, #{updater}, #{updateTime}, #{areaScope}, #{areaScopeParam},
        #{firstCaseWarning}, #{firstCaseAreaLevel}, #{followStatus}, #{addressType})
        on conflict(id) do update set
        updater = excluded.updater,
        <if test="diseaseInfoId != null and diseaseInfoId != ''">
            disease_info_id = excluded.disease_info_id,
        </if>
        <if test="riskLevel != null and riskLevel != ''">
            risk_level = excluded.risk_level,
        </if>
        <if test="ruleName != null and ruleName != ''">
            rule_name = excluded.rule_name,
        </if>
        <if test="warningMethod != null and warningMethod != ''">
            warning_method = excluded.warning_method,
        </if>
        <if test="warningPeriod != null and warningPeriod != ''">
            warning_period = excluded.warning_period,
        </if>
        <if test="warningPeriodTime != null">
            warning_period_time = excluded.warning_period_time,
        </if>
        <if test="warningIndicator != null and warningIndicator != ''">
            warning_indicator = excluded.warning_indicator,
        </if>
        <if test="caseScope != null and caseScope != ''">
            case_scope = excluded.case_scope,
        </if>
        <if test="populationScope != null and populationScope != ''">
            population_scope = excluded.population_scope,
        </if>
        <if test="populationScopeParam != null and populationScopeParam != ''">
            population_scope_param = excluded.population_scope_param,
        </if>
        <if test="areaCount != null">
            area_count = excluded.area_count,
        </if>
        <if test="areaLevel != null">
            area_level = excluded.area_level,
        </if>
        <if test="modelParam != null and modelParam != ''">
            model_param = excluded.model_param,
        </if>
        <if test="warningThreshold != null">
            warning_threshold = excluded.warning_threshold,
        </if>
        <if test="eventLevel != null and eventLevel != ''">
            event_level = excluded.event_level,
        </if>
        <if test="eventReportType != null and eventReportType != ''">
            event_report_type = excluded.event_report_type,
        </if>
        <if test="notes != null and notes != ''">
            notes = excluded.notes,
        </if>
        <if test="status != null">
            status = excluded.status,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        <if test="areaScope != null and areaScope != ''">
            area_scope = excluded.area_scope,
        </if>
        <if test="areaScopeParam != null and areaScopeParam != ''">
            area_scope_param = excluded.area_scope_param,
        </if>
        <if test="firstCaseWarning != null and firstCaseWarning != ''">
            first_case_warning = excluded.first_case_warning,
        </if>
        <if test="firstCaseAreaLevel != null and firstCaseAreaLevel != ''">
            first_case_area_level = excluded.first_case_area_level,
        </if>
        <if test="followStatus != null and followStatus != ''">
            follow_status = excluded.follow_status,
        </if>
        <if test="addressType != null and addressType != ''">
            address_type = excluded.address_type,
        </if>
        update_time = excluded.update_time,
        updater_id = excluded.updater_id
    </insert>

    <select id="getListByInfectedDiseaseWarningId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseWarningRule">
        select <include refid="Base_Column_List"/>
        from
        tb_cdcmr_infected_disease_warning_rule r
        where r.delete_flag = '0' and r.status = 1
        and r.disease_info_id = #{diseaseInfoId}
        <if test="riskLevel != null and riskLevel != ''">
            and r.risk_level = #{riskLevel}
        </if>
        <if test="warningMethod != null and warningMethod != ''">
            and r.warning_method = #{warningMethod}
        </if>
        <choose>
            <when test='"0".equals(followStatus)'>
                and (r.follow_status is null or r.follow_status = '' or r.follow_status = '0')
            </when>
            <when test="followStatus != null and followStatus != ''">
                and r.follow_status = #{followStatus}
            </when>
        </choose>
        order by create_time desc
    </select>

    <select id="getRiskRuleCountBy" resultType="com.iflytek.cdc.admin.vo.InfectedWarningRuleDetailVO">
        select
        i.id,
        i.disease_type_code ,
        i.disease_type_name ,
        i.id as diseaseCode ,
        i.disease_name,
        i.parent_disease_id as parentDiseaseCode,
        i.parent_disease_name ,
        r.risk_level ,
        count(r.risk_level) riskLevelCount,
        (select r1.updater from tb_cdcmr_infected_disease_warning_rule r1 where i.id = r1.disease_info_id order by r1.update_time desc limit 1) updater,
        max(r.update_time)   updateTime
        from
        tb_cdcmr_infected_disease_info i
        left join tb_cdcmr_infected_disease_warning_rule r on
        i.id = r.disease_info_id  and r.delete_flag = '0' and r.status = 1
        where
        i.delete_flag = '0' and i.status = 1
        group by
        i.id,
        i.disease_type_code ,
        i.disease_type_name ,
        diseaseCode ,
        i.disease_name,
        parentDiseaseCode,
        i.parent_disease_name ,
        r.risk_level
        order by
        i.disease_code
    </select>


</mapper>
