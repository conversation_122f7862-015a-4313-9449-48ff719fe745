<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrPathogenInfoMapper">

    <sql id="Base_Column_List">
        id, pathogen_name, pathogen_abbr, parent_id, pathogen_class_code, pathogen_class_name, pathogen_type1_id,
        pathogen_type1_name, pathogen_type2_id, pathogen_type2_name, notes, status, delete_flag, creator_id,
        creator, create_time, updater_id, updater, update_time
    </sql>

    <select id="listAll" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrPathogenInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_pathogen_info
        where delete_flag = '0'
    </select>

</mapper>