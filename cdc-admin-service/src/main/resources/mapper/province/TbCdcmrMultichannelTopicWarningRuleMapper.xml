<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicWarningRuleMapper">

    <sql id="Base_Colum_List">
        id, topic_id, model_param, notes, status, delete_flag, create_time, update_time, creator_id,
        creator, updater_id, updater
    </sql>

    <insert id="insertOrUpdate">
        insert into tb_cdcmr_multichannel_topic_warning_rule
        (id, topic_id, model_param, notes, status, delete_flag, create_time, update_time, creator_id, creator, updater_id, updater)
        values
        (#{id}, #{topicId}, #{modelParam}, #{notes}, #{status}, #{deleteFlag}, #{createTime}, #{updateTime}, #{creatorId},
        #{creator}, #{updaterId}, #{updater})
        on conflict(id) do update set
        update_time = now(),
        <if test="modelParam != null and modelParam != ''">
            model_param = excluded.model_param,
        </if>
        <if test="notes != null and notes != ''">
            notes = excluded.notes,
        </if>
        <if test="status != null">
            status = excluded.status,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        updater_id = excluded.updater_id,
        updater = excluded.updater
    </insert>

    <update id="updateRuleByTopicId">
        update tb_cdcmr_multichannel_topic_warning_rule
        set delete_flag = '1'
        where topic_id = #{topicId}
    </update>

    <select id="getWarningRuleByTopicId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicWarningRule">
        select <include refid="Base_Colum_List"/>
        from tb_cdcmr_multichannel_topic_warning_rule
        where topic_id = #{topicId} and delete_flag = '0'
        limit 1
    </select>

</mapper>