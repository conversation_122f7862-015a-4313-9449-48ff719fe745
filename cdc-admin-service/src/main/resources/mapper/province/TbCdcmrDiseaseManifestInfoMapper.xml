<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseManifestInfoMapper">

    <sql id="BASE_COLUMN">
        id, manifest_code, manifest_name, manifest_classify, disease_manifestation, notes, status, delete_flag,
        create_time, creator, creator_id, update_time, updater, updater_id
    </sql>

    <insert id="insert">
        insert into tb_cdcmr_disease_manifest_info
        (id, manifest_code, manifest_name, manifest_classify, disease_manifestation, notes, status, delete_flag,
        create_time, creator, creator_id, update_time, updater, updater_id)
        values
        (#{id}, #{manifestCode}, #{manifestName}, #{manifestClassify}, #{diseaseManifestation}, #{notes}, #{status},
        #{deleteFlag}, #{createTime}, #{creator}, #{creatorId}, #{updateTime}, #{updater}, #{updaterId})
    </insert>

    <update id="update">
        update tb_cdcmr_disease_manifest_info
        <set>
            <if test="manifestCode != null and manifestCode != ''">
                manifest_code = #{manifestCode},
            </if>
            <if test="manifestName != null and manifestName != ''">
                manifest_name = #{manifestName},
            </if>
            <if test="manifestClassify != null and manifestClassify != ''">
                manifest_classify = #{manifestClassify},
            </if>
            <if test="diseaseManifestation != null and diseaseManifestation != ''">
                disease_manifestation = #{diseaseManifestation},
            </if>
            <if test="notes != null and notes != ''">
                notes = #{notes},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="deleteFlag != null and deleteFlag != ''">
                delete_flag = #{deleteFlag},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getDiseaseManifestInfoList"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo">
        select
        i.id, i.manifest_code, i.manifest_name, i.manifest_classify, i.disease_manifestation, i.notes,
        i.status, i.delete_flag, i.create_time, i.creator, i.creator_id,
        case when r.update_time >= i.update_time then r.update_time else i.update_time end update_time,
        case when r.update_time >= i.update_time then r.updater else i.updater end updater,
        case when r.update_time >= i.update_time then r.updater_id else i.updater_id end updater_id
        from tb_cdcmr_disease_manifest_info i
        left join
            (select manifest_id, update_time, updater, updater_id
            from (SELECT manifest_id, update_time, updater, updater_id ,ROW_NUMBER() OVER (PARTITION BY manifest_id ORDER BY update_time DESC) as rn
                  FROM tb_cdcmr_disease_manifest_rule) tmp
            where tmp.rn = 1) r
        on i.id = r.manifest_id
        where i.delete_flag = '0'
        <if test="id != null and id != ''">
            and i.id like concat('%', #{id}, '%')
        </if>
        <if test="manifestName != null and manifestName != ''">
            and i.manifest_name like concat('%', #{manifestName}, '%')
        </if>
        <if test="manifestClassify != null and manifestClassify != ''">
            and i.manifest_classify = #{manifestClassify}
        </if>
        <if test="diseaseManifestation != null and diseaseManifestation != ''">
            and i.disease_manifestation = #{diseaseManifestation}
        </if>
        <if test="status != null and status != ''">
            and i.status = #{status}
        </if>
        order by update_time desc
    </select>

    <select id="queryById" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_disease_manifest_info
        where id = #{id}
    </select>

    <select id="selectManifestInfoBy" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_disease_manifest_info
        where 1=1
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        and delete_flag = '0'
    </select>

    <select id="getAllManifestCode" resultType="java.lang.String">
        select manifest_code
        from tb_cdcmr_disease_manifest_info
        where manifest_code is not null and manifest_code != ''
        and delete_flag = '0'
    </select>

</mapper>