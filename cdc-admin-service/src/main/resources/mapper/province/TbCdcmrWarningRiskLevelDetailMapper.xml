<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningRiskLevelDetailMapper">

    <sql id="Base_Column_List">
        id, risk_level_id, risk_level_desc, check_time_limit, invest_time_limit, judge_time_limit, disease_id,
        disease_name, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, disease_code
    </sql>

    <insert id="insertBatch">
        insert into tb_cdcmr_warning_risk_level_detail
        (id, risk_level_id, risk_level_desc, check_time_limit, invest_time_limit, judge_time_limit, disease_id,
        disease_code, disease_name, warning_type, creator_id, creator, create_time, updater_id, updater, update_time,
        delete_flag)
        values
        <foreach collection="detailList" item="item" separator=",">
            (#{item.id}, #{item.riskLevelId}, #{item.riskLevelDesc}, #{item.checkTimeLimit}, #{item.investTimeLimit},
            #{item.judgeTimeLimit}, #{item.diseaseId}, #{item.diseaseCode}, #{item.diseaseName}, #{item.warningType},
            #{item.creatorId}, #{item.creator}, #{item.createTime}, #{item.updaterId}, #{item.updater}, #{item.updateTime},
            #{item.deleteFlag})
        </foreach>
    </insert>

    <delete id="deleteDetailInfoBy">
        <if test="(diseaseInfoIdList != null and diseaseInfoIdList.size() &gt; 0) or (diseaseCodeList != null and diseaseCodeList.size() &gt; 0)">
            delete from tb_cdcmr_warning_risk_level_detail
            where 1=1 and
            (
            <if test="diseaseInfoIdList != null and diseaseInfoIdList.size() &gt; 0">
                disease_id in
                <foreach collection="diseaseInfoIdList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
                <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
                    or
                </if>
            </if>
            <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
                disease_code in
                <foreach collection="diseaseCodeList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
            </if>
            )
        </if>
    </delete>

    <!-- 通用查询映射结果 -->
    <select id="listRiskLevelDetail" resultType="com.iflytek.cdc.admin.vo.RiskLevelDetailVO">
        select
            r.risk_level_code ,
            r.risk_level_name ,
            r.highlight_color ,
            r.notes ,
            d.id ,
            d.disease_id,
            d.disease_name,
            d.risk_level_id ,
            d.disease_code,
            d.risk_level_desc ,
            d.check_time_limit ,
            d.invest_time_limit ,
            d.judge_time_limit,
            d.creator_id,
            d.creator,
            d.create_time,
            d.updater_id,
            d.updater,
            d.update_time
        from tb_cdcmr_warning_risk_level_detail d
        left join tb_cdcmr_warning_risk_level r on r.id = d.risk_level_id
        where d.delete_flag = '0'
        <if test="warningType != null and warningType != ''">
            and d.warning_type = #{warningType}
        </if>
        <if test="diseaseIds != null and diseaseIds.size() > 0 ">
            and d.disease_id in
            <foreach collection="diseaseIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="detailIds != null and detailIds.size() > 0 ">
            and d.id in
            <foreach collection="detailIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by r.sort

    </select>

    <select id="loadByDiseaseIdAndLevelCode" resultType="com.iflytek.cdc.admin.vo.RiskLevelDetailVO">
        select
            r.risk_level_code ,
            r.risk_level_name ,
            r.highlight_color ,
            r.notes ,
            r.id as risk_level_id,
            d.id ,
            d.risk_level_desc ,
            d.check_time_limit ,
            d.invest_time_limit ,
            d.judge_time_limit,
            d.creator_id,
            d.creator,
            d.create_time,
            d.updater_id,
            d.updater,
            d.update_time,
            d.disease_id,
            d.disease_code
        from tb_cdcmr_warning_risk_level_detail d
        left join tb_cdcmr_warning_risk_level r on r.id = d.risk_level_id
            and d.delete_flag = '0'
        where d.disease_id = #{diseaseId}
        <if test="riskLevelCode != null and riskLevelCode != ''">
            and r.risk_level_code = #{riskLevelCode}
        </if>
        <if test="warningType != null and warningType != ''">
            and d.warning_type = #{warningType}
        </if>
    </select>

    <select id="loadByDiseaseIdAndLevelId" resultType="com.iflytek.cdc.admin.vo.RiskLevelDetailVO">
        select
            r.risk_level_code ,
            r.risk_level_name ,
            r.highlight_color ,
            r.notes ,
            r.id as risk_level_id,
            d.id ,
            d.risk_level_desc ,
            d.check_time_limit ,
            d.invest_time_limit ,
            d.judge_time_limit,
            d.creator_id,
            d.creator,
            d.create_time,
            d.updater_id,
            d.updater,
            d.update_time,
            d.disease_id,
            d.disease_code
        from tb_cdcmr_warning_risk_level_detail d
        left join tb_cdcmr_warning_risk_level r on r.id = d.risk_level_id
            and d.delete_flag = '0'
        where d.disease_id = #{diseaseId}
        <if test="riskLevelId != null and riskLevelId != ''">
            and r.id = #{riskLevelId}
        </if>
    </select>

    <select id="getRiskLevelDetailBy"
            resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_warning_risk_level_detail
        where delete_flag = '0'
        <if test="idList != null and idList.size() &gt; 0">
            and disease_id in
            <foreach close=")" collection="idList" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
