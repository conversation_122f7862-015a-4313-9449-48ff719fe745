<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcdmDataDictMapper">

    <sql id="Base_Column_List">
        id, code, name, attr_count, attr_value_count, notes, status, delete_flag, create_time, update_time, creator_id,
        creator, updater_id, updater
    </sql>

    <insert id="editValueDomain">
        insert into tb_cdcdm_data_dict
        (id, code, name, attr_count, attr_value_count, type, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater)
        values
        (#{id}, #{code}, #{name}, #{attrCount}, #{attrValueCount}, #{type}, #{notes}, #{status}, #{deleteFlag},
        #{createTime}, #{updateTime}, #{creatorId}, #{creator}, #{updaterId}, #{updater})
        on conflict(id)
        do update set
        updater_id = excluded.updater_id,
        <if test="code != null and code != ''">
            code = excluded.code,
        </if>
        <if test="name != null and name != ''">
            name = excluded.name,
        </if>
        <if test="attrCount != null and attrCount != ''">
            attr_count = excluded.attr_count,
        </if>
        <if test="attrValueCount != null and attrValueCount != ''">
            attr_value_count = excluded.attr_value_count,
        </if>
        <if test="type != null and type != ''">
            type = excluded.type,
        </if>
        <if test="notes != null and notes != ''">
            notes = excluded.notes,
        </if>
        <if test="status != null and status != ''">
            status = excluded.status,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        update_time         = excluded.update_time,
        updater             = excluded.updater
    </insert>

    <update id="updateDataDictCount">
        update tb_cdcdm_data_dict
        set attr_value_count = (select count(*) from tb_cdcdm_data_dict_value where data_dict_id = #{dictId} and delete_flag = '0')
        where id = #{dictId}
    </update>

    <select id="selectById" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict">
        select *
        from tb_cdcdm_data_dict
        where delete_flag = '0'
          and id = #{id, jdbcType=VARCHAR}
    </select>

    <select id="getDataDictList" resultType="com.iflytek.cdc.admin.model.dm.vo.DataDictListVO">
        select
        id                  as id,
        name                as name,
        attr_count          as attrCount,
        attr_value_count    as attrValueCount,
        type                as type,
        create_time         as createTime,
        update_time         as updateTime,
        updater             as updater
        from tb_cdcdm_data_dict
        where
        delete_flag = '0'
        <if test="id != null and id != ''">
            and id like concat('%', #{id, jdbcType=VARCHAR}, '%')
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="attrCount != null and attrCount != ''">
            and attr_count = #{attrCount, jdbcType=INTEGER}
        </if>
        order by update_time desc
    </select>

    <select id="countByNameNorId" resultType="int">
        select count(1)
        from tb_cdcdm_data_dict
        where name = #{name}
        <if test="null != excludeId and '' != excludeId">
            and id != #{excludeId, jdbcType=VARCHAR}
        </if>
        and delete_flag = '0'
    </select>

    <select id="getDictIdBy" resultType="java.lang.String">
        select id
        from tb_cdcdm_data_dict
        where name = #{name} and delete_flag = '0'
    </select>

</mapper>