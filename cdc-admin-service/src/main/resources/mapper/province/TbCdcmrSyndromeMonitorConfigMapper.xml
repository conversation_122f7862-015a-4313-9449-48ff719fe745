<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeMonitorConfigMapper">

    <sql id="BASE_COLUMN">
        id, disease_info_id, person_scope, person_scope_param, rule_name, rule_list, condition_type, notes, status,
        delete_flag, create_time, creator, creator_id, update_time, updater, updater_id
    </sql>

    <insert id="batchInsert">
        insert into tb_cdcmr_syndrome_monitor_config
        (id, disease_info_id, person_scope, person_scope_param, rule_name, rule_list, condition_type, notes, status,
        delete_flag, create_time, creator, creator_id, update_time, updater, updater_id)
        VALUES
        <foreach collection="monitorConfigs" item="item" separator=",">
            (#{item.id}, #{item.diseaseInfoId}, #{item.personScope}, #{item.personScopeParam}, #{item.ruleName},
            #{item.ruleList}, #{item.conditionType}, #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime},
            #{item.creator}, #{item.creatorId}, #{item.updateTime}, #{item.updater}, #{item.updaterId})
        </foreach>
    </insert>

    <update id="updateBySyndromeInfoId">
        update tb_cdcmr_syndrome_monitor_config
        set delete_flag = '1'
        where disease_info_id = #{diseaseInfoId}
    </update>

    <select id="queryByDiseaseInfoId" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeMonitorConfig">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_syndrome_monitor_config
        where disease_info_id = #{diseaseInfoId}
        <if test="conditionType != null and conditionType != ''">
            and condition_type = #{conditionType}
        </if>
        and delete_flag = '0'
    </select>

    <select id="getDiagnoseCodeList" resultType="com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO">
        select
        id              as id,
        diagnosis_code  as diagnoseCode,
        diagnosis_name  as diagnoseName,
        diagnosis_type  as diagnoseType
        from tb_cdcmr_infectious_diagnosis
    </select>

    <select id="getSyndromeSubgroupBy" resultType="java.lang.String">
        select rule_list
        from tb_cdcmr_syndrome_monitor_config
        where disease_info_id = #{diseaseInfoId} and delete_flag = '0'
        <if test="conditionType != null and conditionType != ''">
            and condition_type = #{conditionType}
        </if>
        limit 1
    </select>

</mapper>