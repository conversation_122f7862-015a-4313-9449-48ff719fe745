<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicDiseaseWarningRuleMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseWarningRule">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_endemic_disease_warning_rule-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="address_type" jdbcType="VARCHAR" property="addressType" />
    <result column="area_count" jdbcType="BIGINT" property="areaCount" />
    <result column="area_level" jdbcType="BIGINT" property="areaLevel" />
    <result column="area_scope" jdbcType="VARCHAR" property="areaScope" />
    <result column="area_scope_param" jdbcType="VARCHAR" property="areaScopeParam" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="disease_info_id" jdbcType="VARCHAR" property="diseaseInfoId" />
    <result column="event_level" jdbcType="VARCHAR" property="eventLevel" />
    <result column="event_report_type" jdbcType="VARCHAR" property="eventReportType" />
    <result column="first_case_area_level" jdbcType="VARCHAR" property="firstCaseAreaLevel" />
    <result column="first_case_warning" jdbcType="VARCHAR" property="firstCaseWarning" />
    <result column="model_param" jdbcType="VARCHAR" property="modelParam" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="population_scope" jdbcType="VARCHAR" property="populationScope" />
    <result column="population_scope_param" jdbcType="VARCHAR" property="populationScopeParam" />
    <result column="risk_level" jdbcType="VARCHAR" property="riskLevel" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
    <result column="warning_indicator" jdbcType="VARCHAR" property="warningIndicator" />
    <result column="warning_method" jdbcType="VARCHAR" property="warningMethod" />
    <result column="warning_period" jdbcType="VARCHAR" property="warningPeriod" />
    <result column="warning_period_time" jdbcType="BIGINT" property="warningPeriodTime" />
    <result column="warning_threshold" jdbcType="NUMERIC" property="warningThreshold" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, address_type, area_count, area_level, area_scope, area_scope_param, create_time, 
    creator, creator_id, delete_flag, disease_info_id, event_level, event_report_type, 
    first_case_area_level, first_case_warning, model_param, notes, population_scope, 
    population_scope_param, risk_level, rule_name, "status", update_time, updater, updater_id, 
    warning_indicator, warning_method, warning_period, warning_period_time, warning_threshold
  </sql>

  <select id="getListByEndemicDiseaseWarningId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from
    tb_cdcmr_endemic_disease_warning_rule r
    where r.delete_flag = '0' and r.status = 1
    and r.disease_info_id = #{diseaseInfoId}
    <if test="riskLevel != null and riskLevel != ''">
      and r.risk_level = #{riskLevel}
    </if>
    <if test="warningMethod != null and warningMethod != ''">
      and r.warning_method = #{warningMethod}
    </if>
    order by create_time desc
  </select>

  <select id="getRiskRuleCountBy" resultType="com.iflytek.cdc.admin.vo.EndemicWarningRuleDetailVO">
     select
        i.id,
        i.disease_code,
        i.disease_name,
        r.risk_level ,
        count(r.risk_level) riskLevelCount,
        (select r1.updater from tb_cdcmr_endemic_disease_warning_rule r1 where i.id = r1.disease_info_id order by r1.update_time desc limit 1) updater,
        max(r.update_time)   updateTime
        from
        tb_cdcmr_endemic_disease_info i
        left join tb_cdcmr_endemic_disease_warning_rule r on
        i.id = r.disease_info_id  and r.delete_flag = '0' and r.status = 1
        where
        i.delete_flag = '0' and i.status = 1
        group by
        i.id,
        i.disease_code,
        i.disease_name,
        r.risk_level
        order by
        i.disease_code
  </select>

    <insert id="upsert">
        insert into tb_cdcmr_endemic_disease_warning_rule
        (id, address_type, area_count, area_level, area_scope, area_scope_param, create_time,
        creator, creator_id, delete_flag, disease_info_id, event_level, event_report_type,
        first_case_area_level, first_case_warning, model_param, notes, population_scope,
        population_scope_param, risk_level, rule_name, status, update_time, updater, updater_id,
        warning_indicator, warning_method, warning_period, warning_period_time, warning_threshold)
        values
        (#{id}, #{addressType}, #{areaCount}, #{areaLevel}, #{areaScope}, #{areaScopeParam}, #{createTime},
        #{creator}, #{creatorId}, #{deleteFlag}, #{diseaseInfoId}, #{eventLevel}, #{eventReportType},
        #{firstCaseAreaLevel}, #{firstCaseWarning}, #{modelParam}, #{notes}, #{populationScope},
        #{populationScopeParam}, #{riskLevel}, #{ruleName}, #{status}, #{updateTime}, #{updater}, #{updaterId},
        #{warningIndicator}, #{warningMethod}, #{warningPeriod}, #{warningPeriodTime}, #{warningThreshold})
        on conflict(id) do update set
        update_time = now(),
        <if test="addressType != null and addressType != ''">
            address_type = excluded.address_type,
        </if>
        <if test="areaCount != null">
            area_count = excluded.area_count,
        </if>
        <if test="areaLevel != null">
            area_level = excluded.area_level,
        </if>
        <if test="areaScope != null and areaScope != ''">
            area_scope = excluded.area_scope,
        </if>
        <if test="areaScopeParam != null and areaScopeParam != ''">
            area_scope_param = excluded.area_scope_param,
        </if>
        <if test="diseaseInfoId != null and diseaseInfoId != ''">
            disease_info_id = excluded.disease_info_id,
        </if>
        <if test="eventLevel != null and eventLevel != ''">
            event_level = excluded.event_level,
        </if>
        <if test="eventReportType != null and eventReportType != ''">
            event_report_type = excluded.event_report_type,
        </if>
        <if test="firstCaseAreaLevel != null and firstCaseAreaLevel != ''">
            first_case_area_level = excluded.first_case_area_level,
        </if>
        <if test="firstCaseWarning != null and firstCaseWarning != ''">
            first_case_warning = excluded.first_case_warning,
        </if>
        <if test="modelParam != null and modelParam != ''">
            model_param = excluded.model_param,
        </if>
        <if test="notes != null and notes != ''">
            notes = excluded.notes,
        </if>
        <if test="populationScope != null and populationScope != ''">
            population_scope = excluded.population_scope,
        </if>
        <if test="populationScopeParam != null and populationScopeParam != ''">
            population_scope_param = excluded.population_scope_param,
        </if>
        <if test="riskLevel != null and riskLevel != ''">
            risk_level = excluded.risk_level,
        </if>
        <if test="ruleName != null and ruleName != ''">
            rule_name = excluded.rule_name,
        </if>
        <if test="warningIndicator != null and warningIndicator != ''">
            warning_indicator = excluded.warning_indicator,
        </if>
        <if test="warningMethod != null and warningMethod != ''">
            warning_method = excluded.warning_method,
        </if>
        <if test="warningPeriod != null and warningPeriod != ''">
            warning_period = excluded.warning_period,
        </if>
        <if test="warningPeriodTime != null">
            warning_period_time = excluded.warning_period_time,
        </if>
        <if test="warningThreshold != null">
            warning_threshold = excluded.warning_threshold,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        <if test="status != null">
            status = excluded.status,
        </if>
        updater_id = excluded.updater_id,
        updater = excluded.updater
    </insert>
</mapper>