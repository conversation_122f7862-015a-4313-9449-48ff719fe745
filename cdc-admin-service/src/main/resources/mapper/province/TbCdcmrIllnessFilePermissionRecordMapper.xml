<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessFilePermissionRecordMapper">


    <select id="getAuthApplyRecordListByUserId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord">
        SELECT
            r.id,
            r.auth_code,
            r.auth_name,
            r.archive_id,
            r.overview_json,
            r.status,
            r.create_time,
            r.creator,
            r.valid_start_time,
            r.valid_end_time,
            r.update_time,
            r.updater
        FROM tb_cdcmr_illness_file_permission_record r
        JOIN app.tb_cdcmr_illness_file_approval_auth a
        ON r.org_id = a.org_id AND r.auth_code = a.auth_code  AND a.delete_flag = '0'  AND a.approval_user_id = #{loginUserId}
        WHERE r.delete_flag = '0'
        <if test="record.applyStartTime != null and record.applyEndTime != null">
            and r.create_time between #{record.applyStartTime} and #{record.applyEndTime}
        </if>
        <if test="record.creator!= null and record.creator!= ''">
            and r.creator like concat('%', #{record.creator}, '%')
        </if>
        <if test="record.authCode!= null and record.authCode!= ''">
            and r.auth_code = #{record.authCode}
        </if>
        <choose>
            <when test='record.status != "0"'>
                and r.status in ('-1','1')
            </when>
            <otherwise>
                and r.status = #{record.status}
            </otherwise>
        </choose>
        order by r.create_time desc
    </select>


    <select id="getAuthApplyRecordListByCreateUserId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord">
        SELECT
        r.id,
        r.auth_code,
        r.auth_name,
        r.archive_id,
        r.overview_json,
        r.status,
        r.create_time,
        r.creator,
        r.valid_start_time,
        r.valid_end_time,
        r.update_time,
        r.updater
        FROM tb_cdcmr_illness_file_permission_record r
        WHERE r.delete_flag = '0'  and r.creator_id = #{loginUserId}
        <if test="record.applyStartTime != null and record.applyEndTime != null">
            and create_time between #{record.applyStartTime} and #{record.applyEndTime}
        </if>
        <if test="record.status!=null and record.status!=''">
            and r.status = #{record.status}
       </if>
        order by r.create_time desc
    </select>
</mapper>