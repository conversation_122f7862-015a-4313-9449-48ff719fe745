<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicConfigMapper">

    <sql id="Base_Column_List">
        id, topic_id, channel_type, data_id, data_name, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater, data_parent_id
    </sql>

    <update id="updateByTopicId">
        update tb_cdcmr_multichannel_topic_config
        set delete_flag = '1'
        where topic_id = #{topicId}
    </update>

    <select id="queryByTopicId" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_multichannel_topic_config
        where topic_id = #{topicId} and delete_flag = '0'
        <if test="channelType != null and channelType != ''">
            and channel_type = #{channelType}
        </if>
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into tb_cdcmr_multichannel_topic_config
        (id, topic_id, channel_type, data_id, data_name, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater, data_parent_id)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.id}, #{item.topicId}, #{item.channelType}, #{item.dataId}, #{item.dataName}, #{item.notes},
            #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.updateTime}, #{item.creatorId},
            #{item.creator}, #{item.updaterId}, #{item.updater}, #{item.dataParentId})
        </foreach>
    </insert>

</mapper>