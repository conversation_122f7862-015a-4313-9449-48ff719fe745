<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningRiskLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel">
        <id column="id" property="id" />
        <result column="risk_level_code" property="riskLevelCode" />
        <result column="risk_level_name" property="riskLevelName" />
        <result column="highlight_color" property="highlightColor" />
        <result column="notes" property="notes" />
        <result column="status" property="status" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
        <result column="updater_id" property="updaterId" />
        <result column="updater" property="updater" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, risk_level_code, risk_level_name, highlight_color, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater, sort
    </sql>

    <select id="getWarningRiskLevelList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_warning_risk_level
        where status = 1 and delete_flag = '0'
        order by sort desc
    </select>

</mapper>
