<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessFileApprovalAuthMapper">


    <select id="queryIllnessFileApprovalAuthByOrgId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFileApprovalAuth">
        select
        auth_code,
        string_agg(approval_user_id,',') as  approvalUserId,
        string_agg(approval_user_name,',') as approvalUserName,
        Max(update_time) as updateTime
        from app.tb_cdcmr_illness_file_approval_auth
        where delete_flag ='0'
        <if test="orgId!= null and orgId!= ''">
            and org_id = #{orgId}
        </if>
        group by auth_code
        order by auth_code
    </select>
</mapper>