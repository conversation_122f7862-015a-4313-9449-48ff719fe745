<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessRecordAuthorityMapper">

    <sql id="chooseAddress">
        <if test="provinceCode != null and provinceCode != ''">
            and province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <insert id="upsert">
        insert into tb_cdcmr_illness_record_authority
        (id, auth_code, auth_class, province_code, province_name, city_code, city_name, district_code, district_name,
        person_info, status, delete_flag, create_time, creator_id, creator, update_time, update_id, updater)
        values
        (#{id}, #{authCode}, #{authClass}, #{provinceCode}, #{provinceName}, #{cityCode}, #{cityName}, #{districtCode},
        #{districtName}, #{personInfo}, #{status}, #{deleteFlag}, #{createTime}, #{creatorId}, #{creator},
        #{updateTime}, #{updateId}, #{updater})
        on conflict (id) do update set
        update_time = excluded.update_time,
        <if test="authCode != null and authCode != ''">
            auth_code = excluded.auth_code,
        </if>
        <if test="authClass != null and authClass != ''">
            auth_class = excluded.auth_class,
        </if>
        province_code = excluded.province_code,
        province_name = excluded.province_name,
        city_code = excluded.city_code,
        city_name = excluded.city_name,
        district_code = excluded.district_code,
        district_name = excluded.district_name,
        <if test="personInfo != null and personInfo != ''">
            person_info = excluded.person_info,
        </if>
        <if test="status != null">
            status = excluded.status,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        update_id = excluded.update_id,
        updater = excluded.updater
    </insert>

    <select id="listAuthBy" resultType="com.iflytek.cdc.admin.model.mr.vo.EDRAuthInfoVO">
        select
        id as id,
        auth_code as authCode,
        auth_class as authClass,
        province_code as provinceCode,
        city_code as cityCode,
        district_code as districtCode,
        person_info as personInfo,
        CONCAT(COALESCE(province_name, ''), COALESCE(city_name, ''), COALESCE(district_name, '')) as areaScope,
        status as status,
        update_time as operatingTime
        from tb_cdcmr_illness_record_authority
        where delete_flag = '0'
        <include refid="chooseAddress"/>
        <if test="authCode != null and authCode != ''">
            and auth_code = #{authCode}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="startDate != null and endDate != null">
            and update_time between #{startDate} and #{endDate}
        </if>
        order by update_time desc
    </select>

    <select id="listPersonByAuthCode" resultType="java.lang.String">
        select person_info
        from tb_cdcmr_illness_record_authority
        where delete_flag = '0' and status = 1
        and auth_code = #{authCode}
    </select>

</mapper>