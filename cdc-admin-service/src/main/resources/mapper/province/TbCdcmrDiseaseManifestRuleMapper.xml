<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseManifestRuleMapper">

    <sql id="BASE_COLUMN">
        id, manifest_id, rule_code, rule_name, rule_data_source, rule_list, notes, status, delete_flag, create_time,
        creator, creator_id, update_time, updater, updater_id
    </sql>

    <insert id="insertOrUpdate">
        insert into tb_cdcmr_disease_manifest_rule
        (id, manifest_id, rule_code, rule_name, rule_data_source, rule_list, notes, status, delete_flag, create_time,
        creator, creator_id, update_time, updater, updater_id)
        values
        <foreach collection="manifestRules" item="item" separator=",">
            (#{item.id}, #{item.manifestId}, #{item.ruleCode}, #{item.ruleName}, #{item.ruleDataSource},
            #{item.ruleList}, #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator},
            #{item.creatorId}, #{item.updateTime}, #{item.updater}, #{item.updaterId})
        </foreach>
        on conflict (id) do update set
        manifest_id         = excluded.manifest_id,
        rule_code           = excluded.rule_code,
        rule_name           = excluded.rule_name,
        rule_data_source    = excluded.rule_data_source,
        rule_list           = excluded.rule_list,
        notes               = excluded.notes,
        status              = excluded.status,
        delete_flag         = excluded.delete_flag,
        update_time         = excluded.update_time,
        updater             = excluded.updater,
        updater_id          = excluded.updater_id
    </insert>

    <insert id="batchInsert">
        insert into tb_cdcmr_disease_manifest_rule
        (id, manifest_id, rule_code, rule_name, rule_data_source, rule_list, notes, status, delete_flag, create_time,
        creator, creator_id, update_time, updater, updater_id)
        values
        <foreach collection="manifestRules" item="item" separator=",">
            (#{item.id}, #{item.manifestId}, #{item.ruleCode}, #{item.ruleName}, #{item.ruleDataSource},
            #{item.ruleList}, #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator},
            #{item.creatorId}, #{item.updateTime}, #{item.updater}, #{item.updaterId})
        </foreach>
    </insert>

    <update id="updateByDiseaseManifestId">
        update tb_cdcmr_disease_manifest_rule
        set delete_flag = '1'
        where manifest_id = #{diseaseManifestId}
    </update>

    <select id="queryByManifestId" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_disease_manifest_rule
        where manifest_id = #{manifestId} and delete_flag = '0'
    </select>

    <select id="selectManifestRuleBy" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_disease_manifest_rule
        where delete_flag = '0'
        <if test="manifestId != null and manifestId != ''">
            and manifest_id = #{manifestId}
        </if>
    </select>

</mapper>