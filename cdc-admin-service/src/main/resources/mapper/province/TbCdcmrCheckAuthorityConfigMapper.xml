<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrCheckAuthorityConfigMapper">

    <sql id="Base_Column_List">
        id, type, name, disease_code, disease_name, population_scope, time_limit, check_level, check_person, status,
        delete_flag, creator, creator_id, create_time, updater, updater_id, update_time
    </sql>

    <insert id="upsert">
        insert into tb_cdcmr_check_authority_config
        (id, type, name, disease_code, disease_name, population_scope, time_limit, check_level, check_person, status,
        delete_flag, creator, creator_id, create_time, updater, updater_id, update_time)
        values
        (#{id}, #{type}, #{name}, #{diseaseCode}, #{diseaseName}, #{populationScope}, #{timeLimit}, #{checkLevel},
        #{checkPerson}, #{status}, #{deleteFlag}, #{creator}, #{creatorId}, #{createTime}, #{updater}, #{updaterId},
        #{updateTime})
        on conflict(id) do update set
        updater = excluded.updater,
        <if test="type != null and type != ''">
            type = excluded.type,
        </if>
        <if test="name != null and name != ''">
            name = excluded.name,
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            disease_code = excluded.disease_code,
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            disease_name = excluded.disease_name,
        </if>
        <if test="populationScope != null and populationScope != ''">
            population_scope = excluded.population_scope,
        </if>
        <if test="timeLimit != null and timeLimit != ''">
            time_limit = excluded.time_limit,
        </if>
        <if test="checkLevel != null and checkLevel != ''">
            check_level = excluded.check_level,
        </if>
        <if test="checkPerson != null and checkPerson != ''">
            check_person = excluded.check_person,
        </if>
        <if test="status != null">
            status = excluded.status,
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = excluded.delete_flag,
        </if>
        updater_id = excluded.updater_id,
        update_time = excluded.update_time
    </insert>

    <select id="getCheckConfigBy" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrCheckAuthorityConfig">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_check_authority_config
        where delete_flag = '0'
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and disease_name like concat('%', #{diseaseName}, '%')
        </if>
        <if test="timeLimit != null">
            and time_limit = #{timeLimit}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="startDate != null and endDate != null">
            and update_time between #{startDate} and #{endDate}
        </if>
        order by update_time desc, id
    </select>


</mapper>