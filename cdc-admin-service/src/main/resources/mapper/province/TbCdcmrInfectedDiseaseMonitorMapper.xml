<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseMonitorMapper">

    <sql id="Base_Column_List">
        m.id, m.infected_disease_info_id, m.disease_code, m.disease_name, m.type, m.title, m.monitor_definition, m.notes,
        m.status, m.delete_flag, m.create_time, m.creator, m.creator_id, m.update_time, m.updater, m.updater_id
    </sql>

    <insert id="insertBatch">
        insert into tb_cdcmr_infected_disease_monitor
        (id, infected_disease_info_id, disease_code, disease_name, type, title, monitor_definition, notes, status, delete_flag,
        create_time, creator, creator_id, update_time, updater, updater_id)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.id}, #{item.infectedDiseaseInfoId}, #{item.diseaseCode}, #{item.diseaseName}, #{item.type}, #{item.title}, #{item.monitorDefinition},
            #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator}, #{item.creatorId}, #{item.updateTime}, #{item.updater},
            #{item.updaterId})
        </foreach>
    </insert>

    <update id="updateByDiseaseId">
        update tb_cdcmr_infected_disease_monitor
        set delete_flag = '1'
        where infected_disease_info_id = #{infectedDiseaseInfoId}
    </update>

    <select id="getConfigByDiseaseId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseMonitor">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_infected_disease_info i
        left join tb_cdcmr_infected_disease_monitor m
        on i.id = m.infected_disease_info_id
        where i.delete_flag = '0' and m.delete_flag = '0'
        <if test="infectedDiseaseInfoId != null and infectedDiseaseInfoId != ''">
            and m.infected_disease_info_id = #{infectedDiseaseInfoId}
        </if>
    </select>
</mapper>