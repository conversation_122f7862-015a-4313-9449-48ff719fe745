<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningChargePersonMapper">
    <sql id="Base_Column">
        id,
        warning_type,
        disease_id,
        disease_code,
        disease_name,
        risk_level_detail_id,
        deal_person_type,
        deal_person_info,
        creator,
        creator_id,
        create_time,
        updater,
        updater_id,
        update_time,
        delete_flag,
        status
    </sql>

    <insert id="mergeInto">
        insert into tb_cdcmr_warning_charge_person (
            id,
            warning_type,
            disease_id,
            disease_code,
            disease_name,
            risk_level_detail_id,
            deal_person_type,
            deal_person_info,
            creator,
            creator_id,
            create_time,
            updater,
            updater_id,
            update_time,
            delete_flag,
            status)
        values
        <foreach collection="entities" item="entity" separator="," >
            (#{entity.id},
                #{entity.warningType},
                #{entity.diseaseId},
                #{entity.diseaseCode},
                #{entity.diseaseName},
                #{entity.riskLevelDetailId},
                #{entity.dealPersonType},
                #{entity.dealPersonInfo},
                #{entity.creator},
                #{entity.creatorId},
                #{entity.createTime},
                #{entity.updater},
                #{entity.updaterId},
                #{entity.updateTime},
                #{entity.deleteFlag},
            #{entity.status})
        </foreach>
        on conflict (id)
        do update set
            updater = EXCLUDED.updater,
            warning_type = EXCLUDED.warning_type,
            disease_id = EXCLUDED.disease_id,
            disease_code = EXCLUDED.disease_code,
            disease_name = EXCLUDED.disease_name,
            risk_level_detail_id = EXCLUDED.risk_level_detail_id,
            deal_person_type = EXCLUDED.deal_person_type,
            deal_person_info = EXCLUDED.deal_person_info,
            delete_flag = EXCLUDED.delete_flag,
            updater_id = EXCLUDED.updater_id,
            update_time = EXCLUDED.update_time,
            status = EXCLUDED.status
    </insert>

    <sql id="chooseTable">
        <if test="warningType == 'infected'">
            join tb_cdcmr_infected_disease_info i
            on d.disease_info_id = i.id and d.warning_type = 'infected'
        </if>
        <if test="warningType == 'syndrome'">
            join tb_cdcmr_syndrome_disease_info i
            on d.disease_info_id = i.id and d.warning_type = 'syndrome'
        </if>
        <if test="warningType == 'emerging'">
            join tb_cdcmr_emerging_disease_info i
            on d.disease_info_id = i.id and d.warning_type = 'emerging'
        </if>
        <if test="warningType == 'endemic'">
            join tb_cdcmr_endemic_disease_info i
            on d.disease_info_id = i.id and d.warning_type = 'endemic'
        </if>
    </sql>

    <select id="listDiseaseConfig" resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">

        select
            rd.id as risk_level_detail_id,
            l.risk_level_code,
            l.risk_level_name,
            rd.risk_level_desc,
            l.highlight_color,
            p.id as id,
            p.deal_person_type,
            p.deal_person_info ,
            p.status,
            i.id as disease_id,
            d.disease_code,
            d.disease_name,
        spc.id as signal_push_config_id,
        spc.is_repeat as is_repeat,
        TO_CHAR(spc.repeat_start_time, 'HH24:MI:SS') as repeat_start_time,
        TO_CHAR(spc.repeat_end_time, 'HH24:MI:SS') as repeat_end_time,
        spc.repeat_frequency as repeat_frequency,
        p.status as is_enable
        from tb_cdcmr_disease_warning d
        <include refid="chooseTable"/>
        join tb_cdcmr_warning_risk_level l on l.delete_flag = '0'
        left join tb_cdcmr_warning_risk_level_detail rd on rd.risk_level_id = l.id
        <if test="riskLevelDetailId != null and riskLevelDetailId != ''">
            and rd.id = #{riskLevelDetailId}
        </if>
        left join tb_cdcmr_warning_charge_person p on p.disease_code = d.disease_code and p.risk_level_detail_id = rd.id
        left join tb_cdcmr_signal_push_configuration spc
        on spc.warning_charge_person_table_id = p.id
        where i.delete_flag = '0'
        <if test="timeLimitType != null and timeLimitType != ''">
            and rd.warning_type = #{timeLimitType}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and d.disease_code = #{diseaseCode}
        </if>
        <if test="diseaseCodes != null and diseaseCodes.size() > 0">
            and d.disease_code in
            <foreach collection="diseaseCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="riskLevelId != null and riskLevelId != ''">
            and l.id = #{riskLevelId}
        </if>

    </select>

    <select id="loadById" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson">
        select <include refid="Base_Column"/>
        from tb_cdcmr_warning_charge_person
        where id = #{id}
    </select>

    <select id="bulkLoadByIds" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson">
        select <include refid="Base_Column"/>
        from tb_cdcmr_warning_charge_person
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="getDealPersonInfoBy" resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">
        select <include refid="Base_Column"/>
        from tb_cdcmr_warning_charge_person
        where status = '1' and
        <foreach collection="dtoList" index="index" item="item" separator="or" open="(" close=")">
            disease_code = #{item.diseaseCode} and risk_level_detail_id = #{item.riskLevelDetailId}
        </foreach>
    </select>


    <select id="loadByDiseaseCode" resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">
        select <include refid="Base_Column"/>
        from tb_cdcmr_warning_charge_person
        where 1=1 and
            disease_code = #{diseaseCode} and warning_type = #{warningType}
        limit 1
    </select>

    <select id="getDealPersonInfoByDiseaseCodeAndRiskLevelDetailId"
            resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson">
        SELECT <include refid="Base_Column"/>
        FROM tb_cdcmr_warning_charge_person
        WHERE 1=1
        AND disease_code = #{diseaseCode}
        <if test="riskLevelDetailId != null">
            AND risk_level_detail_id = #{riskLevelDetailId}
        </if>
        LIMIT 1
    </select>

    <select id="listMultichannelDiseaseConfig" resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">
        select
        p.id as id,
        p.deal_person_type,
        p.deal_person_info ,
        p.status,
        p.disease_id,
        mt.id as diseaseCode,
        mt.topic_name as diseaseName,
        spc.id as signal_push_config_id,
        spc.is_repeat as is_repeat,
        TO_CHAR(spc.repeat_start_time, 'HH24:MI:SS') as repeat_start_time,
        TO_CHAR(spc.repeat_end_time, 'HH24:MI:SS') as repeat_end_time,
        spc.repeat_frequency as repeat_frequency,
        p.status as is_enable
        from tb_cdcmr_multichannel_topic mt
        left join tb_cdcmr_warning_charge_person p on p.disease_code = mt.id
        left join tb_cdcmr_signal_push_configuration spc
        on spc.warning_charge_person_table_id = p.id
        where 1=1 and mt.delete_flag = '0'
        <if test="diseaseCode != null and diseaseCode != ''">
            and mt.id = #{diseaseCode}
        </if>
        <if test="diseaseCodes != null and diseaseCodes.size() > 0">
            and mt.topic_code in
            <foreach collection="diseaseCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listIntegratedDiseaseConfig" resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">
        select
        id,
        warning_type,
        disease_id,
        disease_code,
        disease_name,
        risk_level_detail_id,
        deal_person_type,
        deal_person_info,
        creator,
        creator_id,
        create_time,
        updater,
        updater_id,
        update_time,
        delete_flag,
        status as isEnable
        from tb_cdcmr_warning_charge_person
        where
        warning_type = #{warningType}
        limit 1
    </select>

    <select id="getIntegratedWarningDealPersonInfo"
            resultType="com.iflytek.cdc.admin.vo.WarningChargePersonVO">
        select <include refid="Base_Column"/>
        from tb_cdcmr_warning_charge_person
        where status = '1' and warning_type = #{warningType}
        limit 1
    </select>


</mapper>
