<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcdmDataDictValueMapper">

    <sql id="Base_Column_List">
        id, data_dict_id, code, name, description, parent_id, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater
    </sql>

    <insert id="batchInsertIntoValue">
        insert into tb_cdcdm_data_dict_value
        (id, data_dict_id, code, name, description, parent_id, notes, status, delete_flag, create_time, update_time,
        creator_id, creator, updater_id, updater,sort_order)
        values
        <foreach collection="dictValues" item="item" separator=",">
            (#{item.id}, #{item.dataDictId}, #{item.code}, #{item.name}, #{item.description}, #{item.parentId},
            #{item.notes}, #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.updateTime}, #{item.creatorId},
            #{item.creator}, #{item.updaterId}, #{item.updater},#{item.sortOrder})
        </foreach>
    </insert>

    <update id="updateValueDetail">
        update tb_cdcdm_data_dict_value
        set delete_flag = '1', update_time = now(), updater_id = #{loginUerId}, updater = #{loginUserName},sort_order = null
        where id = #{id}
    </update>

    <update id="updateValueDomainDetail">
        <foreach collection="dictValues" item="item" index="index" separator=";">
            update tb_cdcdm_data_dict_value
            <set>
                <if test="item.dataDictId != null and item.dataDictId != ''">
                    data_dict_id = #{item.dataDictId},
                </if>
                <if test="item.code != null and item.code != ''">
                    code = #{item.code},
                </if>
                <if test="item.name != null and item.name != ''">
                    name = #{item.name},
                </if>
                <if test="item.description != null and item.description != ''">
                    description = #{item.description},
                </if>
                <if test="item.parentId != null and item.parentId != ''">
                    parent_id = #{item.parentId},
                </if>
                <if test="item.notes != null and item.notes != ''">
                    notes = #{item.notes},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.deleteFlag != null and item.deleteFlag != ''">
                    delete_flag = #{item.deleteFlag},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updaterId != null and item.updaterId != ''">
                    updater_id = #{item.updaterId},
                </if>
                <if test="item.updater != null and item.updater != ''">
                    updater = #{item.updater},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <select id="getValueDomainDetailList" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue">
        select *
        from tb_cdcdm_data_dict_value
        where data_dict_id = #{dataDictId} and delete_flag = '0'
        order by sort_order asc, update_time desc
    </select>

    <select id="getDataDetailBy" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue">
        select v.id, v.code, v.name, v.parent_id, d.type, v.data_dict_id
        from tb_cdcdm_data_dict d
        join tb_cdcdm_data_dict_value v
        on d.id = v.data_dict_id
        where v.delete_flag = '0'
        <if test="id != null and id != ''">
            and d.id = #{id}
        </if>
        <if test="ids != null and ids.size() > 0">
            and d.id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="code != null and code != ''">
            and d.code = #{code}
        </if>
        <if test="name != null and name != ''">
            and d.name = #{name}
        </if>
        order by v.sort_order asc, v.update_time desc
    </select>

    <select id="listNameByCode" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_data_dict_value
        where delete_flag = '0'
        <if test="id != null and id != ''">
            and data_dict_id = #{id}
        </if>
        <if test="codeList != null and codeList.size() &gt; 0">
            and code in
            <foreach collection="codeList" item="item" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        order by sort_order asc, update_time desc
    </select>

    <select id="getValueByKey" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue">
        select v.id, v.code, v.name, v.parent_id
        from tb_cdcdm_data_dict d
        join tb_cdcdm_data_dict_value v
        on d.id = v.data_dict_id
        where v.delete_flag = '0'
        <if test="id != null and id != ''">
            and d.id = #{id}
        </if>
        <if test="code != null and code != ''">
            and d.code = #{code}
        </if>
        <if test="name != null and name != ''">
            and d.name = #{name}
        </if>
        order by v.sort_order asc, v.update_time desc
    </select>
    <update id="updateSortOrderByAdd">
        update tb_cdcdm_data_dict_value
        set sort_order = sort_order + #{size}
        where data_dict_id = #{dataDictId} and delete_flag = '0'
    </update>
    <update id="updateSortOrderByUpdateOne">
        update tb_cdcdm_data_dict_value
        set sort_order = 1
        where id = #{id}
    </update>
    <update id="updateSortOrderByUpdate">
        update tb_cdcdm_data_dict_value
        set sort_order = sort_order + 1
        where data_dict_id = #{dataDictId} and delete_flag = '0'
        and sort_order &lt; #{sortOrder}
    </update>
    <update id="updateSortOrderByDelete">
        update tb_cdcdm_data_dict_value
        set sort_order = sort_order - 1
        where data_dict_id = #{dataDictId} and delete_flag = '0'
        and sort_order &gt; #{sortOrder}
    </update>
    <update id="saveDragSort">
        UPDATE tb_cdcdm_data_dict_value as a
            SET sort_order = v.sort_order
        FROM (
        VALUES
            <foreach collection="list" item="item" separator=",">
                (#{item.id}, #{item.sortOrder})
            </foreach>
            ) AS v(id, sort_order)
        WHERE a.id = v.id
        and a.data_dict_id = #{dataDictId}
    </update>
</mapper>