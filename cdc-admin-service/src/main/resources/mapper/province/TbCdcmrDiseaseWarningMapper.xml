<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseWarningMapper">

    <insert id="insertOrUpdateBatch">
        insert into tb_cdcmr_disease_warning
        (id, disease_info_id, disease_code, disease_name, warning_type, warning_priority, update_time, updater_id,
        updater, delete_flag, status)
        values
        <foreach collection="entities" item="entity" separator="," >
            (#{entity.id}, #{entity.diseaseInfoId}, #{entity.diseaseCode}, #{entity.diseaseName}, #{entity.warningType},
            #{entity.warningPriority}, #{entity.updateTime}, #{entity.updaterId}, #{entity.updater},
            #{entity.deleteFlag}, #{entity.status})
        </foreach>
        on conflict(disease_code, warning_type) do update set
        update_time = now(),
        disease_name = excluded.disease_name,
        delete_flag = excluded.delete_flag,
        status = excluded.status,
        updater_id = excluded.updater_id,
        updater = excluded.updater
    </insert>

    <update id="updateWarningRulePriority">
        update tb_cdcmr_disease_warning
        set
        update_time = now(),
        <if test="warningPriority != null and warningPriority != ''">
            warning_priority = #{warningPriority},
        </if>
        updater_id = #{updaterId},
        updater = #{updater}
        where disease_info_id = #{diseaseInfoId} and warning_type = #{warningType}
    </update>

    <delete id="deleteAll">
        delete from tb_cdcmr_disease_warning
        where warning_type = #{warningType};
    </delete>

    <select id="listAll" resultType="com.iflytek.cdc.admin.model.mr.vo.InfectedDiseaseWarningVO">
        select
        i.id                    as id,
        i.id                    as diseaseCode,
        i.disease_name          as diseaseName,
        i.infected_class_code 	as infectedClassCode,
        i.infected_class_name 	as infectedClassName,
        i.disease_type_code     as diseaseTypeCode,
        i.disease_type_name     as diseaseTypeName,
        i.parent_disease_id     as parentDiseaseCode,
        i.parent_disease_name   as parentDiseaseName,
        i.notes                 as notes,
        i.status                as status,
        i.delete_flag           as deleteFlag,
        i.create_time           as createTime,
        i.update_time           as updateTime,
        i.creator_id            as creatorId,
        i.creator               as creator,
        i.updater_id            as updaterId,
        i.updater               as updater
        from tb_cdcmr_infected_disease_info i
        where i.delete_flag='0'
        <if test="diseaseClassCode != null and diseaseClassCode != ''">
            and i.infected_class_code = #{diseaseClassCode}
        </if>
        <if test="diseaseTypeCode != null and diseaseTypeCode != ''">
            and i.disease_type_code = #{diseaseTypeCode}
        </if>
    </select>

    <select id="getWarningRulePriority" resultType="java.util.Map">
        select warning_priority
        from tb_cdcmr_disease_warning
        where 1=1
        <if test="diseaseCode != null and diseaseCode != ''">
            and disease_code #= {diseaseCode}
        </if>
        <if test="diseaseInfoId != null and diseaseInfoId != ''">
            and disease_info_id = #{diseaseInfoId}
        </if>
        <if test="warningType != null and warningType != ''">
            and warning_type = #{warningType}
        </if>
    </select>


</mapper>