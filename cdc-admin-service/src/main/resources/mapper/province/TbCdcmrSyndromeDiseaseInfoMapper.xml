<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeDiseaseInfoMapper">

    <sql id="BASE_COLUMN">
        id, disease_code, disease_name, disease_parent_id, notes, status, delete_flag, create_time, creator, creator_id,
        update_time, updater, updater_id, order_flag
    </sql>

    <insert id="insertOrUpdate">
        insert into tb_cdcmr_syndrome_disease_info
        (id, disease_code, disease_name, disease_parent_id, notes, status, delete_flag, create_time, creator,
        creator_id, update_time, updater, updater_id, order_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.diseaseCode}, #{item.diseaseName}, #{item.diseaseParentId}, #{item.notes},
            #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator}, #{item.creatorId},
            #{item.updateTime}, #{item.updater}, #{item.updaterId}, #{item.orderFlag})
        </foreach>
        on conflict (id) do update set
        disease_code        = excluded.disease_code,
        disease_name        = excluded.disease_name,
        disease_parent_id   = excluded.disease_parent_id,
        notes               = excluded.notes,
        status              = excluded.status,
        delete_flag         = excluded.delete_flag,
        update_time         = excluded.update_time,
        updater             = excluded.updater,
        updater_id          = excluded.updater_id,
        order_flag          = excluded.order_flag;
    </insert>

    <insert id="insertBatch">
        insert into tb_cdcmr_syndrome_disease_info
        (id, disease_code, disease_name, disease_parent_id, notes, status, delete_flag, create_time, creator,
        creator_id, update_time, updater, updater_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.diseaseCode}, #{item.diseaseName}, #{item.diseaseParentId}, #{item.notes},
            #{item.status}, #{item.deleteFlag}, #{item.createTime}, #{item.creator}, #{item.creatorId},
            #{item.updateTime}, #{item.updater}, #{item.updaterId})
        </foreach>
    </insert>

    <update id="updateDeleteFlagById">
        update tb_cdcmr_syndrome_disease_info
        set delete_flag = '1'
        where id = #{id}
    </update>

    <update id="updateDeleteFlagByIds">
        update tb_cdcmr_syndrome_disease_info
        set
        delete_flag = '1',
        update_time = now(),
        updater_id = #{loginUserId},
        updater = #{loginUserName}
        where id in
        <foreach collection="ids" item="item" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
    </update>

    <update id="updateSubDiseaseParent">
        update tb_cdcmr_syndrome_disease_info
        set
        disease_parent_id = #{parentDiseaseId},
        update_time = now(),
        updater_id = #{loginUserId},
        updater = #{loginUserName}
        where disease_parent_id = #{id}
    </update>

    <update id="update">
        update tb_cdcmr_syndrome_disease_info
        <set>
            <if test="diseaseCode != null and diseaseCode != ''">
                disease_code = #{diseaseCode},
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                disease_name = #{diseaseName},
            </if>
            <if test="notes != null and notes != ''">
                notes = #{notes},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="deleteFlag != null and deleteFlag != ''">
                delete_flag = #{deleteFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="creatorId != null and creatorId != ''">
                creator_id = #{creatorId},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator},
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="orderFlag != null and orderFlag != ''">
                order_flag = #{orderFlag},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getSyndromeTreeInfo" resultType="com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO">
        select id, disease_code, disease_name, disease_parent_id, order_flag
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0'
    </select>

    <select id="listAll" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0'
    </select>

    <select id="loadById" resultType="com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO">
        select id, disease_code, disease_name, disease_parent_id
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0' and id = #{id}
    </select>

    <select id="loadByCode" resultType="com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO">
        select id, disease_code, disease_name, disease_parent_id
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0' and disease_code = #{code}
    </select>

    <select id="getAllDiseaseCodeBy" resultType="java.lang.String">
        select disease_code
        from tb_cdcmr_syndrome_disease_info
        where disease_code is not null and disease_code != ''
    </select>

    <select id="getSyndromeDiseaseCodeByName" resultType="java.lang.String">
        select disease_code
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0' and disease_name = #{diseaseName} and disease_code is not null
        limit 1
    </select>

    <select id="loadByIdList" resultType="com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO">
        select id, disease_code, disease_name, disease_parent_id
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0'
        <if test="idList != null and idList.size() &gt; 0">
            and id in
            <foreach collection="idList" separator="," close=")" open="(" item="item" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeDiseaseInfoByIds"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_syndrome_disease_info
        where 1=1
        <if test="ids != null and ids.size() &gt; 0">
            and disease_parent_id in
            <foreach collection="ids" item="item" separator="," close=")" open="(" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getDiseaseInfoById" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo">
        select <include refid="BASE_COLUMN"/>
        from tb_cdcmr_syndrome_disease_info
        where id = #{id}
    </select>

    <select id="getDiseaseInfoByCode" resultType="com.iflytek.cdc.admin.model.mr.dto.CommonMasterData">
        select
        id as id,
        disease_code as masterDataCode,
        disease_name as masterDataName,
        disease_parent_id as parentDataId
        from tb_cdcmr_syndrome_disease_info
        where delete_flag = '0' and disease_code = #{masterDataCode}
        limit 1
    </select>

</mapper>