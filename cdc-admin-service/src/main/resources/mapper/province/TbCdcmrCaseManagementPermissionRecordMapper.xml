<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrCaseManagementPermissionRecordMapper">


    <select id="getAuthApplyRecordListByUserId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord">
        SELECT
        r.id,
        r.case_id,
        r.apply_reason,
        r.reject_reason,
        r.status,
        r.create_time,
        r.creator,
        r.valid_start_time,
        r.valid_end_time,
        r.update_time,
        r.updater
        FROM tb_cdcmr_case_management_permission_record r
        JOIN app.tb_cdcmr_illness_file_approval_auth a
        ON r.org_id = a.org_id AND r.auth_code = a.auth_code  AND a.delete_flag = '0'
        AND a.auth_code = 'recordRetrieval' AND a.approval_user_id = #{loginUserId}
        WHERE r.delete_flag = '0'
        <if test = "record.moduleType != null and record.moduleType != ''">
            AND r.module_type = #{record.moduleType}
        </if>
        <if test="record.applyStartTime != null and record.applyEndTime != null">
            and r.create_time between #{record.applyStartTime} and #{record.applyEndTime}
        </if>
        <if test="record.creatorId!= null and record.creatorId!= ''">
            and r.creator_id = #{record.creatorId}
        </if>
        <if test="record.authCode!= null and record.authCode!= ''">
            and r.auth_code = #{record.authCode}
        </if>
        <if test="record.status!= null and record.status!= ''">
           and r.status = #{record.status}
        </if>
        <if test="record.caseId!=null and record.caseId!=''">
            and r.case_id like concat('%', #{record.caseId}, '%')
        </if>
        <if test="record.id!=null and record.id!=''">
            and r.id like concat('%', #{record.id}, '%')
        </if>
        order by r.create_time desc
    </select>

    <select id="getAuthApplyRecordListByCreateUserId"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord">
        SELECT
        r.id,
        r.case_id,
        r.apply_reason,
        r.reject_reason,
        r.status,
        r.create_time,
        r.creator,
        r.valid_start_time,
        r.valid_end_time,
        r.update_time,
        r.updater
        FROM tb_cdcmr_case_management_permission_record r
        WHERE r.delete_flag = '0'  and r.creator_id = #{loginUserId}
        <if test = "record.moduleType != null and record.moduleType != ''">
            AND module_type = #{record.moduleType}
        </if>
        <if test="record.applyStartTime != null and record.applyEndTime != null">
            and create_time between #{record.applyStartTime} and #{record.applyEndTime}
        </if>
        <if test="record.approveStartTime != null and record.approveEndTime != null">
            and update_time between #{record.approveStartTime} and #{record.approveEndTime}
            and r.status in ('1','-1')
        </if>
        <if test="record.updaterId!= null and record.updaterId!= ''">
            and r.updater_id = #{record.updaterId}
            and r.status in ('1','-1')
        </if>
        <if test="record.status!=null and record.status!=''">
            and r.status = #{record.status}
        </if>
        <if test="record.caseId!=null and record.caseId!=''">
            and r.case_id like concat('%', #{record.caseId}, '%')
        </if>
        <if test="record.id!=null and record.id!=''">
            and r.id like concat('%', #{record.id}, '%')
        </if>
        order by r.create_time desc
    </select>

    <select id="getCreateUserList"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord">
         SELECT DISTINCT
            r.creator_id,
            r.creator
        FROM tb_cdcmr_case_management_permission_record r
        JOIN app.tb_cdcmr_illness_file_approval_auth a
        ON r.org_id = a.org_id AND r.auth_code = a.auth_code  AND a.delete_flag = '0'
        AND a.auth_code = 'recordRetrieval' AND a.approval_user_id = #{loginUserId}
        WHERE r.delete_flag = '0'
        <if test = "moduleType!= null and moduleType!= ''">
            AND r.module_type = #{moduleType}
        </if>
    </select>

    <select id="getUpdateUserList"
            resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord">
        SELECT DISTINCT
            r.updater_id,
            r.updater
        FROM tb_cdcmr_case_management_permission_record r
        WHERE r.delete_flag = '0'  and r.creator_id = #{loginUserId}
        and r.status != '0'
        <if test = "moduleType!= null and moduleType!= ''">
            AND r.module_type = #{moduleType}
        </if>
    </select>
</mapper>