<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicMapper">

    <sql id="Base_Column_List">
        id, topic_code, topic_name, notes, status, delete_flag, create_time, update_time, creator_id, creator,
        updater_id, updater, follow_status
    </sql>

    <insert id="editTopic">
        insert into tb_cdcmr_multichannel_topic
        (id, topic_code, topic_name, notes, status, delete_flag, create_time, update_time, creator_id, creator,
        updater_id, updater, follow_status)
        values
        (#{id}, #{topicCode}, #{topicName}, #{notes}, #{status}, #{deleteFlag}, #{createTime}, #{updateTime},
        #{creatorId}, #{creator}, #{updaterId}, #{updater}, #{followStatus})
        on conflict (id) do update set
        update_time = now(),
        <if test="topicName != null and topicName != ''">
            topic_name = #{topicName},
        </if>
        <if test="notes != null and notes != ''">
            notes = #{notes},
        </if>
        <if test="status != null and status != ''">
            status = #{status},
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            delete_flag = #{deleteFlag},
        </if>
        <if test="followStatus != null and followStatus != ''">
            follow_status = #{followStatus},
        </if>
        updater_id = excluded.updater_id,
        updater = excluded.updater
    </insert>

    <select id="getTopicList" resultType="com.iflytek.cdc.admin.model.mr.vo.MultichannelTopicInfoVO">
        select
        id              as topicId,
        topic_name      as topicName,
        notes           as notes,
        updater         as updater,
        update_time     as updateTime,
        follow_status   as followStatus
        from tb_cdcmr_multichannel_topic
        where delete_flag = '0' and status = 1
        <if test="topicName != null and topicName != ''">
            and topic_name like concat('%', #{topicName}, '%')
        </if>
        <if test="followStatus != null and followStatus != ''">
            and follow_status like concat('%', #{followStatus}, '%')
        </if>
        order by create_time desc
    </select>

    <select id="queryByTopicId" resultType="com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopic">
        select <include refid="Base_Column_List"/>
        from tb_cdcmr_multichannel_topic
        where delete_flag = '0'
        <if test="topicId != null and topicId != ''">
            and id = #{topicId}
        </if>
        <if test="topicName != null and topicName != ''">
            and topic_name = #{topicName}
        </if>
    </select>

    <select id="listAllTopicName" resultType="java.lang.String">
        select topic_name
        from tb_cdcmr_multichannel_topic
        where delete_flag = '0'
    </select>


</mapper>