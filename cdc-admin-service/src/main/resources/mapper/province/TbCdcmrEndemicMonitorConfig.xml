<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicMonitorConfigMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_endemic_monitor_config-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="disease_info_id" jdbcType="VARCHAR" property="diseaseInfoId" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="person_scope" jdbcType="VARCHAR" property="personScope" />
    <result column="person_scope_param" jdbcType="VARCHAR" property="personScopeParam" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="symptom_rule_list" jdbcType="VARCHAR" property="symptomRuleList" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="updater_id" jdbcType="VARCHAR" property="updaterId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator, creator_id, delete_flag, disease_info_id, notes, person_scope, 
    person_scope_param, "status", symptom_rule_list, update_time, updater, updater_id
  </sql>

  <select id="queryByDiseaseInfoId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tb_cdcmr_endemic_monitor_config
    where disease_info_id = #{diseaseInfoId}
    and delete_flag = '0'
  </select>

  <update id="updateByEndemicInfoId">
    update tb_cdcmr_endemic_monitor_config
    set delete_flag = '1'
    where disease_info_id = #{diseaseInfoId}
  </update>

  <insert id="batchInsert">
    insert into tb_cdcmr_endemic_monitor_config
    (id, create_time, creator, creator_id, delete_flag, disease_info_id, notes,
    person_scope, person_scope_param, status, symptom_rule_list, update_time, updater, updater_id)
    VALUES
    <foreach collection="monitorConfigs" item="item" separator=",">
      (#{item.id}, #{item.createTime}, #{item.creator}, #{item.creatorId}, #{item.deleteFlag},
      #{item.diseaseInfoId}, #{item.notes}, #{item.personScope}, #{item.personScopeParam},
      #{item.status}, #{item.symptomRuleList}, #{item.updateTime}, #{item.updater}, #{item.updaterId})
    </foreach>
  </insert>

  <select id="getDiagnoseCodeList" resultType="com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO">
    select
    id, disease_info_id, pathogen_rule_list, person_scope, person_scope_param, status, symptom_rule_list
    from tb_cdcmr_endemic_monitor_config
    where delete_flag = '0'
  </select>

</mapper>