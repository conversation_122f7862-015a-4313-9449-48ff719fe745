<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataFormTemplateMapper">

    <sql id="Base_Column_List">
        form_template_detail_id, form_template_code, form_template_name, config_info, config_json, model_id, model_name,
        model_version_id, is_enable, is_deleted, create_time, creator, update_time, updater, key_field, master_table_id,
        master_table, fields_config, model_group, web_config, related_disease, business_key, retrieve_table,
        es_index_name, es_index_template
    </sql>

    <insert id="insert">
        insert into tb_cdcdm_data_form_template
        (form_template_detail_id, form_template_code, form_template_name, config_info, config_json, model_id, model_name,
        model_version_id, is_enable, is_deleted, create_time, creator, update_time, updater, key_field, master_table_id, master_table,
        fields_config, model_group, web_config, related_disease, business_key, retrieve_table, es_index_name, es_index_template)
        values
        (#{formTemplateDetailId}, #{formTemplateCode}, #{formTemplateName}, #{configInfo}, #{configJson}, #{modelId}, #{modelName},
        #{modelVersionId}, #{isEnable}, #{isDeleted}, #{createTime}, #{creator}, #{updateTime}, #{updater}, #{keyField}, #{masterTableId},
        #{masterTable}, #{fieldsConfig}, #{modelGroup}, #{webConfig}, #{relatedDisease}, #{businessKey}, #{retrieveTable},
        #{esIndexName}, #{esIndexTemplate})
    </insert>

    <update id="update">
        update tb_cdcdm_data_form_template
        <set>
            <if test="formTemplateCode != null and formTemplateCode != ''">
                form_template_code = #{formTemplateCode},
            </if>
            <if test="formTemplateName != null and formTemplateName != ''">
                form_template_name = #{formTemplateName},
            </if>
            <if test="configInfo != null and configInfo != ''">
                config_info = #{configInfo},
            </if>
            <if test="configJson != null and configJson != ''">
                config_json = #{configJson},
            </if>
            <if test="modelId != null and modelId != ''">
                model_id = #{modelId},
            </if>
            <if test="modelName != null and modelName != ''">
                model_name = #{modelName},
            </if>
            <if test="modelVersionId != null and modelVersionId != ''">
                model_version_id = #{modelVersionId},
            </if>
            <if test="isEnable != null and isEnable != ''">
                is_enable = #{isEnable},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted = #{isDeleted},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            <if test="keyField != null and keyField != ''">
                key_field = #{keyField},
            </if>
            <if test="masterTableId != null and masterTableId != ''">
                master_table_id = #{masterTableId},
            </if>
            <if test="masterTable != null and masterTable != ''">
                master_table = #{masterTable},
            </if>
            <if test="fieldsConfig != null and fieldsConfig != ''">
                fields_config = #{fieldsConfig},
            </if>
            <if test="modelGroup != null and modelGroup != ''">
                model_group = #{modelGroup},
            </if>
            <if test="webConfig != null and webConfig != ''">
                web_config = #{webConfig},
            </if>
            <if test="relatedDisease != null and relatedDisease != ''">
                related_disease = #{relatedDisease},
            </if>
            <if test="businessKey != null and businessKey != ''">
                business_key = #{businessKey},
            </if>
            <if test="retrieveTable != null and retrieveTable != ''">
                retrieve_table = #{retrieveTable},
            </if>
            <if test="esIndexName != null and esIndexName != ''">
                es_index_name = #{esIndexName},
            </if>
            <if test="esIndexTemplate != null and esIndexTemplate != ''">
                es_index_template = #{esIndexTemplate},
            </if>
        </set>
        where form_template_detail_id = #{formTemplateDetailId}
    </update>

    <update id="updateModelVersionByModelId">
        update tb_cdcdm_data_form_template
        <set>
            <if test="modelVersionId != null and modelVersionId != ''">
                model_version_id = #{modelVersionId},
            </if>
        </set>
        where model_id = #{modelId}
    </update>

    <update id="updateEsIndexTemplateById">
        update tb_cdcdm_data_form_template
        set es_index_template = #{esIndexTemplate}
        where form_template_detail_id = #{formTemplateDetailId}
    </update>

    <select id="queryById" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_data_form_template
        where form_template_detail_id = #{formTemplateDetailId}
    </select>

    <select id="getAllModelTemplate" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_data_form_template
        where 1=1
        <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
            and form_template_detail_id = #{formTemplateDetailId}
        </if>
        <if test="modelId != null and modelId != ''">
            and model_id = #{modelId}
        </if>
        <if test="configInfo != null and configInfo != ''">
            and config_info = #{configInfo}
        </if>
        <if test="modelGroup != null and modelGroup != ''">
            and model_group = #{modelGroup}
        </if>
        <if test="formTemplateCode != null and formTemplateCode != ''">
            and form_template_code like concat ('%', #{formTemplateCode}, '%')
        </if>
        <if test="formTemplateName != null and formTemplateName != ''">
            and form_template_name like concat ('%', #{form_template_name}, '%')
        </if>
    </select>

    <select id="getConfigByModelId"
            resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_data_form_template
        where model_id = #{modelId}
        limit 1
    </select>

    <select id="getConfigInfoByDiseaseCode" resultType="java.lang.String">
        select config_info
        from tb_cdcdm_data_form_template
        where related_disease = #{diseaseCode}
        limit 1
    </select>

</mapper>