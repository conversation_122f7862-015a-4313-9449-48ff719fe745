<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.DataModelConfigMapper">

    <sql id="ModelVersion">
        model_version_id, model_id, model_label, version, status, note, is_enable, is_deleted, create_time, creator,
        update_time, updater, publish_time, publisher, invalid_time, invalidator
    </sql>

    <sql id="DataModelForm">
        model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt,
        form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model
    </sql>

    <insert id="insertIntoDataModel">
        insert into tb_cdcdm_data_model
        (model_id, model_name, model_type, is_built_in, note, latest_version)
        values
        (#{modelId, jdbcType=VARCHAR}, #{modelName, jdbcType=VARCHAR}, #{modelType, jdbcType=VARCHAR},
        #{isBuiltIn, jdbcType=INTEGER}, #{note, jdbcType=VARCHAR}, #{latestVersion, jdbcType=VARCHAR})
        on conflict (model_id)
        do update set
        model_name = excluded.model_name,
        <if test="note != null and note != ''">
            note = excluded.note,
        </if>
        <if test="latestVersion != null and latestVersion != ''">
            latest_version = excluded.latest_version,
        </if>
        model_type = excluded.model_type
    </insert>

    <insert id="insertIntoDataModelVersion">
        insert into tb_cdcdm_data_model_version
        (model_version_id, model_id, model_label, version, status, note, is_enable, is_deleted, create_time, creator,
        update_time, updater, publish_time, publisher, invalid_time, invalidator)
        values
        (#{modelVersionId, jdbcType=VARCHAR}, #{modelId, jdbcType=VARCHAR}, #{modelLabel, jdbcType=VARCHAR},
        #{version, jdbcType=VARCHAR}, #{status, jdbcType=VARCHAR}, #{note, jdbcType=VARCHAR},
        #{isEnable, jdbcType=INTEGER}, #{isDeleted, jdbcType=INTEGER}, #{createTime, jdbcType=TIMESTAMP},
        #{creator, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP}, #{updater, jdbcType=VARCHAR} ,
        #{publishTime, jdbcType=TIMESTAMP}, #{publisher, jdbcType=VARCHAR}, #{invalidTime, jdbcType=TIMESTAMP}, #{invalidator, jdbcType=VARCHAR})
        on conflict (model_version_id)
        do update set
        model_label     = excluded.model_label,
        note            = excluded.note,
        update_time     = excluded.update_time,
        updater         = excluded.updater
    </insert>

    <insert id="editModelForm">
        insert into tb_cdcdm_data_model_form
        (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column,
        form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model)
        values
        (#{modelFormId, jdbcType=VARCHAR}, #{modelId, jdbcType=VARCHAR}, #{modelVersionId, jdbcType=VARCHAR},
        #{formName, jdbcType=VARCHAR}, #{isRepeat, jdbcType=INTEGER}, #{maxRepeatCnt, jdbcType=INTEGER},
        #{formIdentityColumn, jdbcType=VARCHAR}, #{formJson, jdbcType=VARCHAR}, #{isDeleted, jdbcType=INTEGER},
        #{createTime, jdbcType=TIMESTAMP}, #{creator, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP},
        #{updater, jdbcType=VARCHAR}, #{orderFlag, jdbcType=INTEGER}, #{quoteModel, jdbcType=VARCHAR})
        on conflict (model_form_id)
        do update set
        update_time = excluded.update_time,
        <if test="formName != null and formName != ''">
            form_name = excluded.form_name,
        </if>
        <if test="isRepeat != null and isRepeat != ''">
            is_repeat = excluded.is_repeat,
        </if>
        <if test="maxRepeatCnt != null and maxRepeatCnt != ''">
            max_repeat_cnt = excluded.max_repeat_cnt,
        </if>
        <if test="formIdentityColumn != null and formIdentityColumn != ''">
            form_identity_column = excluded.form_identity_column,
        </if>
        <if test="formJson != null and formJson != ''">
            form_json = excluded.form_json,
        </if>
        <if test="isDeleted != null and isDeleted != ''">
            is_deleted = excluded.is_deleted,
        </if>
        <if test="orderFlag != null and orderFlag != ''">
            order_flag = excluded.order_flag,
        </if>
        <if test="quoteModel != null and quoteModel != ''">
            quote_model = excluded.quote_model,
        </if>
        updater = excluded.updater
    </insert>

    <insert id="saveModelFormData">
        insert into tb_cdcdm_data_form_value
        (id, form_template_detail_id, model_id, model_version_id, model_form_id, model_form_identity_id, model_form_name,
        data_json, create_time, creator, update_time, updater)
        values
        (#{id, jdbcType=VARCHAR}, #{formTemplateDetailId, jdbcType=VARCHAR}, #{modelId, jdbcType=VARCHAR},
        #{modelVersionId, jdbcType=VARCHAR}, #{modelFormId, jdbcType=VARCHAR}, #{modelFormIdentityId, jdbcType=VARCHAR},
        #{modelFormName, jdbcType=VARCHAR}, #{dataJson, jdbcType=VARCHAR}, #{createTime, jdbcType=TIMESTAMP},
        #{creator, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP}, #{updater, jdbcType=VARCHAR})
        on conflict (id)
        do update set
        data_json               = excluded.data_json,
        update_time             = excluded.update_time,
        updater                 = excluded.updater
    </insert>

    <!-- 批量新增数据 -->
    <insert id="batchInsertFormList">
        insert into tb_cdcdm_data_model_form
        (model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, form_identity_column, form_json,
        is_deleted, create_time, creator, update_time, updater, order_flag, quote_model)
        values
        <foreach collection="tbCdcdmDataModelForms" item="item" separator=",">
            (#{item.modelFormId}, #{item.modelId}, #{item.modelVersionId}, #{item.formName}, #{item.isRepeat},
            #{item.maxRepeatCnt}, #{item.formIdentityColumn}, #{item.formJson}, #{item.isDeleted}, #{item.createTime},
            #{item.creator}, #{item.updateTime}, #{item.updater}, #{item.orderFlag}, #{item.quoteModel})
        </foreach>
    </insert>

    <update id="updateDataModel">
        update tb_cdcdm_data_model_version
        set
        update_time = now(),
        updater = #{loginUserName, jdbcType=VARCHAR},
        invalid_time = now(),
        <if test="status != null and status != ''">
            status = #{status, jdbcType=INTEGER},
        </if>
        invalidator = #{loginUserName, jdbcType=VARCHAR}
        where
        1=1
        <if test="modelId != null and modelId != ''">
            and model_id = #{modelId, jdbcType=VARCHAR}
        </if>
        <if test="modelVersionId != null and modelVersionId != ''">
            and model_version_id = #{modelVersionId, jdbcType=VARCHAR}
        </if>
        <if test="currStatus != null and currStatus != ''">
            and status = #{currStatus, jdbcType=VARCHAR}
        </if>
    </update>

    <update id="deleteDataModel">
        update tb_cdcdm_data_model_version
        set
        is_deleted = 1,
        update_time = now(),
        updater = #{loginUserName, jdbcType=VARCHAR}
        where model_version_id = #{modelVersionId, jdbcType=VARCHAR}
    </update>

    <update id="publishDataModel">
        update tb_cdcdm_data_model_version
        set
        status = #{status, jdbcType=VARCHAR},
        update_time = now(),
        updater = #{loginUserName, jdbcType=VARCHAR},
        publish_time = now(),
        publisher = #{loginUserName, jdbcType=VARCHAR}
        where
        1=1
        <if test="modelId != null and modelId != ''">
            and model_id = #{modelId, jdbcType=VARCHAR}
        </if>
        <if test="modelVersionId != null and modelVersionId != ''">
            and model_version_id = #{modelVersionId, jdbcType=VARCHAR}
        </if>
    </update>

    <update id="updateDataModelLatestVersion">
        update tb_cdcdm_data_model
        set latest_version = #{version, jdbcType=VARCHAR}
        where model_id = #{modelId, jdbcType=VARCHAR}
    </update>

    <update id="editFormSequence">
        <foreach collection="tbCdcdmDataModelForms" separator=";" item="item">
            update tb_cdcdm_data_model_form
            set order_flag = #{item.orderFlag}, update_time = #{item.updateTime}, updater = #{item.updater}
            where model_form_id = #{item.modelFormId}
        </foreach>
    </update>

    <update id="updatePublishDataModel">
        update tb_cdcdm_data_model_version
        set status = #{status}, update_time = now()
        where model_id = #{modelId} and model_version_id = #{modelVersionId}
    </update>

    <select id="getDataModelList" resultType="com.iflytek.cdc.admin.datamodel.model.vo.DataModelListVO">
        select
        dm.model_id            as modelId,
        dm.model_name          as dataModelName,
        dm.model_type          as dataModelType,
        mv.model_version_id    as modelVersionId,
        mv.model_label         as dataModelLabel,
        mv.status              as status,
        mv.publish_time        as publishDate,
        mv.update_time         as updateDate,
        mv.create_time         as createDate,
        tcdme.entity_er_model_id as erModelId,
        ft.config_info         as configInfo
        from tb_cdcdm_data_model dm
        left join tb_cdcdm_data_model_version mv
        on dm.model_id = mv.model_id
        left join tb_cdcdm_data_model_extend tcdme on tcdme.model_id = dm.model_id and tcdme.model_version_id = mv.model_version_id
        join tb_cdcdm_data_form_template ft
        on dm.model_id = ft.model_id
        where
        mv.is_deleted = 0
        <if test="status != null and status != ''">
            and mv.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="keyWord != null and keyWord != ''">
            and (dm.model_name like concat('%', #{keyWord, jdbcType=VARCHAR}, '%')
                or dm.model_type like concat('%', #{keyWord, jdbcType=VARCHAR}, '%')
                or mv.model_label like concat('%', #{keyWord, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="modelId != null and modelId != ''">
            and dm.model_id = #{modelId}
        </if>
        <if test="modelName!= null and modelName!= ''">
            and dm.model_name like concat('%', #{modelName}, '%')
        </if>
        <if test="modelType!= null and modelType!= ''">
            and dm.model_type = #{modelType}
        </if>
        <if test="(modelType == null or modelType == '') and systemType!= null and systemType!= ''">
            and dm.model_type in ('监测业务数据','原始数据','专题特征库','大数据协同')
        </if>
        <if test="modelLabel!= null and modelLabel!= ''">
            and mv.model_label like concat('%', #{modelLabel}, '%')
        </if>
        <if test="createStartTime != null">
            <![CDATA[ AND mv.create_time >= #{createStartTime} ]]>
        </if>
        <if test="createEndTime != null">
            <![CDATA[ AND mv.create_time <= #{createEndTime} ]]>
        </if>
        <!-- 处理更新时间开始 -->
        <if test="updateStartTime != null">
            <![CDATA[ AND mv.update_time >= #{updateStartTime} ]]>
        </if>
        <if test="updateEndTime != null">
            <![CDATA[ AND mv.update_time <= #{updateEndTime} ]]>
        </if>
        order by mv.update_time desc, mv.publish_time desc
    </select>

    <select id="getModelFormList" resultType="com.iflytek.cdc.admin.datamodel.model.vo.ModelFormListVO">
        select
        model_form_id           as model_form_id,
        form_name               as formName,
        is_repeat               as isRepeat,
        max_repeat_cnt          as maxRepeatCnt,
        form_identity_column    as formIdentityColumn,
        form_json               as formJson,
        quote_model             as quoteModel,
        is_deleted 				as isDeleted,
        updater                 as updater,
        update_time             as updateTime
        from tb_cdcdm_data_model_form
        where
        model_id = #{modelId}
        and model_version_id = #{modelVersionId}
        and is_deleted = 0
        <if test="isRepeat != null">
            and is_repeat = #{isRepeat, jdbcType=INTEGER}
        </if>
        <if test="formName != null and formName != ''">
            and form_name like concat('%', #{formName, jdbcType=VARCHAR}, '%')
        </if>
        order by order_flag desc, update_time desc
    </select>

    <select id="getModelFormData" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormValue">
        select *
        from tb_cdcdm_data_form_value
        where id = #{id, jdbcType=VARCHAR}
    </select>

    <select id="getDataModelVersion" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelVersion">
        select <include refid="ModelVersion"/>
        from tb_cdcdm_data_model_version
        where
        1=1
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" open="(" separator="," close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="modelId != null and modelId != ''">
            and model_id = #{modelId, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getModelIdByModelName" resultType="java.lang.String">
        select model_id
        from tb_cdcdm_data_model
        where model_name = #{modelName, jdbcType=VARCHAR}
    </select>

    <select id="getFormListByModelId" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm">
        select mf.*
        from tb_cdcdm_data_model_version mv
        left join tb_cdcdm_data_model_form mf
        on mv.model_version_id = mf.model_version_id
        where mv.model_id = #{modelId, jdbcType=VARCHAR} and mv.status = #{status, jdbcType=VARCHAR}
        order by mf.order_flag desc, update_time desc
    </select>

    <select id="getFormInfoByFormId" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm">
        select
        <include refid="DataModelForm"/>
        from tb_cdcdm_data_model_form
        where model_form_id = #{modelFormId, jdbcType=VARCHAR}
    </select>

    <select id="getModelInfoByModelId" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModel">
        select
        m.model_id, m.model_name, m.model_type, m.is_built_in, m.note, m.latest_version, v.model_version_id,tcdme.entity_er_model_id as erModelId
        from tb_cdcdm_data_model m
        join tb_cdcdm_data_model_version v
        on m.model_id = v.model_id
        left join tb_cdcdm_data_model_extend tcdme on tcdme.model_id = m.model_id and tcdme.model_version_id = v.model_version_id
        where m.model_id = #{modelId, jdbcType=VARCHAR}
        <if test="status != null and status != ''">
            and v.status = #{status}
        </if>

    </select>

    <select id="getDataModelVersionHistory"
            resultType="com.iflytek.cdc.admin.datamodel.model.vo.DataModelVersionHistoryVO">
        select
        mv.model_version_id     as modelVersionId,
        mv.model_id             as modelId,
        mv."version"            as version,
        mv.status               as status,
        mv.publish_time         as publishTime,
        mv.invalid_time         as invalidTime,
        mv.invalidator          as invalidator,
        dm.model_name           as dataModelName
        from tb_cdcdm_data_model_version mv
        left join tb_cdcdm_data_model dm
        on mv.model_id = dm.model_id
        where mv.model_id = #{modelId, jdbcType=VARCHAR}
        order by mv."version" desc
    </select>

    <select id="getVersionStatusByVersionId" resultType="java.lang.String">
        select status
        from tb_cdcdm_data_model_version
        where model_version_id = #{modelVersionId, jdbcType=VARCHAR}
    </select>

    <select id="getVersionInfoByVersionId"
            resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelVersion">
        select <include refid="ModelVersion"/>
        from tb_cdcdm_data_model_version
        where model_version_id = #{modelVersionId, jdbcType=VARCHAR}
    </select>

    <select id="getFormListByVersionId" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm">
        select
        <include refid="DataModelForm"/>
        from tb_cdcdm_data_model_form
        where 1=1
        <if test="modelVersionIds != null and modelVersionIds.size() &gt; 0">
            and model_version_id in
            <foreach collection="modelVersionIds" open="(" close=")" separator="," item="item" index="index">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getModelFormListByVersion"
            resultType="com.iflytek.cdc.admin.datamodel.model.vo.DataModelFormConfigVO">
        select
        m.model_id, m.model_name, v.model_version_id
        from tb_cdcdm_data_model m
        left join tb_cdcdm_data_model_version v
        on m.model_id = v.model_id
        where v.status = #{versionStatus}
    </select>

    <select id="getDataModelInfoById" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModel">
        select model_id, model_name, model_type, is_built_in, note, latest_version
        from tb_cdcdm_data_model
        where model_id = #{modelId}
    </select>
</mapper>