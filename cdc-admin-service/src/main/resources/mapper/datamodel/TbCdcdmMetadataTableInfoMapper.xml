<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_metadata_table_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="table_code" jdbcType="VARCHAR" property="tableCode" />
    <result column="schema" jdbcType="VARCHAR" property="schema" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="table_type" jdbcType="VARCHAR" property="tableType" />
    <result column="table_desc" jdbcType="VARCHAR" property="tableDesc" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, table_code, "schema", "table_name", table_type, table_desc, "alias", memo, create_time,
    update_time, creator, updater, creator_id, updater_id, delete_flag
  </sql>

  <select id="getTableInfoByTableName" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
    select <include refid="Base_Column_List"/>
    from tb_cdcdm_metadata_table_info
    where 1=1
    <if test="tableName != null and tableName != ''">
      and table_name like concat('%', #{tableName, jdbcType=VARCHAR}, '%')
    </if>
  </select>

  <select id="getTableInfoByTableNames" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
    select <include refid="Base_Column_List"/>
    from tb_cdcdm_metadata_table_info
    where 1=1
    <if test="tableNames != null and tableNames.size() > 0">
      and table_name in
        <foreach collection="tableNames" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
  </select>

  <select id="getTableInfoById" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
      select <include refid="Base_Column_List"/>
      from tb_cdcdm_metadata_table_info
      where id = #{tableId}
  </select>

  <select id="getTableInfoByIds" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
      select <include refid="Base_Column_List"/>
      from tb_cdcdm_metadata_table_info
      where 1=1
      <if test="ids != null and ids.size() &gt; 0">
          and id in
          <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
      </if>
  </select>

    <select id="getAllTableInfo" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_metadata_table_info
        where delete_flag = '0'
    </select>

</mapper>