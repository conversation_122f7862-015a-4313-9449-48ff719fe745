<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableColumnInfoMapper">
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, table_id, column_code, column_name, column_type, column_desc, data_type, business_column, memo, create_time, update_time, creator, updater, delete_flag, model_name, form_name, group_name, field_name, filter_condition, is_primary_key, table_code, creator_id, updater_id, column_length, column_precision, is_required, sort
  </sql>

  <select id="getFieldByTableName" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo">
    select
    ci.id, ci.table_id, ci.column_code, ci."column_name", ci.data_type as columnType, ci.column_desc, ci.data_type, ci.business_column,
    ci.memo, ci.create_time, ci.update_time, ci.creator, ci.updater, ci.delete_flag, ci.is_primary_key
    from tb_cdcdm_metadata_table_info ti
    left join tb_cdcdm_metadata_table_column_info ci
    on ti.id = ci.table_id
    where 1=1
    <if test="tableName != null and tableName != ''">
      and ti.table_name = #{tableName, jdbcType=VARCHAR}
    </if>
    <if test="columnName != null and columnName != ''">
      and ci.column_name like concat('%', #{columnName, jdbcType=VARCHAR}, '%')
    </if>
    order by ci.id
  </select>
  <select id="getFieldByTableNames" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo">
      select
      ci.id, ci.table_id, ci.column_code, ci."column_name", ci.data_type as columnType, ci.column_desc, ci.data_type, ci.business_column,
      ci.memo, ci.create_time, ci.update_time, ci.creator, ci.updater, ci.delete_flag, ci.is_primary_key
      from tb_cdcdm_metadata_table_info ti
      left join tb_cdcdm_metadata_table_column_info ci
      on ti.id = ci.table_id
      where 1=1
      <if test="tableNames != null and tableNames.size() > 0">
        and ti.table_name in
        <foreach collection="tableNames" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      order by ci.id
    </select>
    <select id="getColumnInfoByIds"
            resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo">
      select
      <include refid="Base_Column_List" />
      from tb_cdcdm_metadata_table_column_info
      where 1=1
      <if test="ids != null and ids.size() &gt; 0">
          and id in
          <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
             #{item}
          </foreach>
      </if>
    </select>

  <select id="getAllColumnInfo" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo">
    select <include refid="Base_Column_List"/>
    from tb_cdcdm_metadata_table_column_info
    where delete_flag = '0'
  </select>

</mapper>