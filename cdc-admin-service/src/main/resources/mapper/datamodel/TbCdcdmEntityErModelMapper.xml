<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmEntityErModelMapper">
    <sql id="base_column">
	    id,obj_name,obj_code,relation_json,obj_structure,creator_id,creator,create_time,updater_id,updater,update_time,delete_flag
    </sql>

	<!-- 通过ID查询单条数据 -->
	<select id="pageList" resultType="com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel">
        select
			<include refid="base_column"/>
        from tb_cdccs_entity_er_model
        where delete_flag = '0'
		<if test="queryKey != null and queryKey !=''">
			and obj_name like concat('%', #{queryKey,jdbcType=VARCHAR}, '%')
		</if>
		order by create_time desc
    </select>

</mapper>