<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataModelExtendMapper">
	<select id="loadErModelIdByModelId" resultType="string">
		select
			e.entity_er_model_id
		from
			tb_cdcdm_data_model_extend e
		join tb_cdcdm_data_model tcdm on
			tcdm .model_id = e.model_id
		join tb_cdcdm_data_model_version tcdmv on
			tcdmv.model_version_id = e.model_version_id
		where
			tcdmv.status = '已发布'
		    and e.model_id = #{modelId}

	</select>

</mapper>