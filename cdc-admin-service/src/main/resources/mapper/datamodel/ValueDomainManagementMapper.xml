<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.datamodel.mapper.ValueDomainManagementMapper">

    <insert id="editValueDomain">
        insert into tb_cdcdm_data_attr
        (id, name, attr_count, attr_value_count, type,data_url, data_dict_code, note, is_deleted, create_time, creator, update_time, updater)
        values
        (#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{attrCount, jdbcType=INTEGER}, #{attrValueCount, jdbcType=INTEGER},
        #{type, jdbcType=VARCHAR}, #{dataUrl, jdbcType=VARCHAR}, #{dataDictCode, jdbcType=VARCHAR}, #{note, jdbcType=VARCHAR},
        #{isDeleted, jdbcType=INTEGER}, #{createTime, jdbcType=TIMESTAMP}, #{creator, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP},
        #{updater, jdbcType=VARCHAR})
        on conflict(id)
        do update set
        name                = excluded.name,
        attr_count          = excluded.attr_count,
        attr_value_count    = excluded.attr_value_count,
        type                = excluded.type,
        data_url            = excluded.data_url,
        data_dict_code      = excluded.data_dict_code,
        note                = excluded.note,
        update_time         = excluded.update_time,
        updater             = excluded.updater
    </insert>

    <insert id="batchInsertIntoValue">
        insert into tb_cdcdm_data_attr_value
        (id, data_attr_id, attr_value1, attr_value2, attr_value3, attr_value4, attr_value5, attr_value6, attr_value7, attr_value8, attr_value9, attr_value10, is_deleted, create_time, creator, update_time, updater)
        values
        <foreach collection="values" item="item" separator=",">
            (#{item.id}, #{item.dataAttrId}, #{item.attrValue1}, #{item.attrValue2}, #{item.attrValue3}, #{item.attrValue4}, #{item.attrValue5}, #{item.attrValue6}, #{item.attrValue7}, #{item.attrValue8}, #{item.attrValue9}, #{item.attrValue10}, #{item.isDeleted}, #{item.createTime}, #{item.creator}, #{item.updateTime}, #{item.updater})
        </foreach>
        on conflict(id) do
        update set
        attr_value1 = excluded.attr_value1,
        attr_value2 = excluded.attr_value2,
        attr_value3 = excluded.attr_value3,
        attr_value4 = excluded.attr_value4,
        attr_value5 = excluded.attr_value5,
        attr_value6 = excluded.attr_value6,
        attr_value7 = excluded.attr_value7,
        attr_value8 = excluded.attr_value8,
        attr_value9 = excluded.attr_value9,
        attr_value10 = excluded.attr_value10,
        update_time = excluded.update_time,
        updater = excluded.updater
    </insert>

    <update id="updateDataAttr">
        update tb_cdcdm_data_attr
        set attr_value_count = (select count(*) from tb_cdcdm_data_attr_value where data_attr_id = #{attrId} and is_deleted = 0)
        where id = #{attrId}
    </update>

    <update id="updateValueDetail">
        update tb_cdcdm_data_attr_value
        set is_deleted = 1, update_time = now(), updater = #{loginUserName, jdbcType=VARCHAR}
        where id = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getValueDomainList" resultType="com.iflytek.cdc.admin.model.dm.vo.DataDictListVO">
        select
        id                  as id,
        name                as name,
        attr_count          as attrCount,
        attr_value_count    as attrValueCount,
        type                as type,
        create_time         as createTime,
        update_time         as updateTime,
        updater             as updater
        from tb_cdcdm_data_attr
        where
        is_deleted = 0
        <if test="id != null and id != ''">
            and id like concat('%', #{id, jdbcType=VARCHAR}, '%')
        </if>
        <if test="name != null and name != ''">
            and name like concat('%', #{name, jdbcType=VARCHAR}, '%')
        </if>
        <if test="attrCount != null and attrCount != ''">
            and attr_count = #{attrCount, jdbcType=INTEGER}
        </if>
        order by update_time desc
    </select>

    <select id="getValueDomainDetailList" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataAttrValue">
        select *
        from tb_cdcdm_data_attr_value
        where data_attr_id = #{dataAttrId, jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="getDetailListByName" resultType="com.iflytek.cdc.admin.entity.dm.TbCdcdmDataAttrValue">
        select a.attr_count, v.*
        from tb_cdcdm_data_attr a
        join tb_cdcdm_data_attr_value v on a.id = v.data_attr_id
        where v.is_deleted = 0
        <if test="id != null and id != ''">
            and a.id = #{id, jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != ''">
            and a.name = #{name, jdbcType=VARCHAR}
        </if>
    </select>

</mapper>
