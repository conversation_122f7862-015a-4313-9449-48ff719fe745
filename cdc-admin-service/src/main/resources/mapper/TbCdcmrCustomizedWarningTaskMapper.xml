<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningTaskMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="monitor_area" jdbcType="VARCHAR" property="monitorArea"/>
        <result column="monitor_start_time" jdbcType="TIMESTAMP" property="monitorStartTime"/>
        <result column="monitor_end_time" jdbcType="TIMESTAMP" property="monitorEndTime"/>
        <result column="warning_rule_id" jdbcType="VARCHAR" property="warningRuleId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , task_name, monitor_area, monitor_start_time, monitor_end_time, warning_rule_id,
    "status", memo, delete_flag, create_time, creator, update_time, updater, updater_name
    </sql>
    <select id="selectByPrimaryKey" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO">
        select *
        from "tb_cdcmr_customized_warning_task"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO">
        select
        a.*,c.name as ruleName
        from "tb_cdcmr_customized_warning_task" a left join tb_cdcmr_customized_warning_rule b on a.warning_rule_id =
        b.id
        left join tb_cdcmr_customized_warn c on b.customized_warn_id = c.id
        where a.delete_flag = '0'
        <if test="taskName != null and taskName != '' ">
            and a.task_name like concat('%',#{taskName},'%')
        </if>
        <if test="timeType == 1">
            <if test="startTime != null">
                and a.monitor_start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and a.monitor_start_time &lt;= #{endTime}
            </if>
        </if>
        <if test="timeType == 2">
            <if test="startTime != null">
                and a.monitor_end_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and a.monitor_end_time &lt;= #{endTime}
            </if>
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="updaterName != null and updaterName != ''">
            and a.updater_name like concat('%',#{updaterName},'%')
        </if>
        <if test="warningType != null and warningType != ''">
            and c.warning_type = #{warningType}
        </if>
        order by a.update_time desc
    </select>

    <select id="selectByWarnRuleId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_task"
        where warning_rule_id = #{warnRuleId,jdbcType=VARCHAR} and delete_flag = '0'
    </select>
    <select id="getEnabledWarn" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO">
        select a.*, b.sms_send_type_code, b.sms_send_type_desc, c.warning_type
        from "tb_cdcmr_customized_warning_task" a
        left join tb_cdcmr_customized_warning_rule b on a.warning_rule_id =
        b.id
        left join tb_cdcmr_customized_warn c on c.id = b.customized_warn_id
        where a.delete_flag = '0'
          and a.status = 1
    </select>
    <select id="getByName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        select *
        from "tb_cdcmr_customized_warning_task"
        where delete_flag = '0'
          and task_name = #{name}
    </select>

    <select id="getWarningIdByTaskId" resultType="java.lang.String">
        select t.warning_rule_id
        from tb_cdcmr_customized_warning_task t
        left join tb_cdcmr_customized_warning_rule r
        on t.warning_rule_id = r.id
        where t.id = #{taskId, jdbcType=VARCHAR}
        and r.delete_flag = '0'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        update "tb_cdcmr_customized_warning_task"
        set delete_flag = '1'
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        insert into "tb_cdcmr_customized_warning_task" (id, task_name, monitor_area,
                                                        monitor_start_time, monitor_end_time, warning_rule_id,
                                                        "status", memo, delete_flag,
                                                        create_time, creator, update_time,
                                                        updater, updater_name)
        values (#{id,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, #{monitorArea,jdbcType=VARCHAR},
                #{monitorStartTime,jdbcType=TIMESTAMP}, #{monitorEndTime,jdbcType=TIMESTAMP},
                #{warningRuleId,jdbcType=VARCHAR},
                #{status,jdbcType=SMALLINT}, #{memo,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        insert into "tb_cdcmr_customized_warning_task"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="monitorArea != null">
                monitor_area,
            </if>
            <if test="monitorStartTime != null">
                monitor_start_time,
            </if>
            <if test="monitorEndTime != null">
                monitor_end_time,
            </if>
            <if test="warningRuleId != null">
                warning_rule_id,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null">
                updater_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="monitorArea != null">
                #{monitorArea,jdbcType=VARCHAR},
            </if>
            <if test="monitorStartTime != null">
                #{monitorStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="monitorEndTime != null">
                #{monitorEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="warningRuleId != null">
                #{warningRuleId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        update "tb_cdcmr_customized_warning_task"
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="monitorArea != null">
                monitor_area = #{monitorArea,jdbcType=VARCHAR},
            </if>
                monitor_start_time = #{monitorStartTime,jdbcType=TIMESTAMP},
                monitor_end_time = #{monitorEndTime,jdbcType=TIMESTAMP},
            <if test="warningRuleId != null">
                warning_rule_id = #{warningRuleId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningTask">
        update "tb_cdcmr_customized_warning_task"
        set task_name          = #{taskName,jdbcType=VARCHAR},
            monitor_area       = #{monitorArea,jdbcType=VARCHAR},
            monitor_start_time = #{monitorStartTime,jdbcType=TIMESTAMP},
            monitor_end_time   = #{monitorEndTime,jdbcType=TIMESTAMP},
            warning_rule_id    = #{warningRuleId,jdbcType=VARCHAR},
            "status"           = #{status,jdbcType=SMALLINT},
            memo               = #{memo,jdbcType=VARCHAR},
            delete_flag        = #{deleteFlag,jdbcType=VARCHAR},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            creator            = #{creator,jdbcType=VARCHAR},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            updater            = #{updater,jdbcType=VARCHAR},
            updater_name       = #{updaterName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>