<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrSympWarnRuleMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="time_range" jdbcType="SMALLINT" property="timeRange"/>
        <result column="time_range_unit" jdbcType="SMALLINT" property="timeRangeUnit"/>
        <result column="monitoring_object" jdbcType="SMALLINT" property="monitoringObject"/>
        <result column="formula" jdbcType="SMALLINT" property="formula"/>
        <result column="param1" jdbcType="VARCHAR" property="param1"/>
        <result column="param2" jdbcType="VARCHAR" property="param2"/>
        <result column="param3" jdbcType="VARCHAR" property="param3"/>
        <result column="param4" jdbcType="VARCHAR" property="param4"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="warn_id" jdbcType="VARCHAR" property="warnId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , time_range, time_range_unit, monitoring_object, formula, param1, param2, param3,
    param4, is_deleted, "status", update_time, updater, warn_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_symp_warn_rule"
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_symp_warn_rule"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        insert into "tb_cdcmr_symp_warn_rule" (id, time_range, time_range_unit,
                                             monitoring_object, formula, param1,
                                             param2, param3, param4,
                                             is_deleted, "status", update_time,
                                             updater, warn_id)
        values (#{id,jdbcType=VARCHAR}, #{timeRange,jdbcType=SMALLINT}, #{timeRangeUnit,jdbcType=SMALLINT},
                #{monitoringObject,jdbcType=SMALLINT}, #{formula,jdbcType=SMALLINT}, #{param1,jdbcType=VARCHAR},
                #{param2,jdbcType=VARCHAR}, #{param3,jdbcType=VARCHAR}, #{param4,jdbcType=VARCHAR},
                #{isDeleted,jdbcType=SMALLINT}, #{status,jdbcType=SMALLINT}, #{updateTime,jdbcType=DATE},
                #{updater,jdbcType=VARCHAR}, #{warnId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        insert into "tb_cdcmr_symp_warn_rule"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="timeRange != null">
                time_range,
            </if>
            <if test="timeRangeUnit != null">
                time_range_unit,
            </if>
            <if test="monitoringObject != null">
                monitoring_object,
            </if>
            <if test="formula != null">
                formula,
            </if>
            <if test="param1 != null">
                param1,
            </if>
            <if test="param2 != null">
                param2,
            </if>
            <if test="param3 != null">
                param3,
            </if>
            <if test="param4 != null">
                param4,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="warnId != null">
                warn_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="timeRange != null">
                #{timeRange,jdbcType=SMALLINT},
            </if>
            <if test="timeRangeUnit != null">
                #{timeRangeUnit,jdbcType=SMALLINT},
            </if>
            <if test="monitoringObject != null">
                #{monitoringObject,jdbcType=SMALLINT},
            </if>
            <if test="formula != null">
                #{formula,jdbcType=SMALLINT},
            </if>
            <if test="param1 != null">
                #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                #{param4,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="warnId != null">
                #{warnId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        update "tb_cdcmr_symp_warn_rule"
        <set>
            <if test="timeRange != null">
                time_range = #{timeRange,jdbcType=SMALLINT},
            </if>
            <if test="timeRangeUnit != null">
                time_range_unit = #{timeRangeUnit,jdbcType=SMALLINT},
            </if>
            <if test="monitoringObject != null">
                monitoring_object = #{monitoringObject,jdbcType=SMALLINT},
            </if>
            <if test="formula != null">
                formula = #{formula,jdbcType=SMALLINT},
            </if>
            <if test="param1 != null">
                param1 = #{param1,jdbcType=VARCHAR},
            </if>
            <if test="param2 != null">
                param2 = #{param2,jdbcType=VARCHAR},
            </if>
            <if test="param3 != null">
                param3 = #{param3,jdbcType=VARCHAR},
            </if>
            <if test="param4 != null">
                param4 = #{param4,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=DATE},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="warnId != null">
                warn_id = #{warnId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        update "tb_cdcmr_symp_warn_rule"
        set time_range        = #{timeRange,jdbcType=SMALLINT},
            time_range_unit   = #{timeRangeUnit,jdbcType=SMALLINT},
            monitoring_object = #{monitoringObject,jdbcType=SMALLINT},
            formula           = #{formula,jdbcType=SMALLINT},
            param1            = #{param1,jdbcType=VARCHAR},
            param2            = #{param2,jdbcType=VARCHAR},
            param3            = #{param3,jdbcType=VARCHAR},
            param4            = #{param4,jdbcType=VARCHAR},
            is_deleted        = #{isDeleted,jdbcType=SMALLINT},
            "status"          = #{status,jdbcType=SMALLINT},
            update_time       = #{updateTime,jdbcType=DATE},
            updater           = #{updater,jdbcType=VARCHAR},
            warn_id           = #{warnId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="deleteOtherByIds">
        update "tb_cdcmr_symp_warn_rule" set is_deleted = 1
        where warn_id = #{warnId,jdbcType=VARCHAR}
        <if test="idList.size() != 0">
            and id not in
            <foreach collection="idList" open="(" separator="," close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="upsertRules">
        <foreach collection="recordList" item="item" separator=";">
            insert into "tb_cdcmr_symp_warn_rule"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.timeRange != null">
                    time_range,
                </if>
                <if test="item.timeRangeUnit != null">
                    time_range_unit,
                </if>
                <if test="item.monitoringObject != null">
                    monitoring_object,
                </if>
                <if test="item.formula != null">
                    formula,
                </if>
                <if test="item.param1 != null">
                    param1,
                </if>
                <if test="item.param2 != null">
                    param2,
                </if>
                <if test="item.param3 != null">
                    param3,
                </if>
                <if test="item.param4 != null">
                    param4,
                </if>
                <if test="item.isDeleted != null">
                    is_deleted,
                </if>
                <if test="item.status != null">
                    "status",
                </if>
                <if test="item.updateTime != null">
                    update_time,
                </if>
                <if test="item.updater != null">
                    updater,
                </if>
                <if test="item.warnId != null">
                    warn_id,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=VARCHAR},
                </if>
                <if test="item.timeRange != null">
                    #{item.timeRange,jdbcType=SMALLINT},
                </if>
                <if test="item.timeRangeUnit != null">
                    #{item.timeRangeUnit,jdbcType=SMALLINT},
                </if>
                <if test="item.monitoringObject != null">
                    #{item.monitoringObject,jdbcType=SMALLINT},
                </if>
                <if test="item.formula != null">
                    #{item.formula,jdbcType=SMALLINT},
                </if>
                <if test="item.param1 != null">
                    #{item.param1,jdbcType=VARCHAR},
                </if>
                <if test="item.param2 != null">
                    #{item.param2,jdbcType=VARCHAR},
                </if>
                <if test="item.param3 != null">
                    #{item.param3,jdbcType=VARCHAR},
                </if>
                <if test="item.param4 != null">
                    #{item.param4,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null">
                    #{item.isDeleted,jdbcType=SMALLINT},
                </if>
                <if test="item.status != null">
                    #{item.status,jdbcType=SMALLINT},
                </if>
                <if test="item.updateTime != null">
                    #{item.updateTime,jdbcType=DATE},
                </if>
                <if test="item.updater != null">
                    #{item.updater,jdbcType=VARCHAR},
                </if>
                <if test="item.warnId != null">
                    #{item.warnId,jdbcType=VARCHAR},
                </if>
            </trim>

        on conflict(id) do
        update
        set time_range = excluded.time_range,
        time_range_unit = excluded.time_range_unit,
        monitoring_object =excluded.monitoring_object,
        formula = excluded.formula,
        param1 = excluded.param1,
        param2 = excluded.param2,
        param3 = excluded.param3,
        param4 = excluded.param4,
        is_deleted = excluded.is_deleted,
        "status" = excluded.status,
        update_time = excluded.update_time,
        updater = excluded.updater,
        warn_id = excluded.warn_id
        </foreach>
    </update>
    <select id="getRuleListByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        select *
        from tb_cdcmr_symp_warn_rule
        where is_deleted = 0
          and warn_id = #{warnId}

    </select>
    <select id="getAllRuleList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule">
        select  *   from tb_cdcmr_symp_warn_rule
        where is_deleted = 0 and status = 1
    </select>
    <select id="getExportData" resultType="com.iflytek.cdc.admin.dto.SympWarnRuleExportDataDto">
        SELECT tcsw.symptom_name as symptomName,
               tcsw.incubation as incubation,
               tcsw.max_Life_Cycle as maxLifeCycle,
               tcswr.monitoring_object as  monitorObject,
               tcswr.time_range as timeRange,
               tcswr.time_range_unit as timeRangeUnit,
               tcswr.formula as formula,
               tcswr.param1 as param1,
               tcswr.param2 as param2,
               tcswr.param3 as param3,
               tcswr.param4 as param4
        FROM tb_cdcmr_symp_warn_rule  tcswr
                 LEFT JOIN tb_cdcmr_symp_warn  tcsw ON tcswr.warn_id = tcsw.ID
        WHERE  tcswr.is_deleted = '0'
          and tcsw.is_deleted = '0'
        order by warn_id
    </select>

</mapper>