<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcewGaodeContourCacheMapper">

    <select id="selectOneByCondition" resultType="com.iflytek.cdc.admin.entity.TbCdcewGaodeContourCache">
        select id, key, keywords, subdistrict, extensions, json_data
        from tb_cdcew_gaode_contour_cache
        where
        delete_flag = '0'
        and key = #{key}
        and keywords = #{keywords}
        and subdistrict = #{subdistrict}
        and extensions = #{extensions}
        order by create_datetime desc limit 1
    </select>
</mapper>
