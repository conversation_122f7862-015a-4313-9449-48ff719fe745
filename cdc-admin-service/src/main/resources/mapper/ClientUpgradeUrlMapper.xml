<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.ClientUpgradeUrlMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.ClientUpgradeUrl">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_client_upgrade_url-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="package_file_url" jdbcType="VARCHAR" property="packageFileUrl" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, area_code, area_name, org_code, org_name, package_file_url, create_user, create_time,
    update_user, update_time, is_delete
  </sql>
  <select id="queryClientUpgradeUrlDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeParamDto"
          resultType="com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_client_upgrade_url
    <where>
       is_delete ='1'
      <if test="orgCodes != null">
       and org_code in
        <foreach collection="orgCodes" open="(" separator="," close=")" item="item" index="index">
          #{item}
        </foreach>
      </if>
      <if test="orgName != null  and orgName != ''">
        and org_name like concat('%',#{orgName,jdbcType=VARCHAR},'%')
      </if>
      order by  update_time desc
    </where>
  </select>


  <select id="getPackageFileUrlByAreaCode" resultType="com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_client_upgrade_url
    <where>
      is_delete ='1'
      <if test="areaCode != null">
        and area_code = #{areaCode}
      </if>
      order by  update_time desc
    </where>
  </select>



  <insert id="addClientUpgradeUrlDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto">
    <!--@mbg.generated-->
    insert into tb_cdcmr_client_upgrade_url
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="packageFileUrl != null">
        package_file_url,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      create_time,
      update_time,
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="packageFileUrl != null">
        #{packageFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      NOW(),
      NOW(),
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateClientUpgradeUrlDto" parameterType="com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto">
    <!--@mbg.generated-->
    update tb_cdcmr_client_upgrade_url
    <set>
      <if test="packageFileUrl != null">
        package_file_url = #{packageFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      UPDATE_TIME = now(),
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>