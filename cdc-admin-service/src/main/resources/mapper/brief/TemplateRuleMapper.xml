<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.admin.mapper.brief.TemplateRuleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.iflytek.cdc.admin.entity.brief.TemplateRuleEntity" id="templateRuleMap">
        <result property="id" jdbcType="VARCHAR" column="id"/>
        <result property="templateId" jdbcType="VARCHAR" column="template_id"/>
        <result property="startTime" jdbcType="VARCHAR" column="start_time"/>
        <result property="endTime" jdbcType="VARCHAR" column="end_time"/>
        <result property="repeatFlag" jdbcType="VARCHAR" column="repeat_flag"/>
        <result property="customTime" jdbcType="VARCHAR" column="custom_time"/>
        <result property="status" jdbcType="VARCHAR" column="status"/>
        <result property="provinceCode" jdbcType="VARCHAR" column="province_code"/>
        <result property="provinceName" jdbcType="VARCHAR" column="province_name"/>
        <result property="cityCode" jdbcType="VARCHAR" column="city_code"/>
        <result property="cityName" jdbcType="VARCHAR" column="city_name"/>
        <result property="districtCode" jdbcType="VARCHAR" column="district_code"/>
        <result property="districtName" jdbcType="VARCHAR" column="district_name"/>
        <result property="creatorId" jdbcType="VARCHAR" column="creator_id"/>
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="updatorId" jdbcType="VARCHAR" column="updator_id"/>
        <result property="updatorName" jdbcType="VARCHAR" column="updator_name"/>
        <result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
        <result property="deleteFlag" jdbcType="VARCHAR" column="delete_flag"/>
    </resultMap>
    <sql id="Base_Column_List">
    id,template_id,start_time,end_time,repeat_flag,custom_time,status,province_code,province_name,city_code,city_name,district_code,district_name,creator_id,creator_name,create_time,updator_id,updator_name,update_time,delete_flag    </sql>
    <delete id="deleteUserByRuleId">
        delete from tb_cdcbr_push_user where rule_id = #{ruleId} and org_id = #{orgId}
    </delete>
    <delete id="deleteDetailByRuleId">
        delete from tb_cdcbr_template_rule_detail where rule_id = #{ruleId}
    </delete>
    <select id="listBySearch" resultType="com.iflytek.cdc.admin.vo.brief.TemplateRuleVo">
        select
        a.id,a.template_id,start_time,end_time,repeat_flag,custom_time,a.status,a.province_name,a.city_name,a.district_name,a.updator_name,a.create_time,a.update_time,b.title,b.statistics_cycle
        ,b.brief_report_type
        from tb_cdcbr_template_rule a join tb_cdcbr_template b on a.template_id = b.id
        where a.delete_flag = '0'
        <if test="statisticsCycle != null and statisticsCycle != ''">
            and b.statistics_cycle = #{statisticsCycle}
        </if>
        <if test="analysisObject != null and analysisObject != ''">
            and b.analysis_object = #{analysisObject}
        </if>
        <if test="businessType != null and businessType != ''">
            and b.business_type = #{businessType}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and a.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and a.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and a.district_code = #{districtCode}
        </if>
        <if test="title != null and title != ''">
            and b.title like concat('%',#{title},'%')
        </if>
        <if test="idList != null and idList.size() &gt; 0">
            and a.id in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="briefReportType != null and briefReportType != ''">
            and b.brief_report_type = #{briefReportType}
        </if>
        order by a.update_time desc
    </select>
    <select id="pushRuleList" resultType="com.iflytek.cdc.admin.vo.brief.TemplatePushRuleVo">
        select
        a.id,a.province_name,a.city_name,a.district_name,a.auth_user_name,a.auth_time,a.statistics_cycle,b.title,b.brief_report_type
        from tb_cdcbr_template_rule a join tb_cdcbr_template b on a.template_id = b.id
        where a.delete_flag = '0'
        <if test="businessType != null and businessType != ''">
            and b.business_type = #{businessType}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and a.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and a.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and a.district_code = #{districtCode}
        </if>
        <if test="statisticsCycle != null and statisticsCycle != ''">
            and a.statistics_cycle = #{statisticsCycle}
        </if>
        <if test="title != null and title != ''">
            and b.title like concat('%',#{title},'%')
        </if>
        <if test="briefReportType != null and briefReportType != ''">
            and b.brief_report_type = #{briefReportType}
        </if>
        order by CASE WHEN a.auth_time IS NULL THEN 1 ELSE 0
        END, a.auth_time desc
    </select>
    <select id="pushUserList" resultType="com.iflytek.cdc.admin.entity.brief.PushUserEntity">
        select id,uap_user_id,uap_user_name,phone,org_id,org_name
        from tb_cdcbr_push_user
        where rule_id = #{ruleId}
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId}
        </if>
    </select>
    <select id="selectByDate" resultType="com.iflytek.cdc.admin.vo.brief.TemplateRuleDetailVo">
        select
            a.id,
            a.rule_id,
            a.statistics_cycle,
            a.statistics_time,
            a.start_date,
            a.end_date,
            a.template_id,
            b.content_title,
            b.content,
            c.province_code,
            c.province_name,
            c.city_code,
            c.city_name,
            c.district_code,
            c.district_name,
            b.business_type,
            b.analysis_object,
            b.attachment_title,
            b.attachment_flag,
            b.brief_report_type
        from tb_cdcbr_template_rule_detail a
        join tb_cdcbr_template b on a.template_id = b.id
        join tb_cdcbr_template_rule c on a.rule_id = c.id
        where b.delete_flag = '0' and c.delete_flag = '0' and c.status = '1'
        and end_date = TO_DATE(#{currentDate},'YYYY-MM-DD')
    </select>
    <insert id="saveAuthUser">
        insert into tb_cdcbr_push_user(id,rule_id,uap_user_id,uap_user_name,phone,org_id,org_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.ruleId,jdbcType=VARCHAR},#{item.uapUserId,jdbcType=VARCHAR},
            #{item.uapUserName,jdbcType=VARCHAR},#{item.phone,jdbcType=VARCHAR},#{item.orgId},#{item.orgName})
        </foreach>
    </insert>
    <insert id="batchSaveRuleDetail">
        insert into tb_cdcbr_template_rule_detail(id,rule_id,statistics_cycle, statistics_time,start_date,end_date,template_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.ruleId,jdbcType=VARCHAR},#{item.statisticsCycle,jdbcType=VARCHAR},
            #{item.statisticsTime,jdbcType=VARCHAR},#{item.startDate},#{item.endDate},#{item.templateId,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>