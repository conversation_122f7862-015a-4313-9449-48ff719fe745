<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.admin.mapper.brief.BriefTemplateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.iflytek.cdc.admin.entity.brief.TemplateEntity" id="templateMap">
        <result property="id" jdbcType="VARCHAR" column="id"/>
        <result property="statisticsCycle" jdbcType="VARCHAR" column="statistics_cycle"/>
        <result property="title" jdbcType="VARCHAR" column="title"/>
        <result property="analysisObject" jdbcType="VARCHAR" column="analysis_object"/>
        <result property="content" jdbcType="VARCHAR" column="content"/>
        <result property="businessType" jdbcType="VARCHAR" column="business_type"/>
        <result property="analysisObjectName" jdbcType="VARCHAR" column="analysis_object_name"/>
        <result property="creatorId" jdbcType="VARCHAR" column="creator_id"/>
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="updatorId" jdbcType="VARCHAR" column="updator_id"/>
        <result property="updatorName" jdbcType="VARCHAR" column="updator_name"/>
        <result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
        <result property="deleteFlag" jdbcType="VARCHAR" column="delete_flag"/>
    </resultMap>
    <resultMap type="com.iflytek.cdc.admin.vo.brief.TemplateVo" id="templateVoMap">
        <result property="id" jdbcType="VARCHAR" column="id"/>
        <result property="statisticsCycle" jdbcType="VARCHAR" column="statistics_cycle"/>
        <result property="title" jdbcType="VARCHAR" column="title"/>
        <result property="analysisObject" jdbcType="VARCHAR" column="analysis_object"/>
        <result property="content" jdbcType="VARCHAR" column="content"/>
        <result property="businessType" jdbcType="VARCHAR" column="business_type"/>
        <result property="analysisObjectName" jdbcType="VARCHAR" column="analysis_object_name"/>
        <result property="creatorId" jdbcType="VARCHAR" column="creator_id"/>
        <result property="creatorName" jdbcType="VARCHAR" column="creator_name"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="updatorId" jdbcType="VARCHAR" column="updator_id"/>
        <result property="updatorName" jdbcType="VARCHAR" column="updator_name"/>
        <result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
        <result property="deleteFlag" jdbcType="VARCHAR" column="delete_flag"/>
        <result property="ruleStatus" jdbcType="VARCHAR" column="ruleStatus"/>
        <result property="applicableDisease" jdbcType="VARCHAR" column="applicable_disease"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="briefReportType" jdbcType="VARCHAR" column="briefReportType"/>
    </resultMap>
    <sql id="Base_Column_List">
    id,statistics_cycle,title,analysis_object,content,business_type,analysis_object_name,creator_id,creator_name,create_time,updator_id,updator_name,update_time,delete_flag    </sql>
    
    <insert id="insertBatchIndicators">
        insert into tb_cdcbr_template_indicators (id,place_holder,dimension_id,dimension_name,index_id,index_name,
        template_id, index_scope, location, parent_dimension_id, label_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.placeHolder},#{item.dimensionId},#{item.dimensionName},#{item.indexId},#{item.indexName},
            #{item.templateId},#{item.indexScope},#{item.location}, #{item.parentDimensionId}, #{item.labelName})
        </foreach>
    </insert>

    <select id="listBySearch" resultMap="templateVoMap">
        select
        a.id,a.statistics_cycle,a.title,a.analysis_object,a.analysis_object_name,a.updator_name,a.update_time,a.applicable_disease,
        b.status as ruleStatus,a.brief_report_type as briefReportType
        from tb_cdcbr_template a left join tb_cdcbr_template_rule b on a.id = b.template_id
        where a.delete_flag = '0'
        <if test="statisticsCycle != null and statisticsCycle != ''">
            and a.statistics_cycle = #{statisticsCycle}
        </if>
        <if test="analysisObject != null and analysisObject != ''">
            and a.analysis_object = #{analysisObject}
        </if>
        <if test="businessType != null and businessType != ''">
            and a.business_type = #{businessType}
        </if>
        <if test="null != applicableDiseaseQuery">
            <if test="null != applicableDiseaseQuery.type and '' != applicableDiseaseQuery.type">
                and applicable_disease::json->>'type' = #{applicableDiseaseQuery.type}
            </if>
            <if test="null != applicableDiseaseQuery.diseaseId and '' != applicableDiseaseQuery.diseaseId">
                and applicable_disease::json->>'diseaseId' = #{applicableDiseaseQuery.diseaseId}
            </if>
        </if>
        <if test="idList != null and idList.size() &gt; 0">
            and a.id in
            <foreach collection="idList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="briefReportType != null and briefReportType != ''">
            and a.brief_report_type = #{briefReportType}
        </if>
        <if test="title != null and title != ''">
            and a.title like concat('%',#{title},'%')
        </if>
        order by a.update_time desc
    </select>
    
    <select id="getSettingDetail" resultType="com.iflytek.cdc.admin.vo.brief.TemplateSettingVo">
        select
        id as templateId,content, content_title, original_content,attachment_title,attachment_flag
        from tb_cdcbr_template
        where delete_flag = '0'
        and id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="listIndicators" resultType="com.iflytek.cdc.admin.entity.brief.IndicatorsEntity">
        select
        id,place_holder,dimension_id,dimension_name,index_id,index_name,template_id,index_scope,
        location,index_value,parent_dimension_id,label_name
        from tb_cdcbr_template_indicators
        where template_id = #{templateId,jdbcType=VARCHAR}
        order by id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from tb_cdcbr_template
        where id = #{id,jdbcType=VARCHAR}
     </delete>
    <delete id="deleteIndicatorsByTemplateId">
        delete from tb_cdcbr_template_indicators
        where template_id = #{templateId,jdbcType=VARCHAR}
        <if test="attachmentFlag != null and attachmentFlag =='0'.toString()">
            and index_scope = 'FJ'
        </if>
    </delete>
</mapper>