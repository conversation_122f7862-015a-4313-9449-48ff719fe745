<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.admin.mapper.brief.OverviewMapper">

    <select id="queryBriefList" resultType="com.iflytek.cdc.admin.entity.brief.BriefInfoEntity">
        select distinct(a.id), a.title, a.analysis_object, a.content, a.business_type, a.province_code,
        a.province_name, a.city_code, a.city_name, a.district_code, a.district_name, a.statistics_time,
        a.create_time, a.statistics_cycle, a.attachment_title, a.template_id
        from tb_cdcbr_brief_info a join tb_cdcbr_push_record b on a.id = b.brief_id
        left join tb_cdcbr_template t on a.template_id = t.id
        <where>
            a.delete_flag = '0'
            <if test="userType != null and userType != 1">
                and b.push_person_id = #{loginUserId}
            </if>
            <if test="statisticsCycle != null and statisticsCycle != ''">
                and a.statistics_cycle = #{statisticsCycle}
            </if>
            <if test="businessType != null and businessType != ''">
                and a.business_type = #{businessType}
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                and a.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
               and a.city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and a.district_code = #{districtCode}
            </if>
            <if test="startDate != null">
                and a.create_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and a.create_time &lt;= #{endDate}
            </if>
            <if test="briefReportType != null and briefReportType != ''">
                t.brief_report_type = #{briefReportType}
            </if>
            order by a.create_time desc
        </where>
    </select>

    <select id="queryPushList" resultType="com.iflytek.cdc.admin.entity.brief.PushRecordEntity">
        select * from tb_cdcbr_push_record
        <where>
            <if test="businessType != null and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                and province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </if>
            <if test="startDate != null">
                and create_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and create_time &lt;= #{endDate}
            </if>
            <if test="pushUserName != null and pushUserName != ''">
                and push_person_name like concat('%',#{pushUserName},'%')
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="getById" resultType="com.iflytek.cdc.admin.vo.brief.BriefInfoVo">
        select id,title,content, attachment_title, template_id from tb_cdcbr_brief_info where id = #{id}
            and delete_flag = '0'
    </select>

    <update id="deleteInfoById">
        update tb_cdcbr_brief_info set delete_flag = '1' where id = #{id}
    </update>
</mapper>