<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrFzcdcOrgMappingMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_fzcdc_org_mapping-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="fz_sys_org_id" jdbcType="VARCHAR" property="fzSysOrgId" />
    <result column="fz_sys_org_name" jdbcType="VARCHAR" property="fzSysOrgName" />
    <result column="cdc_sys_org_id" jdbcType="VARCHAR" property="cdcSysOrgId" />
    <result column="cdc_sys_org_name" jdbcType="VARCHAR" property="cdcSysOrgName" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cdc_sys_org_code" jdbcType="VARCHAR" property="cdcSysOrgCode" />
    <result column="fz_sys_org_code" jdbcType="VARCHAR" property="fzSysOrgCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fz_sys_org_id, fz_sys_org_name, cdc_sys_org_id, cdc_sys_org_name, is_delete, 
    create_user, create_time, update_user, update_time, cdc_sys_org_code, fz_sys_org_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_fzcdc_org_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_fzcdc_org_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fzcdc_org_mapping (id, fz_sys_org_id, fz_sys_org_name, 
      cdc_sys_org_id, cdc_sys_org_name, is_delete, 
      create_user, create_time, update_user, 
      update_time, cdc_sys_org_code, fz_sys_org_code
      )
    values (#{id,jdbcType=VARCHAR}, #{fzSysOrgId,jdbcType=VARCHAR}, #{fzSysOrgName,jdbcType=VARCHAR}, 
      #{cdcSysOrgId,jdbcType=VARCHAR}, #{cdcSysOrgName,jdbcType=VARCHAR}, #{isDelete,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{cdcSysOrgCode,jdbcType=VARCHAR}, #{fzSysOrgCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fzcdc_org_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fzSysOrgId != null">
        fz_sys_org_id,
      </if>
      <if test="fzSysOrgName != null">
        fz_sys_org_name,
      </if>
      <if test="cdcSysOrgId != null">
        cdc_sys_org_id,
      </if>
      <if test="cdcSysOrgName != null">
        cdc_sys_org_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="cdcSysOrgCode != null">
        cdc_sys_org_code,
      </if>
      <if test="fzSysOrgCode != null">
        fz_sys_org_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fzSysOrgId != null">
        #{fzSysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fzSysOrgName != null">
        #{fzSysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="cdcSysOrgId != null">
        #{cdcSysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="cdcSysOrgName != null">
        #{cdcSysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cdcSysOrgCode != null">
        #{cdcSysOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="fzSysOrgCode != null">
        #{fzSysOrgCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping">
    <!--@mbg.generated-->
    update tb_cdcmr_fzcdc_org_mapping
    <set>
      <if test="fzSysOrgId != null">
        fz_sys_org_id = #{fzSysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fzSysOrgName != null">
        fz_sys_org_name = #{fzSysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="cdcSysOrgId != null">
        cdc_sys_org_id = #{cdcSysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="cdcSysOrgName != null">
        cdc_sys_org_name = #{cdcSysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cdcSysOrgCode != null">
        cdc_sys_org_code = #{cdcSysOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="fzSysOrgCode != null">
        fz_sys_org_code = #{fzSysOrgCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzcdcOrgMapping">
    <!--@mbg.generated-->
    update tb_cdcmr_fzcdc_org_mapping
    set fz_sys_org_id = #{fzSysOrgId,jdbcType=VARCHAR},
      fz_sys_org_name = #{fzSysOrgName,jdbcType=VARCHAR},
      cdc_sys_org_id = #{cdcSysOrgId,jdbcType=VARCHAR},
      cdc_sys_org_name = #{cdcSysOrgName,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      cdc_sys_org_code = #{cdcSysOrgCode,jdbcType=VARCHAR},
      fz_sys_org_code = #{fzSysOrgCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByFzId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_fzcdc_org_mapping
    where fz_sys_org_id = #{fzOrgId,jdbcType=VARCHAR}
  </select>

  <select id="selectByCdcSysId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_fzcdc_org_mapping
    where cdc_sys_org_id = #{fzOrgId,jdbcType=VARCHAR}
  </select>
</mapper>