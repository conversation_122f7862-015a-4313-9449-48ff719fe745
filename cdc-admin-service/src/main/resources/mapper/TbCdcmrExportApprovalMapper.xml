<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrExportApprovalMapper">

    <select id="queryTaskApprovals" resultType="com.iflytek.cdc.admin.dto.ExportApplicationListDTO">
        select a.id, a.task_id, b.task_name, b.export_type, b.status, a.creator_time, a.approval_status,
               b.attachment_size as file_size, b.attachment_id, b.attachment_url
          from tb_cdcmr_export_approval a
          join tb_cdcmr_export_task b on a.task_id = b.id
         where b.delete_flag = '0'
           and b.app_code = 'cdc-admin-export'
        <if test="status != null">
            and b.status = #{status}
        </if>
        <if test="taskName != null and taskName != ''">
            and b.task_name like concat('%',#{taskName},'%')
        </if>
        <if test="startDate != null and endDate != null ">
            and b.create_time between #{startDate} and #{endDate}
        </if>
        <if test="creator != null and creator != ''">
            and b.creator like concat('%',#{creator},'%')
        </if>
        <if test="loginUserId != null and loginUserId != ''">
            and b.creator_id = #{loginUserId}
        </if>
        <if test="exportType != null and exportType != ''">
            and b.export_type = #{exportType}
        </if>
        <if test="approvalStatus != null and approvalStatus == 'PENDING_APPROVAL'">
            and a.approval_status in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL')
        </if>
        <if test="approvalStatus != null and approvalStatus != '' and approvalStatus != 'PENDING_APPROVAL'">
            and a.approval_status = #{approvalStatus}
        </if>
         order by b.create_time desc
    </select>

    <select id="queryManualApprovalList" resultType="com.iflytek.cdc.admin.dto.ExportApprovalListDTO">
        select a.id, a.task_id, b.task_name, b.export_type, b.status, b.creator, a.creator_time,
              a.approval_status, b.attachment_size as file_size,
              case when a.approval_status in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL') then '0' else '1' end as approval_process
          from tb_cdcmr_export_approval a
          join tb_cdcmr_export_task b on a.task_id = b.id
          join tb_cdcmr_export_permission_config c on a.export_code = c.export_code
         where b.delete_flag = '0'
           and (a.approver is null or a.approver != '系统自动')
        <if test="status != null">
            and b.status = #{status}
        </if>
        <if test="taskName != null and taskName != ''">
            and b.task_name like concat('%',#{taskName},'%')
        </if>
        <if test="startDate != null and endDate != null ">
            and b.create_time between #{startDate} and #{endDate}
        </if>
        <if test="creator != null and creator != ''">
            and b.creator like concat('%',#{creator},'%')
        </if>
        <if test="loginUserId != null and loginUserId != ''">
            and (
                a.level1_approver::jsonb @> concat('"', #{loginUserId}, '"')::jsonb = true
                or
                a.level2_approver::jsonb @> concat('"', #{loginUserId}, '"')::jsonb = true
            )
        </if>
        <if test="exportType != null and exportType != ''">
            and b.export_type = #{exportType}
        </if>
        <if test="appCode != null and appCode != ''">
            and b.app_code = #{appCode}
        </if>
        <choose>
            <when test="approvalProcess == '0'.toString()">
                and a.approval_status in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL')
            </when>
            <otherwise>
                and a.approval_status not in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL')
            </otherwise>
        </choose>
         order by b.create_time desc
    </select>

    <update id="autoPassDelayedApprovals">
        update tb_cdcmr_export_approval
           set approval_status = 'APPROVED',
               approver = '系统自动',
               approval_time = now()
         where id in (
        select a.id
          from tb_cdcmr_export_approval a join tb_cdcmr_export_permission_config c on a.export_code = c.export_code
          join tb_cdcmr_export_permission_org o on c.id = o.export_id
         where a.approval_status in ('PENDING_APPROVAL', 'PENDING_SECOND_APPROVAL')
           and a.approval_time is null
           and extract(epoch from (now() - a.updater_time))/3600 > o.approval_time_limit group by a.id
        )
    </update>
</mapper>
