<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrFzOrgMappingMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_fz_org_mapping-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="AREA_CODE_PROVINCE" jdbcType="VARCHAR" property="areaCodeProvince" />
    <result column="AREA_CODE_CITY" jdbcType="VARCHAR" property="areaCodeCity" />
    <result column="AREA_CODE_COUNTY" jdbcType="VARCHAR" property="areaCodeCounty" />
    <result column="SOURCE_CODE" jdbcType="VARCHAR" property="sourceCode" />
    <result column="SOURCE_NAME" jdbcType="VARCHAR" property="sourceName" />
    <result column="SOURCE_ORG_CODE" jdbcType="VARCHAR" property="sourceOrgCode" />
    <result column="SOURCE_ORG_NAME" jdbcType="VARCHAR" property="sourceOrgName" />
    <result column="SYS_ORG_ID" jdbcType="VARCHAR" property="sysOrgId" />
    <result column="SYS_ORG_NAME" jdbcType="VARCHAR" property="sysOrgName" />
    <result column="SYS_COMMUNITY_TYPE" jdbcType="SMALLINT" property="sysCommunityType" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DELETED" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, AREA_CODE_PROVINCE, AREA_CODE_CITY, AREA_CODE_COUNTY, SOURCE_CODE, SOURCE_NAME, 
    SOURCE_ORG_CODE, SOURCE_ORG_NAME, SYS_ORG_ID, SYS_ORG_NAME, SYS_COMMUNITY_TYPE, CREATOR, 
    CREATE_TIME, UPDATOR, UPDATE_TIME, DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_fz_org_mapping
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from tb_cdcmr_fz_org_mapping
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_org_mapping (ID, AREA_CODE_PROVINCE, AREA_CODE_CITY, 
      AREA_CODE_COUNTY, SOURCE_CODE, SOURCE_NAME, 
      SOURCE_ORG_CODE, SOURCE_ORG_NAME, SYS_ORG_ID, 
      SYS_ORG_NAME, SYS_COMMUNITY_TYPE, CREATOR, 
      CREATE_TIME, UPDATOR, UPDATE_TIME, 
      DELETED)
    values (#{id,jdbcType=INTEGER}, #{areaCodeProvince,jdbcType=VARCHAR}, #{areaCodeCity,jdbcType=VARCHAR}, 
      #{areaCodeCounty,jdbcType=VARCHAR}, #{sourceCode,jdbcType=VARCHAR}, #{sourceName,jdbcType=VARCHAR}, 
      #{sourceOrgCode,jdbcType=VARCHAR}, #{sourceOrgName,jdbcType=VARCHAR}, #{sysOrgId,jdbcType=VARCHAR}, 
      #{sysOrgName,jdbcType=VARCHAR}, #{sysCommunityType,jdbcType=SMALLINT}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_org_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="areaCodeProvince != null">
        AREA_CODE_PROVINCE,
      </if>
      <if test="areaCodeCity != null">
        AREA_CODE_CITY,
      </if>
      <if test="areaCodeCounty != null">
        AREA_CODE_COUNTY,
      </if>
      <if test="sourceCode != null">
        SOURCE_CODE,
      </if>
      <if test="sourceName != null">
        SOURCE_NAME,
      </if>
      <if test="sourceOrgCode != null">
        SOURCE_ORG_CODE,
      </if>
      <if test="sourceOrgName != null">
        SOURCE_ORG_NAME,
      </if>
      <if test="sysOrgId != null">
        SYS_ORG_ID,
      </if>
      <if test="sysOrgName != null">
        SYS_ORG_NAME,
      </if>
      <if test="sysCommunityType != null">
        SYS_COMMUNITY_TYPE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="deleted != null">
        DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="areaCodeProvince != null">
        #{areaCodeProvince,jdbcType=VARCHAR},
      </if>
      <if test="areaCodeCity != null">
        #{areaCodeCity,jdbcType=VARCHAR},
      </if>
      <if test="areaCodeCounty != null">
        #{areaCodeCounty,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null">
        #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrgCode != null">
        #{sourceOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrgName != null">
        #{sourceOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sysOrgId != null">
        #{sysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="sysOrgName != null">
        #{sysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sysCommunityType != null">
        #{sysCommunityType,jdbcType=SMALLINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_org_mapping
    <set>
      <if test="areaCodeProvince != null">
        AREA_CODE_PROVINCE = #{areaCodeProvince,jdbcType=VARCHAR},
      </if>
      <if test="areaCodeCity != null">
        AREA_CODE_CITY = #{areaCodeCity,jdbcType=VARCHAR},
      </if>
      <if test="areaCodeCounty != null">
        AREA_CODE_COUNTY = #{areaCodeCounty,jdbcType=VARCHAR},
      </if>
      <if test="sourceCode != null">
        SOURCE_CODE = #{sourceCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        SOURCE_NAME = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrgCode != null">
        SOURCE_ORG_CODE = #{sourceOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrgName != null">
        SOURCE_ORG_NAME = #{sourceOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sysOrgId != null">
        SYS_ORG_ID = #{sysOrgId,jdbcType=VARCHAR},
      </if>
      <if test="sysOrgName != null">
        SYS_ORG_NAME = #{sysOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sysCommunityType != null">
        SYS_COMMUNITY_TYPE = #{sysCommunityType,jdbcType=SMALLINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        DELETED = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzOrgMapping">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_org_mapping
    set AREA_CODE_PROVINCE = #{areaCodeProvince,jdbcType=VARCHAR},
      AREA_CODE_CITY = #{areaCodeCity,jdbcType=VARCHAR},
      AREA_CODE_COUNTY = #{areaCodeCounty,jdbcType=VARCHAR},
      SOURCE_CODE = #{sourceCode,jdbcType=VARCHAR},
      SOURCE_NAME = #{sourceName,jdbcType=VARCHAR},
      SOURCE_ORG_CODE = #{sourceOrgCode,jdbcType=VARCHAR},
      SOURCE_ORG_NAME = #{sourceOrgName,jdbcType=VARCHAR},
      SYS_ORG_ID = #{sysOrgId,jdbcType=VARCHAR},
      SYS_ORG_NAME = #{sysOrgName,jdbcType=VARCHAR},
      SYS_COMMUNITY_TYPE = #{sysCommunityType,jdbcType=SMALLINT},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DELETED = #{deleted,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectBySysOrgId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_fz_org_mapping
    where sys_org_id = #{sysOrgid,jdbcType=INTEGER}
  </select>
</mapper>