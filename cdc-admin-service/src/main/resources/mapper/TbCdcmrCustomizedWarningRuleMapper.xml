<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningRuleMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="customized_warn_id" jdbcType="VARCHAR" property="customizedWarnId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="incubation" jdbcType="SMALLINT" property="incubation"/>
        <result column="max_life_cycle" jdbcType="SMALLINT" property="maxLifeCycle"/>
        <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode"/>
        <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , customized_warn_id, description, incubation, max_life_cycle, sms_send_type_code,
    sms_send_type_desc, "status", delete_flag, create_time, creator, update_time, updater
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String"
            resultType="com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO">
        select a.*,
               c.name                as name,
               c.monitor_data_source as monitorDataSource,
               c.warning_correlation as warningCorrelation
        from "tb_cdcmr_customized_warning_rule" as a
                 LEFT JOIN tb_cdcmr_customized_warn c on a.customized_warn_id = c.id
        where a.id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByWarnId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_customized_warning_rule"
        where delete_flag = '0'
        and customized_warn_id = #{warnId}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO">
        select a.*,
        (select count(b.id)
        from tb_cdcmr_customized_warning_rule_item b
        where a.id = b.warning_id
        and b.delete_flag = '0') as itemCount,
        c.name as name,
        c.monitor_data_source as monitorDataSource,
        c.warning_correlation as warningCorrelation,
        c.warning_type as warningType
        from "tb_cdcmr_customized_warning_rule" as a
        LEFT JOIN tb_cdcmr_customized_warn c on a.customized_warn_id = c.id
        where a.delete_flag = '0'
        <if test="name != null and name != ''">
            and c.name like concat('%', #{name}, '%')
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="warningType != null and warningType != ''">
            and c.warning_type = #{warningType}
        </if>
        order by a.update_time desc
    </select>
    <select id="getEnabledWarn" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO">
        select a.*,
               c.name                as name,
               c.monitor_data_source as monitorDataSource,
               c.warning_correlation as warningCorrelation
        from "tb_cdcmr_customized_warning_rule" as a
                 LEFT JOIN tb_cdcmr_customized_warn c on a.customized_warn_id = c.id
        where a.delete_flag = '0'
          and a.status = 1
        <if test="warningType != null and warningType != ''">
            and c.warning_type = #{warningType}
        </if>
    </select>
    <select id="getExportData" resultType="com.iflytek.cdc.admin.dto.CustomizedWarningRuleExportDataDto">
        select c.name,
               b.incubation,
               b.max_life_cycle,
               a.monitor_object,
               a.time_range,
               a.time_range_unit,
               a.medical_count,
               a.medical_attribute,
               a.warn_type
        from "tb_cdcmr_customized_warning_rule_item" a
                 left join tb_cdcmr_customized_warning_rule b on a.warning_id = b.id
                 left join tb_cdcmr_customized_warn c on b.customized_warn_id = c.id
        where a.delete_flag = '0'
          and b.delete_flag = '0'
          and c.delete_flag = '0'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_customized_warning_rule"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByWarnId">
        update tb_cdcmr_customized_warning_rule
        set delete_flag = '1'
        where customized_warn_id = #{warnId}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        insert into "tb_cdcmr_customized_warning_rule" (id, customized_warn_id, description,
                                                        incubation, max_life_cycle, sms_send_type_code,
                                                        sms_send_type_desc, "status", delete_flag,
                                                        create_time, creator, update_time,
                                                        updater)
        values (#{id,jdbcType=VARCHAR}, #{customizedWarnId,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{incubation,jdbcType=SMALLINT}, #{maxLifeCycle,jdbcType=SMALLINT}, #{smsSendTypeCode,jdbcType=VARCHAR},
                #{smsSendTypeDesc,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{deleteFlag,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        insert into "tb_cdcmr_customized_warning_rule"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="customizedWarnId != null">
                customized_warn_id,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="incubation != null">
                incubation,
            </if>
            <if test="maxLifeCycle != null">
                max_life_cycle,
            </if>
            <if test="smsSendTypeCode != null">
                sms_send_type_code,
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="customizedWarnId != null">
                #{customizedWarnId,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="incubation != null">
                #{incubation,jdbcType=SMALLINT},
            </if>
            <if test="maxLifeCycle != null">
                #{maxLifeCycle,jdbcType=SMALLINT},
            </if>
            <if test="smsSendTypeCode != null">
                #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        update "tb_cdcmr_customized_warning_rule"
        <set>
            <if test="customizedWarnId != null">
                customized_warn_id = #{customizedWarnId,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            incubation = #{incubation,jdbcType=SMALLINT},
            max_life_cycle = #{maxLifeCycle,jdbcType=SMALLINT},
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule">
        update "tb_cdcmr_customized_warning_rule"
        set customized_warn_id = #{customizedWarnId,jdbcType=VARCHAR},
            description        = #{description,jdbcType=VARCHAR},
            incubation         = #{incubation,jdbcType=SMALLINT},
            max_life_cycle     = #{maxLifeCycle,jdbcType=SMALLINT},
            sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            "status"           = #{status,jdbcType=SMALLINT},
            delete_flag        = #{deleteFlag,jdbcType=VARCHAR},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            creator            = #{creator,jdbcType=VARCHAR},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            updater            = #{updater,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>