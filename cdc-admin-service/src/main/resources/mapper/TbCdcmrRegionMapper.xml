<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrRegionMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_region-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="region_level" jdbcType="INTEGER" property="regionLevel" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="alias_name" jdbcType="VARCHAR" property="aliasName" />
    <result column="parent_region_code" jdbcType="VARCHAR" property="parentRegionCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="village_code" jdbcType="VARCHAR" property="villageCode" />
    <result column="village_name" jdbcType="VARCHAR" property="villageName" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="ur_type_code" jdbcType="VARCHAR" property="urTypeCode" />
    <result column="ur_type_name" jdbcType="VARCHAR" property="urTypeName" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="amap_area_code" jdbcType="VARCHAR" property="amapAreaCode" />
    <result column="amap_area_name" jdbcType="VARCHAR" property="amapAreaName" />
    <result column="longitude" jdbcType="NUMERIC" property="longitude" />
    <result column="latitude" jdbcType="NUMERIC" property="latitude" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="is_enable" jdbcType="VARCHAR" property="isEnable" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, region_level, region_code, region_name, alias_name, parent_region_code, province_code, 
    province_name, city_code, city_name, district_code, district_name, street_code, street_name, 
    village_code, village_name, group_code, group_name, ur_type_code, ur_type_name, address_detail, 
    amap_area_code, amap_area_name, longitude, latitude, data_source, source_id, memo, 
    is_enable, create_datetime, update_datetime, creator, updater, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_region
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="softDeleteById" parameterType="java.lang.String">
    update tb_cdcmr_region
    set delete_flag = '1', updater = #{loginUserId,jdbcType=VARCHAR}, update_datetime = localtimestamp
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
    <!--@mbg.generated-->
    insert into tb_cdcmr_region (id, region_level, region_code,
                                 region_name, alias_name, parent_region_code,
                                 province_code, province_name, city_code,
                                 city_name, district_code, district_name,
                                 street_code, street_name, village_code,
                                 village_name, group_code, group_name,
                                 ur_type_code, ur_type_name, address_detail,
                                 amap_area_code, amap_area_name, longitude,
                                 latitude, data_source, source_id,
                                 memo, is_enable, create_datetime,
                                 update_datetime, creator, updater,
                                 delete_flag)
    values (#{id,jdbcType=VARCHAR}, #{regionLevel,jdbcType=INTEGER}, #{regionCode,jdbcType=VARCHAR},
            #{regionName,jdbcType=VARCHAR}, #{aliasName,jdbcType=VARCHAR}, #{parentRegionCode,jdbcType=VARCHAR},
            #{provinceCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR},
            #{cityName,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, #{districtName,jdbcType=VARCHAR},
            #{streetCode,jdbcType=VARCHAR}, #{streetName,jdbcType=VARCHAR}, #{villageCode,jdbcType=VARCHAR},
            #{villageName,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR},
            #{urTypeCode,jdbcType=VARCHAR}, #{urTypeName,jdbcType=VARCHAR}, #{addressDetail,jdbcType=VARCHAR},
            #{amapAreaCode,jdbcType=VARCHAR}, #{amapAreaName,jdbcType=VARCHAR}, #{longitude,jdbcType=NUMERIC},
            #{latitude,jdbcType=NUMERIC}, #{dataSource,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR},
            #{memo,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR}, localtimestamp, localtimestamp,
            #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR},
            #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
    <!--@mbg.generated-->
    insert into tb_cdcmr_region
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="regionLevel != null">
        region_level,
      </if>
      <if test="regionCode != null and regionCode != ''">
        region_code,
      </if>
      <if test="regionName != null and regionName != ''">
        region_name,
      </if>
      <if test="aliasName != null and aliasName != ''">
        alias_name,
      </if>
      <if test="parentRegionCode != null and parentRegionCode != ''">
        parent_region_code,
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        province_code,
      </if>
      <if test="provinceName != null and provinceName != ''">
        province_name,
      </if>
      <if test="cityCode != null and cityCode != ''">
        city_code,
      </if>
      <if test="cityName != null and cityName != ''">
        city_name,
      </if>
      <if test="districtCode != null and districtCode != ''">
        district_code,
      </if>
      <if test="districtName != null and districtName != ''">
        district_name,
      </if>
      <if test="streetCode != null and streetCode != ''">
        street_code,
      </if>
      <if test="streetName != null and streetName != ''">
        street_name,
      </if>
      <if test="villageCode != null and villageCode != ''">
        village_code,
      </if>
      <if test="villageName != null and villageName != ''">
        village_name,
      </if>
      <if test="groupCode != null and groupCode != ''">
        group_code,
      </if>
      <if test="groupName != null and groupName != ''">
        group_name,
      </if>
      <if test="urTypeCode != null and urTypeCode != ''">
        ur_type_code,
      </if>
      <if test="urTypeName != null and urTypeName != ''">
        ur_type_name,
      </if>
      <if test="addressDetail != null and addressDetail != ''">
        address_detail,
      </if>
      <if test="amapAreaCode != null and amapAreaCode != ''">
        amap_area_code,
      </if>
      <if test="amapAreaName != null and amapAreaName != ''">
        amap_area_name,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="dataSource != null and dataSource != ''">
        data_source,
      </if>
      <if test="sourceId != null and sourceId != ''">
        source_id,
      </if>
      <if test="memo != null and memo != ''">
        memo,
      </if>
      <if test="isEnable != null and isEnable != ''">
        is_enable,
      </if>
      <if test="createDatetime != null">
        create_datetime,
      </if>
      <if test="updateDatetime != null">
        update_datetime,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="regionLevel != null">
        #{regionLevel,jdbcType=INTEGER},
      </if>
      <if test="regionCode != null and regionCode != ''">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null and regionName != ''">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null and aliasName != ''">
        #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="parentRegionCode != null and parentRegionCode != ''">
        #{parentRegionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null and provinceName != ''">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null and cityCode != ''">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null and cityName != ''">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null and districtCode != ''">
        #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="districtName != null and districtName != ''">
        #{districtName,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null and streetCode != ''">
        #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="villageCode != null and villageCode != ''">
        #{villageCode,jdbcType=VARCHAR},
      </if>
      <if test="villageName != null and villageName != ''">
        #{villageName,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null and groupName != ''">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="urTypeCode != null and urTypeCode != ''">
        #{urTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="urTypeName != null and urTypeName != ''">
        #{urTypeName,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null and addressDetail != ''">
        #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="amapAreaCode != null and amapAreaCode != ''">
        #{amapAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="amapAreaName != null and amapAreaName != ''">
        #{amapAreaName,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="dataSource != null and dataSource != ''">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null and sourceId != ''">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null and isEnable != ''">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDatetime != null">
        #{updateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
    <!--@mbg.generated-->
    update tb_cdcmr_region
    <set>
      <if test="regionLevel != null">
        region_level = #{regionLevel,jdbcType=INTEGER},
      </if>
      <if test="regionCode != null and regionCode != ''">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null and regionName != ''">
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null and aliasName != ''">
        alias_name = #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="parentRegionCode != null and parentRegionCode != ''">
        parent_region_code = #{parentRegionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null and provinceName != ''">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null and cityCode != ''">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null and cityName != ''">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null and districtCode != ''">
        district_code = #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="districtName != null and districtName != ''">
        district_name = #{districtName,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null and streetCode != ''">
        street_code = #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        street_name = #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="villageCode != null and villageCode != ''">
        village_code = #{villageCode,jdbcType=VARCHAR},
      </if>
      <if test="villageName != null and villageName != ''">
        village_name = #{villageName,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        group_code = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null and groupName != ''">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="urTypeCode != null and urTypeCode != ''">
        ur_type_code = #{urTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="urTypeName != null and urTypeName != ''">
        ur_type_name = #{urTypeName,jdbcType=VARCHAR},
      </if>
      <if test="addressDetail != null and addressDetail != ''">
        address_detail = #{addressDetail,jdbcType=VARCHAR},
      </if>
      <if test="amapAreaCode != null and amapAreaCode != ''">
        amap_area_code = #{amapAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="amapAreaName != null and amapAreaName != ''">
        amap_area_name = #{amapAreaName,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="dataSource != null and dataSource != ''">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null and sourceId != ''">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null and isEnable != ''">
        is_enable = #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDatetime != null">
        update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateGroupRegion" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
    <!--@mbg.generated-->
    update tb_cdcmr_region
    set region_level = #{regionLevel,jdbcType=INTEGER},
      region_code = #{regionCode,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=VARCHAR},
      alias_name = #{aliasName,jdbcType=VARCHAR},
      parent_region_code = #{parentRegionCode,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      district_code = #{districtCode,jdbcType=VARCHAR},
      district_name = #{districtName,jdbcType=VARCHAR},
      street_code = #{streetCode,jdbcType=VARCHAR},
      street_name = #{streetName,jdbcType=VARCHAR},
      village_code = #{villageCode,jdbcType=VARCHAR},
      village_name = #{villageName,jdbcType=VARCHAR},
      group_code = #{groupCode,jdbcType=VARCHAR},
      group_name = #{groupName,jdbcType=VARCHAR},
      ur_type_code = #{urTypeCode,jdbcType=VARCHAR},
      ur_type_name = #{urTypeName,jdbcType=VARCHAR},
      address_detail = #{addressDetail,jdbcType=VARCHAR},
      amap_area_code = #{amapAreaCode,jdbcType=VARCHAR},
      amap_area_name = #{amapAreaName,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=NUMERIC},
      latitude = #{latitude,jdbcType=NUMERIC},
      data_source = #{dataSource,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>


  <update id="enableGroupRegion">
    update tb_cdcmr_region
    set is_enable = #{isEnable,jdbcType=VARCHAR}, updater = #{loginUserId,jdbcType=VARCHAR}, update_datetime = localtimestamp
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <delete id="deleteByCodeLevel">
        update tb_cdcmr_region
        set delete_flag = '1'
        where region_level between #{currLevel} and #{toLevel}
            <if test="currLevel == 1">
                and province_code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="currLevel == 2">
                and city_code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="currLevel == 3">
                and district_code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="currLevel == 4">
                and street_code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="currLevel == 5">
                and village_code = #{code,jdbcType=VARCHAR}
            </if>
    </delete>

    <insert id="batchUpsert">
      insert into tb_cdcmr_region (id, region_level, region_code, region_name, alias_name, parent_region_code,
                                          province_code, province_name, city_code, city_name,
                                          district_code, district_name, street_code, street_name,
                                          village_code, village_name, group_code, group_name,
                                          ur_type_code, ur_type_name, address_detail,
                                          amap_area_code, amap_area_name, longitude, latitude,
                                          data_source, source_id, memo,
                                          is_enable, create_datetime, update_datetime,
                                          creator, updater, delete_flag)
        values
      <foreach collection="regionList" item="item" separator=",">
        (#{item.id,jdbcType=VARCHAR}, #{item.regionLevel,jdbcType=INTEGER}, #{item.regionCode,jdbcType=VARCHAR},
         #{item.regionName,jdbcType=VARCHAR}, #{item.aliasName,jdbcType=VARCHAR},
         #{item.parentRegionCode,jdbcType=VARCHAR},
         #{item.provinceCode,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR},
         #{item.cityCode,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR},
         #{item.districtCode,jdbcType=VARCHAR}, #{item.districtName,jdbcType=VARCHAR},
         #{item.streetCode,jdbcType=VARCHAR}, #{item.streetName,jdbcType=VARCHAR},
         #{item.villageCode,jdbcType=VARCHAR}, #{item.villageName,jdbcType=VARCHAR},
         #{item.groupCode,jdbcType=VARCHAR}, #{item.groupName,jdbcType=VARCHAR},
         #{item.urTypeCode,jdbcType=VARCHAR}, #{item.urTypeName,jdbcType=VARCHAR},
         #{item.addressDetail,jdbcType=VARCHAR},
         #{item.amapAreaCode,jdbcType=VARCHAR}, #{item.amapAreaName,jdbcType=VARCHAR},
         #{item.longitude,jdbcType=NUMERIC}, #{item.latitude,jdbcType=NUMERIC},
         #{item.dataSource,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, #{item.memo,jdbcType=VARCHAR},
         #{item.isEnable,jdbcType=VARCHAR}, localtimestamp, localtimestamp,
         #{item.creator,jdbcType=VARCHAR}, #{item.updater,jdbcType=VARCHAR}, #{item.deleteFlag,jdbcType=VARCHAR})
        </foreach>
      ON CONFLICT (region_code) do update set region_level       = excluded.region_level,
                                              region_name        = excluded.region_name,
                                              alias_name         = excluded.alias_name,
                                              parent_region_code = excluded.parent_region_code,
                                              province_code      = excluded.province_code,
                                              province_name      = excluded.province_name,
                                              city_code          = excluded.city_code,
                                              city_name          = excluded.city_name,
                                              district_code      = excluded.district_code,
                                              district_name      = excluded.district_name,
                                              street_code        = excluded.street_code,
                                              street_name        = excluded.street_name,
                                              village_code       = excluded.village_code,
                                              village_name       = excluded.village_name,
                                              group_code         = excluded.group_code,
                                              group_name         = excluded.group_name,
                                              ur_type_code       = excluded.ur_type_code,
                                              ur_type_name       = excluded.ur_type_name,
                                              address_detail     = excluded.address_detail,
                                              amap_area_code     = excluded.amap_area_code,
                                              amap_area_name     = excluded.amap_area_name,
                                              longitude          = excluded.longitude,
                                              latitude           = excluded.latitude,
                                              data_source        = excluded.data_source,
                                              source_id          = excluded.source_id,
                                              is_enable          = excluded.is_enable,
                                              memo               = excluded.memo,
                                              updater            = excluded.updater,
                                              update_datetime    = localtimestamp,
                                              delete_flag        = excluded.delete_flag
    </insert>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_region
        where region_code = #{regionCode,jdbcType=VARCHAR}
        and delete_flag = '0'
    </select>

  <select id="selectByParentCodeAndName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_region
    where region_name = #{regionName,jdbcType=VARCHAR}
      and parent_region_code = #{parentCode,jdbcType=VARCHAR}
      and delete_flag = '0'
  </select>

    <select id="selectByCodeLevelUpdateTime" resultType="com.iflytek.cdc.admin.entity.TbCdcmrRegion">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_region
        where region_level between #{currLevel,jdbcType=INTEGER} and #{toLevel,jdbcType=INTEGER}
          and delete_flag = '0'
          and update_datetime between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        <if test="currLevel == 1">
            and province_code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="currLevel == 2">
            and city_code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="currLevel == 3">
            and district_code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="currLevel == 4">
            and street_code = #{code,jdbcType=VARCHAR}
        </if>
        <if test="currLevel == 5">
            and village_code = #{code,jdbcType=VARCHAR}
        </if>
    </select>

    <update id="batchUpdateAmapInfos">
        <foreach collection="regionList" item="item" separator=";">
            update tb_cdcmr_region
            set amap_area_code = #{item.amapAreaCode,jdbcType=VARCHAR},
                amap_area_name = #{item.amapAreaName,jdbcType=VARCHAR},
                longitude      = #{item.longitude,jdbcType=DOUBLE},
                latitude       = #{item.longitude,jdbcType=DOUBLE}
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

  <select id="regionSearch" resultType="com.iflytek.cdc.admin.vo.region.GroupRegionVO">
    select *, alias_name as group_alias_name
    from tb_cdcmr_region r
    where delete_flag = '0'
      and region_level = #{param.regionLevel,jdbcType=INTEGER}
      <if test="param.provinceCode != null and param.provinceCode != ''">
        and province_code = #{param.provinceCode,jdbcType=VARCHAR}
      </if>
      <if test="param.cityCode != null and param.cityCode != ''">
        and city_code = #{param.cityCode,jdbcType=VARCHAR}
      </if>
      <if test="param.districtCode != null and param.districtCode != ''">
        and district_code = #{param.districtCode,jdbcType=VARCHAR}
      </if>
      <if test="param.streetCode != null and param.streetCode != ''">
        and street_code = #{param.streetCode,jdbcType=VARCHAR}
      </if>
      <if test="param.villageCode != null and param.villageCode != ''">
        and village_code = #{param.villageCode,jdbcType=VARCHAR}
      </if>
      <choose>
        <when test="param.groupCondition == 1 and param.groupName != null">
          and group_name like concat('%', #{param.groupName}, '%')
        </when>
        <when test="param.groupCondition == 2 and param.groupName != null">
          and alias_name like concat('%', #{param.groupName}, '%')
        </when>
      </choose>
  </select>

  <select id="selectByCodesLevel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_region
    where region_level = #{level,jdbcType=INTEGER}
    and delete_flag = '0'
    <choose>
      <when test="codes != null and codes.size() != 0">
        and region_code in
        <foreach collection="codes" item="item" open="(" close=")" separator=",">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </when>
      <otherwise>
        and region_code is null
      </otherwise>
    </choose>

  </select>

  <select id="selectByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_region
    where delete_flag = '0'
    and region_level = #{regionLevel,jdbcType=INTEGER}
    <if test="regionParam.id != null and regionParam.id != ''">
      and id = #{regionParam.id,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.regionCode != null and regionParam.regionCode != ''">
      and region_code = #{regionParam.regionCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.parentRegionCode != null and regionParam.parentRegionCode != ''">
      and parent_region_code = #{regionParam.parentRegionCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.provinceCode != null and regionParam.provinceCode != ''">
      and province_code = #{regionParam.provinceCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.cityCode != null and regionParam.cityCode != ''">
      and city_code = #{regionParam.cityCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.districtCode != null and regionParam.districtCode != ''">
      and district_code = #{regionParam.districtCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.streetCode != null and regionParam.streetCode != ''">
      and street_code = #{regionParam.streetCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.villageCode != null and regionParam.villageCode != ''">
      and village_code = #{regionParam.villageCode,jdbcType=VARCHAR}
    </if>
    <if test="regionParam.groupCode != null and regionParam.groupCode != ''">
      and group_code = #{regionParam.groupCode,jdbcType=VARCHAR}
    </if>

  </select>


  <select id="selectRecurseByParam" resultMap="BaseResultMap">
    with recursive recur_region as (
      select id,
             region_level,
             region_code,
             region_name,
             parent_region_code
      from tb_cdcmr_region
      where delete_flag = '0'
        and region_level between #{fromLevel,jdbcType=INTEGER} and #{toLevel,jdbcType=INTEGER}
      <if test="regionParam.id != null and regionParam.id != ''">
        and id = #{regionParam.id,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.regionCode != null and regionParam.regionCode != ''">
        and region_code = #{regionParam.regionCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.parentRegionCode != null and regionParam.parentRegionCode != ''">
        and parent_region_code = #{regionParam.parentRegionCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.provinceCode != null and regionParam.provinceCode != ''">
        and province_code = #{regionParam.provinceCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.cityCode != null and regionParam.cityCode != ''">
        and city_code = #{regionParam.cityCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.districtCode != null and regionParam.districtCode != ''">
        and district_code = #{regionParam.districtCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.streetCode != null and regionParam.streetCode != ''">
        and street_code = #{regionParam.streetCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.villageCode != null and regionParam.villageCode != ''">
        and village_code = #{regionParam.villageCode,jdbcType=VARCHAR}
      </if>
      <if test="regionParam.groupCode != null and regionParam.groupCode != ''">
        and group_code = #{regionParam.groupCode,jdbcType=VARCHAR}
      </if>
      union
      select r.id,
             r.region_level,
             r.region_code,
             r.region_name,
             r.parent_region_code
      from tb_cdcmr_region r
             inner join recur_region rh on rh.parent_region_code = r.region_code
      where r.region_level between #{fromLevel,jdbcType=INTEGER} and #{toLevel,jdbcType=INTEGER}
    )
    select *
    from recur_region;

  </select>

  <select id="selectVillageRegionsByNameInfo" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcmr_region
    where delete_flag = '0'
      and region_level = 5
    <if test="nameInfos != null and nameInfos.size() != 0">
      and (
      <foreach collection="nameInfos" item="villageInfo" separator="OR">
        (province_name = #{villageInfo[0],jdbcType=VARCHAR}
          AND city_name = #{villageInfo[1],jdbcType=VARCHAR}
          AND district_name = #{villageInfo[2],jdbcType=VARCHAR}
          AND street_name = #{villageInfo[3],jdbcType=VARCHAR}
          AND village_name = #{villageInfo[4],jdbcType=VARCHAR})
      </foreach>
      )
    </if>
  </select>

</mapper>