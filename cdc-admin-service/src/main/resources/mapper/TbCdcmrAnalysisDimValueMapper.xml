<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimValueMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimValue">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_analysis_dim_value-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="analysis_dim_id" jdbcType="VARCHAR" property="analysisDimId" />
    <result column="data_attr_value_id" jdbcType="VARCHAR" property="dataAttrValueId" />
    <result column="analysis_flag" jdbcType="VARCHAR" property="analysisFlag" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, analysis_dim_id, data_attr_value_id, analysis_flag, delete_flag, create_time, 
    update_time
  </sql>
</mapper>