<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.DictMappingMapper">
    <select id="queryTargetInfo" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.DictMappingInfo">
        select  origin_group_code as originGroupCode,origin_code as originCode,origin_name as originName,
        target_code as targetCode,target_name as targetName from tb_cdcmr_dict_mapping_detail
        where 1=1
        <if test="originCode != null and originCode != ''">
            and origin_code=#{originCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryOriginInfo" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.DictMappingInfo">
        select  origin_group_code as originGroupCode,origin_code as originCode,origin_name as originName,
        target_code as targetCode,target_name as targetName from tb_cdcmr_dict_mapping_detail
        where 1=1
        <if test="targetCode != null and targetCode != ''">
            and target_code=#{targetCode,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>