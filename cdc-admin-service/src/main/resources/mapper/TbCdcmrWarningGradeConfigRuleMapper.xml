<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeConfigRuleMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="config_id" jdbcType="VARCHAR" property="configId"/>
        <result column="grade_type" jdbcType="SMALLINT" property="gradeType"/>
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName"/>
        <result column="grade_code" jdbcType="VARCHAR" property="gradeCode"/>
        <result column="alert_value" jdbcType="NUMERIC" property="alertValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , config_id, grade_type, grade_name, grade_code, alert_value, remark, update_time,
    update_user, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config_rule"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_warning_grade_config_rule"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByGradeCode">
        update "tb_cdcmr_warning_grade_config_rule"
        set is_deleted = 1
        where grade_code = #{gradeCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        insert into "tb_cdcmr_warning_grade_config_rule" (id, config_id, grade_type,
                                                          grade_name, grade_code, alert_value,
                                                          remark, update_time, update_user,
                                                          is_deleted)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=VARCHAR}, #{gradeType,jdbcType=SMALLINT},
                #{gradeName,jdbcType=VARCHAR}, #{gradeCode,jdbcType=VARCHAR}, #{alertValue,jdbcType=NUMERIC},
                #{remark,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
                #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        insert into "tb_cdcmr_warning_grade_config_rule"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="gradeType != null">
                grade_type,
            </if>
            <if test="gradeName != null">
                grade_name,
            </if>
            <if test="gradeCode != null">
                grade_code,
            </if>
            <if test="alertValue != null">
                alert_value,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=VARCHAR},
            </if>
            <if test="gradeType != null">
                #{gradeType,jdbcType=SMALLINT},
            </if>
            <if test="gradeName != null">
                #{gradeName,jdbcType=VARCHAR},
            </if>
            <if test="gradeCode != null">
                #{gradeCode,jdbcType=VARCHAR},
            </if>
            <if test="alertValue != null">
                #{alertValue,jdbcType=NUMERIC},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        update "tb_cdcmr_warning_grade_config_rule"
        <set>
            <if test="configId != null">
                config_id = #{configId,jdbcType=VARCHAR},
            </if>
            <if test="gradeType != null">
                grade_type = #{gradeType,jdbcType=SMALLINT},
            </if>
            <if test="gradeName != null">
                grade_name = #{gradeName,jdbcType=VARCHAR},
            </if>
            <if test="gradeCode != null">
                grade_code = #{gradeCode,jdbcType=VARCHAR},
            </if>
            <if test="alertValue != null">
                alert_value = #{alertValue,jdbcType=NUMERIC},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        update "tb_cdcmr_warning_grade_config_rule"
        set config_id   = #{configId,jdbcType=VARCHAR},
            grade_type  = #{gradeType,jdbcType=SMALLINT},
            grade_name  = #{gradeName,jdbcType=VARCHAR},
            grade_code  = #{gradeCode,jdbcType=VARCHAR},
            alert_value = #{alertValue,jdbcType=NUMERIC},
            remark      = #{remark,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_user = #{updateUser,jdbcType=VARCHAR},
            is_deleted  = #{isDeleted,jdbcType=SMALLINT}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="getByConfigId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config_rule"
        where config_id = #{id,jdbcType=VARCHAR}
        and is_deleted !='1'
        order by alert_value desc
    </select>
    <select id="getAllByConfigId" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config_rule"
        where config_id = #{id,jdbcType=VARCHAR}
    </select>

    <update id="deleteOtherByIds">
        update "tb_cdcmr_warning_grade_config_rule" set is_deleted = 1
        where config_id = #{configId,jdbcType=VARCHAR}
        <if test="idList.size() != 0">
            and id not in
            <foreach collection="idList" open="(" separator="," close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
    </update>

    <insert id="upsertRules">
        <foreach collection="recordList" item="item" separator=";">
            insert into "tb_cdcmr_warning_grade_config_rule"
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    id,
                </if>
                <if test="item.configId != null">
                    config_id,
                </if>
                <if test="item.gradeType != null">
                    grade_type,
                </if>
                <if test="item.gradeName != null">
                    grade_name,
                </if>
                <if test="item.gradeCode != null">
                    grade_code,
                </if>
                <if test="item.alertValue != null">
                    alert_value,
                </if>
                <if test="item.remark != null">
                    remark,
                </if>
                <if test="item.updateTime != null">
                    update_time,
                </if>
                <if test="item.updateUser != null">
                    update_user,
                </if>
                <if test="item.isDeleted != null">
                    is_deleted,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=VARCHAR},
                </if>
                <if test="item.configId != null">
                    #{item.configId,jdbcType=VARCHAR},
                </if>
                <if test="item.gradeType != null">
                    #{item.gradeType,jdbcType=SMALLINT},
                </if>
                <if test="item.gradeName != null">
                    #{item.gradeName,jdbcType=VARCHAR},
                </if>
                <if test="item.gradeCode != null">
                    #{item.gradeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.alertValue != null">
                    #{item.alertValue,jdbcType=NUMERIC},
                </if>
                <if test="item.remark != null">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateUser != null">
                    #{item.updateUser,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null">
                    #{item.isDeleted,jdbcType=SMALLINT},
                </if>
            </trim>
            on conflict(id) do
            update
            set
            config_id =excluded.config_id,
            grade_type =excluded.grade_type,
            grade_name =excluded.grade_name,
            grade_code =excluded.grade_code,
            alert_value =excluded.alert_value,
            remark =excluded.remark,
            update_time =excluded.update_time,
            update_user =excluded.update_user,
            is_deleted =excluded.is_deleted
        </foreach>
    </insert>
</mapper>