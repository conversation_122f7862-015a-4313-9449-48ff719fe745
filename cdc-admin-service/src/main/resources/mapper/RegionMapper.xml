<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.RegionMapper">
    <select id="getRegionList" resultType="java.util.Map">
        SELECT b.province,
               a.city,
               REGEXP_REPLACE(b.province, '省|市|自治区|特别行政区|维吾尔|壮族|回族', '', 'g') AS short_province,
               REGEXP_REPLACE(a.city, '市|自治区|自治州|盟|土家族|苗族|壮族|回族|藏族|彝族|羌族|布依族|哈尼族|朝鲜族|傣族|白族|景颇族|傈僳族|蒙古族|侗族|地区|蒙古自治州', '',
                              'g') AS short_city
          FROM tb_cdcew_district a
          JOIN tb_cdcew_district b ON a.parent_id::TEXT = b.code
         WHERE b.parent_id = 1
    </select>

    <select id="findAll" resultType="com.iflytek.cdc.admin.entity.Area">
        select
       *
        from tb_cdcew_district
    </select>
    <select id="getMappingDistrictCodeList" resultType="java.lang.String">
        SELECT
            district_code
        FROM
            tb_cdcew_district_mapping
        WHERE
            map_district_code != district_code
    </select>
</mapper>