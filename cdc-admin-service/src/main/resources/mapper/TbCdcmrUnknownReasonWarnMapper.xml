<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarn">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode"/>
        <result column="disease_name" jdbcType="VARCHAR" property="diseaseName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="incubation" jdbcType="SMALLINT" property="incubation"/>
        <result column="sms_send_type_code" jdbcType="VARCHAR" property="smsSendTypeCode"/>
        <result column="sms_send_type_desc" jdbcType="VARCHAR" property="smsSendTypeDesc"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , disease_code, disease_name, remark, "status", is_deleted, update_time, updater,
    incubation, sms_send_type_code, sms_send_type_desc
    </sql>
    <insert id="insert">
        insert into "tb_cdcmr_unknown_reason_warn" (id, disease_code, disease_name, remark,
                                                    "status", is_deleted, update_time,
                                                    updater, incubation, sms_send_type_code,
                                                    sms_send_type_desc)
        values (#{id,jdbcType=VARCHAR}, #{diseaseCode,jdbcType=VARCHAR}, #{diseaseName,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=VARCHAR}, #{incubation,jdbcType=SMALLINT}, #{smsSendTypeCode,jdbcType=VARCHAR},
                #{smsSendTypeDesc,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKeySelective">
        update "tb_cdcmr_unknown_reason_warn"
        <set>
            <if test="diseaseCode != null">
                disease_code = #{diseaseCode,jdbcType=VARCHAR},
            </if>
            <if test="diseaseName != null">
                disease_name = #{diseaseName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
                incubation = #{incubation,jdbcType=SMALLINT},
            <if test="smsSendTypeCode != null">
                sms_send_type_code = #{smsSendTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="smsSendTypeDesc != null">
                sms_send_type_desc = #{smsSendTypeDesc,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateStatusByPrimaryKey">
        update "tb_cdcmr_unknown_reason_warn"
        set
            "status" = #{status,jdbcType=SMALLINT}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteById">
        update "tb_cdcmr_unknown_reason_warn"
        set is_deleted = 1
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <select id="getByDiseaseCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarn">
        select *
        from tb_cdcmr_unknown_reason_warn
        where disease_code = #{diseaseCode,jdbcType=VARCHAR}
          and is_deleted = 0 limit 1
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.dto.UnknownReasonWarnDto">
        select * ,(select count(b.id) from tb_cdcmr_unknown_reason_warn_rule b where a.id = b.warn_id and
        b.is_deleted = 0) as ruleCount from "tb_cdcmr_unknown_reason_warn" a
        where is_deleted = 0
        <if test="diseaseName!= null and diseaseName !=''">
            and disease_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="status != null">
            and "status" = #{status,jdbcType=SMALLINT}
        </if>
        order by id
    </select>
    <select id="selectByPrimaryKey" resultType="com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonWarn">
        select *
        from tb_cdcmr_unknown_reason_warn
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getAllList" resultType="com.iflytek.cdc.admin.dto.UnknownReasonWarnDto">
        select * from "tb_cdcmr_unknown_reason_warn"
        where is_deleted = 0 and "status" = 1
        <if test="diseaseCode!= null and diseaseCode !=''">
            and disease_code = #{diseaseCode}
        </if>
        order by id
    </select>

</mapper>