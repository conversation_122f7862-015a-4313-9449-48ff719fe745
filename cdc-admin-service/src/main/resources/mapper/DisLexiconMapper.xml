<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.DisLexiconMapper">

    <select id="selectByDiseasesCode" resultType="com.iflytek.cdc.admin.entity.DisLexicon">
        select *
        from tb_cdcmr_disease_lexicon
        where diseases_code = #{diseasesCode}
        and is_delete = '0'
    </select>
    <select id="getDiseaseLexiconPage" resultType="com.iflytek.cdc.admin.entity.DisLexicon" parameterType="com.iflytek.cdc.admin.dto.SearchDiseaseLexiconDTO">
        SELECT *
        FROM tb_cdcmr_disease_lexicon
        <where>
            is_delete = '0'
            <if test="dto.diseasesName != null and dto.diseasesName != ''">
                AND diseases_name LIKE CONCAT('%', #{dto.diseasesName}, '%')
            </if>
            <if test="dto.diseasesType != null and dto.diseasesType != ''">
                AND diseases_type = #{dto.diseasesType}
            </if>
            <if test="dto.businessType != null and dto.businessType != ''">
                AND business_type = #{dto.businessType}
            </if>
            <if test="dto.diseaseCode != null and dto.diseaseCode != ''">
                AND diseases_code LIKE CONCAT('%', #{dto.diseaseCode}, '%')
            </if>
        </where>
        ORDER BY diseases_code
    </select>

    <update id="updateDeleteFlag" parameterType="com.iflytek.cdc.admin.dto.DiseaseLexiconDTO">
        UPDATE tb_cdcmr_disease_lexicon
        SET delete_flag = #{dto.isDelete}  <!-- 设置 delete_flag 为传入的值 -->
        WHERE diseases_code = #{dto.diseasesCode}  <!-- 根据 diseases_code 进行更新 -->
    </update>

    <select id="countChildDiseases" resultType="int">
        <![CDATA[
            SELECT COUNT(*)
            FROM tb_cdcmr_disease_lexicon
            WHERE parent_diseases_name = (
                SELECT diseases_name
                FROM tb_cdcmr_disease_lexicon
                WHERE id = #{diseaseId}
            )
            AND is_delete = '0';
        ]]>
    </select>

    <select id="getChildDiseases" resultType="java.lang.String">
        SELECT id
        FROM tb_cdcmr_disease_lexicon
        WHERE parent_diseases_name = (
            SELECT diseases_name
            FROM tb_cdcmr_disease_lexicon
            WHERE id = #{parentDiseaseId}
        )
        AND is_delete = '0';
    </select>

</mapper>