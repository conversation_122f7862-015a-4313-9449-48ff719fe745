<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionOrgMapper">
    <insert id="saveExportPermissionOrg">
        insert into tb_cdcmr_export_permission_org (id,org_id,export_id, approval_required,approval_time_limit,approval_levels
        ,creator_id,creator_time,creator,updater_id,updater_time,updater)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=VARCHAR},#{item.orgId,jdbcType=VARCHAR},#{item.exportId,jdbcType=VARCHAR},
            #{item.approvalRequired},#{item.approvalTimeLimit},#{item.approvalLevels}
            ,#{item.creatorId},#{item.creatorTime},#{item.creator},#{item.updaterId},#{item.updaterTime},#{item.updater})
        </foreach>
    </insert>
</mapper>