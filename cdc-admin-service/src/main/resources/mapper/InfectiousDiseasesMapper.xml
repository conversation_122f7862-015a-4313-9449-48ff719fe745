<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.InfectiousDiseasesMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_infectious_diseases-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="diseases_code" jdbcType="VARCHAR" property="diseasesCode" />
    <result column="diseases_name" jdbcType="VARCHAR" property="diseasesName" />
    <result column="diseases_type" jdbcType="VARCHAR" property="diseasesType" />
    <result column="diseases_type_name" jdbcType="VARCHAR" property="diseasesTypeName" />
    <result column="diseases_classify" jdbcType="VARCHAR" property="diseasesClassify" />
    <result column="diseases_classify_name" jdbcType="VARCHAR" property="diseasesClassifyName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_enable" jdbcType="VARCHAR" property="isEnable" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
    <result column="infect_class_code" jdbcType="VARCHAR" property="infectClassCode" />
    <result column="infect_class_name" jdbcType="VARCHAR" property="infectClassName" />
    <result column="transmission_type_code" jdbcType="VARCHAR" property="transmissionTypeCode" />
    <result column="transmission_type_name" jdbcType="VARCHAR" property="transmissionTypeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, diseases_code, diseases_name, diseases_type, diseases_type_name, diseases_classify,
    diseases_classify_name, remark, is_enable, create_user, create_time, update_user,
    update_time, sync_time, is_delete,
    infect_class_code, infect_class_name, transmission_type_code, transmission_type_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diseases
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcmr_infectious_diseases
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    insert into tb_cdcmr_infectious_diseases (id, diseases_code, diseases_name, 
      diseases_type, diseases_type_name, diseases_classify, 
      diseases_classify_name, remark, is_enable, 
      create_user, create_time, update_user, 
      update_time, sync_time, is_delete,
      infect_class_code, infect_class_name, transmission_type_code, transmission_type_name
      )
    values (#{id,jdbcType=VARCHAR}, #{diseasesCode,jdbcType=VARCHAR}, #{diseasesName,jdbcType=VARCHAR}, 
      #{diseasesType,jdbcType=VARCHAR}, #{diseasesTypeName,jdbcType=VARCHAR}, #{diseasesClassify,jdbcType=VARCHAR}, 
      #{diseasesClassifyName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{syncTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=VARCHAR},
      #{infectClassCode,jdbcType=VARCHAR}, #{infectClassName,jdbcType=VARCHAR},
      #{transmissionTypeCode,jdbcType=VARCHAR}, #{transmissionTypeName,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diseases
    set diseases_code = #{diseasesCode,jdbcType=VARCHAR},
      diseases_name = #{diseasesName,jdbcType=VARCHAR},
      diseases_type = #{diseasesType,jdbcType=VARCHAR},
      diseases_type_name = #{diseasesTypeName,jdbcType=VARCHAR},
      diseases_classify = #{diseasesClassify,jdbcType=VARCHAR},
      diseases_classify_name = #{diseasesClassifyName,jdbcType=VARCHAR},
      infect_class_code = #{infectClassCode,jdbcType=VARCHAR},
      infect_class_name = #{infectClassName,jdbcType=VARCHAR},
      transmission_type_code = #{transmissionTypeCode,jdbcType=VARCHAR},
      transmission_type_name = #{transmissionTypeName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      sync_time = #{syncTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryInfecInfo" parameterType="com.iflytek.cdc.admin.dto.SearchInfecInfoDTO"
          resultType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    select
    id as id , diseases_code as diseasesCode, diseases_name as diseasesName , diseases_type as diseasesType,
    diseases_type_name as diseasesTypeName,
    diseases_classify as diseasesClassify,diseases_classify_name as diseasesClassifyName,
    infect_class_code, infect_class_name, transmission_type_code, transmission_type_name,
    remark as remark , is_enable as isEnable,
    create_user as createUser, create_time as createTime, update_user as updateUser, update_time as updateTime
    from tb_cdcmr_infectious_diseases
    where 1=1
    <if test="diseasesName != null and diseasesName != ''">
      and diseases_name like concat('%',#{diseasesName,jdbcType=VARCHAR},'%')
    </if>
    <if test="diseasesType != null and diseasesType != ''">
      and diseases_type = #{diseasesType,jdbcType=VARCHAR}
    </if>
    <if test="diseasesClassify != null and diseasesClassify !=''">
      and diseases_classify = #{diseasesClassify,jdbcType=VARCHAR}
    </if>
    order by diseases_code
  </select>

  <select id="queryInfectedInfoByGrade" parameterType="com.iflytek.cdc.admin.dto.SearchInfecInfoDTO"
          resultType="com.iflytek.cdc.admin.dto.InfectedGradeConfigVO">
    SELECT

    t2.remark AS remark,
    COALESCE ( t2.status,0) AS status,
    t1.*,
    (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
    t3.is_deleted = 0)as ruleCount
    FROM
    tb_cdcmr_infectious_diseases t1
    LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'infected'
    AND t1.diseases_code = t2.disease_code
    where 1=1
    <if test="diseasesName != null and diseasesName != ''">
      and t1.diseases_name like concat('%',#{diseasesName,jdbcType=VARCHAR},'%')
    </if>
    <if test="diseasesType != null and diseasesType != ''">
      and t1.diseases_type = #{diseasesType,jdbcType=VARCHAR}
    </if>
    <if test="diseasesClassify != null and diseasesClassify !=''">
      and t1.diseases_classify = #{diseasesClassify,jdbcType=VARCHAR}
    </if>
    <if test="status != null ">
      and COALESCE ( t2.status,0) = #{status}
    </if>
    order by diseases_code
  </select>

  <insert id="insertInfectiousDiseases" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    insert into tb_cdcmr_infectious_diseases(id, diseases_code, diseases_name,
    diseases_type, diseases_type_name, diseases_classify,
    diseases_classify_name, remark, is_enable,
    create_user, create_time, update_user,
    update_time, sync_time, is_delete,
    infect_class_code, infect_class_name, transmission_type_code, transmission_type_name
    )
    values
    <foreach collection="infectiousDiseases" item="sn" separator="," index="index">
      (#{sn.id,jdbcType=VARCHAR}, #{sn.diseasesCode,jdbcType=VARCHAR}, #{sn.diseasesName,jdbcType=VARCHAR},
      #{sn.diseasesType,jdbcType=VARCHAR}, #{sn.diseasesTypeName,jdbcType=VARCHAR},
      #{sn.diseasesClassify,jdbcType=VARCHAR},
      #{sn.diseasesClassifyName,jdbcType=VARCHAR},#{sn.remark,jdbcType=VARCHAR},
      #{sn.isEnable,jdbcType=VARCHAR}, #{sn.createUser,jdbcType=VARCHAR},
      now(), #{sn.updateUser,jdbcType=VARCHAR}, now(),now(),
      #{sn.infectClassCode,jdbcType=VARCHAR}, #{sn.infectClassName,jdbcType=VARCHAR},
      #{sn.transmissionTypeCode,jdbcType=VARCHAR}, #{sn.transmissionTypeName,jdbcType=VARCHAR})
    </foreach>
  </insert>


  <update id="updateInfecInfo" parameterType="com.iflytek.cdc.admin.dto.UpdateInfecDTO">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diseases
    <set>
      <if test="isEnable != null and isEnable != ''">
        is_enable= #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user=#{updateUser},
      </if>
      update_time=now(),
      sync_time=now()
    </set>
    where id in
    <foreach collection="ids" open="(" separator="," close=")" item="item" index="index">
      #{item}
    </foreach>
  </update>

  <update id="updateInfectiousDiseases" parameterType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    update tb_cdcmr_infectious_diseases
    <set>

    <if test="diseasesType != null">
      diseases_type = #{diseasesType,jdbcType=VARCHAR},
    </if>
    <if test="diseasesTypeName != null">
      diseases_type_name = #{diseasesTypeName,jdbcType=VARCHAR},
    </if>
    <if test="diseasesClassify != null">
      diseases_classify = #{diseasesClassify,jdbcType=VARCHAR},
    </if>
    <if test="diseasesClassifyName != null">
      diseases_classify_name = #{diseasesClassifyName,jdbcType=VARCHAR},
    </if>

      <if test="infectClassCode != null">
        infect_class_code = #{infectClassCode,jdbcType=VARCHAR},
      </if>
      <if test="infectClassName != null">
        infect_class_name = #{infectClassName,jdbcType=VARCHAR},
      </if>
      <if test="transmissionTypeCode != null">
        transmission_type_code = #{transmissionTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="transmissionTypeName != null">
        transmission_type_name = #{transmissionTypeName,jdbcType=VARCHAR},
      </if>
    <if test="remark != null">
      remark = #{remark,jdbcType=VARCHAR},
    </if>
    <if test="isEnable != null">
      is_enable = #{isEnable,jdbcType=VARCHAR},
    </if>
    <if test="updateUser != null">
      update_user = #{updateUser,jdbcType=VARCHAR},
    </if>
      UPDATE_TIME = now()
    </set>
   where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="queryInfoById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diseases
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="findInfectiousDiagnosisByInfectedCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diseases
    where diseases_code = #{infectedCode,jdbcType=VARCHAR} and is_enable = '1'
  </select>
  <select id="selectByDiseasesCode" resultType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diseases
    where diseases_code = #{diseasesCode,jdbcType=VARCHAR}
  </select>
  <select id="selectByDiseasesName" resultType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    select
    <include refid="Base_Column_List" />
    from tb_cdcmr_infectious_diseases
    where diseases_name = #{diseasesName,jdbcType=VARCHAR}
  </select>
    <select id="findByInfectClass" resultType="com.iflytek.cdc.admin.entity.CascadeVO">
      select diseases_code as value, diseases_name as label
      from tb_cdcmr_infectious_diseases
      where is_enable ='1'
      <if test="infectClassCode != null and infectClassCode != ''">
        and infect_class_code = #{infectClassCode}
      </if>
      order by diseases_code
    </select>
    <update id="updateCodeName" parameterType="java.lang.String">
    update tb_cdcmr_infectious_diseases
    <set>
      <if test="updateCodeName != null and updateCodeName != ''">
        diseases_name=#{updateCodeName,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null and updateUser != ''">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      update_time =now()
    </set>
    where diseases_code=#{updateCode,jdbcType=VARCHAR}
  </update>
  <select id="queryInfecInfo2" parameterType="com.iflytek.cdc.admin.dto.SearchInfecInfoDTO"
          resultType="com.iflytek.cdc.admin.entity.InfectiousDiseases">
    <!--@mbg.generated-->
    select
    id as id , diseases_code as diseasesCode, diseases_name as diseasesName , diseases_type as diseasesType,
    diseases_type_name as diseasesTypeName,
    diseases_classify as diseasesClassify,diseases_classify_name as diseasesClassifyName,
    infect_class_code, infect_class_name, transmission_type_code, transmission_type_name,
    remark as remark , is_enable as isEnable,
    create_user as createUser, create_time as createTime, update_user as updateUser, update_time as updateTime
    from tb_cdcmr_infectious_diseases
    where is_enable = '1'
    <if test="diseasesName != null and diseasesName != ''">
      and diseases_name like concat('%',#{diseasesName,jdbcType=VARCHAR},'%')
    </if>
    <if test="diseaseCode != null and diseaseCode != ''">
      and diseases_code= #{diseaseCode,jdbcType=VARCHAR}
    </if>
    order by diseases_code
  </select>
</mapper>