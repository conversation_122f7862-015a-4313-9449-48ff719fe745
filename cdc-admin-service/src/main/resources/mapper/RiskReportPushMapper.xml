<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.RiskReportPushMapper">

    <resultMap type="com.iflytek.cdc.admin.dto.RiskReportPushRecordDto" id="riskReportPushRecordDto">
        <result property="id" jdbcType="VARCHAR" column="id"/>
        <result property="pushTime" jdbcType="TIMESTAMP" column="pushTime"/>
        <result property="taskId" jdbcType="VARCHAR" column="taskId"/>
        <result property="recipient" jdbcType="VARCHAR" column="recipient"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="sender" jdbcType="VARCHAR" column="sender"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="signalId" jdbcType="VARCHAR" column="signalId"/>
        <result property="attachmentId" jdbcType="VARCHAR" column="attachmentId"/>
        <result property="attachmentUrl" jdbcType="VARCHAR" column="attachmentUrl"/>
        <result property="region" jdbcType="VARCHAR" column="region"/>
        <result property="districtName" jdbcType="VARCHAR" column="districtName"/>
        <result property="districtCode" jdbcType="VARCHAR" column="districtCode"/>
        <result property="cityName" jdbcType="VARCHAR" column="cityName"/>
        <result property="cityCode" jdbcType="TIMESTAMP" column="cityCode"/>
        <result property="provinceName" jdbcType="VARCHAR" column="provinceName"/>
        <result property="provinceCode" jdbcType="VARCHAR" column="provinceCode"/>
    </resultMap>

    <select id="queryList" resultMap="riskReportPushRecordDto">
        select
        r.id as id,
        r.push_time as pushTime,
        r.task_id as taskId,
        r.recipient as recipient,
        r.sender as sender ,
        t.notes as signalId ,
        t.attachment_id as attachmentId,
        t.attachment_url as attachmentUrl,
        r.province_name as provinceName,
        r.province_code as provinceCode,
        r.city_name as cityName,
        r.city_code as cityCode,
        r.district_name as districtName,
        r.district_code as districtCode,
        concat_ws('', r.province_name, r.city_name, r.district_name) as region
        from
        tb_cdcmr_risk_report_push_record r
        join tb_cdcmr_export_task t on
        r.task_id = t.id
        where
        t.delete_flag = '0'
        and
        (
        r.recipient::json ->> 'userId' = #{loginUserId} or
        r.sender::json ->> 'userId' = #{loginUserId}
        )
        <if test="dto.startTime != null and dto.startTime != ''">
            and r.push_time &gt;= TO_TIMESTAMP(#{dto.startTime}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="dto.endTime != null and dto.endTime != '' ">
            and r.push_time &lt; TO_TIMESTAMP(#{dto.endTime}, 'YYYY-MM-DD HH24:MI:SS') + interval '1 day'
        </if>
        <if test="dto.recipientName != null and dto.recipientName != '' ">
            and r.recipient::json ->> 'userName' ilike concat('%', #{dto.recipientName}, '%')
        </if>
        <if test="dto.provinceCode != null and dto.provinceCode != '' ">
            and r.province_code = #{dto.provinceCode}
        </if>
        <if test="dto.cityCode != null and dto.cityCode != '' ">
            and r.city_code = #{dto.cityCode}
        </if>
        <if test="dto.districtCode != null and dto.districtCode != '' ">
            and r.district_code = #{dto.districtCode}
        </if>
    </select>
</mapper>