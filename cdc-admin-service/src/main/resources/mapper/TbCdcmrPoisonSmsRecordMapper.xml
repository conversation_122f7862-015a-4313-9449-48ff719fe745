<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrPoisonSmsRecordMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="login_user_id" jdbcType="VARCHAR" property="loginUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, event_id, rule_id, phone, batch_id, create_time, login_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_poison_sms_record"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "tb_cdcmr_poison_sms_record"
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord">
    insert into "tb_cdcmr_poison_sms_record" (id, event_id, rule_id, 
      phone, batch_id, create_time, 
      login_user_id)
    values (#{id,jdbcType=VARCHAR}, #{eventId,jdbcType=VARCHAR}, #{ruleId,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{loginUserId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord">
    insert into "tb_cdcmr_poison_sms_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="eventId != null">
        event_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="loginUserId != null">
        login_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null">
        #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginUserId != null">
        #{loginUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord">
    update "tb_cdcmr_poison_sms_record"
    <set>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loginUserId != null">
        login_user_id = #{loginUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.dto.TbCdcmrPoisonSmsRecord">
    update "tb_cdcmr_poison_sms_record"
    set event_id = #{eventId,jdbcType=VARCHAR},
      rule_id = #{ruleId,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      login_user_id = #{loginUserId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>