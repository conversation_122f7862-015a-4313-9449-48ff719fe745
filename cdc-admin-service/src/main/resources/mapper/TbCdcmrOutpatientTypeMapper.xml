<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientTypeMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="outpatient_type_code" jdbcType="VARCHAR" property="outpatientTypeCode" />
    <result column="outpatient_type_name" jdbcType="VARCHAR" property="outpatientTypeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    id, outpatient_type_code, outpatient_type_name, remark, "status", is_deleted, update_time, 
    updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_outpatient_type"
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    update  "tb_cdcmr_outpatient_type" set is_deleted = 1 where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    insert into "tb_cdcmr_outpatient_type" (id, outpatient_type_code, outpatient_type_name, 
      remark, "status", is_deleted, 
      update_time, updater)
    values (#{id,jdbcType=VARCHAR}, #{outpatientTypeCode,jdbcType=VARCHAR}, #{outpatientTypeName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    insert into "tb_cdcmr_outpatient_type"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="outpatientTypeCode != null">
        outpatient_type_code,
      </if>
      <if test="outpatientTypeName != null">
        outpatient_type_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeCode != null">
        #{outpatientTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeName != null">
        #{outpatientTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    update "tb_cdcmr_outpatient_type"
    <set>
      <if test="outpatientTypeCode != null">
        outpatient_type_code = #{outpatientTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="outpatientTypeName != null">
        outpatient_type_name = #{outpatientTypeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    update "tb_cdcmr_outpatient_type"
    set outpatient_type_code = #{outpatientTypeCode,jdbcType=VARCHAR},
      outpatient_type_name = #{outpatientTypeName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    select *
    from "tb_cdcmr_outpatient_type"
    where is_deleted = 0
      and status = 1
    order by id
  </select>
  <select id="getByOutpatientTypeCode" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    select *
    from "tb_cdcmr_outpatient_type"
    where is_deleted = 0
      and outpatient_type_code = #{outpatientTypeCode}

  </select>
  <select id="getByOutpatientTypeName" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    select *
    from "tb_cdcmr_outpatient_type"
    where is_deleted = 0
      and outpatient_type_name = #{outpatientTypeName}

  </select>
  <select id="findAll" resultType="com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType">
    select *
    from "tb_cdcmr_outpatient_type"
    where is_deleted = 0
  </select>
  <select id="queryOutpatientInfoByGrade" resultType="com.iflytek.cdc.admin.dto.OutpatientGradeConfigVO">
    SELECT
    t2.remark AS remark,
    COALESCE ( t2.status, 0 ) AS status,
    t1.*,
    (select count(t3.id) from tb_cdcmr_warning_grade_config_rule t3 where t2.id = t3.config_id and
    t3.is_deleted = 0)as ruleCount
    FROM "tb_cdcmr_outpatient_type" t1 LEFT JOIN tb_cdcmr_warning_grade_config t2 ON t2.config_type = 'outpatient'
    AND
    t1.outpatient_type_code = t2.disease_code
    where t1.is_deleted = 0
    <if test="outpatientTypeCode!= null and outpatientTypeCode !=''">
      and t1.outpatient_type_code = #{outpatientTypeCode}
    </if>
    <if test="status != null ">
      and COALESCE ( t2.status,0) = #{status}
    </if>
    order by id
  </select>
</mapper>