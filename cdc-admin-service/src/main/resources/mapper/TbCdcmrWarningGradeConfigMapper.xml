<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeConfigMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
      <id column="id" jdbcType="VARCHAR" property="id"/>
      <result column="config_type" jdbcType="VARCHAR" property="configType"/>
      <result column="disease_name" jdbcType="VARCHAR" property="diseaseName"/>
      <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode"/>
      <result column="status" jdbcType="SMALLINT" property="status"/>
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
      <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
      <result column="remark" jdbcType="VARCHAR" property="remark"/>
  </resultMap>
    <sql id="Base_Column_List">
        id
        , config_type, disease_name, disease_code, "status", update_time, update_user,
    remark
    </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "tb_cdcmr_warning_grade_config"
    where id = #{id,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_warning_grade_config"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
      insert into "tb_cdcmr_warning_grade_config" (id, config_type, disease_name,
                                                   disease_code, "status", update_time,
                                                   update_user, remark)
      values (#{id,jdbcType=VARCHAR}, #{configType,jdbcType=VARCHAR}, #{diseaseName,jdbcType=VARCHAR},
              #{diseaseCode,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{updateTime,jdbcType=TIMESTAMP},
              #{updateUser,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
    insert into "tb_cdcmr_warning_grade_config"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
        <if test="configType != null">
            config_type,
        </if>
        <if test="diseaseName != null">
            disease_name,
        </if>
        <if test="diseaseCode != null">
            disease_code,
        </if>
        <if test="status != null">
            "status",
        </if>
        <if test="updateTime != null">
            update_time,
        </if>
        <if test="updateUser != null">
            update_user,
        </if>
        <if test="remark != null">
            remark,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
        <if test="configType != null">
            #{configType,jdbcType=VARCHAR},
        </if>
        <if test="diseaseName != null">
            #{diseaseName,jdbcType=VARCHAR},
        </if>
        <if test="diseaseCode != null">
            #{diseaseCode,jdbcType=VARCHAR},
        </if>
        <if test="status != null">
            #{status,jdbcType=SMALLINT},
        </if>
        <if test="updateTime != null">
            #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateUser != null">
            #{updateUser,jdbcType=VARCHAR},
        </if>
        <if test="remark != null">
            #{remark,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
    update "tb_cdcmr_warning_grade_config"
      <set>
          <if test="configType != null">
              config_type = #{configType,jdbcType=VARCHAR},
          </if>
          <if test="diseaseName != null">
              disease_name = #{diseaseName,jdbcType=VARCHAR},
          </if>
          <if test="diseaseCode != null">
              disease_code = #{diseaseCode,jdbcType=VARCHAR},
          </if>
          <if test="status != null">
              "status" = #{status,jdbcType=SMALLINT},
          </if>
          <if test="updateTime != null">
              update_time = #{updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updateUser != null">
              update_user = #{updateUser,jdbcType=VARCHAR},
          </if>
          <if test="remark != null">
              remark = #{remark,jdbcType=VARCHAR},
          </if>
      </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
      update "tb_cdcmr_warning_grade_config"
      set config_type  = #{configType,jdbcType=VARCHAR},
          disease_name = #{diseaseName,jdbcType=VARCHAR},
          disease_code = #{diseaseCode,jdbcType=VARCHAR},
          "status"     = #{status,jdbcType=SMALLINT},
          update_time  = #{updateTime,jdbcType=TIMESTAMP},
          update_user  = #{updateUser,jdbcType=VARCHAR},
          remark       = #{remark,jdbcType=VARCHAR}
      where id = #{id,jdbcType=VARCHAR}
  </update>
    <insert id="upsertConfig">
        insert into "tb_cdcmr_warning_grade_config" (id, config_type, disease_name,
                                                     disease_code, "status", update_time,
                                                     update_user, remark)
        values (#{id,jdbcType=VARCHAR}, #{configType,jdbcType=VARCHAR}, #{diseaseName,jdbcType=VARCHAR},
                #{diseaseCode,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateUser,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}) on conflict(config_type,disease_code) do
        update
            set id = EXCLUDED.id,
            config_type = EXCLUDED.config_type,
            disease_name = EXCLUDED.disease_name,
            disease_code = EXCLUDED.disease_code,
            "status" = EXCLUDED.status,
            update_time = EXCLUDED.update_time ,
            update_user = EXCLUDED.update_user,
            remark = EXCLUDED.remark
    </insert>
    <select id="getList" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config"
    </select>
    <select id="getListByConfigType" resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config"
        where config_type = #{configType}
    </select>
    <select id="getListByConfigTypeAndCode"
            resultType="com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_warning_grade_config"
        where config_type = #{configType}
        and disease_code = #{diseaseCode}
    </select>
</mapper>