<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.AddressParsMapper">
   <select id="queryStandardAddressList" parameterType="com.iflytek.cdc.admin.dto.SearchAddressDTO" resultType="com.iflytek.cdc.admin.entity.StandardAddress">
     select id as id,address_name as addressName,address_detail_desc as addressDesc,address_longitude as addressLongitude,
            address_latitude as addressLatitude,create_datetime as createTime from tb_cdcmr_address_standard where 1=1
       <if test="addressName != null and addressName != ''">
           and (address_name  like concat('%',#{addressName},'%')  or address_detail_desc  like concat('%',#{addressName},'%'))
       </if>
       <if test="addressStandardId != null and addressStandardId != ''">
           and id=#{addressStandardId,jdbcType=VARCHAR}
       </if>
       order by  create_datetime desc,address_name desc
   </select>

    <select id="queryRelationAddressList" parameterType="com.iflytek.cdc.admin.dto.SearchAddressDTO" resultType="com.iflytek.cdc.admin.entity.RelationAddress">
         select id as id,address_detail as addressDesc,org_city_name as searchRange ,similarity,status,address_standard_id from tb_cdcmr_address_detail_mapping where 1=1
        <if test="addressName != null and addressName != ''">
            and address_detail like  concat('%',#{addressName,jdbcType=VARCHAR},'%')
        </if>
        <if test="addressStandardId != null and addressStandardId != ''">
            and address_standard_id=#{addressStandardId,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and status=#{status}
        </if>
        order by create_datetime desc,address_detail desc
    </select>

    <delete id="deleteRelationAddress" parameterType="java.lang.String">
        update tb_cdcmr_address_detail_mapping set status  = 0 , similarity = null where id=#{id}
    </delete>

    <select id="queryExportList" resultType="com.iflytek.cdc.admin.entity.ExportAddress">
        select  address_name as addressName,address_detail_desc as addressDetailDesc,address_city_code  as cityCode,
                address_district_code  as addressDistrictCode,address_longitude as addressLongitude,address_latitude as addressLatitude,
                address_province_name as addressProvinceName,address_city_name as addressCityName,address_district_name as addressDistrictName,
                address_town_name as addressTownName from tb_cdcmr_address_standard where 1=1
        <if test="ids != null and ids.size() != 0">
            and id in (
            <foreach collection="ids" index="index" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </select>

</mapper>