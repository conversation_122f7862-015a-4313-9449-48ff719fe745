<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.OrgMappingMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.UapOrganization">
        <!--@mbg.generated-->
        <!--@Table tb_cdcmr_organization-->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
        <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
        <result column="city_name" jdbcType="VARCHAR" property="cityName" />
        <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
        <result column="district_name" jdbcType="VARCHAR" property="districtName" />
        <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
        <result column="org_short_name" jdbcType="VARCHAR" property="orgShortName" />
        <result column="org_type_name" jdbcType="VARCHAR" property="orgTypeName" />
        <result column="org_type_code" jdbcType="VARCHAR" property="orgTypeCode" />
        <result column="higher_org_id" jdbcType="VARCHAR" property="higherOrgId" />
        <result column="higher_org_name" jdbcType="VARCHAR" property="higherOrgName" />
        <result column="org_level" jdbcType="INTEGER" property="orgLevel" />
        <result column="org_status" jdbcType="INTEGER" property="orgStatus" />
        <result column="org_sort" jdbcType="INTEGER" property="orgSort" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="higher_org_code" jdbcType="VARCHAR" property="higherOrgCode" />
        <result column="street_name" jdbcType="VARCHAR" property="streetName" />
        <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
        <result column="township_hospital_source_key" jdbcType="VARCHAR" property="townshipHospitalSourceKey" />
        <result column="township_hospital_name" jdbcType="VARCHAR" property="townshipHospitalName" />
        <result column="longitude" jdbcType="NUMERIC" property="longitude" />
        <result column="latitude" jdbcType="NUMERIC" property="latitude" />
        <result column="geohash" jdbcType="VARCHAR" property="geohash" />
        <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
        <result column="is_match" jdbcType="VARCHAR" property="isMatch"></result>
        <result column="source_org_type_code" jdbcType="VARCHAR" property="sourceOrgTypeCode"></result>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_name, org_code, province_name, province_code, city_name, city_code, district_name,
        district_code, org_short_name, org_type_name, org_type_code, higher_org_id, higher_org_name,
        org_level, org_status, org_sort, remark, create_time, update_time, higher_org_code,
        street_name, street_code, township_hospital_source_key, township_hospital_name, longitude,
        latitude, geohash, is_deleted,is_match,source_org_type_code
    </sql>
    <select id="queryHisOrgList"  parameterType="com.iflytek.cdc.admin.dto.SearchHisOrgDTO"  resultType="com.iflytek.cdc.admin.entity.HisOrg">
        select id as id,province_code as provinceCode, province_name as provinceName, city_name  as cityName, city_code as cityCode,
        district_name as districtName, district_code as districtCode,his_org_code as hisOrgCode, his_org_name as hisOrgName,
        his_higher_org_name as hisHigherOrgName, source_id as sourceId,source_name  as sourceName, is_delete  as isDelete,
        medical_type as medicalType, creator as creator,created_time as createdTime, updator as updator, updated_time as updatedTime,
        is_match as isMatch from tb_cdcmr_his_org  where 1=1
        <if test="provinceCode != null and provinceCode != ''">
               and province_code=#{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
               and city_code=#{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
               and district_code=#{districtCode}
        </if>
        <if test="isMatch != null and isMatch != ''">
               and is_match=#{isMatch}
        </if>
        <if test="orgName != null and orgName != ''">
            and his_org_name like concat('%',#{orgName},'%')
        </if>
        order by updated_time,his_org_code desc
    </select>

    <insert id="insertHisOrg" parameterType="com.iflytek.cdc.admin.entity.HisOrg">
        <!--@mbg.generated-->
        insert into tb_cdcmr_his_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name,
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code,
            </if>
            <if test="cityName != null and cityName != ''">
                city_name,
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code,
            </if>
            <if test="districtName != null and districtName != ''">
                district_name,
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code,
            </if>
            <if test="hisOrgCode != null and hisOrgCode != ''">
                his_org_code,
            </if>
            <if test="hisOrgName != null and hisOrgName != ''">
                his_org_name,
            </if>
            <if test="hisHigherOrgName != null and hisHigherOrgName != ''">
                his_higher_org_name,
            </if>
            <if test="sourceId != null and sourceId != ''">
                source_id,
            </if>
            <if test="sourceName != null and sourceName != ''">
                source_name,
            </if>
            <if test="doctorId != null and doctorId != ''">
                doctor_id,
            </if>
            <if test="isDelete != null and isDelete != ''">
                is_delete,
            </if>
            <if test="medicalType != null and medicalType != ''">
                medical_type,
            </if>
            <if test="creator != null and creator != ''">
                creator,
            </if>
                created_time,
            <if test="updator != null and updator != ''">
                updator,
            </if>
                updated_time,
            <if test="isMatch != null and isMatch != ''">
                is_match,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null and provinceName != ''">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null and cityCode != ''">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null and districtName != ''">
                #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null and districtCode != ''">
                #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgCode != null and hisOrgCode != ''">
                #{hisOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgName != null and hisOrgName != ''">
                #{hisOrgName,jdbcType=VARCHAR},
            </if>
            <if test="hisHigherOrgName != null and hisHigherOrgName != ''">
                #{hisHigherOrgName,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null and sourceId != ''">
                #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="sourceName != null and sourceName != ''">
                #{sourceName,jdbcType=VARCHAR},
            </if>
            <if test="doctorId != null and doctorId != ''">
                #{doctorId,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null and isDelete != ''">
                #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="medicalType != null and medicalType != ''">
                #{medicalType,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                #{creator,jdbcType=VARCHAR},
            </if>
               now(),
            <if test="updator != null and updator != ''">
                #{updator,jdbcType=VARCHAR},
            </if>
                now(),
            <if test="isMatch != null and isMatch != ''">
                #{isMatch,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="findHisOrgByCode" resultType="com.iflytek.cdc.admin.entity.HisOrg">
        select id as id,province_code as provinceCode, province_name as provinceName, city_name  as cityName, city_code as cityCode,
        district_name as districtName, district_code as districtCode,his_org_code as hisOrgCode, his_org_name as hisOrgName,
        his_higher_org_name as hisHigherOrgName, source_id as sourceId,source_name  as sourceName, is_delete  as isDelete,
        medical_type as medicalType, creator as creator,created_time as createdTime, updator as updator, updated_time as updatedTime,
        is_match as isMatch from tb_cdcmr_his_org
        where his_org_code=#{hisOrgCode,jdbcType=VARCHAR}  and
        source_id=#{sourceId,jdbcType=VARCHAR}
    </select>

    <select id="queryHisOrgInfoById" parameterType="java.lang.String"  resultType="com.iflytek.cdc.admin.entity.HisOrg">
        select id as id,province_code as provinceCode, province_name as provinceName, city_name  as cityName, city_code as cityCode,
        district_name as districtName, district_code as districtCode,his_org_code as hisOrgCode, his_org_name as hisOrgName,
        his_higher_org_name as hisHigherOrgName, source_id as sourceId,source_name  as sourceName, is_delete  as isDelete,
        medical_type as medicalType, creator as creator,created_time as createdTime, updator as updator, updated_time as updatedTime,
        is_match as isMatch from tb_cdcmr_his_org
        where id=#{hisOrgId,jdbcType=VARCHAR}
    </select>

    <update id="updateHisOrg" parameterType="com.iflytek.cdc.admin.entity.HisOrg">
        update tb_cdcmr_his_org
        <set>
            <if test="provinceName != null and provinceName != ''">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null and districtName != ''">
                district_name = #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgCode != null and hisOrgCode != ''">
                his_org_code = #{hisOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgName != null and hisOrgName != ''">
                his_org_name = #{hisOrgName,jdbcType=VARCHAR},
            </if>
            <if test="hisHigherOrgName != null and hisHigherOrgName != ''">
                his_higher_org_name = #{hisHigherOrgName,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null and sourceId != ''">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="sourceName != null and sourceName != ''">
                source_name = #{sourceName,jdbcType=VARCHAR},
            </if>
            <if test="doctorId != null and doctorId != ''">
                doctor_id = #{doctorId,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null and isDelete != ''">
                is_delete = #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="medicalType != null and medicalType != ''">
                medical_type = #{medicalType,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updator != null and updator != ''">
                updator = #{updator,jdbcType=VARCHAR},
            </if>
              updated_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByOrgId" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from tb_cdcmr_his_org
        where id = #{hisOrgId,jdbcType=VARCHAR}
    </delete>

    <select id="queryUapOrgList" parameterType="com.iflytek.cdc.admin.dto.SearchHisOrgDTO" resultType="com.iflytek.cdc.admin.entity.UapOrganization">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_organization
        where 1=1
        <if test="downIds != null and downIds.size() != 0 !=null ">
            and id  in (
            <foreach collection="downIds" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and province_code=#{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code=#{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and district_code=#{districtCode}
        </if>
        <if test="isMatch != null and isMatch != ''">
            and is_match=#{isMatch}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat('%',#{orgName},'%')
        </if>
        order by  update_time desc
    </select>

    <select id="queryMatchUapOrg" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.entity.UapOrganization">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_organization
        where 1=1
        <if test="orgId != null and orgId != ''">
            and id=#{orgId}
        </if>
    </select>

    <insert id="insertOrgMapper" parameterType="java.util.List">
        insert into tb_cdcmr_org_mapping
        (id, area_province_code, area_province_name,
        area_city_code, area_city_name, area_county_code,
        area_county_name, source_code, source_name,
        source_org_code, source_org_name, sys_org_id,
        sys_org_name, is_match, is_delete,
        create_user, create_time, update_user,
        update_time) values
        <foreach collection="list" item="org" separator="," index="index">
           (#{org.id,jdbcType=VARCHAR},
            #{org.areaProvinceCode,jdbcType=VARCHAR},
            #{org.areaProvinceName,jdbcType=VARCHAR},
            #{org.areaCityCode,jdbcType=VARCHAR},
            #{org.areaCityName,jdbcType=VARCHAR},
            #{org.areaCountyCode,jdbcType=VARCHAR},
            #{org.areaCountyName,jdbcType=VARCHAR},
            #{org.sourceCode,jdbcType=VARCHAR},
            #{org.sourceName,jdbcType=VARCHAR},
            #{org.sourceOrgCode,jdbcType=VARCHAR},
            #{org.sourceOrgName,jdbcType=VARCHAR},
            #{org.sysOrgId,jdbcType=VARCHAR},
            #{org.sysOrgName,jdbcType=VARCHAR},
            #{org.isMatch,jdbcType=VARCHAR},
            #{org.isDelete,jdbcType=VARCHAR},
            #{org.createUser,jdbcType=VARCHAR},
            now(),
            #{org.updateUser,jdbcType=VARCHAR},
            now())
        </foreach>
    </insert>

    <select id="queryOrgList" resultType="com.iflytek.cdc.admin.entity.OrgMapping"  parameterType="com.iflytek.cdc.admin.dto.UapOrgMatchDTO">
        select uap.id as uapId,uap.province_code as areaProvinceCode,uap.province_name as areaProvinceName,uap.city_code as areaCityCode,
        uap.city_name as areaCityName,uap.district_code as areaCountyCode,uap.district_name as areaCountyName,his.source_id as sourceCode,his.id as hisId,
        his.source_name as sourceName,his.his_org_code as sourceOrgCode,his.his_org_name as sourceOrgName,uap.id as sysOrgId,uap.org_name as sysOrgName
        from tb_cdcmr_organization as uap join tb_cdcmr_his_org as his on uap.org_name=his.his_org_name where  uap.is_match=#{isMatch} and his.is_match=#{isMatch}
        <if test="downIds != null and downIds.size() != 0">
            and uap.id  in (
            <foreach collection="downIds" separator="," item="item" index="index">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="queryOrgListById" resultType="com.iflytek.cdc.admin.entity.OrgMapping" parameterType="java.lang.String">
        select uap.id as uapId,uap.province_code as areaProvinceCode,uap.province_name as areaProvinceName,uap.city_code as areaCityCode,
        uap.city_name as areaCityName,uap.district_code as areaCountyCode,uap.district_name as areaCountyName,uap.id as sysOrgId,uap.org_name as sysOrgName,
        uap.is_match as isMatch
        from tb_cdcmr_organization as uap where uap.id=#{uapOrgId}
    </select>

    <select id="queryHisOrgListById" resultType="com.iflytek.cdc.admin.entity.OrgMapping" parameterType="java.lang.String">
        select his.source_id as sourceCode,his.id as hisId,
        his.source_name as sourceName,his.his_org_code as sourceOrgCode,his.his_org_name as sourceOrgName,his.is_match as isMatch  from tb_cdcmr_his_org as his where his.id=#{hisOrgId}
    </select>


    <update id="updateHisMatchStatus"  parameterType="com.iflytek.cdc.admin.dto.UpdateOrgDTO">
        <!--@mbg.generated-->
        update tb_cdcmr_his_org
        <set>
            <if test="isMatch != null and isMatch != ''">
                is_match = #{isMatch,jdbcType=VARCHAR},
            </if>
            <if test="updator != null and updator != ''">
                updator=#{updator},
            </if>
            updated_time=now()
        </set>
        where id in (
        <foreach collection="ids" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </update>

    <update id="updateUapMatchStatus" parameterType="java.lang.String">
        <!--@mbg.generated-->
        update tb_cdcmr_organization
        <set>
            <if test="isMatch != null and isMatch != ''">
                is_match = #{isMatch,jdbcType=VARCHAR},
            </if>
            update_time=now()
        </set>
        where id in (
        <foreach collection="ids" separator="," item="item" index="index">
            #{item}
        </foreach>
        )
    </update>

    <select id="queryUapIdByHisCode" resultType="java.lang.String" parameterType="java.lang.String">
        select sys_org_id as uapOrgId from tb_cdcmr_org_mapping where source_org_code=#{hisOrgCode} and source_code=#{sourceId,jdbcType=VARCHAR} and is_delete='0'
    </select>

    <delete id="deleteByUapOrgId" parameterType="java.lang.String">
        delete from tb_cdcmr_org_mapping  where sys_org_id=#{uapOrgId}
    </delete>

    <select id="queryUapByHis" parameterType="com.iflytek.cdc.admin.sdk.entity.HisInfoFilter" resultType="com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo">
        select sys_org_id as uapOrgId,sys_org_name as uapOrgName from tb_cdcmr_org_mapping where 1=1
        <if test="sourceId != null and sourceId != ''">
            and source_code=#{sourceId}
        </if>
        <if test="hisOrgCode != null and hisOrgCode != ''">
            and source_org_code=#{hisOrgCode}
        </if>
        and is_delete='0'
    </select>

    <select id="queryHisByUap" resultType="com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo" parameterType="java.lang.String">
        select source_code as sourceId,source_name as sourceName,source_org_code as hisOrgCode,source_org_name as hisOrgName
        from tb_cdcmr_org_mapping where sys_org_id=#{uapId} and is_delete='0'
    </select>

    <update id="deleteByCdcSysId" parameterType="java.lang.String">
      update  tb_cdcmr_org_mapping set is_delete='1' where sys_org_id=#{cdcSysOrgId,jdbcType=VARCHAR}
    </update>

    <update id="deleteUapOrgById" parameterType="java.lang.String">
        update tb_cdcmr_organization set is_deleted='1' where id=#{uapOrgId,jdbcType=VARCHAR}
    </update>

    <insert id="insertUapOrg" parameterType="com.iflytek.cdc.admin.entity.UapOrganization">
        <!--@mbg.generated-->
        insert into tb_cdcmr_organization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="orgName != null and orgName != ''">
                org_name,
            </if>
            <if test="orgCode != null and orgCode != ''">
                org_code,
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name,
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code,
            </if>
            <if test="cityName != null and cityName != ''">
                city_name,
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code,
            </if>
            <if test="districtName != null and districtName != ''">
                district_name,
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code,
            </if>
            <if test="orgShortName != null and orgShortName != ''">
                org_short_name,
            </if>
            <if test="orgTypeName != null and orgTypeName != ''">
                org_type_name,
            </if>
            <if test="orgTypeCode != null and orgTypeCode != ''">
                org_type_code,
            </if>
            <if test="higherOrgId != null and higherOrgId != ''">
                higher_org_id,
            </if>
            <if test="higherOrgName != null and higherOrgName != ''">
                higher_org_name,
            </if>
            <if test="orgLevel != null and orgLevel != ''">
                org_level,
            </if>
            <if test="orgStatus != null and orgStatus != ''">
                org_status,
            </if>
            <if test="orgSort != null and orgSort != ''">
                org_sort,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="higherOrgCode != null and higherOrgCode != ''">
                higher_org_code,
            </if>
            <if test="streetName != null and streetName != ''">
                street_name,
            </if>
            <if test="streetCode != null and streetCode != ''">
                street_code,
            </if>
            <if test="townshipHospitalSourceKey != null and townshipHospitalSourceKey != ''">
                township_hospital_source_key,
            </if>
            <if test="townshipHospitalName != null and townshipHospitalName != ''">
                township_hospital_name,
            </if>
            <if test="longitude != null and longitude != ''">
                longitude,
            </if>
            <if test="latitude != null and latitude != ''">
                latitude,
            </if>
            <if test="geohash != null and geohash != ''">
                geohash,
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted,
            </if>
            <if test="isMatch != null and isMatch != ''">
                is_match,
            </if>
            <if test="sourceOrgTypeCode != null and sourceOrgTypeCode != ''">
                source_org_type_code,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != ''">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null and orgCode != ''">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null and provinceName != ''">
                #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null and cityCode != ''">
                #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null and districtName != ''">
                #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null and districtCode != ''">
                #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="orgShortName != null and orgShortName != ''">
                #{orgShortName,jdbcType=VARCHAR},
            </if>
            <if test="orgTypeName != null and orgTypeName != ''">
                #{orgTypeName,jdbcType=VARCHAR},
            </if>
            <if test="orgTypeCode != null and orgTypeCode != ''">
                #{orgTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgId != null and higherOrgId != ''">
                #{higherOrgId,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgName != null and higherOrgName != ''">
                #{higherOrgName,jdbcType=VARCHAR},
            </if>
            <if test="orgLevel != null and orgLevel != ''">
                #{orgLevel,jdbcType=VARCHAR},
            </if>
            <if test="orgStatus != null and orgStatus != ''">
                #{orgStatus,jdbcType=VARCHAR},
            </if>
            <if test="orgSort != null and orgSort != ''">
                #{orgSort,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgCode != null and higherOrgCode != ''">
                #{higherOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="streetName != null and streetName != ''">
                #{streetName,jdbcType=VARCHAR},
            </if>
            <if test="streetCode != null and streetCode != ''">
                #{streetCode,jdbcType=VARCHAR},
            </if>
            <if test="townshipHospitalSourceKey != null and townshipHospitalSourceKey != ''">
                #{townshipHospitalSourceKey,jdbcType=VARCHAR},
            </if>
            <if test="townshipHospitalName != null and townshipHospitalName != ''">
                #{townshipHospitalName,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null and longitude != ''">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null and latitude != ''">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="geohash != null and geohash != ''">
                #{geohash,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                #{isDeleted,jdbcType=VARCHAR},
            </if>
            <if test="isMatch != null and isMatch != ''">
                #{isMatch,jdbcType=VARCHAR},
            </if>
            <if test="sourceOrgTypeCode != null and sourceOrgTypeCode != ''">
                #{sourceOrgTypeCode,jdbcType=VARCHAR},
            </if>
            now(),
            now()
        </trim>
    </insert>

    <update id="updateUapOrg" parameterType="com.iflytek.cdc.admin.entity.UapOrganization">
        <!--@mbg.generated-->
        update tb_cdcmr_organization
        <set>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="provinceName != null and provinceName != ''">
                province_name = #{provinceName,jdbcType=VARCHAR},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code = #{provinceCode,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null and cityName != ''">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null and cityCode != ''">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="districtName != null and districtName != ''">
                district_name = #{districtName,jdbcType=VARCHAR},
            </if>
            <if test="districtCode != null and districtCode != ''">
                district_code = #{districtCode,jdbcType=VARCHAR},
            </if>
            <if test="orgShortName != null and orgShortName != ''">
                org_short_name = #{orgShortName,jdbcType=VARCHAR},
            </if>
            <if test="orgTypeName != null and orgTypeName != ''">
                org_type_name = #{orgTypeName,jdbcType=VARCHAR},
            </if>
            <if test="orgTypeCode != null and orgTypeCode != ''">
                org_type_code = #{orgTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgId != null and higherOrgId != ''">
                higher_org_id = #{higherOrgId,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgName != null and higherOrgName != ''">
                higher_org_name = #{higherOrgName,jdbcType=VARCHAR},
            </if>
            <if test="orgLevel != null and orgLevel != ''">
                org_level = #{orgLevel,jdbcType=VARCHAR},
            </if>
            <if test="orgStatus != null and orgStatus != ''">
                org_status = #{orgStatus,jdbcType=VARCHAR},
            </if>
            <if test="orgSort != null and orgSort != ''">
                org_sort = #{orgSort,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="higherOrgCode != null and higherOrgCode != ''">
                higher_org_code = #{higherOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="streetName != null and streetName != ''">
                street_name = #{streetName,jdbcType=VARCHAR},
            </if>
            <if test="streetCode != null and streetCode != ''">
                street_code = #{streetCode,jdbcType=VARCHAR},
            </if>
            <if test="townshipHospitalSourceKey != null and townshipHospitalSourceKey != ''">
                township_hospital_source_key = #{townshipHospitalSourceKey,jdbcType=VARCHAR},
            </if>
            <if test="townshipHospitalName != null and townshipHospitalName != ''">
                township_hospital_name = #{townshipHospitalName,jdbcType=VARCHAR},
            </if>
            <if test="longitude != null and longitude != ''">
                longitude = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null and latitude != ''">
                latitude = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="geohash != null and geohash != ''">
                geohash = #{geohash,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted = #{isDeleted,jdbcType=VARCHAR},
            </if>
            <if test="isMatch != null and isMatch != ''">
                is_match = #{isMatch,jdbcType=VARCHAR},
            </if>
            <if test="sourceOrgTypeCode != null and sourceOrgTypeCode != ''">
                source_org_type_code = #{sourceOrgTypeCode,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="queryNeedUpOrgList" resultType="com.iflytek.cdc.admin.entity.UapOrganization">
        select
        <include refid="Base_Column_List" />
        from tb_cdcmr_organization
        where org_status = 1
        and (longitude is null or latitude is null)
    </select>

    <update id="healthCenter" parameterType="java.lang.String">
        update tb_cdcmr_organization set
                township_hospital_source_key= n.org_code,
                township_hospital_name = n.org_name from (
        select t.id,t.org_code,t.org_name
        from tb_cdcmr_organization t,tb_cdcmr_organization o where t.higher_org_id=o."id"
        and o.org_type_code='100201' and t.org_type_code='100202') n
        where tb_cdcmr_organization.id=n.id
                and n.id = #{orgId};
    </update>

    <update id="functionalArea" parameterType="hashmap">
        UPDATE tb_cdcmr_organization
        SET standard_district_code = #{params.standard_district_code},
            standard_district_name = #{params.standard_district_name}
        WHERE
            district_code = #{params.district_code};
    </update>
</mapper>