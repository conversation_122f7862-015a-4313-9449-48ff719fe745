<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsPageMapper">


	<sql id="base_column_list">
		id,app_id,app_name,page_name,page_code,status,is_process,process_id,extend_json,in_parameter,data_model_id,creator_id,creator,create_time,updater_id,updater,update_time,delete_flag
	</sql>

	<!-- 分页数据查询 -->
	<select id="pageList" resultType="com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage">
        select
            <include refid="base_column_list"/>
        from tb_cdccs_page
        where delete_flag = '0'
		<if test="appId != null and appId != ''">
			and app_id = #{appId}
		</if>
		<if test="queryKey != null and queryKey != ''">
			and page_name like concat('%', #{queryKey} , '%')
		</if>
		order by create_time desc
    </select>

</mapper>