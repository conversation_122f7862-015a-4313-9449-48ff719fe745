<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.UserConfigMapper">

    <sql id="Base_Column_List">
        id, uap_user_id, status, update_time, config_type, config_desc
    </sql>

    <insert id="upsert">
        insert into "tb_cdcew_user_config" (id, uap_user_id, "status",
                                            update_time, config_type, config_desc)
        values (#{id,jdbcType=VARCHAR}, #{uapUserId,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT},
                #{updateTime}, #{configType,jdbcType=VARCHAR}, #{configDesc,jdbcType=VARCHAR})
            on conflict on constraint config_unique do
        update
            set
            "status" = excluded.status,
            update_time = excluded.update_time
    </insert>

    <select id="getUserConfig" resultType="com.iflytek.cdc.admin.dto.UserConfigDto">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_user_config
        where uap_user_id = #{loginUserId, jdbcType=VARCHAR} and config_type = #{configType, jdbcType=VARCHAR}
    </select>

    <select id="getWateMarkSwitch" resultType="java.lang.Integer">
        select status
        from tb_cdcew_user_config
        where uap_user_id = #{loginUserId, jdbcType=VARCHAR} and config_type = #{waterMarkType, jdbcType=VARCHAR}
    </select>

</mapper>