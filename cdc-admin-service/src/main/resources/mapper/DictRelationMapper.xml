<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.DictRelationMapper">
  <sql id="Base_Column_List">
    id,
    directory_id,
    origin_dict_value_code,
    origin_dict_value_name,
    target_dict_value_code,
    target_dict_value_name,
    enabled,
    create_time,
    create_user,
    update_user,
    update_time,
    relation_status
  </sql>
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.DictRelation">
    <result column="id" property="id"/>
    <result column="directory_id" property="directoryId"/>
    <result column="origin_dict_value_code" property="originDictValueCode"/>
    <result column="origin_dict_value_name" property="originDictValueName"/>
    <result column="target_dict_value_code" property="targetDictValueCode"/>
    <result column="target_dict_value_name" property="targetDictValueName"/>
    <result column="enabled" property="enabled"/>
    <result column="create_time" property="createTime"/>
    <result column="create_user" property="createUser"/>
    <result column="update_user" property="updateUser"/>
    <result column="update_time" property="updateTime"/>
    <result column="relation_status" property="relationStatus"/>
  </resultMap>
  <insert id="insertDirectory" parameterType="com.iflytek.cdc.admin.entity.DictRelationDirectory">
    insert into tb_cdcmr_dict_relation_directory (id, directory_name, origin_dict_code,
    origin_dict_name, target_dict_code, target_dict_name,
    enabled, create_datetime, creator,
    updator, update_datetime, delete_flag
    )
    values (#{id,jdbcType=VARCHAR}, #{directoryName,jdbcType=VARCHAR}, #{originDictCode,jdbcType=VARCHAR},
    #{originDictName,jdbcType=VARCHAR}, #{targetDictCode,jdbcType=VARCHAR}, #{targetDictName,jdbcType=VARCHAR},
    #{enabled,jdbcType=VARCHAR}, now(), #{createUser,jdbcType=VARCHAR},
    #{updateUser,jdbcType=VARCHAR}, now(), #{isDeleted,jdbcType=VARCHAR}
    )
  </insert>

  <select id="queryDirectory" resultType="com.iflytek.cdc.admin.entity.DictRelationDirectory">
    select id as id, directory_name as directoryName , origin_dict_code as originDictCode, origin_dict_name as originDictName,
    target_dict_code as targetDictCode, target_dict_name as targetDictName,enabled as enabled,
    create_datetime as createTime, creator as createUser, updator as updateUser, update_datetime as updateTime,delete_flag as isDeleted from tb_cdcmr_dict_relation_directory
    where delete_flag=#{deleteFlag,jdbcType=VARCHAR}
    <if test="originDictCode != null and originDictCode != ''">
      and origin_dict_code=#{originDictCode,jdbcType=VARCHAR}
    </if>
    <if test="targetDictCode != null and targetDictCode != ''">
      and  target_dict_code=#{targetDictCode,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryDirectoryList" resultType="com.iflytek.cdc.admin.entity.DictRelationDirectory" parameterType="java.lang.String">
    select id as id, directory_name as directoryName , origin_dict_code as originDictCode, origin_dict_name as originDictName,
    target_dict_code as targetDictCode, target_dict_name as targetDictName,enabled as enabled,
    create_datetime as createTime, creator as createUser, updator as updateUser, update_datetime as updateTime,delete_flag as isDeleted from tb_cdcmr_dict_relation_directory
    where delete_flag=#{deleteFlag,jdbcType=VARCHAR}
    <if test="directoryName != null and directoryName != ''">
      and directory_name like concat('%',#{directoryName},'%')
    </if>
    order by create_datetime desc ;
  </select>

  <select id="queryDirectoryById" resultType="com.iflytek.cdc.admin.entity.DictRelationDirectory">
    select id as id, directory_name as directoryName , origin_dict_code as originDictCode, origin_dict_name as originDictName,
           target_dict_code as targetDictCode, target_dict_name as targetDictName,enabled as enabled,
           create_datetime as createTime, creator as createUser, updator as updateUser, update_datetime as updateTime,delete_flag as isDeleted from tb_cdcmr_dict_relation_directory
    where id=#{id,jdbcType=VARCHAR}
  </select>

  <delete id="deleteDirectory">
    update tb_cdcmr_dict_relation_directory  set delete_flag=#{deleteFlag,jdbcType=VARCHAR},
    update_datetime=now(),updator=#{loginUserId,jdbcType=VARCHAR} where 1=1
    <if test="id != null and id != ''">
      and id=#{id,jdbcType=VARCHAR}
    </if>
  </delete>

  <delete id="deleteRelationById">
    delete  from tb_cdcmr_dict_relation  where  directory_id=#{directoryId,jdbcType=VARCHAR}
  </delete>

  <select id="queryDictRelation" resultType="com.iflytek.cdc.admin.entity.DictRelation" parameterType="java.lang.String">
    select id as id, directory_id as directoryId, origin_dict_value_code as originDictValueCode, origin_dict_value_name as originDictValueName,
           target_dict_value_code as targetDictValueCode,  target_dict_value_name as targetDictValueName from tb_cdcmr_dict_relation where 1=1
    <if test="directoryId != null and directoryId != ''">
      and  directory_id=#{directoryId,jdbcType=VARCHAR}
    </if>
    <if test="dictValueCode != null and dictValueCode != ''">
      and  origin_dict_value_code=#{dictValueCode,jdbcType=VARCHAR}
    </if>
    <if test="targetValueCode != null and targetValueCode != ''">
      and target_dict_value_code=#{targetValueCode,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="originDictRelationList" resultType="com.iflytek.cdc.admin.entity.DictValue" parameterType="com.iflytek.cdc.admin.dto.SearchDictValueDTO">
    select  origin_dict_value_code as dictValueCode, origin_dict_value_name as dictValueName
          from tb_cdcmr_dict_relation where 1=1
    <if test="dictValueCode != null and dictValueCode != ''">
      and origin_dict_value_code=#{dictValueCode,jdbcType=VARCHAR}
    </if>
    <if test="directoryId != null and directoryId != ''">
      and directory_id=#{directoryId,jdbcType=VARCHAR}
    </if>
    <if test="dictValueName != null and dictValueName != ''">
      and origin_dict_value_name like  concat('%',#{dictValueName,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <select id="targetDictRelationList" resultType="com.iflytek.cdc.admin.entity.DictValue" parameterType="com.iflytek.cdc.admin.dto.SearchDictValueDTO">
    select target_dict_value_code as dictValueCode,  target_dict_value_name as dictValueName from tb_cdcmr_dict_relation where 1=1
    <if test="dictValueCode != null and dictValueCode != ''">
      and target_dict_value_code=#{dictValueCode,jdbcType=VARCHAR}
    </if>
    <if test="originDictValueCode != null and originDictValueCode != ''">
      and origin_dict_value_code=#{originDictValueCode,jdbcType=VARCHAR}
    </if>
    <if test="directoryId != null and directoryId != ''">
      and directory_id=#{directoryId,jdbcType=VARCHAR}
    </if>
    <if test="dictValueName != null and dictValueName != ''">
      and target_dict_value_name like  concat('%',#{dictValueName,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <insert id="insertRelation" parameterType="com.iflytek.cdc.admin.entity.DictRelation">
    insert into tb_cdcmr_dict_relation (id, directory_id, origin_dict_value_code,
    origin_dict_value_name, target_dict_value_code,
    target_dict_value_name, enabled, create_datetime,
    creator, updator, update_datetime
    )
    values (#{id,jdbcType=VARCHAR}, #{directoryId,jdbcType=VARCHAR}, #{originDictValueCode,jdbcType=VARCHAR},
    #{originDictValueName,jdbcType=VARCHAR}, #{targetDictValueCode,jdbcType=VARCHAR},
    #{targetDictValueName,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR}, now(),
    #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, now()
    )
  </insert>

  <delete id="deleteRelationByMdm" parameterType="java.lang.String">
    delete  from tb_cdcmr_dict_relation  where directory_id=#{directoryId,jdbcType=VARCHAR}
    <if test="valueCode != null and valueCode != ''">
      and (origin_dict_value_code=#{valueCode} or target_dict_value_code=#{valueCode})
    </if>
    <if test="valueName != null and valueName != ''">
      and (origin_dict_value_name=#{valueName} or target_dict_value_name=#{valueName})
    </if>
  </delete>

  <select id="queryOriginDirectoryIds" parameterType="java.lang.String" resultType="java.lang.String">
    select id from tb_cdcmr_dict_relation_directory where origin_dict_code=#{dictCode,jdbcType=VARCHAR}
    and delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <select id="queryTargetDirectoryIds" parameterType="java.lang.String" resultType="java.lang.String">
    select id from tb_cdcmr_dict_relation_directory where target_dict_code=#{dictCode,jdbcType=VARCHAR}
    and delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <delete id="deleteOriginRelationByMdms">
    delete  from tb_cdcmr_dict_relation where origin_dict_value_code=#{valueCode}
    <if test="directoryIds != null and directoryIds.size() != 0">
      and directory_id in (
      <foreach collection="directoryIds" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </delete>

  <delete id="deleteTargetRelationByMdms">
    delete  from tb_cdcmr_dict_relation where target_dict_value_code=#{valueCode}
    <if test="directoryIds != null and directoryIds.size() != 0">
      and directory_id in (
      <foreach collection="directoryIds" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </delete>

  <update id="updateOriginNameByMdm" parameterType="java.lang.String">
    update tb_cdcmr_dict_relation  set origin_dict_value_name=#{valueName,jdbcType=VARCHAR}  where directory_id=#{directoryId,jdbcType=VARCHAR}
    and origin_dict_value_code=#{valueCode,jdbcType=VARCHAR}
  </update>

  <update id="updateOriginCodeByMdm" parameterType="java.lang.String">
    update tb_cdcmr_dict_relation  set origin_dict_value_code=#{valueCode,jdbcType=VARCHAR}  where directory_id=#{directoryId,jdbcType=VARCHAR}
    and origin_dict_value_name=#{valueName,jdbcType=VARCHAR}
  </update>

  <update id="updateTargetNameByMdm" parameterType="java.lang.String">
    update tb_cdcmr_dict_relation  set target_dict_value_name=#{valueName,jdbcType=VARCHAR}  where directory_id=#{directoryId,jdbcType=VARCHAR}
    and target_dict_value_code=#{valueCode,jdbcType=VARCHAR}
  </update>

  <update id="updateTargetCodeByMdm" parameterType="java.lang.String">
    update tb_cdcmr_dict_relation  set target_dict_value_code=#{valueCode,jdbcType=VARCHAR}  where directory_id=#{directoryId,jdbcType=VARCHAR}
    and target_dict_value_name=#{valueName,jdbcType=VARCHAR}
  </update>

  <delete id="deleteRelation" parameterType="com.iflytek.cdc.admin.dto.DeleteRelationDTO">
    delete  from  tb_cdcmr_dict_relation where directory_id=#{directoryId,jdbcType=VARCHAR} and origin_dict_value_code=#{originDictValueCode,jdbcType=VARCHAR}
    and target_dict_value_code=#{targetDictValueCode,jdbcType=VARCHAR}
  </delete>

  <select id="queryExportRelation" parameterType="com.iflytek.cdc.admin.dto.SearchExportRelationDTO" resultType="com.iflytek.cdc.admin.entity.ExportDictRelation">
    select  rd.directory_name as directoryName,rd.origin_dict_name as originDictName,rd.origin_dict_code as originDictCode,rd.target_dict_name as targetDictName,
     rd.target_dict_code as targetDictCode,dr.origin_dict_value_code  as originDictValueCode,dr.origin_dict_value_name as originDictValueName,
    dr.target_dict_value_code as targetDictValueCode,dr.target_dict_value_name as targetDictValueName
    from tb_cdcmr_dict_relation_directory rd left join  tb_cdcmr_dict_relation dr on rd.id=dr.directory_id where 1=1
    <if test="directoryIds != null and directoryIds.size() != 0">
      and rd.id in (
      <foreach collection="directoryIds" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
    order by  dr.origin_dict_value_code asc
  </select>

  <select id="queryDirectoryId" parameterType="java.lang.String" resultType="java.lang.String">
    select id from tb_cdcmr_dict_relation_directory where 1=1
    <if test="originDictCode != null and originDictCode != ''">
      and origin_dict_code=#{originDictCode,jdbcType=VARCHAR}
    </if>
    <if test="targetDictCode != null and targetDictCode != ''">
      and target_dict_code=#{targetDictCode,jdbcType=VARCHAR}
    </if>
  </select>

  <delete id="deleteRelationByCode" >
    delete  from tb_cdcmr_dict_relation where 1=1
    <if test="originDictValueCode != null and originDictValueCode != ''">
      and origin_dict_value_code=#{originDictValueCode,jdbcType=VARCHAR}
    </if>
    <if test="ids != null and ids.size() != 0">
      and directory_id in (
      <foreach collection="ids" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
    <if test="targetDictValueCode != null and targetDictValueCode != ''">
      and target_dict_value_code=#{targetDictValueCode,jdbcType=VARCHAR}
    </if>
  </delete>

  <update id="updateRelationName" parameterType="com.iflytek.cdc.admin.dto.UpdateRelationDTO">
    update tb_cdcmr_dict_relation_directory set directory_name=#{directoryName,jdbcType=VARCHAR} ,updator=#{updateUser,jdbcType=VARCHAR}, update_datetime=now() where id=#{id,jdbcType=VARCHAR}
  </update>

  <select id="queryOriginAllRelation" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.dto.AsyncRelationDTO">
    select rd.origin_dict_code  as dictCode,rd.origin_dict_name  as dictName,
    dr.origin_dict_value_code as dictValueCode,dr.origin_dict_value_name as dictValueName
    from tb_cdcmr_dict_relation_directory  rd join tb_cdcmr_dict_relation  dr on rd.id=dr.directory_id where rd.delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <select id="queryTargetAllRelation" parameterType="java.lang.String" resultType="com.iflytek.cdc.admin.dto.AsyncRelationDTO">
  select rd.target_dict_code as dictCode,rd.target_dict_name as dictName,dr.target_dict_value_code as dictValueCode,
        dr.target_dict_value_name as dictValueName from tb_cdcmr_dict_relation_directory  rd join tb_cdcmr_dict_relation
    dr on rd.id=dr.directory_id where rd.delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <select id="queryDirectoryIdsByAsync" parameterType="com.iflytek.cdc.admin.dto.AsyncRelationDTO" resultType="java.lang.String">
    select dr.id from tb_cdcmr_dict_relation_directory rd
    JOIN tb_cdcmr_dict_relation dr  ON rd.id = dr.directory_id
    where  1=1
    <if test="asyncRelationDtos != null and asyncRelationDtos.size() != 0">
      and   ( rd.origin_dict_code  in (
      <foreach collection="asyncRelationDtos" item="item" index="index" separator=",">
        #{item.dictCode,jdbcType=VARCHAR}
      </foreach>
      )
      and dr.origin_dict_value_code in (
      <foreach collection="asyncRelationDtos" item="item" index="index" separator=",">
        #{item.dictValueCode,jdbcType=VARCHAR}
      </foreach>
      ))
      or (rd.target_dict_code in (
      <foreach collection="asyncRelationDtos" item="item" index="index" separator=",">
        #{item.dictCode,jdbcType=VARCHAR}
      </foreach>
      )
      and dr.target_dict_value_code in (
      <foreach collection="asyncRelationDtos" item="item" index="index" separator=",">
        #{item.dictValueCode,jdbcType=VARCHAR}
      </foreach>
      ))
      and rd.delete_flag='0'
    </if>
  </select>

  <select id="queryDirectoryIdByAsync" parameterType="com.iflytek.cdc.admin.dto.AsyncRelationDTO" resultType="java.lang.String">
    select dr.id from tb_cdcmr_dict_relation_directory rd
    JOIN tb_cdcmr_dict_relation dr  ON rd.id = dr.directory_id
    where  1=1
       and (rd.origin_dict_code=#{dictCode,jdbcType=VARCHAR}
      and dr.origin_dict_value_code=#{dictValueCode,jdbcType=VARCHAR} )
      or (rd.target_dict_code =#{dictCode,jdbcType=VARCHAR}
      and dr.target_dict_value_code= #{dictValueCode,jdbcType=VARCHAR})
      and rd.delete_flag='0'
  </select>

  <select id="queryOriginIdByAsync" parameterType="com.iflytek.cdc.admin.dto.AsyncRelationDTO"  resultType="java.lang.String">
    select dr.id from tb_cdcmr_dict_relation_directory rd
    JOIN tb_cdcmr_dict_relation dr  ON rd.id = dr.directory_id
    where  1=1
    and (rd.origin_dict_code=#{dictCode,jdbcType=VARCHAR}
    and dr.origin_dict_value_code=#{dictValueCode,jdbcType=VARCHAR} )
    and rd.delete_flag='0'
  </select>

  <select id="queryTargetIdByAsync" parameterType="com.iflytek.cdc.admin.dto.AsyncRelationDTO" resultType="java.lang.String">
    select dr.id from tb_cdcmr_dict_relation_directory rd
    JOIN tb_cdcmr_dict_relation dr  ON rd.id = dr.directory_id
    where  1=1 and
    (rd.target_dict_code =#{dictCode,jdbcType=VARCHAR}
    and dr.target_dict_value_code= #{dictValueCode,jdbcType=VARCHAR})
    and rd.delete_flag='0'
  </select>

  <delete id="deleteRelationByIds" parameterType="java.lang.String">
    delete from tb_cdcmr_dict_relation  where 1=1
    <if test="ids != null and ids.size() != 0">
      and id in (
      <foreach collection="ids" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </delete>

  <delete id="deleteRelationByDirectoryId" parameterType="java.lang.String">
    delete from tb_cdcmr_dict_relation  where 1=1
    <if test="directoryIds != null and directoryIds.size() != 0">
      and directory_id in (
      <foreach collection="directoryIds" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </delete>

  <update id="updateOriginRelationNameByAsync" >
    update tb_cdcmr_dict_relation
    set origin_dict_value_name = #{relation.dictValueName,jdbcType=VARCHAR},
    update_datetime=now(),
    updator=#{loginUserId,jdbcType=VARCHAR}
    where origin_dict_value_code=#{relation.dictValueCode,jdbcType=VARCHAR}
    <if test="ids != null and ids.size() != 0">
      and id in (
      <foreach collection="ids" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </update>

  <update id="updateTargetRelationNameByAsync" >
    update tb_cdcmr_dict_relation
    set target_dict_value_name = #{relation.dictValueName,jdbcType=VARCHAR},
    update_datetime=now(),
    updator=#{loginUserId,jdbcType=VARCHAR}
    where target_dict_value_code=#{relation.dictValueCode,jdbcType=VARCHAR}
    <if test="ids != null and ids.size() != 0">
      and id in (
      <foreach collection="ids" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </update>

  <select id="queryDictValueInfo" resultType="com.iflytek.cdc.admin.sdk.pojo.DictValueInfo" parameterType="com.iflytek.cdc.admin.sdk.entity.DictRelationFilter">
    select target_dict_value_code as dictValueCode, target_dict_value_name as dictValueName  from tb_cdcmr_dict_relation
     where directory_id=#{directoryId,jdbcType=VARCHAR}
    <if test="originValueCode != null and originValueCode != ''">
      and origin_dict_value_code=#{originValueCode,jdbcType=VARCHAR}
    </if>
    <if test="originValueName != null and originValueName != ''">
      and origin_dict_value_Name=#{originValueName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryOriginDictValueInfo" resultType="com.iflytek.cdc.admin.sdk.pojo.DictValueInfo" parameterType="com.iflytek.cdc.admin.sdk.entity.DictRelationFilter">
    select origin_dict_value_code  as dictValueCode, origin_dict_value_Name as dictValueName  from tb_cdcmr_dict_relation
    where directory_id=#{directoryId,jdbcType=VARCHAR}
    <if test="targetValueCode != null and targetValueCode != ''">
      and target_dict_value_code=#{targetValueCode,jdbcType=VARCHAR}
    </if>
    <if test="targetValueName != null and targetValueName != ''">
      and target_dict_value_name=#{targetValueName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryDirectorys" resultType="com.iflytek.cdc.admin.entity.DictRelationDirectory">
    select id as id, directory_name as directoryName , origin_dict_code as originDictCode, origin_dict_name as originDictName,
    target_dict_code as targetDictCode, target_dict_name as targetDictName,enabled as enabled,
    create_datetime as createTime, creator as createUser, updator as updateUser, update_datetime as updateTime,delete_flag as isDeleted from tb_cdcmr_dict_relation_directory
    where  origin_dict_code=#{valueCode,jdbcType=VARCHAR}  or target_dict_code=#{valueCode,jdbcType=VARCHAR} and delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <select id="queryRelationId" resultType="java.lang.String">
    SELECT cr.id FROM "tb_cdcmr_dict_relation_directory" di join tb_cdcmr_dict_relation  cr on di.id=cr.directory_id where
    di.origin_dict_code=#{dictCode,jdbcType=VARCHAR}  or di.target_dict_code=#{dictCode,jdbcType=VARCHAR} and
    cr.origin_dict_value_code=#{dictValueCode,jdbcType=VARCHAR} or cr.target_dict_value_code=#{dictValueCode,jdbcType=VARCHAR}
    and di.delete_flag=#{deleteFlag,jdbcType=VARCHAR}
  </select>

  <select id="queryDeleteIdByCode" resultType="java.lang.String">
    select id from tb_cdcmr_dict_relation_directory  where 1=1
    <if test="deleteCodes != null and deleteCodes.size() != 0">
        and target_dict_code in (
      <foreach collection="deleteCodes" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
      or origin_dict_code in (
      <foreach collection="deleteCodes" item="item" index="index" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2022-04-07-->
  <select id="findOriginDictValueName" resultType="java.lang.String">
    select origin_dict_value_name
    from tb_cdcmr_dict_relation
    where directory_id=#{directoryId} and origin_dict_value_code=#{originDictValueCode}
  </select>


<!--auto generated by MybatisCodeHelper on 2022-04-07-->
  <select id="findTargetDictValueName" resultType="java.lang.String">
    select target_dict_value_name
    from tb_cdcmr_dict_relation
    where directory_id=#{directoryId} and target_dict_value_code=#{targetDictValueCode}
  </select>
</mapper>