<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrInspectionMappingMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="infected_name" jdbcType="VARCHAR" property="infectedName"/>
        <result column="infected_code" jdbcType="VARCHAR" property="infectedCode"/>
        <result column="mapping_name" jdbcType="VARCHAR" property="mappingName"/>
        <result column="clinical_diagnosis" jdbcType="VARCHAR" property="clinicalDiagnosis"/>
        <result column="clinical_diagnosis_code" jdbcType="VARCHAR" property="clinicalDiagnosisCode"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , infected_name, infected_code, mapping_name, clinical_diagnosis, clinical_diagnosis_code,
    "status", update_user, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from "tb_cdcmr_inspection_mapping"
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getList" resultType="com.iflytek.cdc.admin.dto.InspectionMappingVO">
        SELECT t1.*,
               (SELECT COUNT(t2.ID) FROM tb_cdcmr_inspection_rule t2 WHERE t2.mapping_id = t1.ID) AS ruleCount
        FROM tb_cdcmr_inspection_mapping t1
        where t1.infected_code = #{infectedCode,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from "tb_cdcmr_inspection_mapping"
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByInfectedCode">
        delete
        from "tb_cdcmr_inspection_mapping"
        where infected_code = #{infectedCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping">
        insert into "tb_cdcmr_inspection_mapping" (id, infected_name, infected_code,
                                                   mapping_name, clinical_diagnosis, clinical_diagnosis_code,
                                                   "status", update_user, update_time)
        values (#{id,jdbcType=VARCHAR}, #{infectedName,jdbcType=VARCHAR}, #{infectedCode,jdbcType=VARCHAR},
                #{mappingName,jdbcType=VARCHAR}, #{clinicalDiagnosis,jdbcType=VARCHAR},
                #{clinicalDiagnosisCode,jdbcType=VARCHAR},
                #{status,jdbcType=SMALLINT}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping">
        insert into "tb_cdcmr_inspection_mapping"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="infectedName != null">
                infected_name,
            </if>
            <if test="infectedCode != null">
                infected_code,
            </if>
            <if test="mappingName != null">
                mapping_name,
            </if>
            <if test="clinicalDiagnosis != null">
                clinical_diagnosis,
            </if>
            <if test="clinicalDiagnosisCode != null">
                clinical_diagnosis_code,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="infectedName != null">
                #{infectedName,jdbcType=VARCHAR},
            </if>
            <if test="infectedCode != null">
                #{infectedCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="clinicalDiagnosis != null">
                #{clinicalDiagnosis,jdbcType=VARCHAR},
            </if>
            <if test="clinicalDiagnosisCode != null">
                #{clinicalDiagnosisCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into "tb_cdcmr_inspection_mapping" (id, infected_name, infected_code,
        mapping_name, clinical_diagnosis, clinical_diagnosis_code,
        "status", update_user, update_time
        )
        values
        <foreach collection="list" item="item" separator="," index="index">
            (#{item.id,jdbcType=VARCHAR}, #{item.infectedName,jdbcType=VARCHAR}, #{item.infectedCode,jdbcType=VARCHAR},
            #{item.mappingName,jdbcType=VARCHAR}, #{item.clinicalDiagnosis,jdbcType=VARCHAR},
            #{item.clinicalDiagnosisCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT}, #{item.updateUser,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping">
        update "tb_cdcmr_inspection_mapping"
        <set>
            <if test="infectedName != null">
                infected_name = #{infectedName,jdbcType=VARCHAR},
            </if>
            <if test="infectedCode != null">
                infected_code = #{infectedCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingName != null">
                mapping_name = #{mappingName,jdbcType=VARCHAR},
            </if>
            <if test="clinicalDiagnosis != null">
                clinical_diagnosis = #{clinicalDiagnosis,jdbcType=VARCHAR},
            </if>
            <if test="clinicalDiagnosisCode != null">
                clinical_diagnosis_code = #{clinicalDiagnosisCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrInspectionMapping">
        update "tb_cdcmr_inspection_mapping"
        set infected_name           = #{infectedName,jdbcType=VARCHAR},
            infected_code           = #{infectedCode,jdbcType=VARCHAR},
            mapping_name            = #{mappingName,jdbcType=VARCHAR},
            clinical_diagnosis      = #{clinicalDiagnosis,jdbcType=VARCHAR},
            clinical_diagnosis_code = #{clinicalDiagnosisCode,jdbcType=VARCHAR},
            "status"                = #{status,jdbcType=SMALLINT},
            update_user             = #{updateUser,jdbcType=VARCHAR},
            update_time             = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="pageList" resultType="com.iflytek.cdc.admin.dto.InspectionMappingPageVO">
        select a.id,a.disease_code,a.disease_name,a.rule_desc,a.rule_status as status,c.diseases_type as
        diseaseTypeCode,'1' as businessType,a.is_single_case as isSingleCase,
        (select count(b.id) from tb_cdcmr_inspection_mapping b where a.disease_code = b.infected_code
        ) as mappingCount
        from tb_cdcmr_medical_warn a left join tb_cdcmr_infectious_diseases c on a.disease_code = c.diseases_code
        where 1=1
        AND A.disease_code IN ( SELECT diseases_code FROM tb_cdcmr_infectious_diseases where COALESCE (
        diseases_classify, '' ) = '' )
        <if test="diseaseCode != null and diseaseCode != ''">
            and (a.disease_code = #{diseaseCode}
            or a.disease_code in (select diseases_classify from tb_cdcmr_infectious_diseases where diseases_code =
            #{diseaseCode}))
        </if>
        <if test="diseaseType != null and diseaseType != ''">
            and c.diseases_type = #{diseaseType}
        </if>
        and a.delete_flag = '0'
        order by a.disease_code asc
    </select>
    <select id="subPageList" resultType="com.iflytek.cdc.admin.dto.InspectionMappingPageVO">
        select a.id,a.disease_code,a.disease_name,a.rule_desc,a.rule_status as status,c.diseases_classify as
        diseaseParentCode,c.diseases_type as diseaseTypeCode,'2' as businessType,a.is_single_case as isSingleCase,
        (select count(b.id) from tb_cdcmr_inspection_mapping b where a.disease_code = b.infected_code
        ) as mappingCount
        from tb_cdcmr_medical_warn a left join tb_cdcmr_infectious_diseases c on a.disease_code = c.diseases_code
        where 1=1
        and a.disease_code not in (SELECT COALESCE ( diseases_classify, '' ) FROM tb_cdcmr_infectious_diseases )
        <if test="diseaseCode != null and diseaseCode != ''">
            and a.disease_code = #{diseaseCode}
            or a.disease_code in (select diseases_code from tb_cdcmr_infectious_diseases where diseases_classify =
            #{diseaseCode})
        </if>
        <if test="diseaseType != null and diseaseType != ''">
            and c.diseases_type = #{diseaseType}
        </if>
        and a.delete_flag = '0'
    </select>
</mapper>