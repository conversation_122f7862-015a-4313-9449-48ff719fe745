<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrFzHisOrgMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrFzHisOrg">
    <!--@mbg.generated-->
    <!--@Table tb_cdcmr_fz_his_org-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="DISTRICT_CODE" jdbcType="VARCHAR" property="districtCode" />
    <result column="DISTRICT" jdbcType="VARCHAR" property="district" />
    <result column="HIS_HIGHER_ORGNAME" jdbcType="VARCHAR" property="hisHigherOrgname" />
    <result column="HIS_ORGCODE" jdbcType="VARCHAR" property="hisOrgcode" />
    <result column="HIS_ORGNAME" jdbcType="VARCHAR" property="hisOrgname" />
    <result column="SOURCE_ID" jdbcType="VARCHAR" property="sourceId" />
    <result column="SOURCE_NAME" jdbcType="VARCHAR" property="sourceName" />
    <result column="HIS_CREATOR" jdbcType="VARCHAR" property="hisCreator" />
    <result column="HIS_CREATE_TIME" jdbcType="TIMESTAMP" property="hisCreateTime" />
    <result column="DOCTOR_ID" jdbcType="VARCHAR" property="doctorId" />
    <result column="DELETED" jdbcType="SMALLINT" property="deleted" />
    <result column="MEDICAL_TYPE" jdbcType="SMALLINT" property="medicalType" />
    <result column="MATCH_STATUS" jdbcType="SMALLINT" property="matchStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CITY_CODE, DISTRICT_CODE, DISTRICT, HIS_HIGHER_ORGNAME, HIS_ORGCODE, HIS_ORGNAME, 
    SOURCE_ID, SOURCE_NAME, HIS_CREATOR, HIS_CREATE_TIME, DOCTOR_ID, DELETED, MEDICAL_TYPE, 
    MATCH_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcmr_fz_his_org
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tb_cdcmr_fz_his_org
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzHisOrg">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_his_org (ID, CITY_CODE, DISTRICT_CODE, 
      DISTRICT, HIS_HIGHER_ORGNAME, HIS_ORGCODE, 
      HIS_ORGNAME, SOURCE_ID, SOURCE_NAME, 
      HIS_CREATOR, HIS_CREATE_TIME, DOCTOR_ID, 
      DELETED, MEDICAL_TYPE, MATCH_STATUS
      )
    values (#{id,jdbcType=BIGINT}, #{cityCode,jdbcType=VARCHAR}, #{districtCode,jdbcType=VARCHAR}, 
      #{district,jdbcType=VARCHAR}, #{hisHigherOrgname,jdbcType=VARCHAR}, #{hisOrgcode,jdbcType=VARCHAR}, 
      #{hisOrgname,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, #{sourceName,jdbcType=VARCHAR}, 
      #{hisCreator,jdbcType=VARCHAR}, #{hisCreateTime,jdbcType=TIMESTAMP}, #{doctorId,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=SMALLINT}, #{medicalType,jdbcType=SMALLINT}, #{matchStatus,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzHisOrg">
    <!--@mbg.generated-->
    insert into tb_cdcmr_fz_his_org
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="cityCode != null">
        CITY_CODE,
      </if>
      <if test="districtCode != null">
        DISTRICT_CODE,
      </if>
      <if test="district != null">
        DISTRICT,
      </if>
      <if test="hisHigherOrgname != null">
        HIS_HIGHER_ORGNAME,
      </if>
      <if test="hisOrgcode != null">
        HIS_ORGCODE,
      </if>
      <if test="hisOrgname != null">
        HIS_ORGNAME,
      </if>
      <if test="sourceId != null">
        SOURCE_ID,
      </if>
      <if test="sourceName != null">
        SOURCE_NAME,
      </if>
      <if test="hisCreator != null">
        HIS_CREATOR,
      </if>
      <if test="hisCreateTime != null">
        HIS_CREATE_TIME,
      </if>
      <if test="doctorId != null">
        DOCTOR_ID,
      </if>
      <if test="deleted != null">
        DELETED,
      </if>
      <if test="medicalType != null">
        MEDICAL_TYPE,
      </if>
      <if test="matchStatus != null">
        MATCH_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="hisHigherOrgname != null">
        #{hisHigherOrgname,jdbcType=VARCHAR},
      </if>
      <if test="hisOrgcode != null">
        #{hisOrgcode,jdbcType=VARCHAR},
      </if>
      <if test="hisOrgname != null">
        #{hisOrgname,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="hisCreator != null">
        #{hisCreator,jdbcType=VARCHAR},
      </if>
      <if test="hisCreateTime != null">
        #{hisCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorId != null">
        #{doctorId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=SMALLINT},
      </if>
      <if test="medicalType != null">
        #{medicalType,jdbcType=SMALLINT},
      </if>
      <if test="matchStatus != null">
        #{matchStatus,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzHisOrg">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_his_org
    <set>
      <if test="cityCode != null">
        CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="districtCode != null">
        DISTRICT_CODE = #{districtCode,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        DISTRICT = #{district,jdbcType=VARCHAR},
      </if>
      <if test="hisHigherOrgname != null">
        HIS_HIGHER_ORGNAME = #{hisHigherOrgname,jdbcType=VARCHAR},
      </if>
      <if test="hisOrgcode != null">
        HIS_ORGCODE = #{hisOrgcode,jdbcType=VARCHAR},
      </if>
      <if test="hisOrgname != null">
        HIS_ORGNAME = #{hisOrgname,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        SOURCE_NAME = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="hisCreator != null">
        HIS_CREATOR = #{hisCreator,jdbcType=VARCHAR},
      </if>
      <if test="hisCreateTime != null">
        HIS_CREATE_TIME = #{hisCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="doctorId != null">
        DOCTOR_ID = #{doctorId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        DELETED = #{deleted,jdbcType=SMALLINT},
      </if>
      <if test="medicalType != null">
        MEDICAL_TYPE = #{medicalType,jdbcType=SMALLINT},
      </if>
      <if test="matchStatus != null">
        MATCH_STATUS = #{matchStatus,jdbcType=SMALLINT},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.admin.entity.TbCdcmrFzHisOrg">
    <!--@mbg.generated-->
    update tb_cdcmr_fz_his_org
    set CITY_CODE = #{cityCode,jdbcType=VARCHAR},
      DISTRICT_CODE = #{districtCode,jdbcType=VARCHAR},
      DISTRICT = #{district,jdbcType=VARCHAR},
      HIS_HIGHER_ORGNAME = #{hisHigherOrgname,jdbcType=VARCHAR},
      HIS_ORGCODE = #{hisOrgcode,jdbcType=VARCHAR},
      HIS_ORGNAME = #{hisOrgname,jdbcType=VARCHAR},
      SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      SOURCE_NAME = #{sourceName,jdbcType=VARCHAR},
      HIS_CREATOR = #{hisCreator,jdbcType=VARCHAR},
      HIS_CREATE_TIME = #{hisCreateTime,jdbcType=TIMESTAMP},
      DOCTOR_ID = #{doctorId,jdbcType=VARCHAR},
      DELETED = #{deleted,jdbcType=SMALLINT},
      MEDICAL_TYPE = #{medicalType,jdbcType=SMALLINT},
      MATCH_STATUS = #{matchStatus,jdbcType=SMALLINT}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>