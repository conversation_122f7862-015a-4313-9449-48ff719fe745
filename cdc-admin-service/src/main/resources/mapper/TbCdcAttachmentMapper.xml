<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcAttachmentMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcAttachment">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName"/>
        <result column="attachment_path" jdbcType="VARCHAR" property="attachmentPath"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , attachment_name, attachment_path, create_time, update_time, status
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_attachment
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByPrimaryKeys" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_attachment
        where id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.iflytek.cdc.admin.entity.TbCdcAttachment">
        insert into tb_cdcmr_attachment (id, attachment_name, attachment_path,
                                         create_time, update_time, status)
        values (#{id,jdbcType=VARCHAR}, #{attachmentName,jdbcType=VARCHAR}, #{attachmentPath,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER})
    </insert>
</mapper>