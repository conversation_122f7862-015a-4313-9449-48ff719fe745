<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.MedicalWarnRuleMapper">

    <sql id="all_columns">
        id,rule_id,region_id,disease_code,disease_name,time_unit,time_operate,time_scope,monitor_object,medical_operate,medical_count,medical_attribute,
        warn_type,rule_json,create_datetime,creator,update_datetime,updator,delete_flag, type
    </sql>

    <sql id="all_columns1">
        id,rule_id,region_id,disease_code,disease_name,time_unit,time_operate,time_scope,monitor_object,medical_operate,medical_count,medical_attribute,
        warn_type,create_datetime,creator,update_datetime,updator,delete_flag, type
    </sql>

    <select id="queryByWranId" resultType="com.iflytek.cdc.admin.entity.MedicalWarnRule">
       select <include refid="all_columns1"/> from tb_cdcmr_medical_warn_rule
       where rule_id = #{wranId} and disease_code = #{diseaseCode} and delete_flag = '0'
    </select>

    <select id="queryByRegionCode" resultType="com.iflytek.cdc.admin.entity.MedicalWarnRule">
        select a.*
          from tb_cdcmr_medical_warn_rule a
          where  a.region_id = #{regionCode}
           and a.delete_flag = '0' order by a.create_datetime desc
    </select>
    <select id="selectAll" resultType="com.iflytek.cdc.admin.entity.MedicalWarnRule">
        select a.*
        from tb_cdcmr_medical_warn_rule a
        where a.delete_flag = '0'
        order by a.create_datetime desc
    </select>

    <update id="updateRule">
        update tb_cdcmr_medical_warn_rule set

            <!--时间运算规则-->
            <if test="param.timeOperate != null and param.timeOperate !=''">
                time_operate = #{param.timeOperate},
            </if>
            <!--时间范围数量-->
            <if test="param.timeScope != null and param.timeScope !=''">
                time_scope = #{param.timeScope},
            </if>
            <!--时间单位-->
            <if test="param.timeScope != null and param.timeScope !=''">
                time_unit = #{param.timeUnit},
            </if>
            <!--检测对象-->
            <if test="param.monitorObject != null and param.monitorObject !=''">
                monitor_object = #{param.monitorObject},
            </if>
            <!--病例数量运算规则-->
            <if test="param.medicalOperate != null and param.medicalOperate !=''">
                medical_operate = #{param.medicalOperate},
            </if>
            <!--病例数量-->
            <if test="param.medicalCount != null and param.medicalCount !=''">
                medical_count = #{param.medicalCount},
            </if>
            <!--病例属性-->
            <if test="param.medicalAttribute != null and param.medicalAttribute !=''">
                medical_attribute = #{param.medicalAttribute},
            </if>
            <!--警示类型-->
            <if test="param.warnType != null and param.warnType !=''">
                warn_type = #{param.warnType},
            </if>
            <if test="param.type != null and param.type !=''">
                type = #{param.type},
            </if>
            creator = #{param.creator},
            rule_json = #{param.ruleJson},
            update_datetime = now()
            where id = #{param.id} and rule_id = #{param.ruleId}
    </update>
    <update id="updateSingleCaseByIds">
        update tb_cdcmr_medical_warn_rule set time_scope = 1,time_unit = 1,medical_count = 1, type = '1'
        where id in
        <foreach collection="rules" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
    </update>

</mapper>