<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.admin.mapper.TbCdcmrDistrictJsonDataMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.admin.entity.TbCdcmrDistrictJsonData">
    <id column="json_name" jdbcType="VARCHAR" property="jsonName" />
    <result column="json_data" jdbcType="VARCHAR" property="jsonData" />
  </resultMap>
  <sql id="Base_Column_List">
    json_name, json_data
  </sql>
  <select id="selectNameList" resultType="java.lang.String">
    select json_name from tb_cdcmr_district_json_data order by  json_name
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="string">
    select json_data
    from "tb_cdcmr_district_json_data"
    where json_name = #{jsonName,jdbcType=VARCHAR} limit 1
  </select>

</mapper>