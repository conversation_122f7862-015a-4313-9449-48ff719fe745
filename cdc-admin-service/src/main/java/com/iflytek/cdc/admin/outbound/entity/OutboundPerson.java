package com.iflytek.cdc.admin.outbound.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcmr_outbound_person")
public class OutboundPerson extends BaseEntity {

    @ApiModelProperty("外呼记录id")
    private String outboundRecordId;
    @ApiModelProperty(value = "批次号", required = true)
    private String batchNo;
    @ApiModelProperty(value = "姓名（必须是汉字）", required = true)
    private String name;
    @ApiModelProperty(value = "号码", required = true)
    private String telephone;
    @ApiModelProperty("身份证")
    private String idCard;
    @ApiModelProperty("第三方ID")
    private String relationId;
    @ApiModelProperty("居民扩展信息")
    private String extProperty;
    @ApiModelProperty("解析之后的结果")
    private String parseResult;
    @ApiModelProperty("手动变更的结果")
    private String manualResult;


    
}
