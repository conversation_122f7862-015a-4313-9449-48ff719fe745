package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface StdTextPreProcessService {

    /**
     * 获取规则列表
     * @param configType
     * @return
     */
    public List<TbCdcmrStdTextConfig> getConfigByType(String configType);

    public int cacheConfig();

    /**
     * 停用词，判断是否符合要求。若符合停用词，则返回false, 反之亦然
     * @param text
     * @return
     */
    public boolean isValid(String configType, String text);

    /**
     * 文本替换
     * @param text
     * @return
     */
    public String replace(String configType, String text);

    /**
     * 文本截取
     * @param text
     * @return
     */
    public String subString(String configType, String text);


    /**
     * 文本预处理
     * @param stdCategory
     * @param poiTypes
     * @param text
     * @return
     */
    public String textPreProcess(String stdCategory, String poiTypes, String text);

    /**
     * 文本预处理
     * @param configType
     * @param text
     * @return
     */
    public String textPreProcess(String configType, String text);

}
