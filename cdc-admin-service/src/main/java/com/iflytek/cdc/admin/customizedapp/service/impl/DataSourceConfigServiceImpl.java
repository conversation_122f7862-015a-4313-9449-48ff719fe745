package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsApp;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsDataSourceConfig;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsAppMapper;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsDataSourceConfigMapper;
import com.iflytek.cdc.admin.customizedapp.model.vo.DataSourceConfigVO;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import com.iflytek.cdc.admin.customizedapp.service.DataSourceConfigService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DataSourceConfigServiceImpl extends CdcServiceBaseImpl<TbCdccsDataSourceConfigMapper, TbCdccsDataSourceConfig> implements DataSourceConfigService {
    @Override
    public List<DataSourceConfigVO> listAll() {
        return list().stream().map(e -> {
            DataSourceConfigVO dataSourceConfigVO = new DataSourceConfigVO();
            dataSourceConfigVO.setId(e.getId());
            dataSourceConfigVO.setCode(e.getCode());
            dataSourceConfigVO.setName(e.getName());
            return dataSourceConfigVO;
        }).collect(Collectors.toList());
    }
}
