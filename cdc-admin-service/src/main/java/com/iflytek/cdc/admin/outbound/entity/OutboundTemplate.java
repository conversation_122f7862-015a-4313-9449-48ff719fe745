package com.iflytek.cdc.admin.outbound.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@ApiModel(value = "外呼模板维护")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_cdcmr_outbound_template")
public class OutboundTemplate extends BaseEntity implements Serializable {
    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("模板内容")
    private String templateContent;

    @ApiModelProperty("外呼类型")
    private String type;

    @ApiModelProperty("所属分组")
    private String category;

    @ApiModelProperty("话术id")
    private String speechId;
}
