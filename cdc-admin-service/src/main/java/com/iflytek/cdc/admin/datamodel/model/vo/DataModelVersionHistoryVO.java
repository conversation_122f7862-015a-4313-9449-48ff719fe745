package com.iflytek.cdc.admin.datamodel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@Data
public class DataModelVersionHistoryVO {

    @ApiModelProperty(value = "数据模型id")
    private String modelId;

    @ApiModelProperty(value = "数据模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "数据模型名称")
    private String dataModelName;

    @ApiModelProperty(value = "数据模型版本")
    private String version;

    @ApiModelProperty(value = "数据模型状态")
    private String status;

    @ApiModelProperty(value = "数据模型发布时间")
    @JsonFormat(pattern = "yyyy-mm-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    @ApiModelProperty(value = "数据模型作废时间")
    @JsonFormat(pattern = "yyyy-mm-dd HH:mm:ss", timezone = "GMT+8")
    private Date invalidTime;

    @ApiModelProperty(value = "数据模型作废操作人")
    private String invalidator;
}
