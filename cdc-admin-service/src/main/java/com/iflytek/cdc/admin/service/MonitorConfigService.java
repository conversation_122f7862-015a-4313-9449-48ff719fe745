package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.entity.TbCdcmrDiseaseMonitorRule;

import java.util.List;

public interface MonitorConfigService extends IService<TbCdcmrDiseaseMonitorRule> {

    List<TbCdcmrDiseaseMonitorRule> getMonitorConfigMsg(String diseaseCode,String diseaseName, String configType);

    List<TbCdcmrDiseaseMonitorRule> getMonitorConfigByIds(List<String> ids);

    void updateMonitorConfig(String loginUserId, TbCdcmrDiseaseMonitorRule tbCdcmrDiseaseMonitorRule);
}
