package com.iflytek.cdc.admin.outbound.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OutboundTemplateUtils {
    public static String replaceTemplateVars(String template, Map<String, String> params) {
        Pattern pattern = Pattern.compile("#\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = params.getOrDefault(key, "");
            matcher.appendReplacement(sb, value);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    // public static void main(String[] args) {
    //     String template = "您好，#{name}，#{content}";
    //     Map<String, String> params = new HashMap<>();
    //     params.put("name", "张三");
    //     params.put("content", "欢迎来到我们的系统");
    //     String result = replaceTemplateVars(template, params);
    //     System.out.println(result);
    // }
}
