package com.iflytek.cdc.admin.service.province;


import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseWarning;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseWarningRuleDTO;

import java.util.Map;

public interface DiseaseWarningRuleService extends IService<TbCdcmrDiseaseWarning> {

    /**
     * 更新疾病预警优先级
     * */
    void updateWarningRulePriority(DiseaseWarningRuleDTO dto);

    /**
     * 查看疾病预警优先级
     * */
    Map<String, String> getWarningRulePriority(DiseaseWarningRuleDTO dto);

}
