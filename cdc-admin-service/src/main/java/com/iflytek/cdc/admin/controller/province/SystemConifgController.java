package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapToken;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.service.StdTextPreProcessService;
import com.iflytek.sec.uap.model.UapUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;

import static com.alibaba.nacos.api.common.ResponseCode.OK;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "系统配置接口")
public class SystemConifgController {
    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private StdTextPreProcessService stdTextPreProcessService;

    @Value("${common.externalService.amap.proxy:false}")
    private boolean amapProxyEnable;

    @PostMapping("/{version}/pt/std/config/refreshConfig")
    @ApiOperation("刷新配置缓存")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public int queryStandardAddressList(@PathVariable String version){
        return stdTextPreProcessService.cacheConfig();
    }

    @GetMapping("/{version}/pb/config/amap/proxyEnable")
    @ApiOperation("高德地图是否配置代理")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public boolean proxyEnable(@PathVariable String version) {
        return amapProxyEnable;
    }

    @GetMapping("/{version}/pb/util/remoteStorage/getUapToken")
    @ApiOperation("获取uap token信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UapToken getUapToken(@RequestParam String token){
        return uapServiceApi.getToken(token);
    }

    @GetMapping("/{version}/pb/util/UapAccount/current")
    @ApiOperation("获取uap token信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public Object getUserByToken(@RequestParam String token){
        int code;
        Object data = null;
        try {
            data = uapServiceApi.getUserByToken(token);
            code = 200;
        }catch (Exception e){
            log.error(e.getLocalizedMessage(),e);
            code = 500;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", code);
        result.put("data", data);
        return result;
    }
}
