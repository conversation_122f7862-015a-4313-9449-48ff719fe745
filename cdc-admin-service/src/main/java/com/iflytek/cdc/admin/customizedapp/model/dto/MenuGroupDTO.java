package com.iflytek.cdc.admin.customizedapp.model.dto;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("数据分组")
public class MenuGroupDTO {
    @ApiModelProperty("分组id")
    private String groupId;
    @ApiModelProperty("分组名称")
    private String groupName;
    @ApiModelProperty("应用id")
    private String appId;
    @ApiModelProperty("菜单集合")
    private List<TbCdccsMenu> menuList;
}
