package com.iflytek.cdc.admin.capacity.api.algorithm.config;

import lombok.Data;

@Data
public class AlgorithmApiConfig {
    /**
     * 根路径
     */
    private String rootUrl;
    private String grantType;
    private Long refreshTime;
    private String cityCenterCode;
    private String clientId;
    private String clientSecret;

    private String authUrl;
    private String cdcPolicySimulationUrl;//态势推演
    private String queryCdcAwarenessUrl;//获取态势推演结果
    private String cdcEventClassifyUrl;//事件分级
    private String cdcDispositionAdviceUrl;//处置建议
    private String cdcDiseaseTrendPredictionUrl;//未来的疾病趋势

    private String arimaUrl;//时间序列分析模型路径

    private String stgnnUrl;//时空统计模型路径

    private String lstmUrl;//神经网络模型路径

    private String seirUrl;//传染病特定组合模型 （基于SEIR的机器学习版本）

    private String ensembleUrl;//混合模型

    private String featureSelectionUrl;//随机森林模型/神经网络模型


    private String spreadEpidemicUrl;//传染病传播预测模型路径

    private String riskAssessUrl;//风险评估模型路径

    private String knowledgeBaseSearchUrl;//事件知识库查询路径
}
