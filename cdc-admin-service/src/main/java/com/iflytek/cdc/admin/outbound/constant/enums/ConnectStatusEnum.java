package com.iflytek.cdc.admin.outbound.constant.enums;

/**
 * 接通状态枚举值说明
 * <AUTHOR>
 */

public enum ConnectStatusEnum {

    NORMAL_ANSWER("1", "正常回答"),
    AUTOMATIC_MESSAGE("2", "自动留言"),
    INCONVENIENT_TO_ANSWER("3", "接听不便"),
    RESIDENTS_HAVE_DIED("4", "居民已死亡"),
    FAMILY_MEMBERS_ANSWER("5", "家属不能代答"),
    UNWILLING_TO_COOPERATE("6", "不愿配合"),
    WRONG_NUMBER("7", "号码错误"),
    INTERRUPT("8", "中断");

    private String code;
    private String desc;

    ConnectStatusEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    public static String getDescByCode(String code) {
        for (ConnectStatusEnum connectStatusEnum : values()) {
            if (connectStatusEnum.getCode().equals(code)) {
                return connectStatusEnum.getDesc();
            }
        }
        return null;
    }
}
