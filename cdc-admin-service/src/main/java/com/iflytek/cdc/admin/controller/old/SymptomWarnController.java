package com.iflytek.cdc.admin.controller.old;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.SympWarnDto;
import com.iflytek.cdc.admin.dto.SympQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom;
import com.iflytek.cdc.admin.enums.SchoolSymptomMonitorObjectEnum;
import com.iflytek.cdc.admin.service.SchoolSymptomService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
public class SymptomWarnController {
    @Resource
    SchoolSymptomService schoolSymptomService;

    @ApiOperation("学校症状维护-症状列表查询")
    @PostMapping("/{version}/pt/schoolSymptom/pageList")
    public PageInfo<TbCdcmrSympSymptom> schoolSymptomPageList(@RequestBody SympQueryDTO sympQueryDTO) {
        return schoolSymptomService.getSchoolSymptomPageList(sympQueryDTO);
    }

    @ApiOperation("学校症状维护-新增症状")
    @PostMapping("/{version}/pt/schoolSymptom/add")
    public void addSchoolSymptom(@RequestBody TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        schoolSymptomService.addSchoolSymptom(tbCdcmrSympSymptom,loginUserId);
    }

    @ApiOperation("学校症状维护-删除症状")
    @PostMapping("/{version}/pt/schoolSymptom/delete")
    public void deleteSchoolSymptom(@RequestParam String id) {
        schoolSymptomService.deleteSchoolSymptom(id);
    }

    @ApiOperation("学校症状维护-编辑症状")
    @PostMapping("/{version}/pt/schoolSymptom/update")
    public void updateSchoolSymptom(@RequestBody TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        schoolSymptomService.updateSchoolSymptom(tbCdcmrSympSymptom,loginUserId);
    }


    @ApiOperation("学校症状维护-症状规则预警列表查询")
    @PostMapping("/{version}/pt/schoolSymptomWarn/pageList")
    public PageInfo<SympWarnDto> schoolSymptomWarnPageList(@RequestBody SympQueryDTO sympQueryDTO) {
        return schoolSymptomService.getSchoolSymptomWarnPageList(sympQueryDTO);
    }


    @ApiOperation("学校症状维护-编辑症状预警")
    @PostMapping("/{version}/pt/schoolSymptomWarn/update")
    public void updateSchoolSymptomWarn(@RequestBody SympWarnDto sympWarnDto, String loginUserId) {
        schoolSymptomService.updateSchoolSymptomWarn(sympWarnDto,loginUserId);
    }

    @ApiOperation("学校症状维护-修改症状预警状态")
    @PostMapping("/{version}/pt/schoolSymptomWarn/updateStatus")
    public void updateSchoolSymptomWarnStatus(@RequestBody SympWarnDto sympWarnDto, String loginUserId) {
        schoolSymptomService.updateSchoolSymptomWarnStatus(sympWarnDto,loginUserId);
    }

    @ApiOperation("学校症状维护-获取症状预警")
    @GetMapping("/{version}/pt/schoolSymptomWarn/getWarnById")
    public SympWarnDto getWarnById(@RequestParam String warnId) {
        return schoolSymptomService.getWarnById(warnId);
    }

    @ApiOperation("学校症状维护-获取监测对象列表")
    @GetMapping("/{version}/pt/schoolSymptomWarn/getMonitorObjectList")
    public List<Map<String,Object>> getMonitorObjectList() {
        return SchoolSymptomMonitorObjectEnum.getAllToList();
    }

    @ApiOperation("学校症状维护-症状规则预警列表查询")
    @GetMapping("/{version}/pt/schoolSymptomWarn/getAllList")
    public List<SympWarnDto> schoolSymptomWarnAllList(@RequestParam(required = false) String symptomCode ) {
        return schoolSymptomService.getSchoolSymptomWarnAllList(symptomCode);
    }

    @ApiOperation("学校症状维护-症状规则预警列表导出")
    @GetMapping("/{version}/pt/schoolSymptomWarnRule/export")
    @LogExportAnnotation
    public void exportSchoolSymptomWarnRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
         schoolSymptomService.exportSchoolSymptomWarnRule(response);
    }
}
