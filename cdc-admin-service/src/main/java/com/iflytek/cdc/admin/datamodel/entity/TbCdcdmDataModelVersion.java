package com.iflytek.cdc.admin.datamodel.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据模型版本表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-2
 */
@ApiModel(value = "数据模型版本表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcdmDataModelVersion implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "主键ID")
    private String modelVersionId ;

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型标签
     */
    @ApiModelProperty(name = "数据模型标签")
    private String modelLabel ;

    /**
     * 版本
     */
    @ApiModelProperty(name = "版本")
    private String version ;

    /**
     * 状态
     */
    @ApiModelProperty(name = "状态")
    private String status ;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    private String note ;

    /**
     * 是否启用 1-是，0-否
     */
    @ApiModelProperty(name = "是否启用 1-是，0-否")
    private Integer isEnable ;

    /**
     * 是否删除 1-已删除、0-未删除
     */
    @ApiModelProperty(name = "是否删除 1-已删除、0-未删除")
    private Integer isDeleted ;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String creator ;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    private Date updateTime ;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    private String updater ;

    /**
     * 发布时间
     */
    @ApiModelProperty(name = "发布时间")
    private Date publishTime ;

    /**
     * 发布人
     */
    @ApiModelProperty(name = "发布人")
    private String publisher ;

    /**
     * 作废时间
     */
    @ApiModelProperty(name = "作废时间")
    private Date invalidTime ;

    /**
     * 作废人
     */
    @ApiModelProperty(name = "作废人")
    private String invalidator ;

}
