package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.entity.InfectiousDiagnosis;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.cdc.admin.thread.RedisHandlerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @ClassName InfecDiaController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/2 22:32
 * @Version 1.0
 */

@RestController
@Api(tags = "传染病诊断维护服务")
@AllArgsConstructor
public class InfecDiaController {

    @Resource
    private SyncMdmDataService infecDiaService;

    @Resource
    private RedisHandlerService redisHandlerService;

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("传染病诊断维护服务-查询")
    @PostMapping("/{version}/pt/infectious/diagnosis/queryInfecDia")
    public PageData<InfectiousDiseases> queryInfecDia(@RequestBody SearchInfecInfoDTO sd, @RequestParam String loginUserId) {
        return infecDiaService.queryInfecPageInfo(sd);
    }

    @PostMapping("/{version}/pt/infectious/diagnosis/updateInfecDia")
    @ApiOperation("传染病诊断维护服务-修改")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateInfecDia(@RequestBody InfectiousDiagnosis infecDia, @RequestParam String loginUserId){
        infecDiaService.updateInfecInfo(loginUserId,infecDia);
    }


    @PostMapping("/{version}/pt/infectious/diagnosis/mdmDataSync")
    @ApiOperation("传染病诊断维护服务-同步")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void  mdmDataSync( @RequestParam String batchId,String loginUserId){
        infecDiaService.syncMdmData(batchId,loginUserId);
    }

    @GetMapping("/{version}/pt/infectious/getInfecDiaStatus")
    @ApiOperation("传染病诊断维护服务-启用前判断是否可以启用")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  boolean  getInfecDisStatus(@PathVariable String version,@RequestParam String id){
        return infecDiaService.judgeIsUse(id);
    }

    @GetMapping("/{version}/pt/infectious/getSyncStatus")
    @ApiOperation("传染病诊断维护服务-获取异步调用状态")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  String  getSyncStatus(@PathVariable String version,@RequestParam String batchId){
        return redisHandlerService.getStatus(batchId);
    }
}
