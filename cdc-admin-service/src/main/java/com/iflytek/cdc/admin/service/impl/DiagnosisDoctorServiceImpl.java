package com.iflytek.cdc.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.service.DiagnosisDoctorService;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DiagnosisDoctorServiceImpl implements DiagnosisDoctorService {

    @Resource
    RestTemplate restTemplate;

    private String requestUrl = "http://cdc-platform-api";

    @Override
    public byte[] downloadTemplate(String loginUserName,String loginUserId) {
        return restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/downloadTemplate?loginUserName=" + loginUserName+"&loginUserId="+loginUserId, null, byte[].class);
    }

    @Override
    public UploadResultVO uploadFile(MultipartFile file) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        //设置提交方式都是表单提交
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        //设置表单信息
        File f = File.createTempFile("tmp", file.getOriginalFilename());
        file.transferTo(f);
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("file", new FileSystemResource(f));
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);
        return restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/uploadFile", requestEntity, UploadResultVO.class);
    }

    @Override
    public UploadResultVO batchAdd(String attachmentId, String loginUserName) {
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("attachmentId", attachmentId);
        requestMap.put("loginUserName", loginUserName);
        return restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/batchAdd?attachmentId={attachmentId}&loginUserName={loginUserName}", null, UploadResultVO.class, requestMap);
    }

    @Override
    public void add(DiagnosisDoctorVO vo, String loginUserName) {
        restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/add?loginUserName=" + loginUserName, vo, JSONObject.class);

    }

    @Override
    public void update(DiagnosisDoctorVO vo, String loginUserName) {
        restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/update?loginUserName=" + loginUserName, vo, JSONObject.class);

    }

    @Override
    public void delete(String id, String loginUserId, String loginUserName) {
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("id", id);
        requestMap.put("loginUserId", loginUserId);
        requestMap.put("loginUserName", loginUserName);
        restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/delete?id={id}&loginUserId={loginUserId}&loginUserName={loginUserName}", null, JSONObject.class, requestMap);

    }

    @Override
    public void updateStatus(String id, Integer status, String loginUserId, String loginUserName) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("id", id);
        requestMap.put("loginUserId", loginUserId);
        requestMap.put("status", status);
        requestMap.put("loginUserName", loginUserName);
        restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/updateStatus?id={id}&loginUserId={loginUserId}&loginUserName={loginUserName}&status={status}", null, JSONObject.class, requestMap);

    }

    @Override
    public PageData list(String loginUserName, DiagnosisDoctorQueryVO queryVO) {
        return restTemplate.postForObject(requestUrl + "/pt/v1/diagnosisDoctor/list?loginUserName=" + loginUserName, queryVO, PageData.class);
    }

    @Override
    public List<TbCdcewOrganizationInfo> getSubOrgList(String loginUserName) {
        return restTemplate.getForObject(requestUrl + "/pt/v1/diagnosisDoctor/subOrgList", JSONArray.class).toJavaList(TbCdcewOrganizationInfo.class);
    }

    @Override
    public Object getOrgListForBusinessPerson(String loginUserName) {
        return restTemplate.postForObject(requestUrl + "/pt/v1/org/getOrgListForBusinessPerson?loginUserName="+loginUserName, null,JSONArray.class);
    }

    @Override
    public ResponseEntity<byte[]> downloadFile(String id, String loginUserId) {
        return restTemplate.getForEntity(String.format("%s/pt/v1/file/download?id=%s&loginUserId=%s", requestUrl, id, loginUserId),  byte[].class);
    }
}
