package com.iflytek.cdc.admin.datamodel.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据模型表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-2
 */
@ApiModel(value = "数据模型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcdmDataModel implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型名称
     */
    @ApiModelProperty(name = "数据模型名称")
    private String modelName ;

    /**
     * 数据模型分类
     */
    @ApiModelProperty(name = "数据模型分类")
    private String modelType ;

    /**
     * 是否是系统内置 1-是，0-否，默认0
     */
    @ApiModelProperty(name = "是否是系统内置 1-是，0-否，默认0")
    private Integer isBuiltIn ;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    private String note ;

    /**
     * 该模型的最新版本
     */
    @ApiModelProperty(name = "该模型的最新版本")
    private String latestVersion;

    @ApiModelProperty(value = "发布版本id")
    private String modelVersionId;

    @ApiModelProperty("er模型id")
    private String erModelId;

}
