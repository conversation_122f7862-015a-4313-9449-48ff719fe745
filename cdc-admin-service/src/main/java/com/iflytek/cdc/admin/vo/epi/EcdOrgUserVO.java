package com.iflytek.cdc.admin.vo.epi;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class EcdOrgUserVO {
    //uap用户id
    @ApiModelProperty(value = "uap用户id")
    private String uapUserId;

    //姓名
    @ApiModelProperty(value = "姓名")
    private String name;

    //性别
    @ApiModelProperty(value = "性别")
    private String sex;

    //性别名称
    @ApiModelProperty(value = "性别名称")
    private String sexName;

    //出生日期
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date birthday;

    //身份证号
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    //民族
    @ApiModelProperty(value = "民族")
    private String nation;

    //民族名称
    @ApiModelProperty(value = "民族名称")
    private String nationName;

    //政治面貌
    @ApiModelProperty(value = "政治面貌")
    private String politicsFace;

    //政治面貌名称
    @ApiModelProperty(value = "政治面貌名称")
    private String politicsFaceName;

    //电话
    @ApiModelProperty(value = "电话")
    private String phone;

    //邮箱
    @ApiModelProperty(value = "邮箱")
    private String email;

    //机构id
    @ApiModelProperty(value = "机构id")
    private String orgId;

    //职务
    @ApiModelProperty(value = "职务")
    private String job;

    //职务名称
    @ApiModelProperty(value = "职务名称")
    private String jobName;

    //岗位类别
    @ApiModelProperty(value = "岗位类别")
    private String jobCategory;

    //职务名称
    @ApiModelProperty(value = "岗位类别名称")
    private String jobCategoryName;

    //当前职称
    @ApiModelProperty(value = "当前职称")
    private String title;

    //当前职称名称
    @ApiModelProperty(value = "当前职称名称")
    private String titleName;

    //职称级别
    @ApiModelProperty(value = "职称级别")
    private String titleLevel;

    //职称级别名称
    @ApiModelProperty(value = "职称级别名称")
    private String titleLevelName;

    //毕业院校
    @ApiModelProperty(value = "毕业院校")
    private String graduateSchool;

    //所学专业
    @ApiModelProperty(value = "所学专业")
    private String major;

    //所学专业名称
    @ApiModelProperty(value = "所学专业名称")
    private String majorName;

    //从事专业
    @ApiModelProperty(value = "从事专业")
    private String jobMajor;

    //从事专业名称
    @ApiModelProperty(value = "从事专业名称")
    private String jobMajorName;

    //外语情况
    @ApiModelProperty(value = "外语情况")
    private String foreignLanguage;

    //外语情况名称
    @ApiModelProperty(value = "外语情况名称")
    private String foreignLanguageName;

    //开始工作时间
    @ApiModelProperty(value = "开始工作时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startWorkTime;

    //工作年限
    @ApiModelProperty(value = "工作年限")
    private String workYear;

    //有无护照，0-无，1-有
    @ApiModelProperty(value = "有无护照，0-无，1-有")
    private String passportFlag;

    //有无保险，0-无，1-有
    @ApiModelProperty(value = "有无保险，0-无，1-有")
    private String insureFlag;

    //是否应急领导，0-否，1-是
    @ApiModelProperty(value = "是否应急领导，0-否，1-是")
    private String leaderFlag;

    //是否应急专家，0-否，1-是
    @ApiModelProperty(value = "是否应急专家，0-否，1-是")
    private String expertFlag;

    //专家类别
    @ApiModelProperty(value = "专家类别")
    private String expertCategory;

    //专家类别名称
    @ApiModelProperty(value = "专家类别名称")
    private String expertCategoryName;

    //用户状态，0-不可派遣，1-可派遣
    @ApiModelProperty(value = "用户状态，0-不可派遣，1-可派遣")
    private String userStatus;

    //状态，1-启用，2-停用
    @ApiModelProperty(value = "状态，1-启用，2-停用")
    private String status;

    //人员描述
    @ApiModelProperty(value = "人员描述")
    private String description;

    //是否机构负责人，0-否，1-是
    @ApiModelProperty(value = "是否机构负责人，0-否，1-是")
    private String orgLeader;

    //所属机构名称
    @TableField(exist = false)
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    //地址
    @TableField(exist = false)
    @ApiModelProperty(value = "地址（即使用机构的地址）")
    private String address;
}

