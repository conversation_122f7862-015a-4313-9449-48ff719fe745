package com.iflytek.cdc.admin.customizedapp.entity;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 应用分类;
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "应用分类",description = "")
@TableName("tb_cdccs_app_category")
@Data
public class TbCdccsAppCategory extends BaseEntity implements Serializable{

    /** 分类名称 */
    @ApiModelProperty(value = "分类名称")
    private String categoryName ;
    /** 分类编码 */
    @ApiModelProperty(value = "分类编码",notes = "")
    private String categoryCode ;
    /** 顺序 */
    @ApiModelProperty(value = "顺序",notes = "")
    private Integer orderSeq ;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}