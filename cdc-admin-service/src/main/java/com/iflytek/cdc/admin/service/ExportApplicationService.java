package com.iflytek.cdc.admin.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ExportApplicationDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationQueryDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationRecordDTO;


public interface ExportApplicationService {

    //提交导出申请
    ExportApplicationDTO submit(ExportApplicationDTO exportApplicationDTO, String loginUserId);

    //查询任务列表
    PageInfo<ExportApplicationListDTO> queryApplicationExportList(ExportApplicationQueryDTO queryDTO);

    // 查询导出记录详情
    ExportApplicationRecordDTO getExportApplicationRecord(String id);

    ExportApplicationRecordDTO getExportRecord(String id, String approvalId);
}
