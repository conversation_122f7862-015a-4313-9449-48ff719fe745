package com.iflytek.cdc.admin.service.impl;

import com.baomidou.mybatisplus.extension.api.R;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnDto;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnDto;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.enums.PoisoningMonitorObjectEnum;
import com.iflytek.cdc.admin.enums.UnknownReasonMonitorObjectEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonDiseaseMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnRuleMapper;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.UnknownReasonWarnService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnknownReasonWarnServiceImpl implements UnknownReasonWarnService {

    @Resource
    TbCdcmrUnknownReasonWarnMapper tbCdcmrUnknownReasonWarnMapper;

    @Resource
    TbCdcmrUnknownReasonWarnRuleMapper tbCdcmrUnknownReasonWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;

    @Resource
    TbCdcmrUnknownReasonDiseaseMapper tbCdcmrUnknownReasonDiseaseMapper;

    @Resource
    DataAuthService dataAuthService;

    @Resource
    private ParamConfigService paramConfigService;
    @Override
    public PageInfo<UnknownReasonWarnDto> getUnknownReasonWarnPageList(UnknownReasonQueryDto dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(tbCdcmrUnknownReasonWarnMapper.getList(dto));
    }

    @Override
    public void updateUnknownReasonWarn(UnknownReasonWarnDto unknownReasonWarnDto, String loginUserId) {
        TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn = new TbCdcmrUnknownReasonWarn();
        tbCdcmrUnknownReasonWarn.setId(unknownReasonWarnDto.getId());
        tbCdcmrUnknownReasonWarn.setStatus(unknownReasonWarnDto.getStatus());
        tbCdcmrUnknownReasonWarn.setDiseaseCode(unknownReasonWarnDto.getDiseaseCode());
        tbCdcmrUnknownReasonWarn.setDiseaseName(unknownReasonWarnDto.getDiseaseName());
        tbCdcmrUnknownReasonWarn.setRemark(unknownReasonWarnDto.getRemark());
        tbCdcmrUnknownReasonWarn.setUpdater(loginUserId);
        tbCdcmrUnknownReasonWarn.setIsDeleted(0);
        tbCdcmrUnknownReasonWarn.setUpdateTime(new Date());
        tbCdcmrUnknownReasonWarn.setIncubation(unknownReasonWarnDto.getIncubation());
        tbCdcmrUnknownReasonWarn.setSmsSendTypeCode(unknownReasonWarnDto.getSmsSendTypeCode());
        tbCdcmrUnknownReasonWarn.setSmsSendTypeDesc(unknownReasonWarnDto.getSmsSendTypeDesc());
        tbCdcmrUnknownReasonWarnMapper.updateByPrimaryKeySelective(tbCdcmrUnknownReasonWarn);
        updateRules(tbCdcmrUnknownReasonWarn.getId(), unknownReasonWarnDto.getRuleList(), loginUserId);
    }

    private void updateRules(String warnId, List<TbCdcmrUnknownReasonWarnRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_unknown_reason_warn_rule")));
            }
            rule.setWarnId(warnId);
            rule.setStatus(1);
            rule.setIsDeleted(0);
            rule.setUpdater(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrUnknownReasonWarnRule::getId).collect(Collectors.toList());
        tbCdcmrUnknownReasonWarnRuleMapper.deleteOtherByIds(idList, warnId);
        tbCdcmrUnknownReasonWarnRuleMapper.upsertRules(ruleList);
    }

    @Override
    public void updateUnknownReasonWarnStatus(UnknownReasonWarnDto unknownReasonWarnDto, String loginUserId) {
        TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn = new TbCdcmrUnknownReasonWarn();
        tbCdcmrUnknownReasonWarn.setStatus(unknownReasonWarnDto.getStatus());
        tbCdcmrUnknownReasonWarn.setId(unknownReasonWarnDto.getId());
        tbCdcmrUnknownReasonWarnMapper.updateStatusByPrimaryKey(tbCdcmrUnknownReasonWarn);
    }

    @Override
    public UnknownReasonWarnDto getWarnById(String warnId) {
        UnknownReasonWarnDto dto = new UnknownReasonWarnDto();
        TbCdcmrUnknownReasonWarn warn = tbCdcmrUnknownReasonWarnMapper.selectByPrimaryKey(warnId);
        dto.setId(warn.getId());
        dto.setStatus(warn.getStatus());
        dto.setIsDeleted(warn.getIsDeleted());
        dto.setRemark(warn.getRemark());
        dto.setDiseaseCode(warn.getDiseaseCode());
        dto.setDiseaseName(warn.getDiseaseName());
        dto.setIncubation(warn.getIncubation());
        dto.setSmsSendTypeCode(warn.getSmsSendTypeCode());
        dto.setSmsSendTypeDesc(warn.getSmsSendTypeDesc());
        dto.setRuleList(tbCdcmrUnknownReasonWarnRuleMapper.getRuleListByWarnId(warnId));
        return dto;
    }

    @Override
    public List<UnknownReasonWarnDto> getUnknownReasonWarnAllList(String diseaseCode) {
        List<UnknownReasonWarnDto> warnList = tbCdcmrUnknownReasonWarnMapper.getAllList(diseaseCode);
        List<TbCdcmrUnknownReasonWarnRule> ruleList = tbCdcmrUnknownReasonWarnRuleMapper.getAllRuleList();
        warnList.forEach(UnknownReasonWarnDto -> UnknownReasonWarnDto.setRuleList(ruleList.stream().filter(tbCdcmrUnknownReasonWarnRule -> tbCdcmrUnknownReasonWarnRule.getWarnId().equals(UnknownReasonWarnDto.getId())).collect(Collectors.toList())));
        return warnList;
    }

    @Override
    public void exportUnknownReasonWarnRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.UNKNOWN_REASON_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<UnknownReasonWarnRuleExportDataDto> dataList = tbCdcmrUnknownReasonWarnRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 修改状态名称
        dataList.forEach(this::getMonitorObject);
        dataList.forEach(this::getIncubation);
        dataList.forEach(this::getTimeScope);
        dataList.forEach(this::getMedicalCount);

        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.UNKNOWN_REASON_WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<CascadeVO> getUnknownReasonNameList() {
        return getUnknownReason();
    }

    @Override
    public List<CascadeVO> getUnknownReasonNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_UNKNOWN_REASON);

//        List<CascadeVO> poisonTypeList = getUnknownReason();
//
//        Collections.sort(poisonTypeList);
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getUnknownReasonDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        List<CascadeVO> resultList =poisonTypeList.stream().filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
//        return resultList;
    }

    @Override
    public List<CascadeVO> getUnknownReason() {
        List<TbCdcmrUnknownReasonDisease> result = tbCdcmrUnknownReasonDiseaseMapper.findAll();
        List<CascadeVO> typeList = new ArrayList<>();
        result.forEach(e->{
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setLabel(e.getDiseaseName());
            cascadeVO.setValue(e.getDiseaseCode());
            typeList.add(cascadeVO);
        });
        return typeList;
    }

    private void getMedicalCount(UnknownReasonWarnRuleExportDataDto dto) {
        if (dto.getMedicalCount() != null) {
            dto.setMedicalCount("≥" + dto.getMedicalCount());
        } else {
            dto.setMedicalCount("/");
        }
    }

    private void getTimeScope(UnknownReasonWarnRuleExportDataDto dto) {
        if (!StringUtils.isEmpty(dto.getTimeRange())) {
            if ("1".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "天");
            } else if ("2".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "周");
            } else if ("3".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "月");
            }
        } else {
            dto.setTimeRange("/");
        }
    }

    private void getIncubation(UnknownReasonWarnRuleExportDataDto dto) {
        if (dto.getIncubation() != null) {
            dto.setIncubation(dto.getIncubation() + "天");
        } else {
            dto.setIncubation("/");
        }
    }

    public void getMonitorObject(UnknownReasonWarnRuleExportDataDto dto) {
        if (UnknownReasonMonitorObjectEnum.HOSPITAL.getCode().equals(dto.getMonitorObject())) {
            dto.setMonitorObject(PoisoningMonitorObjectEnum.HOSPITAL.getName());
        } else if (UnknownReasonMonitorObjectEnum.PRIMARY.getCode().equals(dto.getMonitorObject())) {
            dto.setMonitorObject(PoisoningMonitorObjectEnum.PRIMARY.getName());
        } else {
            dto.setMonitorObject("/");
        }
    }
}
