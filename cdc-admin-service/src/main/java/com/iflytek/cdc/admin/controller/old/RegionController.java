package com.iflytek.cdc.admin.controller.old;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.GroupRegionDTO;
import com.iflytek.cdc.admin.dto.GroupRegionInsertDTO;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.UploadResultVO;
import com.iflytek.cdc.admin.dto.addressstandardize.RegionQueryDTO;
import com.iflytek.cdc.admin.service.RegionService;
import com.iflytek.cdc.admin.vo.region.GroupRegionVO;
import com.iflytek.cdc.admin.vo.LabelValueVO;
import com.iflytek.cdc.admin.vo.region.RegionCascadeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@RestController
@Api(tags = "行政区划维护")
public class RegionController {

    @Resource
    RegionService regionService;

    @PostMapping("/{version}/pt/region/getDistrictCascadeByUser")
    @ApiOperation("用户权限-区划查询-省/市/区县级别")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public RegionCascadeVO getDistrictCascadeByUser(@PathVariable String version, @RequestParam("loginUserId") String loginUserId) {

        return regionService.getUserRegionLabel(loginUserId, AreaLevelEnum.PROVINCE.getLevel(), AreaLevelEnum.DISTRICT.getLevel());
    }

    @PostMapping("/{version}/pt/region/getRegionCascadeByUser")
    @ApiOperation("用户权限-区划查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
            @ApiImplicitParam(name = "fromLevel", value = "区划开始层级, 不传默认从省级(1)开始", allowableValues = "1,2,3,4,5,6", defaultValue = "1"),
            @ApiImplicitParam(name = "toLevel", value = "区划结束层级", allowableValues = "1,2,3,4,5,6", required = true)
    })
    public RegionCascadeVO getRegionCascadeByUserLevel(@PathVariable String version,
                                                       @RequestParam("loginUserId") String loginUserId,
                                                       Integer fromLevel,
                                                       Integer toLevel) {
        AreaLevelEnum fromLevelEnum = AreaLevelEnum.level(fromLevel);
        if (AreaLevelEnum.NONE.equals(fromLevelEnum)) {
            return new RegionCascadeVO();
        }
        AreaLevelEnum toLevelEnum = AreaLevelEnum.level(toLevel);
        if (AreaLevelEnum.NONE.equals(toLevelEnum)) {
            return new RegionCascadeVO();
        }

        return regionService.getUserRegionLabel(loginUserId, fromLevel, toLevel);
    }

    @PostMapping("/{version}/pt/region/getChildRegionsByUser")
    @ApiOperation("用户权限-区划查询-根据区划代码查询下级区划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
            @ApiImplicitParam(name = "regionCode", value = "父级区划代码, 为空时查省级")
    })
    public List<LabelValueVO> getChildRegionsByUser(@PathVariable String version,
                                                    @RequestParam("loginUserId") String loginUserId,
                                                    String regionCode) {

        return regionService.getChildLabelByUserCode(loginUserId, regionCode);
    }


    @PostMapping("/{version}/pt/region/group/insert")
    @ApiOperation("区划增删改-小区/组-新增")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void insertGroupRegion(@PathVariable String version,
                                  @RequestParam("loginUserId") String loginUserId,
                                  @RequestBody GroupRegionInsertDTO groupRegionInsertDTO) {
        regionService.insertGroupRegion(loginUserId, groupRegionInsertDTO);
    }

    @PostMapping("/{version}/pt/region/group/update")
    @ApiOperation("区划增删改-小区/组-更新")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateGroupRegion(@PathVariable String version,
                                  @RequestParam("loginUserId") String loginUserId,
                                  @RequestBody GroupRegionDTO groupRegionDTO) {
        regionService.updateGroupRegion(loginUserId, groupRegionDTO);
    }

    @PostMapping("/{version}/pt/region/group/update/enable")
    @ApiOperation("区划增删改-小区/组-更新是否启用状态")
    @ApiImplicitParams({@ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
                        @ApiImplicitParam(name = "id", value = "更新数据的主键ID"),
                        @ApiImplicitParam(name = "isEnable", value = "启用/禁用, 0-禁用;1-启用", allowableValues = "0,1", defaultValue = "1")})
    public void enableGroupRegion(@PathVariable String version,
                                  @RequestParam("loginUserId") String loginUserId,
                                  @RequestParam String id,
                                  @RequestParam String isEnable) {
        regionService.enableGroupRegion(loginUserId, id, isEnable);
    }

    @PostMapping("/{version}/pt/region/group/delete")
    @ApiOperation("区划增删改-小区/组-删除")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteGroupRegion(@PathVariable String version, @RequestParam("loginUserId") String loginUserId, @RequestParam String id) {
        regionService.deleteGroupRegion(loginUserId, id);
    }

    @PostMapping("/{version}/pt/region/group/search")
    @ApiOperation("小区/组-查询")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageData<GroupRegionVO> regionGroupSearch(@PathVariable String version,
                                                     @RequestParam("loginUserId") String loginUserId,
                                                     @RequestBody RegionQueryDTO regionQueryDTO) {
        // 小区/组 数据查询
        regionQueryDTO.setRegionLevel(AreaLevelEnum.GROUP.getLevel());
        return regionService.regionSearch(loginUserId, regionQueryDTO);
    }

    @PostMapping("/{version}/pt/region/group/import")
    @ApiOperation("小区/组-上传")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UploadResultVO importGroupRegion(@PathVariable String version,
                                            @RequestParam("loginUserId") String loginUserId,
                                            @RequestParam MultipartFile file) throws IOException {

        return regionService.importGroupRegion(loginUserId, file);
    }

    @PostMapping("/{version}/pt/region/group/export")
    @ApiOperation("小区/组-导出")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ResponseEntity<byte[]> exportGroupRegion(@PathVariable String version,
                                                    @RequestParam("loginUserId") String loginUserId,
                                                    @RequestBody RegionQueryDTO regionQueryDTO) {
        regionQueryDTO.setRegionLevel(AreaLevelEnum.GROUP.getLevel());

        return regionService.exportGroupRegionBy(loginUserId, regionQueryDTO);
    }

    /*** 数据处理: 1. UAP 同步数据; 2. 高德解析 ***/

    @ApiOperation("区划-同步UAP区划-增量")
    @PostMapping("/{version}/pt/region/syncUapNationByTime")
    @ApiImplicitParams({@ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
                        @ApiImplicitParam(name = "dateStr", value = "UAP数据更新时间")})
    public void syncUapNationByTime(@PathVariable String version, String dateStr) {
        DateTime dateTime = DateUtil.parseDateTime(dateStr);
        regionService.syncUapNationByTime(dateTime);
    }

    @ApiOperation("区划-同步UAP区划")
    @PostMapping("/{version}/pt/region/syncUapNation")
    @ApiImplicitParams({@ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
                        @ApiImplicitParam(name = "regionCode", value = "区划代码, 空表示同步全国下面的区划", defaultValue = "340000"),
                        @ApiImplicitParam(name = "targetLevel", allowableValues = "0,1,2,3,4,5,6", defaultValue = "5")})
    public void syncUapNation(@PathVariable String version, String regionCode, Integer targetLevel) {
        targetLevel = targetLevel == null ? AreaLevelEnum.VILLAGE.getLevel() : targetLevel;
        regionService.syncUapNation(regionCode, targetLevel);
    }

    @ApiOperation("区划-区划数据高德API解析")
    @PostMapping("/{version}/pt/region/amapAnalysis")
    @ApiImplicitParams({@ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true),
                        @ApiImplicitParam(name = "areaCode", value = "区划代码, 空表示同步全国下面的区划", defaultValue = "340000"),
                        @ApiImplicitParam(name = "targetLevel", allowableValues = "0,1,2,3,4,5,6", defaultValue = "6"),
                        @ApiImplicitParam(name = "startTimeStr", defaultValue = Constants.SYS_START_DATE_STR),
                        @ApiImplicitParam(name = "endTimeStr", defaultValue = Constants.SYS_END_DATE_STR)})
    public void amapRegion(@PathVariable String version, String areaCode, Integer targetLevel, String startTimeStr, String endTimeStr) {
        Date startTime = StrUtil.isBlank(startTimeStr) ? DateUtil.parseDateTime(startTimeStr) : Constants.SYS_START_DATE;
        Date endTime = StrUtil.isBlank(endTimeStr) ? DateUtil.parseDateTime(endTimeStr) : Constants.SYS_END_DATE;

        targetLevel = targetLevel == null ? AreaLevelEnum.VILLAGE.getLevel() : targetLevel;
        regionService.amapAnalysis(areaCode, targetLevel, startTime, endTime);
    }
}
