package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.IndicatorQueryDto;
import com.iflytek.cdc.admin.enums.*;
import com.iflytek.cdc.admin.service.TbCdcmrIndicatorService;
import com.iflytek.cdc.admin.vo.CdcmrIndicatorVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据指标管理-接口类
 */
@RestController
@Api(tags = "数据指标管理-接口类")
public class IndicatorController {

    @Resource
    private TbCdcmrIndicatorService tbCdcmrIndicatorService;

    @PostMapping("{version}/pt/indicator/create")
    @ApiOperation("新增指标")
    public CdcmrIndicatorVO create(@RequestBody CdcmrIndicatorVO input, @RequestParam String loginUserId){
        return tbCdcmrIndicatorService.create(input, loginUserId);
    }

    @PostMapping("{version}/pt/indicator/update")
    @ApiOperation("修改指标")
    public void update(@RequestBody CdcmrIndicatorVO input, @RequestParam String loginUserId){
        tbCdcmrIndicatorService.update(input, loginUserId);
    }

    @PostMapping("{version}/pt/indicator/load")
    @ApiOperation("根据id查询")
    public CdcmrIndicatorVO load(@RequestParam String id){
        return tbCdcmrIndicatorService.load(id);
    }

    @PostMapping("{version}/pt/indicator/loadByLabel")
    @ApiOperation("根据标签查询")
    public CdcmrIndicatorVO loadByLabel(@RequestParam String label){
        return tbCdcmrIndicatorService.loadByLabel(label);
    }

    @PostMapping("{version}/pt/indicator/listByLabels")
    @ApiOperation("根据标签批量查询")
    public List<CdcmrIndicatorVO> listByLabels(@RequestBody List<String> labels){
        return tbCdcmrIndicatorService.listByLabels(labels);
    }

    @PostMapping("{version}/pt/indicator/delete")
    @ApiOperation("根据id删除")
    public void delete(@RequestParam String id){
        tbCdcmrIndicatorService.delete(id);
    }

    @PostMapping("{version}/pt/indicator/pageList")
    @ApiOperation("分页查询")
    public PageInfo<CdcmrIndicatorVO> pageList(@RequestBody IndicatorQueryDto input){
        return tbCdcmrIndicatorService.pageList(input);
    }

    @PostMapping("{version}/pt/indicator/getConstant")
    @ApiOperation("常量获取")
    public Map<String, Object> getConstants(){

        Map<String, Object> constants = new HashMap<>();
        constants.put("indicatorBusinessProcess", IndicatorBusinessProcessEnum.values());
        constants.put("indicatorType", IndicatorTypeEnum.values());
        constants.put("analysisTarget", AnalysisTargetEnum.values());
        constants.put("applicableDisease", ApplicableDiseaseEnum.values());
        constants.put("businessProcess", BusinessProcessEnum.values());

        return constants;
    }
}
