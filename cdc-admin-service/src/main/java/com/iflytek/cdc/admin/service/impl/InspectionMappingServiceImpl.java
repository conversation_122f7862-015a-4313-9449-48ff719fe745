package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.InspectionMappingPageVO;
import com.iflytek.cdc.admin.dto.InspectionMappingVO;
import com.iflytek.cdc.admin.mapper.TbCdcmrInspectionMappingMapper;
import com.iflytek.cdc.admin.service.InspectionMappingService;
import com.iflytek.cdc.admin.service.InspectionRuleService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class InspectionMappingServiceImpl implements InspectionMappingService {
    @Resource
    TbCdcmrInspectionMappingMapper tbCdcmrInspectionMappingMapper;
    @Resource
    BatchUidService batchUidService;
    @Resource
    InspectionRuleService inspectionRuleService;


    @Override
    @Transactional
    public void batchInsert(List<InspectionMappingVO> recordList, String loginUserId, String infectedCode) {
        inspectionRuleService.deleteByInfectedCode(infectedCode);
        tbCdcmrInspectionMappingMapper.deleteByInfectedCode(infectedCode);
        recordList.forEach(e -> {
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_inspection_rule")));
            e.setInfectedCode(infectedCode);
            e.setUpdateUser(loginUserId);
            e.setUpdateTime(new Date());
            if (!CollectionUtil.isEmpty(e.getRuleList())) {
                inspectionRuleService.batchInsert(e.getRuleList(), loginUserId, e.getId());
            }
        });
        if (!CollectionUtil.isEmpty(recordList)) {
            tbCdcmrInspectionMappingMapper.batchInsert(recordList);
        }
    }

    @Override
    public List<InspectionMappingVO> getList(String infectedCode) {
        List<InspectionMappingVO> voList = tbCdcmrInspectionMappingMapper.getList(infectedCode);
        voList.forEach(e -> {
            e.setRuleList(inspectionRuleService.getPageList(e.getId()));
        });
        return voList;
    }

    @Override
    public PageInfo<InspectionMappingPageVO> getPageList(Integer pageIndex, Integer pageSize, String diseaseCode, String diseaseType) {
        PageHelper.startPage(pageIndex, pageSize);
        // 查询父级节点
        PageInfo<InspectionMappingPageVO> pageInfo = new PageInfo<>(tbCdcmrInspectionMappingMapper.pageList(diseaseCode, diseaseType));
        //插叙子节点
        List<InspectionMappingPageVO> subList = tbCdcmrInspectionMappingMapper.subPageList(diseaseCode, diseaseType);
        pageInfo.getList().forEach(e -> {
            List<InspectionMappingPageVO> newList = new ArrayList<>();
            subList.forEach(s -> {
                if (e.getDiseaseCode().equals(s.getDiseaseParentCode())) {
                    newList.add(s);
                }
            });
            e.setSubList(newList);
        });
        return pageInfo;
    }
}
