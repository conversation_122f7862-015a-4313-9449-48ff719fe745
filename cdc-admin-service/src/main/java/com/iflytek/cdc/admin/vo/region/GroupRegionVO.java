package com.iflytek.cdc.admin.vo.region;

import com.iflytek.cdc.admin.annotation.ExcelColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 小区/组 - 区划信息维表
 */
@Data
public class GroupRegionVO {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 父级区划代码
     */
    @ApiModelProperty(value = "父级区划代码")
    private String parentRegionCode;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String districtName;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(value = "街道名称")
    private String streetName;

    /**
     * 社区/村编码
     */
    @ApiModelProperty(value = "社区/村编码")
    private String villageCode;

    /**
     * 社区/村名称
     */
    @ApiModelProperty(value = "社区/村名称")
    private String villageName;

    /**
     * 小区/组编码
     */
    @ApiModelProperty(value = "小区/组编码")
    private String groupCode;

    /**
     * 小区/组名称
     */
    @ApiModelProperty(value = "小区/组名称")
    private String groupName;

    @ApiModelProperty(value = "街道/乡镇别名")
    private String streetAliasName;

    @ApiModelProperty(value = "小区/组别名")
    private String groupAliasName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 审核启用
     */
    @ApiModelProperty(value = "审核启用: 1-已审核启用; 2-未审核不启用")
    private String isEnable;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateDatetime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater;

}