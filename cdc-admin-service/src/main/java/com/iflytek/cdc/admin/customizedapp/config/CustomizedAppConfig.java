package com.iflytek.cdc.admin.customizedapp.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties("customized-app-config")
@Data
public class CustomizedAppConfig {
    private String appRoot;

}
