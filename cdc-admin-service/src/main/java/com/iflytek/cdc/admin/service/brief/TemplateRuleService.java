package com.iflytek.cdc.admin.service.brief;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.entity.brief.PushUserEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateRuleEntity;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.TemplatePushRuleVo;
import com.iflytek.cdc.admin.vo.brief.TemplateRuleVo;
import org.springframework.http.ResponseEntity;

/**
 * 模板规则服务接口
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
public interface TemplateRuleService extends IService<TemplateRuleEntity> {

    ApiResult<PageInfo<TemplateRuleVo>> queryPage(TemplateSearchDto searchDto);

    ApiResult<?> changeStatus(String loginUserId, String updatorName, String id, String status);

    void generateRule(TemplateRuleEntity template, String loginUserId);

    TemplateRuleEntity getByTemplateIdOrId(String templateId, String id);

    ApiResult<PageInfo<TemplatePushRuleVo>> pushRuleList(TemplateSearchDto searchDto);

    void saveAuthUser(List<PushUserEntity> userList, String loginUserId, String updatorName);

    List<PushUserEntity> pushUserList(String ruleId,String orgId);

    /**
     * 级联删除模板生成规则
     * 
     * @param templateId 模板ID
     */
    void deleteCascadeByTemplateId(String templateId);

    ResponseEntity<byte[]> exportRuleList(TemplateSearchDto searchDto);
}
