package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.EventChargePersonDTO;
import com.iflytek.cdc.admin.dto.EventChargePersonQuery;
import com.iflytek.cdc.admin.vo.EventChargePersonVO;

import java.util.List;
public interface EventChargePersonService {

    void changeStatus(EventChargePersonDTO input);

    void batchSave(List<EventChargePersonDTO> inputs);

    PageInfo<EventChargePersonVO> listDiseaseConfig(EventChargePersonQuery input);

    List<EventChargePersonVO> getDealPersonInfoBy(List<EventChargePersonQuery> dtoList);
}
