package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedEventTypeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn;
import com.iflytek.cdc.admin.enums.*;
import com.iflytek.cdc.admin.service.CustomizedWarnService;
import com.iflytek.cdc.admin.service.CustomizedWarningRuleService;
import com.iflytek.cdc.admin.service.CustomizedWarningTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@RestController
@Api(tags = "自定义预警接口")
public class CustomizedWarnController {
    @Resource
    CustomizedWarnService customizedWarnService;
    @Resource
    CustomizedWarningRuleService customizedWarningRuleService;
    @Resource
    CustomizedWarningTaskService customizedWarningTaskService;

    @ApiOperation("自定义预警标准维护-自定义预警列表查询")
    @PostMapping("/{version}/pt/customizedWarn/pageList")
    public PageInfo<TbCdcmrCustomizedWarn> getCustomizedWarnPageList(@RequestBody CustomizedWarnQueryDTO customizedWarnQueryDTO, @RequestParam String loginUserId) {
        return customizedWarnService.getCustomizedWarnPageList(customizedWarnQueryDTO, loginUserId);
    }

    @ApiOperation("自定义预警标准维护-自定义预警查询")
    @GetMapping("/{version}/pt/customizedWarn/getById")
    public CustomizedWarnVO getCustomizedWarnById(@RequestParam String id) {
        return customizedWarnService.getCustomizedWarnById(id);
    }

    @ApiOperation("自定义预警标准维护-名称校验")
    @GetMapping("/{version}/pt/customizedWarn/checkByName")
    public Boolean checkCustomizedWarnByName(@RequestParam String name) {
        return customizedWarnService.checkCustomizedWarnByName(name);
    }

    @ApiOperation("自定义预警标准维护-新增自定义预警")
    @PostMapping("/{version}/pt/customizedWarn/add")
    public void addCustomizedWarn(@RequestBody CustomizedWarnVO customizedWarnVO, @RequestParam String loginUserId) {
        customizedWarnService.addCustomizedWarn(customizedWarnVO, loginUserId);
    }

    @ApiOperation("自定义预警标准维护-更新自定义预警状态")
    @PostMapping("/{version}/pt/customizedWarn/updateStatus")
    public void updateCustomizedWarnStatus(@RequestParam Integer status, @RequestParam String id, @RequestParam String loginUserId) {
        customizedWarnService.updateCustomizedWarnStatus(status, id, loginUserId);
    }

    @ApiOperation("自定义预警标准维护-删除自定义预警")
    @PostMapping("/{version}/pt/customizedWarn/delete")
    public void deleteCustomizedWarn(@RequestParam String id) {
        customizedWarnService.deleteCustomizedWarn(id);
    }

    @ApiOperation("自定义预警标准维护-编辑自定义预警")
    @PostMapping("/{version}/pt/customizedWarn/update")
    public void updateCustomizedWarn(@RequestBody CustomizedWarnVO customizedWarnVO, @RequestParam String loginUserId) {
        customizedWarnService.updateCustomizedWarn(customizedWarnVO, loginUserId);
    }

    @ApiOperation("自定义预警规则维护-查询自定义预警规则列表")
    @PostMapping("/{version}/pt/customizedWarningRule/pageList")
    public PageInfo<CustomizedWarningRuleVO> getCustomizedWarningRulePageList(@RequestBody CustomizedWarnQueryDTO customizedWarnQueryDTO) {
        return customizedWarningRuleService.getCustomizedWarningRuleList(customizedWarnQueryDTO);
    }

    @ApiOperation("自定义预警标准维护-自定义预警查询")
    @GetMapping("/{version}/pt/customizedWarningRule/getById")
    public CustomizedWarningRuleVO getCustomizedWarningRuleById(@RequestParam String id) {
        return customizedWarningRuleService.getCustomizedWarningRuleById(id);
    }

    @ApiOperation("自定义预警规则维护-编辑自定义预警规则")
    @PostMapping("/{version}/pt/customizedWarningRule/update")
    public void updateCustomizedWarningRule(@RequestBody CustomizedWarningRuleVO customizedWarningRuleVO, @RequestParam String loginUserId) {
        customizedWarningRuleService.updateCustomizedWarningRule(customizedWarningRuleVO, loginUserId);
    }

    @ApiOperation("自定义预警规则维护-更新自定义预警规则状态")
    @PostMapping("/{version}/pt/customizedWarningRule/updateStatus")
    public void updateCustomizedWarningRuleStatus(@RequestParam Integer status, @RequestParam String id, @RequestParam String loginUserId) {
        customizedWarningRuleService.updateCustomizedWarningRuleStatus(status, id, loginUserId);
    }

    @ApiOperation("自定义预警规则维护-自定义预警规则列表导出")
    @GetMapping("/{version}/pt/customizedWarningRule/export")
    @LogExportAnnotation
    public void exportCustomizedWarningRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        customizedWarningRuleService.exportCustomizedWarningRule(response);
    }


    @ApiOperation("自定义预警监测维护-自定义预警监测列表查询")
    @PostMapping("/{version}/pt/customizedWarningTask/pageList")
    public PageInfo<CustomizedWarningTaskVO> getCustomizedWarningTaskPageList(@RequestBody CustomizedWarningTaskQueryDTO customizedWarningTaskQueryDTO) {
        return customizedWarningTaskService.getCustomizedWarningTaskList(customizedWarningTaskQueryDTO);
    }

    @ApiOperation("自定义预警监测维护-自定义预警监测查询")
    @GetMapping("/{version}/pt/customizedWarningTask/getById")
    public CustomizedWarningTaskVO getCustomizedWarningTaskById(@RequestParam String id) {
        return customizedWarningTaskService.getCustomizedWarningTaskById(id);
    }


    @ApiOperation("自定义预警标准维护-名称校验")
    @GetMapping("/{version}/pt/customizedWarningTask/checkByName")
    public Boolean checkCustomizedWarningTaskWarnByName(@RequestParam String name) {
        return customizedWarningTaskService.checkCustomizedWarnByName(name);
    }


    @ApiOperation("自定义预警监测维护-新增自定义预警监测")
    @PostMapping("/{version}/pt/customizedWarningTask/add")
    public void addCustomizedcustomizedWarningTask(@RequestBody CustomizedWarningTaskVO customizedWarningTaskVO, @RequestParam String loginUserId, @RequestParam String loginUserName) {
        customizedWarningTaskService.addCustomizedWarningTask(customizedWarningTaskVO, loginUserId, loginUserName);
    }

    @ApiOperation("自定义预警监测维护-删除自定义预警监测")
    @PostMapping("/{version}/pt/customizedWarningTask/delete")
    public void deleteCustomizedcustomizedWarningTask(@RequestParam String id, @RequestParam String loginUserId, @RequestParam String loginUserName) {
        customizedWarningTaskService.deleteCustomizedWarningTask(id, loginUserId, loginUserName);
    }

    @ApiOperation("自定义预警监测维护-编辑自定义预警监测")
    @PostMapping("/{version}/pt/customizedWarningTask/update")
    public void updateCustomizedcustomizedWarningTask(@RequestBody CustomizedWarningTaskVO customizedWarningTaskVO, @RequestParam String loginUserId, @RequestParam String loginUserName) {
        customizedWarningTaskService.updateCustomizedWarningTask(customizedWarningTaskVO, loginUserId, loginUserName);
    }

    @ApiOperation("自定义预警监测维护-更新自定义预警监测状态")
    @PostMapping("/{version}/pt/customizedWarningTask/updateStatus")
    public void updateCustomizedWarningTaskStatus(@RequestParam Integer status, @RequestParam String id, @RequestParam String loginUserId, @RequestParam String loginUserName) {
        customizedWarningTaskService.updateCustomizedWarningTaskStatus(status, id, loginUserId, loginUserName);
    }

    @ApiOperation("字典查询-监测数据来源")
    @GetMapping("/{version}/pt/customizedWarn/getMonitorDataSourceEnum")
    public List<Map<String, String>> getMonitorDataSourceEnum() {
        return MonitorDataSourceEnum.getEnumContent();
    }


    @ApiOperation("字典查询-预警类型")
    @GetMapping("/{version}/pt/customizedWarn/getWarningTypeEnum")
    public List<Map<String, String>> getWarningTypeEnum() {
        return WarningTypeEnum.getEnumContent();
    }


    @ApiOperation("字典查询-预警关联")
    @GetMapping("/{version}/pt/customizedWarn/getWarningCorrelationEnum")
    public List<Map<String, String>> getWarningCorrelationEnum() {
        return WarningCorrelationEnum.getEnumContent();
    }

    @ApiOperation("数据查询-所有有效标准")
    @GetMapping("/{version}/pt/customizedWarn/getAllEnabledWarn")
    public List<CustomizedWarnVO> getAllEnabledWarn(@RequestParam(required = false) String warningType) {
        return customizedWarnService.getAllEnabledWarn(warningType);
    }


    @ApiOperation("数据查询-所有有效规则")
    @GetMapping("/{version}/pt/customizedWarn/getAllEnabledWarningRule")
    public List<CustomizedWarningRuleVO> getAllEnabledWarningRule(@RequestParam(required = false) String warningType) {
        return customizedWarningRuleService.getAllEnabledWarningRule(warningType);
    }


    @ApiOperation("数据查询-所有有效规则")
    @GetMapping("/{version}/pt/customizedWarn/getAllEnabledWarningRuleWithoutItem")
    public List<CustomizedWarningRuleVO> getAllEnabledWarningRuleWithoutItem() {
        return customizedWarningRuleService.getAllEnabledWarningRuleWithoutItem();
    }

    @ApiOperation("数据查询-所有有效监测")
    @GetMapping("/{version}/pt/customizedWarn/getAllEnabledWarningTask")
    public List<CustomizedWarningTaskVO> getAllEnabledWarningTask() {
        return customizedWarningTaskService.getAllEnabledWarningTask();
    }

    @ApiOperation("数据查询-根据taskId查询预警规则")
    @GetMapping("/{version}/pt/customizedWarn/getWarningRuleByTaskId")
    public CustomizedWarnVO getWarningRuleByTaskId(String taskId) {
        return customizedWarningTaskService.getWarningRuleByTaskId(taskId);
    }

    @ApiOperation("数据查询-根据taskId查询预警规则id(提供预警平台使用)")
    @GetMapping("/{version}/pt/customizedWarn/getWarningRuleIdByTaskId")
    public String getWarningRuleIdByTaskId(String taskId) {
        return customizedWarningTaskService.getWarningRuleIdByTaskId(taskId);
    }

    @ApiOperation("根据类型查询自定义配置的所有的取值字段")
    @GetMapping("/{version}/pt/customizedWarn/listLogicFieldByTypeAndSource")
    public List<CustomizedLogicFieldVO> listLogicFieldByTypeAndSource(@RequestParam(required = false) String warningType,
                                                                           @RequestParam(required = false) String dataSource) {
        return customizedWarnService.listLogicFieldByTypeAndSource(warningType, dataSource);
    }
    @ApiOperation("根据类型查询自定义配置的逻辑判断字段")
    @GetMapping("/{version}/pt/customizedWarn/listNeedConfigField")
    public List<CustomizedLogicFieldVO> listNeedConfigField(@RequestParam String warningType,
                                                            @RequestParam String dataSource) {
        return customizedWarnService.listNeedConfigField(warningType, dataSource);
    }

    @ApiOperation("根据类型查询自定义配置的聚集性分析字段")
    @GetMapping("/{version}/pt/customizedWarn/listEventTypeConfigByTypeAndSource")
    public List<TbCdcmrCustomizedEventTypeConfig> listEventTypeConfigByTypeAndSource(@RequestParam(required = false) String warningType,
                                                                                     @RequestParam(required = false) String dataSource) {
        return customizedWarnService.listEventTypeConfigByTypeAndSource(warningType, dataSource);
    }

    @ApiOperation("获取自定义的预警类型-相关常量配置")
    @GetMapping("/{version}/pt/customizedWarn/getConstants")
    public Map<String, Object> getConstants() {
        Map<String, Object> constants = new HashMap<>();
        constants.put("monitorDataSource", MonitorDataSourceEnum.values());
        constants.put("customizeType", CustomizedTypeEnum.values());
        constants.put("addressMonitorType", AddressMonitorTypeEnum.getEnabledEnum());
        return constants;
    }

}
