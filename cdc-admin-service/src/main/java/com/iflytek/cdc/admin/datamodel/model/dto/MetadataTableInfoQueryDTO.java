package com.iflytek.cdc.admin.datamodel.model.dto;

import com.iflytek.cdc.admin.dto.QueryListDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MetadataTableInfoQueryDTO {
    @ApiModelProperty("是否可自定义")
    private String customizedEnable;

    private String schema;

    @ApiModelProperty(value = "页码")
    private Integer pageIndex = 1;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize = 20;

    private String tableCode;

    private String tableName;

    private String refId;
}
