package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.constant.UidTableName;
import com.iflytek.cdc.admin.dto.SearchSyndromeInfoDTO;
import com.iflytek.cdc.admin.dto.UpdateSyndDTO;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.entity.SyndromeInfo;
import com.iflytek.cdc.admin.mapper.SyndromeInfoMapper;
import com.iflytek.cdc.admin.sdk.entity.SyndromeInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.SyndInfo;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.SyndromeService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.*;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/26 15:39
 **/
@Service
@Slf4j
public class SyndromeServiceImpl implements SyndromeService {

    private final SyndromeInfoMapper sMapper;

    private final MdmDataSyncService mdmDataSyncService;

    private final UapUserApi uapUserApi;

    private final BatchUidService batchUidService;

    private final DataAuthService dataAuthService;

    public SyndromeServiceImpl(SyndromeInfoMapper sMapper, MdmDataSyncService mdmDataSyncService, UapUserApi uapUserApi, BatchUidService batchUidService, DataAuthService dataAuthService) {
        this.sMapper = sMapper;
        this.mdmDataSyncService = mdmDataSyncService;
        this.uapUserApi = uapUserApi;
        this.batchUidService = batchUidService;
        this.dataAuthService = dataAuthService;
    }


    @Override
    public PageInfo<SyndromeInfo> querySyndromeInfoPage(SearchSyndromeInfoDTO sd) {
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        return new PageInfo<>(sMapper.querySyndromeInfo(sd));
    }

    @Override
    public void insertSyndromeInfo(String loginUserId, SyndromeInfo syndromeInfo) {
        try {
            //获取登录人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            syndromeInfo.setId(String.valueOf(batchUidService.getUid(UidTableName.SYNDROME_INFO)));
            syndromeInfo.setUpdateUser(uapUser.getId());
            sMapper.insert(syndromeInfo);
        } catch (Exception e) {
            log.error("症候群标准信息插入异常：{}", e);
            throw new MedicalBusinessException("症候群标准信息插入异常");
        }
    }

    @Override
    public void updateSyndromeInfo(String loginUserId, SyndromeInfo si) {
        try {
            //获取登录人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            si.setUpdateUser(uapUser.getId());
            sMapper.updateSyndromeInfo(si);
        } catch (Exception e) {
            log.error("症候群标准信息维护异常：{}", e);
            throw new MedicalBusinessException("症候群标准信息维护异常");
        }
    }

    private TUapUser getUapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (userCache == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void syndromeDataSync(String loginUserId) {
        //判断是否被禁用
        if (isCanUse(MdmDataSyncConstants.SYNDROME_CODE)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        //获取登录人员信息
        TUapUser uapUser = getUapUser(loginUserId);
        List<SyndromeInfo> syndromeInfos;
        List<TermCodedValueInfo> mdmCodeValues;
        try {
            //先查询本地数据表所有的编码数据
            syndromeInfos = sMapper.querySyndromeInfo(new SearchSyndromeInfoDTO());
            //获取mdm主数据平台数据
            mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(MdmDataSyncConstants.SYNDROME_CODE,
                    MdmDataSyncConstants.SYNDROME_PAGENUMBER, MdmDataSyncConstants.SYNDROME_PAGESIZE);
        } catch (Exception e) {
            log.error("查询本地和mdm症候群编码信息异常：{}", e);
            throw new MedicalBusinessException("数据同步失败");
        }
        insertCodeValue(uapUser, syndromeInfos, mdmCodeValues);
        updateCodeValue(uapUser, syndromeInfos, mdmCodeValues);
        updateCodeName(uapUser, syndromeInfos, mdmCodeValues);
    }

    private void updateCodeName(TUapUser uapUser, List<SyndromeInfo> syndromeInfos, List<TermCodedValueInfo> mdmCodeValues) {
        try {
            //取出本地库中存在且mdm也存在的数据
            List<TermCodedValueInfo> allExistData = mdmCodeValues.stream().filter(mdm -> syndromeInfos.stream().map(SyndromeInfo::getSyndromeCode).anyMatch(
                    code -> Objects.equals(mdm.getCodedValue(), code))).collect(Collectors.toList());
            //如果本地库中存在且mdm库也存在的数据，如果编码名称有修改
            if (CollUtil.isNotEmpty(allExistData)) {
                allExistData.forEach(allData ->
                        sMapper.updateCodeName(allData.getCodedValue(), allData.getDescription(), uapUser.getId()));
            }
        } catch (Exception e) {
            log.error("更新症候群名称信息异常:{}", e);
            throw new MedicalBusinessException("数据同步失败");
        }
    }

    private void updateCodeValue(TUapUser uapUser, List<SyndromeInfo> syndromeInfos, List<TermCodedValueInfo> mdmCodeValues) {
        try {
            //取出本地库中存在而mdm不存在的数据
            List<SyndromeInfo> localExistData = syndromeInfos.stream().filter(s -> mdmCodeValues.stream().map(TermCodedValueInfo::getCodedValue).
                    noneMatch(code -> Objects.equals(s.getSyndromeCode(), code))).collect(Collectors.toList());
            //本地库中存在而mdm不存在的数据，更新本地库中数据的状态
            UpdateSyndDTO ud = new UpdateSyndDTO();
            List<String> updateIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(localExistData)) {
                localExistData.forEach(localData -> updateIds.add(localData.getId()));
                ud.setUpdateUser(uapUser.getId());
                ud.setUseStatus(MdmDataSyncConstants.NO_USE);
                ud.setIds(updateIds);
                //批量更新mdm已删除的数据状态
                sMapper.updateSyndromeInfoStatus(ud);
            }
        } catch (Exception e) {
            log.error("更新症候群状态信息异常：{}", e);
            throw new MedicalBusinessException("数据同步失败");
        }

    }

    private void insertCodeValue(TUapUser uapUser, List<SyndromeInfo> syndromeInfos, List<TermCodedValueInfo> mdmCodeValues) {
        try {
            //取出mdm库中存在而本地库中不存在的数据
            List<TermCodedValueInfo> mdmExistData = mdmCodeValues.stream().filter(mdm -> syndromeInfos.stream().map(SyndromeInfo::getSyndromeCode).
                    noneMatch(code -> Objects.equals(mdm.getCodedValue(), code))).collect(Collectors.toList());
            //如果mdm库中存在而本地库中不存在的数据，则直接插入到本地库中
            List<SyndromeInfo> insertInfos = new ArrayList<>();
            //格式化需要插入数据库中的数据
            if (CollUtil.isNotEmpty(mdmExistData)) {
                mdmExistData.forEach(mdmData -> {
                    SyndromeInfo si = new SyndromeInfo();
                    si.setId(String.valueOf(batchUidService.getUid(UidTableName.SYNDROME_INFO)));
                    si.setSyndromeCode(mdmData.getCodedValue());
                    si.setSyndromeName(mdmData.getDescription());
                    si.setCreateUser(uapUser.getId());
                    si.setUseStatus(MdmDataSyncConstants.YES_USE);
                    si.setUpdateUser(uapUser.getId());
                    insertInfos.add(si);
                });
                sMapper.insertSyndromeInfo(insertInfos);
            }
        } catch (Exception e) {
            log.error("插入mdm新增的症候群编码信息异常：{}", e);
            throw new MedicalBusinessException("数据同步失败");
        }

    }

    public boolean isCanUse(String originCode) {
        //查询字典目录
        MdmPageData<TermDictInfo, TermDictInfoFilter> mdmPageData = mdmDataSyncService.getMdmTermDictInfo(originCode);
        List<TermDictInfo> dictInfos = mdmPageData.getEntities();
        //判断为空
        if (CollUtil.isEmpty(dictInfos)) {
            return true;
        }
        //如果被删除
        if (MdmDataSyncConstants.MDM_YES_DELETE.equals(dictInfos.get(0).getDeleted().toString())) {
            return true;
        }
        //判断是否被禁用
        return dictInfos.get(0).getEnabled().toString().equals(MdmDataSyncConstants.NO_USE);
    }

    @Override
    public boolean judgeIsUse(String id) {
        //根据id查询该字典值域的详细
        SyndromeInfo si = sMapper.queryInfoById(id);
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(MdmDataSyncConstants.SYNDROME_CODE);
        //判断字典目录是否存在
        if (isCanUse(MdmDataSyncConstants.SYNDROME_CODE)) {
            log.error("未查询到mdm该字典目录，或者该字典目录已被禁用:{}", di.getTargetCode());
            return false;
        }
        //查询字典值域
        String[] codeValue = {si.getSyndromeCode()};
        //获取字典值域数据
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfo(di, MdmDataSyncConstants.SYNDROME_PAGENUMBER,
                MdmDataSyncConstants.SYNDROME_PAGESIZE, codeValue);
        if (CollUtil.isEmpty(mdmData.getEntities())) {
            return false;
        }
        return !StringUtils.isBlank(mdmData.getEntities().get(0).getId().toString());

    }

    @Override
    public PageInfo<SyndInfo> syndromeInfoPage(SyndromeInfoFilter filter) {
        if (filter.getPageIndex() == null || filter.getPageIndex() == 0
                || filter.getPageSize() == null || filter.getPageSize() == 0) {
            throw new MedicalBusinessException("请传入正确的分页参数");
        }
        PageHelper.startPage(filter.getPageIndex(), filter.getPageSize());
        return new PageInfo<>(sMapper.syndromeInfo(filter));
    }

    @Override
    public SyndInfo syndInfoByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new MedicalBusinessException("code编码不能为空");
        }
        SyndInfo sd = sMapper.syndromeInfoByCode(code);
        if (sd == null) {
            return new SyndInfo();
        }
        return sd;
    }

    @Override
    public List<CascadeVO> getSyndromeList() {
        List<SyndromeInfo> syndromeList = sMapper.listAll();
        return syndromeList.stream()
                .collect(Collectors.toMap(SyndromeInfo::getSyndromeCode, e -> e, (k1, k2) -> k1))
                .values().stream().map(e -> {
                    CascadeVO vo = new CascadeVO();
                    vo.setLabel(e.getSyndromeName());
                    vo.setValue(e.getSyndromeCode());
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<CascadeVO> getSyndromeList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId, Constants.DATA_AUTH_SYNDROME);

//        List<SyndromeInfo> syndromeList = sMapper.listAll();
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getSyndromeDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        return syndromeList.stream()
//                .collect(Collectors.toMap(SyndromeInfo::getSyndromeCode, e -> e, (k1, k2) -> k1))
//                .values().stream().map(e -> {
//                    CascadeVO vo = new CascadeVO();
//                    vo.setLabel(e.getSyndromeName());
//                    vo.setValue(e.getSyndromeCode());
//                    return vo;
//                }).filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
    }
}
