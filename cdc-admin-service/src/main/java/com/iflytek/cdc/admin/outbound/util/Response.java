package com.iflytek.cdc.admin.outbound.util;

import com.iflytek.cdc.admin.outbound.constant.enums.MessageCodeEnum;
import lombok.Data;

/**
 * 公共响应基类
 * <AUTHOR>
 * @date 2020/10/19 14:40
 */
@Data
public class Response<T> {

    private int code;

    private String message;

    private T data;

    public Response() {
    }

    public Response(int code, String msg, T data) {
        this.setCode(code);
        this.setMessage(msg);
        this.setData(data);
    }

    public static <T> Response<T> success() {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_0.getCode()), "success", null);
    }
    public static <T> Response<T> success(String message) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_0.getCode()), message, null);
    }
    public static <T> Response<T> success(T data) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_0.getCode()), "success", data);
    }
    public static <T> Response<T> success(String message, T data) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_0.getCode()), message, data);
    }

    public static <T> Response<T> error() {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_99999.getCode()), "fail", null);
    }
    public static <T> Response<T> error(String message) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_99999.getCode()), message, null);
    }
    public static <T> Response<T> error(T data) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_99999.getCode()), "fail", data);
    }
    public static <T> Response<T> error(String message, T data) {
        return new Response<>(Integer.parseInt(MessageCodeEnum.CODE_99999.getCode()), message, data);
    }

    public boolean isSuccess(){
        return Integer.valueOf(MessageCodeEnum.CODE_0.getCode()).equals(code);
    }

    public boolean isSystemError(){
        return Integer.valueOf(MessageCodeEnum.CODE_99999.getCode()).equals(code);
    }

}
