package com.iflytek.cdc.admin.service;


import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedSmsRule;

import java.util.List;

public interface SmsRuleService {

    void upsertSymptomSmsRule(SymptomSmsRuleParam rule, String loginUserId);


    List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleList(String businessPersonId);



    void upsertInfectedSmsRule(InfectedSmsRuleParam rule, String loginUserId);


    List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleList(String businessPersonId);



    void upsertScSymptomSmsRule(SympSmsRuleParam rule, String loginUserId);

    List<TbCdcmrSympSmsRule> getScSymptomSmsRuleList(String businessPersonId);

    /**
     * 中毒预警短信规则
     */

    void upsertPoisonSmsRule(PoisonSmsRuleParam rule, String loginUserId);


    List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleList(String businessPersonId);


    List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleListByCodeList(List<String> codeList);

    void deleteInfectedSmsRuleByLoginUserId(String loginUserId);

    List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleListByCodeList(List<String> codeList);

    void deleteSymptomSmsRuleByLoginUserId(String loginUserId);

    List<TbCdcmrSympSmsRule> getScSymptomSmsRuleListByCodeList(List<String> codeList);

    void deleteScSymptomSmsRuleByLoginUserId(String loginUserId);

    List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleListByCodeList(List<String> codeList);

    void deletePoisonSmsRuleByLoginUserId(String loginUserId);


    void upsertOutpatientSmsRule(OutpatientSmsRuleParam rule, String loginUserId);


    List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleList(String businessPersonId);

    List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleListByCodeList(List<String> codeList);

    void deleteOutpatientSmsRuleByLoginUserId(String loginUserId);


    void upsertUnknownReasonSmsRule(UnknownReasonSmsRuleParam rule, String loginUserId);


    List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleList(String businessPersonId);

    List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleListByCodeList(List<String> codeList);

    void deleteUnknownReasonSmsRuleByLoginUserId(String loginUserId);

    void upsertPreventionControlSmsRule(PreventionControlSmsRuleParam rule, String loginUserId);

    List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleList(String businessPersonId);

    List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleListByCodeList(List<String> codeList);

    void deletePreventionControlSmsRuleByLoginUserId(String loginUserId);
    void upsertCustomizedSmsRule(CustomizedSmsRuleParam rule, String loginUserId);


    List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleList(String businessPersonId);

    List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleListByCodeList(List<String> codeList);

    void deleteCustomizedSmsRuleByLoginUserId(String loginUserId);


}
