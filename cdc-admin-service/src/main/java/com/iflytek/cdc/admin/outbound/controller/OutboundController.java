package com.iflytek.cdc.admin.outbound.controller;


import com.iflytek.cdc.admin.outbound.constant.OutboundConfig;
import com.iflytek.cdc.admin.outbound.entity.OutboundPerson;
import com.iflytek.cdc.admin.outbound.entity.OutboundRecord;
import com.iflytek.cdc.admin.outbound.model.dto.SignDTO;
import com.iflytek.cdc.admin.outbound.model.dto.WarningSignalSmsRecordDTO;
import com.iflytek.cdc.admin.outbound.model.dto.input.BatchList;
import com.iflytek.cdc.admin.outbound.model.dto.input.CallTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.PlanTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.SmsTask;
import com.iflytek.cdc.admin.outbound.model.dto.output.*;
import com.iflytek.cdc.admin.outbound.model.vo.OutboundPersonVO;
import com.iflytek.cdc.admin.outbound.service.*;
import com.iflytek.cdc.admin.outbound.util.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@Api(tags = "外呼开放接口")
public class OutboundController {

    @Resource
    private OutboundConfig config;

    @Resource
    private CreateOutboundService createOutboundService;

    @Resource
    private QueryOutBoundService queryOutBoundService;

    @Resource
    private AudioTextService audioTextService;

    @Resource
    private OutboundService outboundService;

    @Resource
    private OutboundPersonService outboundPersonService;

    @Value("${sound.appid:d8ee43b3}")
    private String APPID;

    @Value("${sound.secretkey:40fd977aef6441c336280304e06fa17b}")
    private String SECRET_KEY;

    @GetMapping("/pt/v1/outbound/getSign")
    @ApiOperation("获取鉴权信息")
    public SignDTO getSign() {
        return createOutboundService.getSign();
    }

    @PostMapping("/pt/v1/outbound/exec/execPlanTaskAndSave")
    @ApiOperation("执行外呼（方案）接口")
    public OutboundRecord execPlanTaskAndSave(@RequestBody(required = false) PlanTask planTask) {
        return outboundService.sendPlan(planTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execCallTaskAndSave")
    @ApiOperation("执行外呼（话术）接口")
    public OutboundRecord execCallTaskAndSave(@RequestBody(required = false) CallTask callTask) {
        return outboundService.sendCall(callTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execSmsTaskAndSave")
    @ApiOperation("执行外呼（短信）接口")
    public OutboundRecord execSmsTaskAndSave(@RequestBody(required = false) SmsTask smsTask) {
        return outboundService.sendSms(smsTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execPlanTask")
    @ApiOperation("执行外呼（方案）接口")
    public Response<CreateCallRs> execPlanTask(@RequestBody(required = false) PlanTask planTask) {
        return createOutboundService.execPlanTask(planTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execCallTask")
    @ApiOperation("执行外呼（话术）接口")
    public Response<CreateCallRs> execCallTask(@RequestBody(required = false) CallTask callTask) {
        return createOutboundService.execCallTask(callTask);
    }

    @PostMapping("/pt/v1/outbound/exec/execSmsTask")
    @ApiOperation("执行外呼（短信）接口")
    public Response<CreateCallRs> execSmsTask(@RequestBody(required = false) SmsTask smsTask) {
        return createOutboundService.execSmsTask(smsTask);
    }

    @PostMapping("/pt/v1/outbound/query/querySpeechVariable")
    @ApiOperation("查询话术自定义变量")
    public Response<SpeechVariable> querySpeechVariable(String speechId) {
        return queryOutBoundService.querySpeechVariable(speechId);
    }

    @PostMapping("/pt/v1/outbound/query/queryBatchDetail")
    @ApiOperation("查询电话批次状态")
    public Response<BatchDetailRs> queryBatchDetail(String batchId) {
        return queryOutBoundService.queryBatchDetail(batchId);
    }

    @PostMapping("/pt/v1/outbound/query/queryBatchList")
    @ApiOperation("查询批次汇总数据")
    public Response<BatchListRs> queryBatchList(@RequestBody(required = false) BatchList batchList) {
        return queryOutBoundService.queryBatchList(batchList);
    }

    @PostMapping("/pt/v1/outbound/query/queryRecordDetail")
    @ApiOperation("查询电话记录详情")
    public Response<RecordDetailRs> queryRecordDetail(@RequestParam String recordId) {
        return queryOutBoundService.queryRecordDetail(recordId);
    }

    @PostMapping("/pt/v1/outbound/query/listPersonByRecordIds")
    @ApiOperation("查询外呼的人员信息")
    public List<OutboundPersonVO> listPersonByRecordIds(@RequestBody List<String> recordIds) {
        return outboundPersonService.listByRecordIds(recordIds);
    }

    @PostMapping("/pt/v1/outbound/manualUpdateResult")
    @ApiOperation("手工变更结果")
    public void manualUpdateResult(@RequestParam String personId, @RequestParam String result) {
        outboundPersonService.updateManualResult(personId, result);
    }

    @PostMapping("/pt/v1/outbound/exec/addSignalSmsRecord")
    @ApiOperation("执行预警信号短信接口")
    public Integer addSignalSmsRecord(@RequestBody(required = false) List<WarningSignalSmsRecordDTO> dto) {
        return outboundService.addSignalSmsRecord(dto);
    }


}
