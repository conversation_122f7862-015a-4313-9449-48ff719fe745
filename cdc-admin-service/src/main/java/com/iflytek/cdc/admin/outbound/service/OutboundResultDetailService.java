package com.iflytek.cdc.admin.outbound.service;

import com.iflytek.cdc.admin.outbound.entity.OutboundResultDetail;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundResultDetailQueryDTO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface OutboundResultDetailService extends ICdcService<OutboundResultDetail> {

    /**
     * 保存
     */
    void save(List<OutboundResultDetail> details);

    /**
     * 更新details
     */
    void updateDetail(List<OutboundResultDetail> details);

    List<OutboundResultDetail> listDetails(OutboundResultDetailQueryDTO queryDTO);

}
