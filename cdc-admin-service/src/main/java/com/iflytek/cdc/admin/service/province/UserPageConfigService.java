package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrUserPageConfig;
import com.iflytek.cdc.admin.service.ICdcService;

public interface UserPageConfigService extends ICdcService<TbCdcmrUserPageConfig> {

    /**
     * 保存配置
     */
    void saveConfig(TbCdcmrUserPageConfig pageConfig);

    /**
     * 根据页面编码及用户id查询
     */
    TbCdcmrUserPageConfig loadPageConfig(String userId, String pageCode);

    /**
     * 根据页面编码及用户id删除
     */
    void deleteUserConfig(String userId, String pageCode);
}
