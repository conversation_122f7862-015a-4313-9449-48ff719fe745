package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.entity.TbCdcmrInspectionRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrInspectionRuleMapper;
import com.iflytek.cdc.admin.service.InspectionRuleService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class InspectionRuleServiceImpl implements InspectionRuleService {
    @Resource
    TbCdcmrInspectionRuleMapper tbCdcmrInspectionRuleMapper;
    @Resource
    BatchUidService batchUidService;

    @Override
    @Transactional
    public void batchInsert(List<TbCdcmrInspectionRule> recordList, String loginUserId, String mappingId) {
        recordList.forEach(e -> {
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_inspection_rule")));
            e.setMappingId(mappingId);
            e.setUpdateUser(loginUserId);
            e.setUpdateTime(new Date());
        });
        tbCdcmrInspectionRuleMapper.batchInsert(recordList);
    }

    @Override
    public List<TbCdcmrInspectionRule> getPageList(String mappingId) {
        return tbCdcmrInspectionRuleMapper.getList(mappingId);
    }

    @Override
    public int deleteByInfectedCode(String infectedCode) {
        return tbCdcmrInspectionRuleMapper.deleteByInfectedCode(infectedCode);
    }
}
