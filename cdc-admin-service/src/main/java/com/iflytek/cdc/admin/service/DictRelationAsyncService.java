package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.DictRelationDirectory;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2021/9/22 14:59
 **/
public interface DictRelationAsyncService {


    /**
     * 创建任务
     * @param batchId
     * @param loginUserId
     * @return
     **/
    void createTask(String  batchId,String loginUserId);

    /**
     * 更新mdm字典信息
     * @param dictCode
     * @param dictValueCode
     * @param dictValueName
     * @param relationDirectory
     * @param flag
     */
    void syncMdmData(String dictCode, String dictValueCode, String dictValueName, DictRelationDirectory relationDirectory, String flag);


    }
