package com.iflytek.cdc.admin.service.province.impl;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningRiskLevelMapper;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 传染病预警-风险等级配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class WarningRiskLevelServiceImpl extends ServiceImpl<TbCdcmrWarningRiskLevelMapper, TbCdcmrWarningRiskLevel> implements WarningRiskLevelService {
    @Resource
    private TbCdcmrWarningRiskLevelMapper warningRiskLevelMapper;

    @Resource
    private UapServiceApi uapServiceApi;

    @Override
    public List<TbCdcmrWarningRiskLevel> listRiskLevelByWarningType(){

        return warningRiskLevelMapper.getWarningRiskLevelList();
    }

}
