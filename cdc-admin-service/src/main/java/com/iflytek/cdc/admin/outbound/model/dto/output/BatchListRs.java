package com.iflytek.cdc.admin.outbound.model.dto.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 查询批次汇总数据结果对象
 * <AUTHOR>
 */
@Data
@Builder
public class BatchListRs {

    /**
     * 记录信息（固定字段定义如下表，业务字段因话术而定，也可根据fieldList来确定字段对应描述）
     */
    @ApiModelProperty("记录信息")
    private List<Record> data;

    @ApiModelProperty("业务数据字段描述")
    private List<Field> fieldList;
}
