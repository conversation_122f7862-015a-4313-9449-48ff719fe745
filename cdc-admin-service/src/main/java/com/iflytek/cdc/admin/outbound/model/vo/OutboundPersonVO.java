package com.iflytek.cdc.admin.outbound.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OutboundPersonVO {
    @ApiModelProperty
    private String id;
    @ApiModelProperty("外呼记录id")
    private String outboundRecordId;
    @ApiModelProperty(value = "批次号", required = true)
    private String batchNo;
    @ApiModelProperty(value = "姓名（必须是汉字）", required = true)
    private String name;
    @ApiModelProperty(value = "号码", required = true)
    private String telephone;
    @ApiModelProperty("身份证")
    private String idCard;
    @ApiModelProperty("第三方ID")
    private String relationId;
    @ApiModelProperty("居民扩展信息")
    private String extProperty;
    @ApiModelProperty("解析之后的结果")
    private String parseResult;
    @ApiModelProperty("电话拨打时间")
    private String callTime;
    @ApiModelProperty("任务开始时间")
    private String taskTime;
    @ApiModelProperty("电话任务状态描述")
    private String callResult;

    @ApiModelProperty("任务状态: 0：成功,2：通话中,3：无法接通,4：关机,5：用户正忙,6：空号,7：号码错误,9：停机,15：客户接听后并主动挂机," +
            "20：用户未接,22：来电提醒,22：转来电提醒,23：呼入限制,26：网络故障,28：线路故障,30：呼叫失败,300 ：任务执行失败")
    private String resultCode;

    @ApiModelProperty("接通状态: 1：正常回答,2：自动留言,3：接听不便,4：居民已死亡,5：家属不能代答,6：不愿配合,7：号码错误,8：中断")
    private String endNode;

    @ApiModelProperty("转存后的音频地址")
    private String audioUrl;
    @ApiModelProperty("手动变更的结果")
    private String manualResult;





}
