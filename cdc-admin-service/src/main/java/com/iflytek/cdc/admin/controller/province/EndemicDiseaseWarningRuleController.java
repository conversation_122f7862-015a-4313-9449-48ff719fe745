package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.EndemicWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.EndemicDiseaseWarningRuleService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Api(tags = "预警规则维护-地方病")
@RequestMapping("/pt/{version}/endemicDiseaseWarning")
public class EndemicDiseaseWarningRuleController {

    @Resource
    private EndemicDiseaseWarningRuleService endemicDiseaseWarningRuleService;

    @GetMapping("/getRuleDetail")
    @ApiOperation("预警规则详情")
    public PageInfo<TbCdcmrEndemicDiseaseWarningRule> getRuleDetail(@RequestParam String diseaseInfoId,
                                                                     @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                     @RequestParam(defaultValue = "20") Integer pageSize,
                                                                     @RequestParam(required = false) String riskLevel,
                                                                     @RequestParam(required = false) String warningMethod){

        return endemicDiseaseWarningRuleService.getRuleDetail(diseaseInfoId, pageIndex, pageSize, riskLevel, warningMethod);
    }


    @PostMapping("/getEndemicTree")
    @ApiOperation("获取新发突发传染病结构树")
    public List<TreeNode> getEndemicTree(@RequestBody EndemicWaringRuleQueryDTO dto){

        return endemicDiseaseWarningRuleService.getEndemicTree(dto);
    }

    @PostMapping("/savaOrUpdateRule")
    @ApiOperation("编辑新发突发传染病规则")
    @OperationLogAnnotation(operationName = "编辑新发突发传染病规则")
    public void savaOrUpdateRule(@RequestBody TbCdcmrEndemicDiseaseWarningRule rule){

        endemicDiseaseWarningRuleService.savaOrUpdateRule(rule);
    }
    
}

