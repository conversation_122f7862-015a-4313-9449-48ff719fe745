package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.outbound.entity.OutboundResultDetail;
import com.iflytek.cdc.admin.outbound.mapper.OutboundResultDetailMapper;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundResultDetailQueryDTO;
import com.iflytek.cdc.admin.outbound.service.OutboundResultDetailService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OutboundResultDetailServiceImpl extends CdcServiceBaseImpl<OutboundResultDetailMapper, OutboundResultDetail> implements OutboundResultDetailService {
   
    @Override
    public void save(List<OutboundResultDetail> details) {
        List<String> recordIds = details.stream().map(OutboundResultDetail::getRecordId).collect(Collectors.toList());
        List<OutboundResultDetail> resultDetails = listByRecordIds(recordIds);
        Map<String, OutboundResultDetail> resultDetailMap = resultDetails.stream().collect(Collectors.toMap(OutboundResultDetail::getRecordId, Function.identity()));
        List<OutboundResultDetail> inserts = new ArrayList<>();
        List<OutboundResultDetail> updates = new ArrayList<>();
        details.forEach(d -> {
            d.setStatus(Constants.COMMON_STRNUM_ZERO);
            if (resultDetailMap.containsKey(d.getRecordId())){
                OutboundResultDetail resultDetail = resultDetailMap.get(d.getRecordId());
                d.setId(resultDetail.getId());
                updates.add(d);
            }else {
                inserts.add(d);
            }
        });
        if (inserts.size() > 0){
            saveBatch(inserts);
        }
        if (updates.size() > 0){
            updateBatchById(updates);
        }
    }

    @Override
    public void updateDetail(List<OutboundResultDetail> details) {
        updateBatchById(details);
    }

    @Override
    public List<OutboundResultDetail> listDetails(OutboundResultDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<OutboundResultDetail> queryWrapper = lambdaQueryWrapper();
        if (queryDTO.getRecordId() != null){
            queryWrapper.eq(OutboundResultDetail::getRecordId, queryDTO.getRecordId());
        }
        if (queryDTO.getStartTime() != null){
            queryWrapper.ge(OutboundResultDetail::getCreateTime, queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null){
            queryWrapper.le(OutboundResultDetail::getCreateTime, queryDTO.getEndTime());
        }
        if (StringUtils.isNotEmpty(queryDTO.getDetailStatus())){
            queryWrapper.eq(OutboundResultDetail::getStatus, queryDTO.getDetailStatus());
        }
        return list(queryWrapper);
    }


    private List<OutboundResultDetail> listByRecordIds(List<String> recordIds){
        LambdaQueryWrapper<OutboundResultDetail> queryWrapper = lambdaQueryWrapper();
        queryWrapper.in(OutboundResultDetail::getRecordId, recordIds);
        return list(queryWrapper);
    }
}
