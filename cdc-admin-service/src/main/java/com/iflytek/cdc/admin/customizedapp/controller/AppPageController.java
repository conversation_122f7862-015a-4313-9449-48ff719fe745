package com.iflytek.cdc.admin.customizedapp.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppPageQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.PageService;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/customizedApp/page")
public class AppPageController {
    @Resource
    private PageService tbCdccsPageService;

    /**
     * 通过ID查询单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation("通过ID查询单条数据")
    public TbCdccsPage queryById(@RequestParam String id){
        return tbCdccsPageService.loadCascade(id);
    }

    /**
     * 根据编码查询数据
     */
    @GetMapping("/queryByCode")
    @ApiOperation("根据编码查询数据")
    public TbCdccsPage queryByCode(@RequestParam String code){
        return tbCdccsPageService.loadCascadeByCode(code);
    }


    /**
     * 新增数据
     */
    @PostMapping("/add")
    @ApiOperation("新增数据")
    public TbCdccsPage addApp(@RequestBody TbCdccsPage input){
        return tbCdccsPageService.create(input);
    }

    /**
     * 更新数据
     */
    @PostMapping("/update")
    @ApiOperation("更新数据")
    public TbCdccsPage update(@RequestBody TbCdccsPage input){
        return tbCdccsPageService.update(input);
    }


    /**
     * 查询数据集合
     */
    @PostMapping("/list")
    @ApiOperation("list查询")
    public List<TbCdccsPage> list(@RequestBody AppPageQueryDTO queryDTO){
        return tbCdccsPageService.list(queryDTO);
    }
    /**
     * 分页查询数据
     */
    @PostMapping("/pageList")
    @ApiOperation("分页查询数据")
    public PageInfo<TbCdccsPage> pageList(@RequestBody AppPageQueryDTO queryDTO){
        return tbCdccsPageService.pageList(queryDTO);
    }
    @GetMapping("/loadErModelByPageId")
    @ApiOperation("获取页面的er模型")
    public TbCdcdmEntityErModel loadErModelByPageId(@RequestParam String pageId){
        return tbCdccsPageService.loadErModelByPageId(pageId);
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public void delete(@RequestParam String id){
        tbCdccsPageService.deleteById(id);
    }

    @PostMapping("/saveByCode")
    @ApiOperation("根据编码保存")
    public void saveByCode(@RequestBody TbCdccsPage page){
        tbCdccsPageService.saveByCode(page);
    }

}
