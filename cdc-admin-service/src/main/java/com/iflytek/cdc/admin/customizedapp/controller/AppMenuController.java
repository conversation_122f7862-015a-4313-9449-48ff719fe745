package com.iflytek.cdc.admin.customizedapp.controller;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.model.dto.MenuGroupDTO;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import com.iflytek.cdc.admin.customizedapp.service.MenuService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/customizedApp/menu")
public class AppMenuController {
    @Resource
    public MenuService tbCdccsMenuService;

    @Resource
    private AppService appService;

    /**
     * 通过菜单分组创建
     * @param menuGroup 菜单分组
     */
    @PostMapping("/create")
    @ApiOperation("通过菜单分组创建")
    public void create(@RequestBody MenuGroupDTO menuGroup){
        tbCdccsMenuService.create(menuGroup);
    }

    /**
     * 通过菜单分组创建
     * @param menuGroupDTO 菜单分组
     */
    @PostMapping("/update")
    @ApiOperation("")
    public void update(@RequestBody MenuGroupDTO menuGroupDTO) {
        tbCdccsMenuService.update(menuGroupDTO);
    }

    /**
     * 获取id
     * @return id
     */
    @GetMapping("/getId")
    @ApiOperation("获取id")
    public String getId(){
        return tbCdccsMenuService.getId();
    }

    /**
     * 根据应用id查询
     * @param appId 应用id
     * @return 菜单集合
     */
    @GetMapping("/listByAppId")
    @ApiOperation("根据应用id查询")
    public List<TbCdccsMenu> listByAppId(@RequestParam String appId){
        return tbCdccsMenuService.listByAppId(appId);
    }

    /**
     * 根据分组id查询
     * @param groupId 分组id
     * @return 菜单集合
     */
    @GetMapping("/listByGroupId")
    @ApiOperation("根据分组id查询")
    public List<TbCdccsMenu> listByGroupId(@RequestParam String groupId){
        return tbCdccsMenuService.listByGroupId(groupId);
    }

    /**
     * 菜单发布
     * @param groupId 分组数据id
     */
    @PostMapping("/published")
    @ApiOperation("菜单发布")
    public void published(@RequestParam String groupId, @RequestParam String loginUserId){
        appService.published(groupId, loginUserId);
    }

    /**
     * 查询分组
     * @param appId 应用id
     * @return 版本数据
     */
    @GetMapping("/listGroup")
    @ApiOperation("查询分组")
    public List<TbCdccsVersion> listGroup(@RequestParam String appId){
        return tbCdccsMenuService.listGroup(appId);
    }

    /**
     * 删除分组
     */
    @PostMapping("/deleteGroup")
    @ApiOperation("删除分组")
    public void deleteGroup(@RequestParam String groupId){
        tbCdccsMenuService.deleteGroup(groupId);
    }

}
