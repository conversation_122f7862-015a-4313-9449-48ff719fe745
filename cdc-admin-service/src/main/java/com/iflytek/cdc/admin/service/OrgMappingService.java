package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.MatchOrgDTO;
import com.iflytek.cdc.admin.dto.OrgChangeDto;
import com.iflytek.cdc.admin.dto.OrgMatchResultDTO;
import com.iflytek.cdc.admin.dto.SearchHisOrgDTO;
import com.iflytek.cdc.admin.entity.ExcelUploadResult;
import com.iflytek.cdc.admin.entity.HisOrg;
import com.iflytek.cdc.admin.entity.UapOrganization;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 机构映射服务类
 * <AUTHOR>
 * @date 2021/6/30 10:56
 **/
public interface OrgMappingService {

    /**
     *  分页查询his机构列表
     *  @param searchHisOrgDTO
     *  @return
     **/
    public PageInfo<HisOrg>  queryHisOrg(SearchHisOrgDTO  searchHisOrgDTO);

    /**
     * 分页查询uap机构列表
     * @param searchOrgDTO
     * @return
     **/
    public  PageInfo<UapOrganization>  queryUapOrg(SearchHisOrgDTO  searchOrgDTO,String loginUserId);

    /**
     *  his机构数据导入
     *  @param file
     *  @return
     **/
    public ExcelUploadResult readUploadHisOrg(MultipartFile file, String loginUserId);

    public void exportFail(List<HisOrg>  hisOrgFail,HttpServletResponse response);

    /**
     * his机构数据删除
     * @param hisOrgId
     * @return
     **/
    void deleteHisOrg(String  hisOrgId);

    /**
     * 机构一键匹配
     * @param mo
     * @return
     **/
    OrgMatchResultDTO matchOrg(MatchOrgDTO  mo, String loginUserId);

    /**
     * 解绑机构映射
     * @param mo
     * @return
     **/
    void unbindOrgMatch(MatchOrgDTO  mo,String loginUserId);

    /**
     * 根据his机构编码查询uap机构信息
     **/
    UapOrgInfo  queryUapOrgByHis(HisInfoFilter  filter);

    /**
     * 根据uapid查询his机构信息
     * @param uapId
     * @return
     **/
    HisOrgInfo  queryHisOrgByUap(String  uapId);


}
