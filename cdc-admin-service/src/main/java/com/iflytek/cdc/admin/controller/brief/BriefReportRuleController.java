package com.iflytek.cdc.admin.controller.brief;

import java.util.List;

import javax.validation.Valid;

import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.entity.brief.PushUserEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateRuleEntity;
import com.iflytek.cdc.admin.service.brief.TemplateRuleService;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.TemplatePushRuleVo;
import com.iflytek.cdc.admin.vo.brief.TemplateRuleVo;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

/**
 * 简报规则控制器
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Api(tags = "简报规则配置")
@RestController
@RequestMapping("/pt/{version}/brief/rule")
@RequiredArgsConstructor
public class BriefReportRuleController {

    private final TemplateRuleService templateRuleService;

    /**
     * 列表
     */
    @PostMapping("/list")
    public ApiResult<PageInfo<TemplateRuleVo>> list(@RequestBody TemplateSearchDto searchDto) {
        return templateRuleService.queryPage(searchDto);
    }

    @PostMapping("/pushRuleList")
    public ApiResult<PageInfo<TemplatePushRuleVo>> pushRuleList(@RequestBody TemplateSearchDto searchDto) {
        return templateRuleService.pushRuleList(searchDto);
    }

    /**
     * 根据模板id获取规则
     */
    @GetMapping("/info")
    public ApiResult<TemplateRuleEntity> info(@RequestParam(required = false) String id, @RequestParam(required = false) String templateId) {
        TemplateRuleEntity template = templateRuleService.getByTemplateIdOrId(templateId, id);
        return ApiResult.ok(template);
    }

    /**
     * 根据规则id获取权限列表
     */
    @GetMapping("/pushUserList/{ruleId}")
    public ApiResult<List<PushUserEntity>> pushUserList(@PathVariable String ruleId,@RequestParam String orgId) {
        List<PushUserEntity> userList = templateRuleService.pushUserList(ruleId,orgId);
        return ApiResult.ok(userList);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/saveOrUpdate")
    @OperationLogAnnotation(operationName = "简报规则配置-保存")
    public ApiResult<?> generateRule(@RequestParam String loginUserId, @RequestBody TemplateRuleEntity template) {
        templateRuleService.generateRule(template, loginUserId);
        return ApiResult.ok();
    }

    /**
     * 保存
     *
     * @param loginUserId 登录用户id
     * @param updatorName 更新程序名称
     * @param userList    用户列表
     * @return {@link ApiResult }
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/saveAuthUser")
    @OperationLogAnnotation(operationName = "简报规则配置-保存认证人")
    public ApiResult<?> saveAuthUser(@RequestParam String loginUserId,  @RequestParam String updatorName,
                                     @RequestBody @Valid List<PushUserEntity> userList) {
        templateRuleService.saveAuthUser(userList, loginUserId, updatorName);
        return ApiResult.ok();
    }

    /**
     * 更改状态
     */
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("/changeStatus")
    @OperationLogAnnotation(operationName = "简报规则配置-更改状态")
    public ApiResult<?> changeStatus(@RequestParam String loginUserId, @RequestParam String updatorName,
                                     @RequestParam String status, @RequestParam String id) {
        return templateRuleService.changeStatus(loginUserId, updatorName, id, status);
    }

    @PostMapping("/exportRuleList")
    @ApiOperation("简报生成规则-导出")
    public ResponseEntity<byte[]> exportRuleList(@RequestBody TemplateSearchDto searchDto) {
        return templateRuleService.exportRuleList(searchDto);
    }

}
