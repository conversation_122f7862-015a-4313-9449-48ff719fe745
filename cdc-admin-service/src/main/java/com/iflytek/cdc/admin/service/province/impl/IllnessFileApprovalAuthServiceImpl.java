package com.iflytek.cdc.admin.service.province.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.dto.IllnessFileApprovalAuthDto;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFileApprovalAuth;
import com.iflytek.cdc.admin.enums.AuthClassEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessFileApprovalAuthMapper;
import com.iflytek.cdc.admin.service.province.IllnessFileApprovalAuthService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;


@Service
public class IllnessFileApprovalAuthServiceImpl extends ServiceImpl<TbCdcmrIllnessFileApprovalAuthMapper, TbCdcmrIllnessFileApprovalAuth> implements IllnessFileApprovalAuthService {

    @Autowired
    private BatchUidService batchUidService;
    @Autowired
    private TbCdcmrIllnessFileApprovalAuthMapper illnessFileApprovalAuthMapper;
    @Autowired
    private UapServiceApi uapServiceApi;

    private static final String TABLE_NAME = "tb_cdcmr_illness_file_approval_auth";


    @Override
    public void saveBatchApprovalAuth(String loginUserId,List<TbCdcmrIllnessFileApprovalAuth> list) {
        List<TbCdcmrIllnessFileApprovalAuth> res = new ArrayList<>();
        list.forEach(item -> getNeedAddRecord(loginUserId, item, res));
        saveBatch(res);
    }

    private void getNeedAddRecord(String loginUserId, TbCdcmrIllnessFileApprovalAuth item, List<TbCdcmrIllnessFileApprovalAuth> res) {
        buildIllnessFileApprovalAuth(loginUserId, item);
        List<String> userIds = item.getApprovalUserIds();
        userIds.forEach(userId -> {
            TbCdcmrIllnessFileApprovalAuth auth = new TbCdcmrIllnessFileApprovalAuth();
            BeanUtils.copyProperties(item, auth);
            auth.setApprovalUserId(userId);
            auth.setApprovalUserName(uapServiceApi.getUser(userId).getName());
            auth.setId(String.valueOf(batchUidService.getUid(TABLE_NAME)));
            res.add(auth);
        });
    }

    private void buildIllnessFileApprovalAuth(String loginUserId, TbCdcmrIllnessFileApprovalAuth item) {
        item.setAuthName(AuthClassEnum.getValueByCode(item.getAuthCode()));
        item.setId(String.valueOf(batchUidService.getUid(TABLE_NAME)));
        item.setCreatorId(loginUserId);
        item.setCreator(USER_INFO.get().getName());
        item.setCreateTime(new Date());
        item.setUpdateId(loginUserId);
        item.setUpdater(USER_INFO.get().getName());
        item.setUpdateTime(new Date());
        item.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        item.setStatus("1");
    }

    @Override
    public List<TbCdcmrIllnessFileApprovalAuth> getApprovalAuthByOrgId(String orgId) {
        return illnessFileApprovalAuthMapper.queryIllnessFileApprovalAuthByOrgId(orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalAuth(String loginUserId,IllnessFileApprovalAuthDto authDto) {
        List<String> userIds = authDto.getApprovalUserIds();
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        TbCdcmrIllnessFileApprovalAuth authRecord = new TbCdcmrIllnessFileApprovalAuth();
        BeanUtils.copyProperties(authDto,authRecord);
        List<TbCdcmrIllnessFileApprovalAuth> res = new ArrayList<>();
        getNeedAddRecord(loginUserId,authRecord,res);
        LambdaQueryWrapper<TbCdcmrIllnessFileApprovalAuth> wrapper = new LambdaQueryWrapper<TbCdcmrIllnessFileApprovalAuth>()
                .eq(TbCdcmrIllnessFileApprovalAuth::getAuthCode,authDto.getAuthCode())
                .eq(TbCdcmrIllnessFileApprovalAuth::getDeleteFlag, DeleteFlagEnum.NO.getCode())
                .eq(TbCdcmrIllnessFileApprovalAuth::getOrgId,authDto.getOrgId());
        List<TbCdcmrIllnessFileApprovalAuth> exist = illnessFileApprovalAuthMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(exist)){
            saveBatch(res);
            return;
        }
        List<String> existApprovalUserIds = exist.stream().map(TbCdcmrIllnessFileApprovalAuth::getApprovalUserId).collect(Collectors.toList());
        List<String> copyUserIds =new ArrayList<>(userIds);
        userIds.removeAll(existApprovalUserIds);
        if (!CollectionUtils.isEmpty(userIds)){
            List<TbCdcmrIllnessFileApprovalAuth> collect = res.stream().filter(item -> userIds.contains(item.getApprovalUserId())).collect(Collectors.toList());
            saveBatch(collect);
        }
        existApprovalUserIds.removeAll(copyUserIds);
        if (!CollectionUtils.isEmpty(existApprovalUserIds)){
            List<TbCdcmrIllnessFileApprovalAuth> collect = exist.stream().filter(item -> existApprovalUserIds.contains(item.getApprovalUserId()))
                    .peek(item -> {
                        item.setDeleteFlag(DeleteFlagEnum.YES.getCode());
                        item.setUpdateId(loginUserId);
                        item.setUpdater(USER_INFO.get().getName());
                    }).collect(Collectors.toList());
            updateBatchById(collect);
        }
    }
}
