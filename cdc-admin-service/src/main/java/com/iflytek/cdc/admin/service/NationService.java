package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.entity.UapNation;
import com.iflytek.cdc.admin.entity.NationInfo;

/**
 * <AUTHOR>
 * @date 2021/7/12 10:30
 **/
public interface NationService {

    /**
     * 根据当前的登录人员所在区域获取
     *
     * @param code
     * @return
     **/
    NationInfo queryAllNationInfo(String code);

    NationInfo queryAllNationInfoByUserId(String userId);

    UapNation getNations(String code, AreaLevelEnum levelEnum);
}
