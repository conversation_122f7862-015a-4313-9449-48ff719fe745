package com.iflytek.cdc.admin;

import com.iflytek.cdc.admin.config.EsModelConfigProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.iflytek.cdc.admin"})
@EnableSwagger2
@EnableAsync
@EnableConfigurationProperties(EsModelConfigProperties.class)
@MapperScan(basePackages = {"com.iflytek.cdc.admin.mapper",
        "com.iflytek.cdc.admin.datamodel.mapper",
        "com.iflytek.cdc.admin.customizedapp.mapper",
        "com.iflytek.cdc.admin.workbench.mapper",
        "com.iflytek.cdc.admin.outbound.mapper",
        "com.iflytek.cdc.admin.expert.mapper"})
public class CdcAdminServiceApplication {


    public static void main(String[] args) {
        SpringApplication.run(CdcAdminServiceApplication.class, args);
    }

}
