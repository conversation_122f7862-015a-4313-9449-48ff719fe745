package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.ParamConfigConstants;
import com.iflytek.cdc.admin.dto.ParamConfigDTO;
import com.iflytek.cdc.admin.dto.SearchParamDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.ParamOrg;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import com.iflytek.cdc.admin.service.ParamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.index.qual.PolyUpperBound;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/7/5 17:00
 **/
@RestController
@Api(tags = "参数配置接口")
public class ParamConfigController {

    private final ParamConfigService pService;

    public ParamConfigController(ParamConfigService pService) {
        this.pService = pService;
    }

    @PostMapping("/{version}/pt/param/config/queryParamConfigPage")
    @ApiOperation("参数配置参数维度分页查询")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<ParamConfig> queryParamConfigPage(@PathVariable String version, @RequestBody SearchParamDTO sd, @RequestParam String loginUserId) {
        return pService.queryParamConfig(sd, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/queryParamOrgPage")
    @ApiOperation("参数配置机构维度分页查询")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<ParamOrg> queryParamOrgPage(@PathVariable String version, @RequestBody SearchParamDTO sd, @RequestParam String loginUserId) {
        return pService.queryParamOrg(sd, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/queryParamConfigById")
    @ApiOperation("根据机构id查询特有的参数列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<ParamConfig> queryParamConfigById(@PathVariable String version, @RequestBody SearchParamDTO sd) {
        return pService.queryParamConfigById(ParamConfigConstants.NO_DELETE, sd);
    }

    @PostMapping("/{version}/pt/param/config/insertParamConfig")
    @ApiOperation("添加参数配置")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void insertParamConfig(@PathVariable String version, @RequestBody @Valid ParamConfigDTO pc, @RequestParam String loginUserId, BindingResult bindingResult) {
        pService.insertParamConfig(pc, loginUserId);
    }

    @GetMapping("/{version}/pt/param/config/removeOrgParamConfig")
    @ApiOperation("机构特有参数页面移除参数配置")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void removeOrgParamConfig(@PathVariable String version, @RequestParam String orgId, @RequestParam String configCode, @RequestParam String loginUserId) {
        pService.removeOrgParamConfig(orgId, configCode, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/queryParamConfigDetail")
    @ApiOperation("查询单个参数配置详细信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ParamConfigDTO queryParamConfigDetail(@PathVariable String version, @RequestBody SearchParamDTO sd, @RequestParam String loginUserId) {
        return pService.queryParamConfigDetail(sd, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/updateParamConfig")
    @ApiOperation("修改参数配置信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateParamConfig(@PathVariable String version, @RequestBody @Valid ParamConfigDTO pc, @RequestParam String loginUserId, BindingResult bindingResult) {
        pService.updateParamConfig(pc, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/deleteParamConfig")
    @ApiOperation("删除参数配置信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteParamConfig(@PathVariable String version, @RequestParam String configId, @RequestParam String loginUserId) {
        pService.deleteParamConfig(configId, loginUserId);
    }

    @PostMapping("/{version}/pt/param/config/paramInfoByCode")
    @ApiOperation("查询参数信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ParamInfo paramInfoByCode(@PathVariable String version, @RequestBody ParamInfoFilter filter) {
        return pService.paramInfoByCode(filter);
    }

    @PostMapping("/{version}/pt/param/config/paramListByCodeList")
    @ApiOperation("批量查询参数信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<ParamInfo> paramListByCodeList(@PathVariable String version, @RequestBody List<ParamInfoFilter> filters) {
        return pService.paramListByCodeList(filters);
    }

    @GetMapping("/{version}/pt/param/config/paramInfoByGroup")
    @ApiOperation("根据参数分组查询下面所有的参数")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<ParamInfo> paramInfoByGroup(@PathVariable String version, @RequestParam String configGroup) {
        return pService.paramInfoByGroup(configGroup);
    }

    @GetMapping("/{version}/pt/param/config/queryParamConfigByKeyword")
    @ApiOperation("参数配置参数关键词检索")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<ParamConfig> queryParamConfigByKeyword(@PathVariable String version, @RequestParam String keyword) {
        return pService.queryParamConfigByKeyword(keyword);
    }

    @GetMapping("/{version}/pt/param/config/getTrendForecastStatus")
    @ApiOperation("获取症候群预测权限开关状态")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public String getTrendForecastStatus(@PathVariable String version, @RequestParam String configCode, @RequestParam String configGroup) {
        return pService.getTrendForecastStatus(configCode, configGroup);
    }

    @GetMapping("/{version}/pt/param/config/getStatusByCodeAndGroup")
    @ApiOperation("获取开关状态")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public String getStatusByCodeAndGroup(@PathVariable String version, @RequestParam String configCode, @RequestParam String configGroup) {
        return pService.getStatusByCodeAndGroup(configCode, configGroup);
    }

    @GetMapping("/{version}/pt/param/config/refreshRedisBy")
    @ApiOperation("手动重刷redis缓存 - 研发使用")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void refreshRedisBy(@PathVariable String version,
                               @RequestParam String configCode,
                               @RequestParam(required = false) String configGroup) {

        pService.refreshRedisBy(configCode, configGroup);
    }

}
