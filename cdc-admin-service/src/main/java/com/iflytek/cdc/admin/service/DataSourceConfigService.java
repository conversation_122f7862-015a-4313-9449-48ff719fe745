package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.DataSourceConfigVO;
import com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig;

import java.util.List;

public interface DataSourceConfigService {
    TbCdcmrDataSourceConfig getByBusinessTypeAndSignalType(String businessType, String signalType);

    /**
     * 通过businessType 查询
     */
    List<TbCdcmrDataSourceConfig> listByBusinessType(String businessType);

    List<DataSourceConfigVO> getList();

    void updateConfig(TbCdcmrDataSourceConfig record);
}
