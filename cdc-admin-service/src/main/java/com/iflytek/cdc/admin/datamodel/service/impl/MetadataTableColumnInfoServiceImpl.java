package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.lang.Pair;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableColumnInfoService;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableSqlLogService;
import com.iflytek.cdc.admin.enums.TableStatusEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class MetadataTableColumnInfoServiceImpl  extends CdcServiceBaseImpl<TbCdcdmMetadataTableColumnInfoMapper, TbCdcdmMetadataTableColumnInfo>  implements MetadataTableColumnInfoService {
    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    @Resource
    private MetadataTableSqlLogService metadataTableSqlLogService;

    @Override
    public List<TbCdcdmMetadataTableColumnInfo> listByTableId(String tableId) {
        return list(lambdaQueryWrapper().eq(TbCdcdmMetadataTableColumnInfo::getTableId, tableId).orderByAsc(TbCdcdmMetadataTableColumnInfo::getSort));
    }

    @Override
    @Transactional
    public void saveByTable(Pair<TbCdcdmMetadataTableInfo, TbCdcdmMetadataTableInfo> tbCdcdmMetadataTableInfo,
                            List<TbCdcdmMetadataTableColumnInfo> columnInfoList) {

        TbCdcdmMetadataTableInfo oldTable = tbCdcdmMetadataTableInfo.getKey();
        TbCdcdmMetadataTableInfo newTable = tbCdcdmMetadataTableInfo.getValue();
        AtomicInteger i = new AtomicInteger();
        columnInfoList.forEach(c -> c.setSort(i.getAndIncrement()));
        batchSaveByForeignKey(tbCdcdmMetadataTableInfo.getValue(),
            TbCdcdmMetadataTableInfo::getId,
            TbCdcdmMetadataTableColumnInfo::getTableId,
            (t, c) -> {
                c.setColumnCode(c.getColumnName());
                c.setTableId(t.getId());
                c.setTableCode(t.getTableCode());
                c.setColumnCode(t.getTableCode() + ":" + c.getColumnCode());
            },
            (inserts) -> {
                if (TableStatusEnum.WAIT_SYNC.getCode().equals(newTable.getTableStatus())) {
                    metadataTableSqlLogService.createAlertSql(tbCdcdmMetadataTableInfo, null, inserts);
                }
            },
            (updates) -> {
                if (TableStatusEnum.WAIT_SYNC.getCode().equals(newTable.getTableStatus())) {
                    metadataTableSqlLogService.createAlertSql(tbCdcdmMetadataTableInfo, updates, null);
                }
            },
            columnInfoList
            );

    }
    
}
