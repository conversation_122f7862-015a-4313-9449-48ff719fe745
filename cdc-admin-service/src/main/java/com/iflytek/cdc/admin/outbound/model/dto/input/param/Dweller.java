package com.iflytek.cdc.admin.outbound.model.dto.input.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 居民信息
 * <AUTHOR>
 */
@Data
@Builder
public class Dweller {

    @ApiModelProperty(value = "姓名（必须是汉字）", required = true)
    private String dwellerName;

    @ApiModelProperty(value = "号码", required = true)
    private String telephone;

    @ApiModelProperty("身份证")
    private String idCard;

    @ApiModelProperty("第三方ID")
    private String relationId;

    @ApiModelProperty("居民扩展信息")
    private Map<String, String> extProperty;

}
