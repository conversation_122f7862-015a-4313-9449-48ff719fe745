package com.iflytek.cdc.admin.capacity.constants;

public class AlgorithmApiConstants {

    /**
     * redis
     */
    public static final String REDIS_COMMON = "cdc-capacity-api-algorithm:";

    /**
     * token取得参数url
     */
    public static final String GRANT_URL = "/auth/oauth/token?";

    /**
     * ACCESS_TOKEN
     */
    public static final String ACCESS_TOKEN = "access_token";
    /**
     * REFRESH_TOKEN
     */
    public static final String REFRESH_TOKEN = "refresh_token";

    /**
     * TRANSACTION_ID
     */
    public static final String TRANSACTION_ID = "transactionId";
    /**
     * APP_KEY
     */
    public static final String APP_KEY = "appKey";
    /**
     * SIGN
     */
    public static final String SIGN = "sign";

    /**
     * DEVICE_ID
     */
    public static final String DEVICE_ID = "deviceId";
}
