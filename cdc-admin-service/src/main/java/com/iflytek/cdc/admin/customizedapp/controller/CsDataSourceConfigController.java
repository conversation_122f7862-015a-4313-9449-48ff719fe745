package com.iflytek.cdc.admin.customizedapp.controller;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsDataSourceConfig;
import com.iflytek.cdc.admin.customizedapp.model.vo.DataSourceConfigVO;
import com.iflytek.cdc.admin.customizedapp.service.DataSourceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据源管理
 */
@RestController
@RequestMapping("/pt/{version}/dataSource")
@Api("数据源管理")
public class CsDataSourceConfigController {

    @Resource
    private DataSourceConfigService dataSourceConfigService;

    @GetMapping("/listDataSourceConfigVO")
    @ApiOperation("数据来源配置")
    public List<DataSourceConfigVO> listDataSourceConfigVO(){
        return dataSourceConfigService.listAll();
    }

    @GetMapping("/listDataSourceConfig")
    @ApiOperation("数据来源配置（服务间调用）")
    public List<TbCdccsDataSourceConfig> listDataSourceConfig(){
        return dataSourceConfigService.list();
    }
    
}
