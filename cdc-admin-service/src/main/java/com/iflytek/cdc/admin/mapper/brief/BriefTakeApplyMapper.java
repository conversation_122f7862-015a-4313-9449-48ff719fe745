package com.iflytek.cdc.admin.mapper.brief;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyQueryDto;
import com.iflytek.cdc.admin.entity.brief.BriefTakeApply;
import com.iflytek.cdc.admin.vo.brief.BriefTakeApplyVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BriefTakeApplyMapper extends BaseMapper<BriefTakeApply> {
    BriefTakeApplyVo queryById(String id);

    List<BriefTakeApplyVo> queryList(BriefTakeApplyQueryDto dto);
}
