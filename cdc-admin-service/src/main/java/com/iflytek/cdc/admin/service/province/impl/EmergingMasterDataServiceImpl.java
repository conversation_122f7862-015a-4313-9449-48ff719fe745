package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseWarning;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseWarningMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingDiseaseInfoMapper;
import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.IMasterDataService;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service("emergingMasterDataCategory")
public class EmergingMasterDataServiceImpl implements IMasterDataService {

    private static final String TB_CDCMR_EMERGING_DISEASE_INFO = "tb_cdcmr_emerging_disease_info";

    public static final String TB_CDCMR_DISEASE_WARNING = "tb_cdcmr_disease_warning";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrEmergingDiseaseInfoMapper emergingDiseaseInfoMapper;

    @Resource
    private TbCdcmrDiseaseWarningMapper diseaseWarningMapper;

    @Resource
    private CommonUtils commonUtils;

    @Value("${generateStrLength:8}")
    private int length;

    @Override
    public List<TreeNode> getMasterData(MasterDataQueryDTO dto) {

        List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = emergingDiseaseInfoMapper.listAll();

        //构建树形结构
        List<TreeNode> root = TreeNode.buildSortTreeByNodeList(diseaseInfoList,
                                                               TbCdcmrEmergingDiseaseInfo::getId,
                                                               TbCdcmrEmergingDiseaseInfo::getDiseaseParentId,
                                                               TbCdcmrEmergingDiseaseInfo::getDiseaseName,
                                                               TbCdcmrEmergingDiseaseInfo::getDiseaseCode,
                                                               TbCdcmrEmergingDiseaseInfo::getOrderFlag);
        //将所有叶子节点的children设为null
        TreeNode.setChildrenToNull(root);
        //排序
        TreeNode.sortTreeBy(root, TreeNode::getOrderFlag);

        return root;
    }

    @Override
    public TreeNode getMasterDataByRootId(String rootId) {
        return null;
    }

    @Override
    public <T extends CommonMasterData> T getMasterDataConfigInfo(MasterDataQueryDTO dto) {
        return null;
    }

    @Override
    @Transactional
    public String addMasterData(MasterDataRecordDTO dto) {

        List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = new ArrayList<>();
        //用户信息拦截
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        List<CommonMasterData> dataRecordList = dto.getRecordList();
        if(CollectionUtil.isNotEmpty(dataRecordList)){
            dataRecordList.forEach(e -> {
                TbCdcmrEmergingDiseaseInfo disease = this.buildEmergingDiseaseInfo(e, uapUserPo);
                diseaseInfoList.add(disease);
            });
        }
        if(CollectionUtil.isNotEmpty(diseaseInfoList)) {
            emergingDiseaseInfoMapper.insertBatch(diseaseInfoList);
            //新增单个数据
            this.syncMasterData(diseaseInfoList.get(0));
            return diseaseInfoList.get(0).getId();
        }
        return null;
    }

    /**
     * 定时执行，每10分钟执行一次
     * */
    @Override
    public void syncMasterData() {
        //重载主数据同步方法
        diseaseWarningMapper.deleteAll(WarningTypeProEnum.EMERGING.getCode());
        this.syncMasterData(null);
    }

    /**
     * 将新发突发主数据同步到disease_warning表
     * */
    @Transactional
    public void syncMasterData(TbCdcmrEmergingDiseaseInfo diseaseInfo) {

        List<TbCdcmrDiseaseWarning> res = new ArrayList<>();

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        //传参为空则查询主数据表中所有新发突发 否则更新指定新发突发的信息
        List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = diseaseInfo == null ? emergingDiseaseInfoMapper.listAll() : Collections.singletonList(diseaseInfo);

        diseaseInfoList.forEach(e -> {
            TbCdcmrDiseaseWarning diseaseWarning = new TbCdcmrDiseaseWarning();
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_DISEASE_WARNING));
            diseaseWarning.setId(id);
            diseaseWarning.setDiseaseCode(e.getDiseaseCode());
            diseaseWarning.setDiseaseName(e.getDiseaseName());
            diseaseWarning.setDiseaseInfoId(e.getId());
            diseaseWarning.setWarningType(WarningTypeProEnum.EMERGING.getCode());
            diseaseWarning.setUpdateTime(new Date());
            diseaseWarning.setUpdaterId(uapUserPo.getId());
            diseaseWarning.setUpdater(uapUserPo.getName());
            diseaseWarning.setDeleteFlag(e.getDeleteFlag());
            diseaseWarning.setStatus(e.getStatus());

            res.add(diseaseWarning);
        });

        //批量插入或者更新disease_warning表
        if (CollectionUtils.isNotEmpty(res)) {
            diseaseWarningMapper.insertOrUpdateBatch(res);
        }
    }

    /**
     * 新发突发基础数据构建
     * */
    private TbCdcmrEmergingDiseaseInfo buildEmergingDiseaseInfo(CommonMasterData record, UapUserPo uapUserPo){

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_DISEASE_INFO));
        TbCdcmrEmergingDiseaseInfo diseaseInfo = new TbCdcmrEmergingDiseaseInfo();

        diseaseInfo.setId(id);
        //查目前疾病code
        List<String> diseaseCodeList = emergingDiseaseInfoMapper.getAllDiseaseCodeBy();
        diseaseInfo.setDiseaseCode(commonUtils.generateSyndromeCode(length, diseaseCodeList));
        diseaseInfo.setDiseaseName(record.getMasterDataName());
        diseaseInfo.setDiseaseParentId(record.getParentDataId());

        //状态默认开启，默认未删除
        diseaseInfo.setStatus(diseaseInfo.getStatus() == null ? Integer.valueOf(StatusEnum.STATUS_ON.getCode()) : diseaseInfo.getStatus());
        diseaseInfo.setDeleteFlag(diseaseInfo.getDeleteFlag() == null ? DeleteFlagEnum.NO.getCode() : diseaseInfo.getDeleteFlag());
        diseaseInfo.setCreateTime(new Date());
        diseaseInfo.setCreator(uapUserPo.getName());
        diseaseInfo.setCreatorId(uapUserPo.getId());
        diseaseInfo.setUpdateTime(new Date());
        diseaseInfo.setUpdater(uapUserPo.getName());
        diseaseInfo.setUpdaterId(uapUserPo.getId());

        return diseaseInfo;
    }

    @Override
    @Transactional
    public void editMasterDataBaseInfo(MasterDataEditRecordDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        TbCdcmrEmergingDiseaseInfo diseaseInfo = new TbCdcmrEmergingDiseaseInfo();
        //基础信息修改
        diseaseInfo.setId(dto.getId());
        diseaseInfo.setDiseaseCode(dto.getMasterDataCode());
        diseaseInfo.setDiseaseName(dto.getMasterDataName());
        diseaseInfo.setDiseaseParentId(dto.getParentDataId());
        diseaseInfo.setUpdateTime(new Date());
        diseaseInfo.setUpdaterId(uapUserPo.getId());
        diseaseInfo.setUpdater(uapUserPo.getName());
        //更新新发突发基础信息
        emergingDiseaseInfoMapper.update(diseaseInfo, new LambdaUpdateWrapper<TbCdcmrEmergingDiseaseInfo>().eq(TbCdcmrEmergingDiseaseInfo::getId, dto.getId()));

        //新发突发主数据更新后，直接同步到disease_warning表
        this.syncMasterData(diseaseInfo);
    }

    @Override
    @Transactional
    public void deleteMasterDataBaseInfo(MasterDataDeleteDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        //判断删除级别
        if(dto.getIsDeleteCurr()) {
            //如果只删除当前层级 : 先判断是否有下级(有-软删该条数据，所有下级向上平移；无-软删该条数据)
            List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = emergingDiseaseInfoMapper.getEmergingDiseaseInfoByIds(Collections.singletonList(dto.getId()));
            //如果当前删除的疾病有子类 则当前疾病子类的父类id、code、name全部设置为当前节点的 父类id、code、name
            if(CollectionUtil.isNotEmpty(diseaseInfoList)) {
                TbCdcmrEmergingDiseaseInfo info = emergingDiseaseInfoMapper.getDiseaseInfoById(dto.getId());
                emergingDiseaseInfoMapper.updateSubDiseaseParent(info, uapUserPo.getId(), uapUserPo.getName());
            }
            //软删除当前疾病
            emergingDiseaseInfoMapper.updateDeleteFlagByIds(Collections.singletonList(dto.getId()), uapUserPo.getId(), uapUserPo.getName());

        } else {
            //如果删除当前层级以及其所有子类
            //查询疾病树状结构
            List<TreeNode> root = this.getMasterData(new MasterDataQueryDTO());
            //根据当前节点id 得到当前节点以及所有子类id，全部做软删除
            List<String> idList = new ArrayList<>();
            for (TreeNode treeNode : root) {
                TreeNode.getAllNodeValue(idList, treeNode, dto.getId());
            }
            if(CollectionUtil.isNotEmpty(idList)) {
                emergingDiseaseInfoMapper.updateDeleteFlagByIds(idList, uapUserPo.getId(), uapUserPo.getName());
            }
        }
        //删除之后同步到disease_warning表
        this.syncMasterData(null);
    }

    @Override
    public void uploadFile(MultipartFile file) {

    }

    @Override
    public CommonMasterData getDiseaseInfoByCode(String masterDataCode) {

        return emergingDiseaseInfoMapper.getDiseaseInfoByCode(masterDataCode);
    }
}
