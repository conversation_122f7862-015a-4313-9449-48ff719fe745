package com.iflytek.cdc.admin.datamodel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

@Data
public class DataFormTemplateQueryDTO {

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageIndex;

    @ApiModelProperty(value = "页面大小", example = "20")
    private Integer pageSize;

    @ApiModelProperty(value = "配置表id")
    private String formTemplateDetailId;

    @ApiModelProperty(value = "模型id")
    private String modelId;

    @ApiModelProperty(value = "模型信息")
    private String configInfo;

    @ApiModelProperty(value = "模型分组")
    private String modelGroup;

    @ApiModelProperty(value = "系统appCode")
    private String formTemplateCode;

    @ApiModelProperty(value = "系统名称")
    private String formTemplateName;
}
