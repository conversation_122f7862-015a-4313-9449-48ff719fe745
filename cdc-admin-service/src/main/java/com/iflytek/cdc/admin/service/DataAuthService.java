package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.DataAuthVO;
import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import com.iflytek.cdc.admin.entity.CascadeVO;

import java.util.List;

public interface DataAuthService {
    void add(DataAuthVO vo);

    List<TbCdcmrUserDataAuth> getSyndromeDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getInfectedDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getSchoolSymptomDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getPoisonDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getOutpatientDataAuthByLoginUserId(String loginUserId);


    List<TbCdcmrUserDataAuth> getUnknownReasonDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getPreventionControlDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getCustomizedDataAuthByLoginUserId(String loginUserId);

    List<TbCdcmrUserDataAuth> getDataAuthByLoginUserIdAndDataType(String loginUserId, Integer dataType);
    List<CascadeVO> getConfiguredDiseaseCodesByAuth(String loginUserId, Integer dataType);
}
