package com.iflytek.cdc.admin.service.brief;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyApproveDto;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyQueryDto;
import com.iflytek.cdc.admin.entity.brief.BriefTakeApply;
import com.iflytek.cdc.admin.vo.brief.BriefTakeApplyVo;

public interface BriefTakeApplyService extends IService<BriefTakeApply> {
    BriefTakeApply create(BriefTakeApply briefTakeApply, String loginUserId);

    BriefTakeApplyVo queryById(String id, String loginUserId);

    PageInfo<BriefTakeApplyVo> queryList(BriefTakeApplyQueryDto dto, String loginUserId);

    Boolean updateApprove(BriefTakeApplyApproveDto dto, String loginUserId);
}
