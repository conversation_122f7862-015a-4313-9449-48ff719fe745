package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.google.common.reflect.TypeToken;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsReminderTaskSettingDetail;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsReminderTaskSettingDetailMapper;
import com.iflytek.cdc.admin.customizedapp.service.ReminderTaskSettingDetailService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReminderTaskSettingDetailServiceImpl extends CdcServiceBaseImpl<TbCdccsReminderTaskSettingDetailMapper, TbCdccsReminderTaskSettingDetail> implements ReminderTaskSettingDetailService {
    @Override
    public void saveByTaskSetting(TbCdccsTaskSetting taskSetting, List<TbCdccsReminderTaskSettingDetail> details) {
        details.forEach(d -> {
            if (d.getReminderCycleSettingList() != null){
                d.setReminderCycleSettingJson(gson.toJson(d.getReminderCycleSettingList()));
            }
        });
        batchSaveByForeignKey(taskSetting,
                TbCdccsTaskSetting::getId,
                TbCdccsReminderTaskSettingDetail::getTaskSettingId,
                (t, d) ->{
                    d.setTaskSettingId(t.getId());
                },
                details);
    }

    @Override
    public List<TbCdccsReminderTaskSettingDetail> listByTaskSettingId(String taskSettingId) {
        List<TbCdccsReminderTaskSettingDetail> list = list(lambdaQueryWrapper().
                eq(TbCdccsReminderTaskSettingDetail::getTaskSettingId, taskSettingId)
                .orderByDesc(TbCdccsReminderTaskSettingDetail::getCreateTime).orderByDesc(TbCdccsReminderTaskSettingDetail::getId));
        list.forEach(d -> {
            if (StringUtils.isNotEmpty(d.getReminderCycleSettingJson())){
                d.setReminderCycleSettingList(gson.fromJson(d.getReminderCycleSettingJson(), new TypeToken<List<TbCdccsReminderTaskSettingDetail.ReminderCycleSetting>>(){}.getType()));
            }
        });
        return list;
    }

    @Override
    public void deleteByTaskSettingId(String taskSettingId) {
        remove(lambdaQueryWrapper().eq(TbCdccsReminderTaskSettingDetail::getTaskSettingId, taskSettingId));
    }

    @Override
    public List<TbCdccsReminderTaskSettingDetail> listByTaskSettingIds(List<String> taskIds) {
        List<TbCdccsReminderTaskSettingDetail> list = list(lambdaQueryWrapper().in(TbCdccsReminderTaskSettingDetail::getTaskSettingId, taskIds));
        list.forEach(d -> {
            if (StringUtils.isNotEmpty(d.getReminderCycleSettingJson())){
                d.setReminderCycleSettingList(gson.fromJson(d.getReminderCycleSettingJson(), new TypeToken<List<TbCdccsReminderTaskSettingDetail.ReminderCycleSetting>>(){}.getType()));
            }
        });
        return list;
    }
}
