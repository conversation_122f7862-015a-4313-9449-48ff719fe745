package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeMonitorConfig;
import com.iflytek.cdc.admin.model.mr.dto.SyndromeMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.service.province.SyndromeProcessMonitorService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 症候群 圈定病例范围
 * */
@Api(tags = "省统筹 - 监测症候群 监测病例定义")
@RequestMapping("/pt/{version}/syndromeMonitor")
@RestController
public class SyndromeMonitorController {

    @Resource
    private SyndromeProcessMonitorService syndromeProcessMonitorService;

    @PostMapping("/getSyndromeTreeInfo")
    @ApiOperation("查询症候群结构树")
    public List<SyndromeDiseaseInfoVO> getSyndromeTreeInfo(){

        return syndromeProcessMonitorService.getSyndromeTreeInfo();
    }

    @PostMapping("/editSubSyndromeInfo")
    @ApiOperation("编辑子类症候群信息")
    public String editSubSyndromeInfo(@RequestBody TbCdcmrSyndromeDiseaseInfo syndromeDiseaseInfo){

        return syndromeProcessMonitorService.editSubSyndromeInfo(syndromeDiseaseInfo);
    }

    @GetMapping("/deleteSubSyndromeInfo")
    @ApiOperation("删除子类症候群信息")
    public void deleteSubSyndromeInfo(@RequestParam String id){

        syndromeProcessMonitorService.deleteSubSyndromeInfo(id);
    }

    @PostMapping("/getSyndromeInfoConfigs")
    @ApiOperation("查询该症候群下监测病例定义配置")
    public List<TbCdcmrSyndromeMonitorConfig> getSyndromeInfoConfigs(@RequestBody SyndromeMonitorConfigQueryDTO queryDTO){

        return syndromeProcessMonitorService.getSyndromeInfoConfigs(queryDTO);
    }

    @PostMapping("/editSyndromeProcessDefinition")
    @ApiOperation("编辑症候群病例定义配置")
    @OperationLogAnnotation(operationName = "编辑症候群病例定义配置")
    public void editSyndromeProcessDefinition(@RequestParam String diseaseInfoId,
                                              @RequestBody List<TbCdcmrSyndromeMonitorConfig> syndromeMonitorConfigs){

        syndromeProcessMonitorService.editSyndromeProcessDefinition(diseaseInfoId, syndromeMonitorConfigs);
    }

    @GetMapping("/getDiagnoseCodeList")
    @ApiOperation("获取诊断编码")
    public List<DiagnoseListVO> getDiagnoseCodeList(){

        return syndromeProcessMonitorService.getDiagnoseCodeList();
    }

    @GetMapping("/getSyndromeDiseaseCodeByName")
    @ApiOperation("根据症候群名称获取症候群code以及其子类code")
    public List<String> getSyndromeDiseaseCodeByName(@RequestParam String diseaseName){

        return syndromeProcessMonitorService.getSyndromeDiseaseCodeByName(diseaseName);
    }

}
