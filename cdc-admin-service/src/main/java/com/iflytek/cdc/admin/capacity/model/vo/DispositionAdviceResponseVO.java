package com.iflytek.cdc.admin.capacity.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("处置建议")
public class DispositionAdviceResponseVO {
    @ApiModelProperty("专家指导")
    private List<String> expertGuidance;//专家指导
    @ApiModelProperty("报告及发布")
    private List<String> reportAndRelease;//报告及发布
    @ApiModelProperty("监测及救治")
    private List<String> monitoringAndTreatment;//监测及救治
    @ApiModelProperty("流行病学调查")
    private List<String> epidemiologicalInvestigation;//流行病学调查
    @ApiModelProperty("实验室监测")
    private List<String> laboratoryTesting;//实验室监测
    @ApiModelProperty("现场处置")
    private List<String> onSiteManagement;//现场处置
    @ApiModelProperty("预防措施")
    private List<String> preventiveMeasures;//预防措施
    @ApiModelProperty("地方管控")
    private List<String> localControl;//地方管控
    @ApiModelProperty("应急物资储备")
    private List<Materials> emergencySupplies;//应急物资储备




    @Data
    public static class Materials {

        @JsonProperty("物资种类")
        public String 物资种类;

        @JsonProperty("适用要求")
        public String 适用要求;

        @JsonProperty("推荐储备数量")
        public String 推荐储备数量;

        @JsonProperty("单位")
        public String 单位;

    }
}
