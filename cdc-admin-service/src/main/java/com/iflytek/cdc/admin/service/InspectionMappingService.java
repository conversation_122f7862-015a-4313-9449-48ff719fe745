package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.InspectionMappingPageVO;
import com.iflytek.cdc.admin.dto.InspectionMappingVO;

import java.util.List;

public interface InspectionMappingService {

    void batchInsert(List<InspectionMappingVO> recordList, String loginUserId, String infectedCode);

    List<InspectionMappingVO> getList(String infectedCode);

    PageInfo<InspectionMappingPageVO> getPageList(Integer pageIndex, Integer pageSize, String diseaseCode, String diseaseType);
}
