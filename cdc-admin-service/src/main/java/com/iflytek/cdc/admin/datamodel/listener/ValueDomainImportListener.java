package com.iflytek.cdc.admin.datamodel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.iflytek.cdc.admin.datamodel.model.dto.ValueDomainImportDTO;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 值域导入监听器
 * 用于处理 Excel 导入过程中的数据验证和转换
 */
@Slf4j
public class ValueDomainImportListener extends AnalysisEventListener<ValueDomainImportDTO> {

    private final String dataDictId;
    private final String dataDictName;
    private final List<TbCdcdmDataDictValue> dataDictValues;
    private final Map<String, TbCdcdmDataDictValue> nodeMap; // 用于存储节点的映射关系
    private int maxLevel = 0;

    public ValueDomainImportListener(String dataDictId, String dataDictName) {
        this.dataDictId = dataDictId;
        this.dataDictName = dataDictName;
        this.dataDictValues = new ArrayList<>();
        this.nodeMap = new HashMap<>();
    }

    @Override
    public void invoke(ValueDomainImportDTO data, AnalysisContext context) {
        // 跳过空行
        if (data.isEmptyRow()) {
            return;
        }

        // 验证数据
        validateData(data);

        // 转换数据
        convertToDataDictValues(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("值域导入完成，共导入 {} 条数据", dataDictValues.size());
    }

    /**
     * 验证导入数据
     */
    private void validateData(ValueDomainImportDTO data) {
        int maxLevel = data.getMaxLevel();
        if (maxLevel == 0) {
            return;
        }

        // 更新最大层级数
        this.maxLevel = Math.max(this.maxLevel, maxLevel);

        // 验证每个层级的数据
        for (int i = 1; i <= maxLevel; i++) {
            String value = data.getLevelValue(i);
            if (StringUtils.isBlank(value)) {
                throw new MedicalBusinessException(String.format("第%d层级数据不能为空", i));
            }
        }
    }

    /**
     * 转换数据为值域字典值
     */
    private void convertToDataDictValues(ValueDomainImportDTO data) {
        int maxLevel = data.getMaxLevel();
        if (maxLevel == 0) {
            return;
        }

        // 处理每一层级的数据
        for (int level = 1; level <= maxLevel; level++) {
            String value = data.getLevelValue(level);
            if (StringUtils.isBlank(value)) {
                continue;
            }

            // 生成唯一标识
            String key = generateKey(data, level);
            
            // 检查是否已存在
            if (nodeMap.containsKey(key)) {
                continue;
            }

            // 创建值域字典值
            TbCdcdmDataDictValue dictValue = new TbCdcdmDataDictValue();
            dictValue.setDataDictId(dataDictId);
            dictValue.setCode(value);
            dictValue.setName(value);
            
            // 设置父级ID
            if (level > 1) {
                String parentKey = generateKey(data, level - 1);
                TbCdcdmDataDictValue parentNode = nodeMap.get(parentKey);
                if (parentNode != null) {
                    dictValue.setParentId(parentNode.getCode()); // 临时使用code作为父级ID
                }
            }

            // 添加到结果列表
            dataDictValues.add(dictValue);
            
            // 记录当前节点
            nodeMap.put(key, dictValue);
        }
    }

    /**
     * 生成唯一标识
     */
    private String generateKey(ValueDomainImportDTO data, int level) {
        StringBuilder key = new StringBuilder();
        for (int i = 1; i <= level; i++) {
            if (i > 1) {
                key.append("_");
            }
            key.append(data.getLevelValue(i));
        }
        return key.toString();
    }

    /**
     * 获取导入的数据
     */
    public List<TbCdcdmDataDictValue> getDataDictValues() {
        return dataDictValues;
    }

    /**
     * 获取最大层级数
     */
    public int getMaxLevel() {
        return maxLevel;
    }
} 