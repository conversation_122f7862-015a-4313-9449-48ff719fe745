package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.*;
import com.iflytek.cdc.admin.dto.amap.inputtips.Tips;
import com.iflytek.cdc.admin.dto.amap.search.PoisV2;
import com.iflytek.cdc.admin.entity.AmapAddressDetailMapping;

import java.util.List;

/**
 * @ClassName AmapService
 * @Description 高德地图 RestfulApi接口封装与解析
 * <AUTHOR>
 * @Date 2021/6/11 9:49
 * @Version 1.0
 */
public interface AmapService {

    /**
     * 地址智能解析
     *
     * @param addressFuzzyNormalizationDTO
     * @param loginUserId
     * @return
     */
    AddressStandardVo fuzzyAddressNormalization(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId);
    //取消根据身份证号和电话号码确定所在城市的逻辑
    AddressStandardVo fuzzyAddressNormalizationSimple(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId);

    AddressStandardVo fuzzyCompanyNormalization(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId);

    /**
     * 高德地图 搜索服务-关键字查询  搜索POI
     *
     * @param keywords 查询关键字
     * @param city     查询城市
     * @return
     */
    String search(String keywords, String city);
    PoisV2 searchV2(String keywords, String city, String type);

    PoisV2 searchV2ById(String id);

    /**
     * 高德地图 逆地理编码
     *
     * @param latitudeAndLongitude 经纬度字符串(经度,纬度)
     * @return
     */
    AmapAddressDetailMapping geocode(String latitudeAndLongitude);

    /**
     * 地址智能解析 分页
     *
     * @param addressFuzzyNormalizationLatestDTO
     * @return
     */
    PageData<AddressStandardLatestVo> fuzzyAddressNormalizationLatest(AddressFuzzyNormalizationLatestDTO addressFuzzyNormalizationLatestDTO);

    /**
     * 根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码
     *
     * @param longitudeAndLatitudeDto
     * @return
     */
    AddressStandardLatestVo getAreaInfoByLongitudeAndLatitude(LongitudeAndLatitudeDto longitudeAndLatitudeDto);

    List<PoisV2> inputTips(String keyWords, String areaCode);

    List<Tips> inputTipsV2(String keyWords, String areaCode);

    void manualMapping(String loginUserId, PoisV2 poisV2);
}