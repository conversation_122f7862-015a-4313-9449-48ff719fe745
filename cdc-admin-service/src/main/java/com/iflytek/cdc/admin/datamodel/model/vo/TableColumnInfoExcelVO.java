package com.iflytek.cdc.admin.datamodel.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TableColumnInfoExcelVO {

    @ExcelProperty("表名")
    private String tableName;

    @ExcelProperty("表描述")
    private String tableDesc;

    /**
     * 字段名称
     */
    @ExcelProperty(value = "字段名称")
    private String columnName ;

    /**
     * 字段名称描述
     */
    @ExcelProperty(value = "字段名称描述")
    private String columnDesc ;

    /**
     * 数据类型
     */
    @ExcelProperty(value = "数据类型")
    private String dataType ;

    /**
     * 列的长度
     */
    @ExcelProperty(value = "列的长度")
    private Integer columnLength;

    /**
     * 小数位数
     */
    @ExcelProperty(value = "小数位数")
    private Integer columnPrecision;

    /**
     * 是否必填
     */
    @ExcelProperty(value = "是否必填")
    private String isRequired;
}
