package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.apiservice.CdcDataServiceApi;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.config.EsModelConfigProperties;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrExportApproval;
import com.iflytek.cdc.admin.entity.TbCdcmrExportPermissionConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;
import com.iflytek.cdc.admin.enums.ApprovalStatusEnum;
import com.iflytek.cdc.admin.enums.ExportCodeEnum;
import com.iflytek.cdc.admin.enums.ExportTypeEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportApprovalMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionConfigMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportTaskMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrOrgApproveMapper;
import com.iflytek.cdc.admin.service.ExportApplicationService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ExportApplicationServiceImpl implements ExportApplicationService {

    public static final String TABLE_TB_CDCMR_EXPORT_APPROVAL = "tb_cdcmr_export_approval";

    public static final String EXPORT_TASK = "cdc-admin-export";

    @Resource
    private TbCdcmrExportTaskMapper tbCdcmrExportTaskMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrExportApprovalMapper exportApprovalMapper;

    @Resource
    private TbCdcmrExportPermissionConfigMapper tbCdcmrExportPermissionConfigMapper;

    @Resource
    private EsModelConfigProperties esModelConfigProperties;

    @Resource
    private CdcDataServiceApi cdcDataServiceApi;

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private TbCdcmrOrgApproveMapper tbCdcmrOrgApproveMapper;

    @Autowired
    public void setTbCdcmrOrgApproveMapper(TbCdcmrOrgApproveMapper tbCdcmrOrgApproveMapper) {
        this.tbCdcmrOrgApproveMapper = tbCdcmrOrgApproveMapper;
    }

    /**
     * 提交导出申请
     */
    @Override
    public ExportApplicationDTO submit(ExportApplicationDTO dto, String loginUserId) {
        // 校验导出项目代码
        ExportCodeEnum.getByCode(dto.getExportCode());
        // 校验导出类型
        ExportTypeEnum.checkCode(dto.getExportType());

        // 获取该用户所属的机构id
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String orgId = user.getOrgId();

        // 查询权限配置
        ExportConfigDTO exportConfigDTO = tbCdcmrExportPermissionConfigMapper.selectByExportCodeAndOrgId(dto.getExportCode(),orgId);
        if (exportConfigDTO == null) {
            throw new MedicalBusinessException("未找到对应的权限配置");
        }
        // 校验当前用户是否有权限 UAP进行控制 删除判断

        // 创建导出任务DTO
        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setTaskName(dto.getTaskName());
        taskDTO.setExportType(dto.getExportType());
        taskDTO.setAppCode(dto.getModuleType());
        if (StrUtil.isNotBlank(dto.getTaskUrl())) {
            taskDTO.setTaskUrl(dto.getTaskUrl());
        } else {
            taskDTO.setTaskUrl("/pt/v1/exportApplication/submit");
        }
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            taskDTO.setTotalCount(dto.getRecordIds().size());
        }
        taskDTO.setCreatorId(loginUserId);

        // 构造任务参数 JSON
        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("dataRange", dto.getDataRange()); // 数据范围
        taskParams.put("fieldSelection", dto.getFieldSelection()); // 自定义导出字段
        taskParams.put("applicationReason", dto.getApplicationReason()); // 申请原因
        taskParams.put("filterConditions", dto.getFilterConditions()); // 筛选条件
        taskParams.put("conditionDTOList", dto.getConditionDTOList());
        taskParams.put("keyWord", dto.getKeyWord());
        taskParams.put("modelId", dto.getModelId());
        taskParams.put("recordIds", dto.getRecordIds());
        // 附加调试信息
        taskParams.put("esEnabled", esModelConfigProperties.isEnabled());

        // 将任务参数转换为 JSON 字符串
        String taskParamJson = JSON.toJSONString(taskParams);
        taskDTO.setTaskParam(taskParamJson);

        // 调用远程方法添加导出任务，同时触发导出文件生成
        TbCdcmrExportTask task;
        try {
            task = cdcDataServiceApi.addAndRunDetailExportTask(taskDTO);
            if (StrUtil.isNotBlank(task.getErrorDesc()) && StrUtil.isBlank(task.getId())) {
                throw new MedicalBusinessException(task.getErrorDesc());
            }
        } catch (Exception e) {
            log.error("导出任务提交失败: {}", taskDTO, e);
            throw new MedicalBusinessException("导出任务提交失败");
        }

        // 创建导出申请记录
        TbCdcmrExportApproval approval = new TbCdcmrExportApproval();
        this.fillApproverInfo(exportConfigDTO, approval);

        approval.setId(String.valueOf(batchUidService.getUid(TABLE_TB_CDCMR_EXPORT_APPROVAL)));
        approval.setTaskId(task.getId());
        approval.setCreatorId(task.getCreatorId());
        approval.setCreator(task.getCreator());
        approval.setCreatorTime(new Date());
        approval.setUpdaterId(task.getCreatorId());
        approval.setUpdater(task.getCreator());
        approval.setUpdaterTime(new Date());
        approval.setExportCode(dto.getExportCode());

        exportApprovalMapper.insert(approval);

        dto.setId(approval.getId());
        return dto;
    }

    private void fillApproverInfo(ExportConfigDTO exportConfigDTO, TbCdcmrExportApproval approval) {
        // 判断是否需要审批
        if (Boolean.FALSE.equals(exportConfigDTO.getApprovalRequired())) {
            // 不需要审批，直接设置为系统自动审批
            approval.setApprover("系统自动");
            approval.setApprovalTime(new Date());
            approval.setApprovalStatus(ApprovalStatusEnum.NO_APPROVAL_REQUIRED.getCode()); // 使用枚举值
        } else {
            // 需要审批，解析 JSON 数据并设置一级和二级审批人
            String permissionOrgId = exportConfigDTO.getPermissionOrgId();
            List<ApproverDTO> approverConfig = tbCdcmrOrgApproveMapper.selectApproveByPermissionOrgId(permissionOrgId);
            // 提取一级和二级审批人
            List<String> level1Approvers = new ArrayList<>();
            List<String> level2Approvers = new ArrayList<>();
            for (ApproverDTO approverInfo : approverConfig) {
                String id = approverInfo.getId();
                if (approverInfo.getLevel() == 1) {
                    level1Approvers.add(id);
                } else if (approverInfo.getLevel() == 2) {
                    level2Approvers.add(id);
                }
            }
            // 检查是否找到了至少一个一级或二级审批人
            if (level1Approvers.isEmpty()) {
                throw new MedicalBusinessException("未找到一级审批人信息");
            }
            if (level2Approvers.isEmpty() && exportConfigDTO.getApprovalLevels() == 2) {
                throw new MedicalBusinessException("未找到二级审批人信息");
            }
            approval.setLevel1Approver(JSON.toJSONString(level1Approvers));
            approval.setLevel2Approver(JSON.toJSONString(level2Approvers));

            // 待审批（或待一级审批）
            approval.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        }
    }

    /**
     * 校验当前用户是否被禁止导出
     *
     * @param loginUserId      当前登录用户
     * @param permissionConfig 导出权限配置
     * @return 是否禁止导出
     */
    private boolean isExportNotAllowed(String loginUserId, TbCdcmrExportPermissionConfig permissionConfig) {
        String allowedUsersJson = permissionConfig.getAllowedUsers();
        // 如果没有配置允许的用户，默认无权限
        if (allowedUsersJson == null || allowedUsersJson.isEmpty()) {
            return true;
        }
        try {
            List<AllowedUsersDTO> allowedUsers = JSON.parseArray(allowedUsersJson, AllowedUsersDTO.class);
            // 检查当前用户是否在允许的用户列表中
            return allowedUsers.stream().noneMatch(user -> user.getId().equals(loginUserId));
        } catch (Exception e) {
            log.error("解析允许用户列表失败: {}", e.getMessage());
            throw new MedicalBusinessException("解析允许用户列表失败");
        }
    }

    // 获取查询列表
    @Override
    public PageInfo<ExportApplicationListDTO> queryApplicationExportList(ExportApplicationQueryDTO queryDTO) {
        // 校验 exportType
        ExportTypeEnum.checkCode(queryDTO.getExportType());
        // 联表分页查询
        try (Page<ExportApplicationListDTO> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            return PageInfo.of(exportApprovalMapper.queryTaskApprovals(queryDTO));
        }
    }

    /**
     * @return 查询导出记录详情
     */
    @Override
    public ExportApplicationRecordDTO getExportApplicationRecord(String id) {

        TbCdcmrExportApproval exportApproval = exportApprovalMapper.selectById(id);
        if (exportApproval == null) {
            return null;
        }
        TbCdcmrExportTask task = tbCdcmrExportTaskMapper.selectById(exportApproval.getTaskId());

        ExportApplicationRecordDTO dto = new ExportApplicationRecordDTO();

        dto.setId(exportApproval.getId());
        dto.setTaskId(task.getId());
        dto.setTaskName(task.getTaskName());
        dto.setExportType(task.getExportType());
        dto.setStatus(task.getStatus());

        String taskParam = task.getTaskParam();

        try {
            JSONObject taskParamMap = JSON.parseObject(taskParam);

            // 提取 fieldRange
            if (taskParamMap.containsKey("fieldRange")) {
                dto.setDataRange(taskParamMap.getString("fieldRange"));
            }

            // 提取 dataRange
            if (taskParamMap.containsKey("dataRange")) {
                dto.setDataRange(taskParamMap.getString("dataRange"));
            }

            // 提取 fieldSelection 并转换为 Map<String, Object>
            if (taskParamMap.containsKey("fieldSelection")) {
                dto.setFieldSelection(taskParamMap.getObject("fieldSelection", new TypeReference<Map<String, Object>>() {
                }));
            }

            // 提取 applicationReason
            if (taskParamMap.containsKey("applicationReason")) {
                dto.setApplicationReason(taskParamMap.getString("applicationReason"));
            }

            // 提取 applicationReason
            if (taskParamMap.containsKey("modelId")) {
                dto.setModelId(taskParamMap.getString("modelId"));
            }

            // 提取 filterConditions 并转换为 List<String>
            if (taskParamMap.containsKey("filterConditions")) {
                dto.setFilterConditions(taskParamMap.getObject("filterConditions", new TypeReference<List<Map<String, Object>>>() {
                }));
            }

            // 提取 conditionDTOList
            if (taskParamMap.containsKey("conditionDTOList")) {
                dto.setConditionDTOList(taskParamMap.getObject("conditionDTOList", new TypeReference<List<CombinationConditionDTO>>() {
                }));
            }

            // 提取 keyWord
            if (taskParamMap.containsKey("keyWord")) {
                dto.setKeyWord(taskParamMap.getString("keyWord"));
            }

            // 提取 recordIds
            if (taskParamMap.containsKey("recordIds")) {
                dto.setRecordIds(taskParamMap.getObject("recordIds", new TypeReference<List<String>>() {
                }));
            }
        } catch (Exception e) {
            log.error("参数转换失败");
            throw new MedicalBusinessException("参数转换失败");
        }

        dto.setCreatorId(exportApproval.getCreatorId());
        dto.setCreator(exportApproval.getCreator());
        dto.setCreatorTime(exportApproval.getCreatorTime());
        dto.setApprovalStatus(exportApproval.getApprovalStatus());
        dto.setApprovalTime(exportApproval.getApprovalTime());
        dto.setApprover(exportApproval.getApprover());
        dto.setRejectReason(exportApproval.getRejectReason());
        dto.setDataCount(task.getTotalCount());
        dto.setFileSize(task.getAttachmentSize());
        return dto;
    }

    @Override
    public ExportApplicationRecordDTO getExportRecord(String id, String approvalId) {
        ExportApplicationRecordDTO dto = new ExportApplicationRecordDTO();

        if (StringUtils.isNotEmpty(approvalId)) {
            TbCdcmrExportApproval exportApproval = exportApprovalMapper.selectById(approvalId);
            if (exportApproval == null) {
                return null;
            }
            // 需要审批的情况
            if (!exportApproval.getApprovalStatus().equals(ApprovalStatusEnum.NO_APPROVAL_REQUIRED.getCode())) {
                // 如果不是NO_APPROVAL_REQUIRED，执行以下设置操作
                dto.setId(exportApproval.getId());
                dto.setCreatorId(exportApproval.getCreatorId());
                dto.setCreator(exportApproval.getCreator());
                dto.setCreatorTime(exportApproval.getCreatorTime());
                dto.setApprovalStatus(exportApproval.getApprovalStatus());
                dto.setApprovalTime(exportApproval.getApprovalTime());
                dto.setApprover(exportApproval.getApprover());
                dto.setRejectReason(exportApproval.getRejectReason());
            }
        }
        TbCdcmrExportTask task = tbCdcmrExportTaskMapper.selectById(id);
        dto.setTaskId(task.getId());
        dto.setTaskName(task.getTaskName());
        dto.setExportType(task.getExportType());
        dto.setStatus(task.getStatus());

        String taskParam = task.getTaskParam();

        try {
            JSONObject taskParamMap = JSON.parseObject(taskParam);

            // 提取 fieldRange
            if (taskParamMap.containsKey("fieldRange")) {
                dto.setDataRange(taskParamMap.getString("fieldRange"));
            }

            // 提取 dataRange
            if (taskParamMap.containsKey("dataRange")) {
                dto.setDataRange(taskParamMap.getString("dataRange"));
            }

            // 提取 fieldSelection 并转换为 Map<String, Object>
            if (taskParamMap.containsKey("fieldSelection")) {
                dto.setFieldSelection(taskParamMap.getObject("fieldSelection", new TypeReference<Map<String, Object>>() {
                }));
            }

            // 提取 applicationReason
            if (StringUtils.isNotEmpty(approvalId)) {
                TbCdcmrExportApproval exportApproval = exportApprovalMapper.selectById(approvalId);
                // 如果获取到审批信息，并且需要审批
                if (exportApproval != null && !exportApproval.getApprovalStatus().equals(ApprovalStatusEnum.NO_APPROVAL_REQUIRED.getCode())) {
                    if (taskParamMap.containsKey("applicationReason")) {
                        dto.setApplicationReason(taskParamMap.getString("applicationReason"));
                    }
                }
            }
            // 提取 applicationReason
            if (taskParamMap.containsKey("modelId")) {
                dto.setModelId(taskParamMap.getString("modelId"));
            }

            // 提取 filterConditions 并转换为 List<String>
            if (taskParamMap.containsKey("filterConditions")) {
                dto.setFilterConditions(taskParamMap.getObject("filterConditions", new TypeReference<List<Map<String, Object>>>() {
                }));
            }

            // 提取 conditionDTOList
            if (taskParamMap.containsKey("conditionDTOList")) {
                dto.setConditionDTOList(taskParamMap.getObject("conditionDTOList", new TypeReference<List<CombinationConditionDTO>>() {
                }));
            }

            // 提取 keyWord
            if (taskParamMap.containsKey("keyWord")) {
                dto.setKeyWord(taskParamMap.getString("keyWord"));
            }

            // 提取 recordIds
            if (taskParamMap.containsKey("recordIds")) {
                dto.setRecordIds(taskParamMap.getObject("recordIds", new TypeReference<List<String>>() {
                }));
            }
        } catch (Exception e) {
            log.error("参数转换失败");
            throw new MedicalBusinessException("参数转换失败");
        }
        dto.setDataCount(task.getTotalCount());
        dto.setFileSize(task.getAttachmentSize());
        return dto;
    }
}
