package com.iflytek.cdc.admin.datamodel.model.dto;

import com.iflytek.cdc.admin.annotation.ExcelColumn;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class UploadDataModelInfo {
    /**
     * export type IFormItemType =
     *   | 'input'
     *   | 'textarea'
     *   | 'number'
     *   | 'date'
     *   | 'datePicker'
     *   | 'radio'
     *   | 'checkbox'
     *   | 'cascader';
     */
    public static final Map<String, String> SHOW_TYPE_MAP = new HashMap<>();

    static {
        SHOW_TYPE_MAP.put("单行文本", "input");
        SHOW_TYPE_MAP.put("级联选择", "cascader");
        SHOW_TYPE_MAP.put("单选", "radio");
        SHOW_TYPE_MAP.put("日期时间", "datePicker");
        SHOW_TYPE_MAP.put("日期", "date");
        SHOW_TYPE_MAP.put("数值", "number");
        SHOW_TYPE_MAP.put("多行文本", "textarea");
        SHOW_TYPE_MAP.put("多选", "checkbox");
    }

    //是否判断
    public static final Map<String, Boolean> JUDGE_BOOLEAN = new HashMap<>();
    static {
        JUDGE_BOOLEAN.put("是", true);
        JUDGE_BOOLEAN.put("否", false);
    }

    @ExcelColumn(name = "数据模型", column = 0)
    private String modelName; // 数据模型名称（如门诊病历、住院病历、传染病监测病例等）

    @ExcelColumn(name = "表单名称", column = 1)
    private String formName; // 表单名称（如就诊主数据、门急诊病历、西药处方等）

    @ExcelColumn(name = "表单可重复", column = 2)
    private String formRepeatFlag; // 表单可重复性标识（1是，0否）

    @ExcelColumn(name = "表单最大可重复次数", column = 3)
    private String formRepeatCnt; // 表单最大可重复次数

    @ExcelColumn(name = "组名称", column = 4)
    private String groupName; // 组名称（如就诊信息、患者基本信息等）

    @ExcelColumn(name = "组可重复", column = 5)
    private String groupRepeatFlag; // 组可重复性标识（1是，0否）

    @ExcelColumn(name = "组最大可重复次数", column = 6)
    private String groupRepeatCnt; // 组最大可重复次数

    @ExcelColumn(name = "字段名称", column = 7)
    private String columnNameDesc; // 字段名称描述

    @ExcelColumn(name = "是否必填", column = 8)
    private String requireFlag; // 必填标识（1是，0否）

    @ExcelColumn(name = "组件", column = 9)
    private String component; // 组件（如单行文本、级联选择等）

    @ExcelColumn(name = "值域", column = 10)
    private String dataValue; // 值域

    @ExcelColumn(name = "文本格式", column = 11)
    private String textFormat; // 文本格式

    @ExcelColumn(name = "固定文本长度", column = 12)
    private String fixedTextLength; // 固定文本长度

    @ExcelColumn(name = "最大允许文本长度", column = 13)
    private String textLength; // 最大允许文本长度

    @ExcelColumn(name = "默认提示文字", column = 14)
    private String defaultText; // 默认提示文字

    @ExcelColumn(name = "仅整数", column = 15)
    private String justIntegerFlag; // 仅整数标识（1是，0否）

    @ExcelColumn(name = "允许负数", column = 16)
    private String hasNegativeFlag; // 允许负数标识（1是，0否）

    @ExcelColumn(name = "最小值", column = 17)
    private String minValue; // 最小值

    @ExcelColumn(name = "最大值", column = 18)
    private String maxValue; // 最大值

    @ExcelColumn(name = "允许未来", column = 19)
    private String hasFutureFlag; // 允许未来

    @ExcelColumn(name = "最早日期", column = 20)
    private String earliestDate; // 最早日期

    @ExcelColumn(name = "选项展示", column = 21)
    private String showType; //

    @ExcelColumn(name = "备注", column = 22)
    private String note; // 备注

    @ExcelColumn(name = "选项值标准", column = 23)
    private String option; // 选项值标准

    @ExcelColumn(name = "选项值", column = 24)
    private String optionValue; // 选项值

    @ExcelColumn(name = "表名", column = 25)
    private String tableName; // ads表

    @ExcelColumn(name = "字段名", column = 26)
    private String columnName; // ads字段


}