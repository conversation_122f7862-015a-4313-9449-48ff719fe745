package com.iflytek.cdc.admin.constant;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/24
 */
public class  Constants {

    public static final String NONE_CLASSIFY = "未分级";

    private Constants() {
    }
    public static final String SYS_START_DATE_STR = "2000-01-01";
    public static final Date SYS_START_DATE = DateUtil.parse(SYS_START_DATE_STR);
    public static final String SYS_END_DATE_STR = "2099-12-31";
    public static final Date SYS_END_DATE = DateUtil.parse(SYS_END_DATE_STR);

    /**
     * 启用
     **/
    public static final String YES_USE = "1";

    /**
     * 禁用
     **/
    public static final String NO_USE = "0";

    /**
     * 已删除
     **/
    public static final String YES_DELETE = "1";

    /**
     * 未删除
     **/
    public static final String NO_DELETE = "0";

    public static final String TAG_DELIMITER = "|";
    public static final String TAG_SPLIT = "\\|";
    public static final String COMMA = ",";

    public static final String SPLIT_POINT = "\\.";

    public static final String CURRENT_VERSION = "v1";

    public static final String DEF_CODE = "1";

    public static final String CHARSET_UTF8 = "UTF-8";
    /**
     * ACCESS_TOKEN
     */
    public static final String ACCESS_TOKEN = "access_token";

    public static final String EXCEL_ADDRESS_TEMPLATE_PATH="excel/address_excel_template.xlsx";

    public static final String EXCEL_ADDRESS_TEMPLATE_NAME="address_excel_template.xlsx";

    public static final  String EXCEL_TEMPLATE_PATH="excel/organization-template.xlsx";

    public static final  String EXCEL_TEMPLATE_NAME="organization-template.xlsx";

    public static final  String MATCH_ALL="all";

    public static final  String MATCH_SINGLE="single";

    public static final  String FAIL_EXCEL_NAME="导入失败结果";

    /**
     * EXCEL数据失败原因
     */
    public static final String EXCEL_DATA_FAIL_REASON = "必填字段不能为空";


    public static final  String  REDIS_KEY="cdc:cdc-admin-service:";

    public static final  Integer MAX_DOWN_ORG_SIZE=1000;

    public static final  Integer DOWN_ORG_GROUP_SIZE=100;

    public static String PACKAGE_URL = "cdc/setup/";

    public static final Integer SUCCESS_CODE = 200;

    public static final Integer ERROR_CODE = 500;

    public static final String SUCCESS_MSG = "操作成功";

    public static final String ERROR_MSG = "操作失败";
    
    public static final String PARAM_ERROR = "参数异常";

    public static final  String WRAN_RULE_FILENAME="传染病预警规则.xlsx";

    public static final String WRAN_RULE_EXCEL_TITLE = "传染病预警规则";

    public static final String SC_WRAN_RULE_FILENAME = "学校症状预警规则.xlsx";

    public static final String SC_WRAN_RULE_EXCEL_TITLE = "学校症状预警规则";

    public static final String POISONING_WRAN_RULE_FILENAME = "中毒类预警规则.xlsx";

    public static final String POISONING_WRAN_RULE_EXCEL_TITLE = "中毒类预警规则";

    public static final String UNKNOWN_REASON_WRAN_RULE_FILENAME = "不明原因类预警规则.xlsx";

    public static final String UNKNOWN_REASON_WRAN_RULE_EXCEL_TITLE = "不明原因类预警规则";

    public static final String PREVENTION_CONTROL_WRAN_RULE_FILENAME = "联防联控预警规则.xlsx";

    public static final String PREVENTION_CONTROL_WRAN_RULE_EXCEL_TITLE = "联防联控预警规则";

    public static final String CUSTOMIZED_WRAN_RULE_FILENAME = "自定义预警规则.xlsx";

    public static final String CUSTOMIZED_WRAN_RULE_EXCEL_TITLE = "自定义预警规则";

    public static final String COMMON_STRNUM_ZERO = "0";

    public static final String COMMON_STRNUM_ONE = "1";

    public static final String COMMON_STRNUM_TWO = "2";

    public static final String COMMON_STRNUM_THREE = "3";

    public static final String COMMON_STRNUM_FOUR = "4";

    public static final  String COMMON_STRNUM_FIVE="5";

    public static final  String TIME_UNIT_DAY="d";

    public static final  String UPLOAD_FILE_TYPE_PDF="pdf";

    public static final  String SPLIT_VAR=",";

    public static final String SS_OUTCALL_TEMPLATE = "【{}】【{}】科室:{},患者姓名:{},家长姓名:{},身份证号:{},性别:{},出生日期:{},年龄:{},单位:{},联系电话:{},联系地址:{},职业:{},病例分类:{},发病日期:{},诊断日期:{},诊断:【{}】,填报医生:{},填报时间:{},备注:{}";

    public static final String STD_CATEGORY_COMPANY = "单位标化";
    public static final String STD_CATEGORY_ADDRESS = "地址标化";

    public static final String STD_TYPE_COMMON = "通用";

    public static final String TYPE_STD_WORD = "STD_WORD:";

    // 数据权限类型
    public static final int DATA_AUTH_SYNDROME = 0; //症候群
    public static final int DATA_AUTH_INFECTIOUS_DISEASE = 1; //传染病
    public static final int DATA_AUTH_SCHOOL_SYMPTOM = 2; //学校症状
    public static final int DATA_AUTH_POISON = 3; //中毒症状

    public static final int DATA_AUTH_OUTPATIENT = 4; //门诊

    public static final int DATA_AUTH_UNKNOWN_REASON = 5; //不明原因

    public static final int DATA_AUTH_PREVENTION_CONTROL = 6; //联防联控

    public static final int DATA_AUTH_CUSTOMIZED = 7; //自定义

    public static final String DESENSITIZATION_TYPE = "1"; //脱敏

    public static final String WATERMARK_TYPE = "2";

    public static final String SHORT_DATE_FORMAT = "yyyy-MM-dd";

    public static final String ADMINISTRATIVE_REGION_EXPORT_NAME = "行政区划维护";
    public static final String GROUP_REGION_EXPORT_NAME = "行政区划-社区小区-维护";

    public static final String INFECTED = "infected";

    public static final String SYNDROME = "syndrome";

    public static final String SYMPTOM = "symptom";

    public static final String POISON = "poison";

    public static final String UNKNOWN_REASON = "unknownReason";

    public static final String PREVENTION_CONTROL = "preventionControl";

    public static final String OUTPATIENT = "outpatient";

    public static final String ID = "id";

    public static final Integer STATUS_ON = 1;

    public static final Integer STATUS_OFF = 0;

    public static final Integer NOT_DELETED = 0;
    public static final Integer IS_DELETED = 1;

    public static final String LOGIN_USER_ID = "loginUserId";

    public static final String UPLOAD_FORM_FILE_TYPE = "xlsx";

    public static final String UPLOAD_PDF_FILE_TYPE = "pdf";
    public static final String DATA_SOURCE_CONFIG_RPT = "1";
    public static final String DATA_SOURCE_CONFIG_MED = "2";
    public static final String POPULATION_DATA_INFO_FILENAME = "人口数据信息.xlsx";

    public static final String TABLE_COLUMN_INFO_FILENAME = "表列信息模板.xlsx";

    public static final String DISEASE_INFLUENCE_FACTOR = "疾病影响因素分析数据模板.xlsx";

    public static final String VERSION_DRAFT = "草稿";

    public static final String VERSION_STATUS_NOT_PUBLISHED = "未发布";

    public static final String VERSION_STATUS_PUBLISHED = "已发布";

    public static final String VERSION_STATUS_EXPIRED = "已失效";

    public static final String FORM_TEMPLATE_DETAIL_ID = "formTemplateDetailId";

    public static final String MODEL_VERSION_ID = "modelVersionId";
    public static final String MODEL_ID = "modelId";

    public static final String DELETE_FLAG_STR_0 = "0";

    public static final String DELETE_FLAG_STR_1 = "1";

    public static final String STR_ZERO = "0";

    public static final String STR_ONE = "1";

    public static final int TASK_STATUS_START = 1;

    public static final int TASK_STATUS_DONE = 2;

    public static final int TASK_STATUS_ERROR = 3;

    /**
     * 公共处理时限类型
     * */
    public static final String COMMON_TIME_LIMIT = "common";

    //中毒类
    public static final class WarnRule {
        public static final String WARN_GENERATE_TYPE_MEDICAL = "1";//根据病历数量
        public static final String WARN_GENERATE_TYPE_DEATH = "2";//根据死亡数量
        public static final String NULL = "/";

        public static final String DEATH_COUNT = "死亡数量";
        public static final String MEDICAL_COUNT = "病例数量";
        public static  String getGenerateType(String medicalOrDeath){
            if (WARN_GENERATE_TYPE_MEDICAL.equals(medicalOrDeath)){
                return MEDICAL_COUNT;
            }else if (WARN_GENERATE_TYPE_DEATH.equals(medicalOrDeath)){
                return DEATH_COUNT;
            }else {
                return NULL;
            }
        }

    }

    public static final class BriefReport {
        
        public static final String INDEX_SCOPE_ZW = "ZW";
        public static final String INDEX_SCOPE_FJ = "FJ";
        public static final String LOCATION_TYPE_HEAD = "HEAD";
        public static final String LOCATION_TYPE_TEXT = "TEXT";
        public static final String ATTACHMENT_FLAG_YES = "1";
        
        /**
         * 传染病疫情监测报告系统
         */
        public static final String BUSINESS_TYPE_INFECTED = "infected";
        /**
         * 症候群监测报告系统
         */
        public static final String BUSINESS_TYPE_SYNDROME = "syndrome";
        /**
         * 传染病报卡督导管理子系统
         */
        public static final String BUSINESS_TYPE_REPORT_CARD = "reportCard";
        /**
         * 突发公共卫生事件管理系统
         */
        public static final String BUSINESS_TYPE_EMERGENCY = "emergency";

        public static String getBusinessTypeDesc(String businessType) {
            if (BUSINESS_TYPE_INFECTED.equals(businessType)) {
                return "传染病疫情监测报告系统";
            }
            if (BUSINESS_TYPE_SYNDROME.equals(businessType)) {
                return "症候群监测报告系统";
            }
            if (BUSINESS_TYPE_REPORT_CARD.equals(businessType)) {
                return "传染病报卡督导管理子系统";
            }
            if (BUSINESS_TYPE_EMERGENCY.equals(businessType)) {
                return "突发公共卫生事件管理系统";
            }
            return businessType + "未知";
        }
    }
}
