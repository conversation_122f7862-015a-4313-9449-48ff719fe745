package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.UserConfigDto;
import com.iflytek.cdc.admin.entity.UapUserExtend;
import com.iflytek.cdc.admin.mapper.UserConfigMapper;
import com.iflytek.cdc.admin.model.mr.vo.UserInfoVO;
import com.iflytek.cdc.admin.service.LoginUserService;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.cdc.admin.common.vo.uap.UapLazyOrgInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.common.vo.uap.UapUserSearchQueryDTO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgTreeNode;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/25 10:17
 **/
@RestController
@Slf4j
public class LoginUserController {

    @Resource
    private UserConfigMapper userConfigMapper;

    @Resource
    private LoginUserService loginUserService;

    private  final UapUserService uapUserService;
    private final BatchUidService batchUidService;

    private final UapOrgService uapOrgService;

    @Resource
    private UapServiceApi uapServiceApi;

    public LoginUserController(UapUserService uapUserService, BatchUidService batchUidService, UapOrgService uapOrgService) {
        this.uapUserService = uapUserService;
        this.batchUidService = batchUidService;
        this.uapOrgService = uapOrgService;
    }

    @GetMapping("/{version}/pt/user/info")
    @ApiOperation("获取登录用户信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @Deprecated
    public UapUserExtend getLoginUserInfo(@PathVariable String version, @RequestParam String loginUserId) {
        return uapUserService.getUserInfo(loginUserId);
    }

    @GetMapping("/{version}/pt/getUserInfo")
    @ApiOperation("获取登录用户信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UserInfoVO getUserInfo(@PathVariable String version, @RequestParam String loginUserId) {
        return loginUserService.getUserInfo(loginUserId);
    }


    @GetMapping("/{version}/pt/org/getOrgTree")
    @ApiOperation("查询下属机构树")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UapOrgTreeNode getOrgTree(@PathVariable String version, @RequestParam String loginUserId) {
        return uapOrgService.getOrgTree(loginUserId);
    }

    @GetMapping("/{version}/pt/org/lazyOrgOrDept")
    @ApiOperation("逐级查询机构")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<UapLazyOrgInfo> lazyOrgOrDept(@PathVariable String version, @RequestParam String id, @RequestParam String loginUserId) {
        return uapServiceApi.lazyOrgOrDept(id, loginUserId);
    }

    @PostMapping("/{version}/pt/user/searchUapUserByOrgId")
    @ApiOperation("查询uap的用户")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<UapOrgUser> searchUapUserByOrgId(@PathVariable String version, @RequestParam String loginUserId, @RequestBody UapUserSearchQueryDTO queryDTO) {
        return uapUserService.searchUapUserByOrgId(queryDTO, loginUserId);
    }


    @PostMapping("/{version}/pt/user/listByOrgId")
    @ApiOperation("根据机构id查询uap的用户")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<UapOrgUser> listByOrgId(@PathVariable String version, @RequestParam String orgId) {
        return uapUserService.listByOrgId(orgId);
    }

    @GetMapping("/pt/v1/waterMarkSwitch")
    @ApiOperation("水印开关")
    public void waterMarkSwitch(@RequestParam String loginUserId, @RequestParam Integer status) {
        UserConfigDto record = new UserConfigDto();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcew_user_config")));
        record.setUapUserId(loginUserId);
        record.setStatus(status);
        record.setUpdateTime(new Date());
        record.setConfigType(Constants.WATERMARK_TYPE);
        record.setConfigDesc("用户是否开启水印开关");
        userConfigMapper.upsert(record);
    }
}
