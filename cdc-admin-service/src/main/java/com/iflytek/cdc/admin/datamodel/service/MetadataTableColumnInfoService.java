package com.iflytek.cdc.admin.datamodel.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;

import java.util.List;

public interface MetadataTableColumnInfoService extends IService<TbCdcdmMetadataTableColumnInfo> {

    /**
     * 根据
     */
    List<TbCdcdmMetadataTableColumnInfo> listByTableId(String tableId);

    /**
     * 根据表保存
     */
    void saveByTable(Pair<TbCdcdmMetadataTableInfo, TbCdcdmMetadataTableInfo> tbCdcdmMetadataTableInfo,
                     List<TbCdcdmMetadataTableColumnInfo> columnInfoList);
}
