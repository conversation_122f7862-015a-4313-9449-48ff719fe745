package com.iflytek.cdc.admin.datamodel.service;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;

import java.util.List;

public interface MetaDataService {

    /**
     * 查询所有的表
     * */
    List<TbCdcdmMetadataTableInfo> getAllTable(String tableName);

    /**
     * 根据表名获取元数据信息列表。
     * @param tableNames 需要查询的表名列表。
     * @return 返回对应表名的元数据信息列表，如果表名不存在或无对应元数据信息，则返回空列表。
     */
    List<TbCdcdmMetadataTableInfo> listByTableNames(List<String> tableNames);

    /**
     * 根据表名查询该表所有字段
     * */
    List<TbCdcdmMetadataTableColumnInfo> getFieldByTableName(String tableName, String columnName);

    /**
     * 根据表名查询该表所有字段
     * */
    List<TbCdcdmMetadataTableColumnInfo> getFieldByTableNames(List<String> tableNames);

}
