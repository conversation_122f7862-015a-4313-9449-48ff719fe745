package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.DictRelation;
import com.iflytek.cdc.admin.entity.DictRelationDirectory;
import com.iflytek.cdc.admin.sdk.pojo.InfectedValueInfo;
import com.iflytek.cdc.admin.service.mdm.MdmDataSyncAndGetStatus;
import com.iflytek.cdc.admin.service.mdm.MdmDataSyncExecute;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermDictInfo;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import com.iflytek.cdc.admin.sdk.entity.DictRelationFilter;
import com.iflytek.cdc.admin.sdk.pojo.DictValueInfo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2021/8/25 13:53
 **/
public interface DictRelationService extends MdmDataSyncAndGetStatus {

    /**
     * 根据分组查询id查询具体的字典目录
     * @param pageNumber
     * @param pageSize
     * @return
     **/
    public List<DictRelationGroupDTO>  queryDirectory(Integer pageNumber, Integer pageSize);

    /**
     * 新增业务关联字典目录
     * @param directory
     * @param loginUserId
     * @return
     **/
    public void  insertDirectory(DictRelationDirectory  directory,String  loginUserId);

    /**
     * 查询字典关联目录列表
     * @param directoryName
     * @return
     **/
    public List<DictRelationDirectory>  queryDirectoryList(String directoryName);

    /**
     * 根据字典关联目录id查询字典关联详细信息
     * @param directoryId
     * @return
     **/
    DictRelationDirectory  queryDirectoryById(String  directoryId);

    /**
     * 删除字典关联目录
     * @param id
     * @return
     **/
    void  deleteDirectory(String id,String loginUserId);

    /**
     * 分页查询字典值
     * @param sd
     * @return
     **/
    PageInfo queryOriginDictValueList(SearchDictValueDTO sd);

    /**
     * 建立关联关系
     * @param dr
     * @param loginUserId
     * @return
     **/
    void  insertRelation(DictRelation  dr,String loginUserId);

    /**
     * 取消关联关系
     * @param relationDTO
     * @return
     **/
    void  deleteRelation(DeleteRelationDTO  relationDTO);

    /**
     * 导出关联关系
     * @param sd
     * @param response
     * @return
     **/
    void exportRelation(SearchExportRelationDTO sd, HttpServletResponse response);

    /**
     * 修改字典目录名称
     * @param ud
     * @return
     **/
    void updateRelationName(UpdateRelationDTO  ud,String loginUserId);

    /**
     * 查询关联字典信息
     * @param filter
     * @return
     **/
    List<DictValueInfo>  queryDictValueInfo(DictRelationFilter  filter);

    /**
     * 查询关联字典信息
     * @param filter
     * @return
     **/
    List<DictValueInfo>  queryOriginDictValueInfo(DictRelationFilter  filter);

    /**
     * 查询病种详细信息
     * @param filter
     * @return
     **/
    List<InfectedValueInfo> queryInfectedValueDetail(DictRelationFilter  filter);

    /**
     * 根据mdm的mq信息同步删除本地库中的关联数据
     * @param dictCode
     * @param dictValueCode
     * @return
     **/
    void deleteRelationByMdmMessage(String dictCode,String  dictValueCode);

    /**
     * 根据mdm的mq信息同步修改本地库中的关联数据
     * @param dictCode
     * @param dictValueCode
     * @param dictValueCodeName
     * @return
     **/
    void  updateRelationNameByMdmMessage(String dictCode,String dictValueCode,String dictValueCodeName);

    Boolean judgeIsUpdateName(String dictCode,String  dictValueCode);

    /**
     * 根据值获取name
     */
    String getTargetName(String originCode, String codeValue);
}
