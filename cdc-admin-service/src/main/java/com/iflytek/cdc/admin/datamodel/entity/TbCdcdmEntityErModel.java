package com.iflytek.cdc.admin.datamodel.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.cdc.admin.datamodel.model.EntityRelation;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * ER模型;
 * <AUTHOR> fengwang35
 * @date : 2024-9-9
 */
@ApiModel(value = "ER模型")
@TableName("tb_cdccs_entity_er_model")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdcdmEntityErModel extends BaseEntity implements Serializable{
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id ;
    /** 模型名称 */
    @ApiModelProperty(value = "模型名称")
    private String objName ;
    /** 模型编码 */
    @ApiModelProperty(value = "模型编码")
    private String objCode ;
    /** 模型关系json */
    @ApiModelProperty(value = "模型关系json")
    private String relationJson ;
    /** 模型结构：一对一；一对多； */
    @ApiModelProperty(value = "模型结构：一对一；一对多；")
    private String objStructure ;

    @ApiModelProperty(value = "数据源标识")
    private String dataSourceKey;

    @ApiModelProperty("实体关系")
    @TableField(exist = false)
    private EntityRelation entityRelation;

}