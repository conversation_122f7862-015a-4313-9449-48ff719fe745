package com.iflytek.cdc.admin.outbound.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("外呼记录查询条件")
@Data
public class OutboundRecordQueryDTO {

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("开始日期")
    private Date startDate;

    @ApiModelProperty("结束日期")
    private Date endDate;

    @ApiModelProperty("状态")
    private List<String> statuses;
}
