package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.dto.PopulationDataHistoricalInfoQueryDTO;
import com.iflytek.cdc.admin.dto.TbCdcmrPopulationDataInfoQueryDTO;
import com.iflytek.cdc.admin.common.enums.AreaLevelEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.excel.SelectDataSheetWriteHandler;
import com.iflytek.cdc.admin.mapper.TbCdcmrPopulationDataInfoMapper;
import com.iflytek.cdc.admin.dto.PopulationDataInfoImportDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo;
import com.iflytek.cdc.admin.service.StreetInfoService;
import com.iflytek.cdc.admin.service.TbCdcmrPopulationDataInfoService;
import com.iflytek.cdc.admin.util.DateUtils;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;
import com.iflytek.cdc.admin.vo.PopulationDataInfoVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TbCdcmrPopulationDataInfoServiceImpl implements TbCdcmrPopulationDataInfoService {
    @Resource
    private BatchUidService batchUidService;
    @Resource
    private TbCdcmrPopulationDataInfoMapper tbCdcmrPopulationDataInfoMapper;
    @Resource
    private UapServiceApi uapServiceApi;
    @Resource
    private StreetInfoService streetInfoService;

    @Override
    public void importByFile(InputStream inputStream, String loginUserId, String loginUserName) {
        List<TbCdcmrPopulationDataInfo> entities = readFile(inputStream, loginUserName);
        entities.forEach(entity -> {
            entity.setId(String.valueOf(batchUidService.getUid(TbCdcmrPopulationDataInfo.TABLE_NAME)));
            entity.setDeleteFlag(Integer.valueOf(DeleteFlagEnum.NO.getCode()));
            entity.setCreator(loginUserId);
            entity.setCreateTime(new Date());
        });
        tbCdcmrPopulationDataInfoMapper.batchInsert(entities);
    }

    @Override
    public PageInfo<TbCdcmrPopulationDataInfo> pageList(TbCdcmrPopulationDataInfoQueryDTO queryDTO, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                TbCdcmrPopulationDataInfoQueryDTO::setProvinceCodes,
                TbCdcmrPopulationDataInfoQueryDTO::setCityCodes,
                TbCdcmrPopulationDataInfoQueryDTO::setDistrictCodes,
                TbCdcmrPopulationDataInfoQueryDTO::setStreetCodes);
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            return new PageInfo<>(tbCdcmrPopulationDataInfoMapper.pageList(queryDTO));
        }
    }

    @Override
    public TbCdcmrPopulationDataInfo load(String id) {
        return tbCdcmrPopulationDataInfoMapper.load(id);
    }

    @Override
    public void update(TbCdcmrPopulationDataInfo input, String loginUserId) {
        if (load(input.getId()) == null){
            throw new MedicalBusinessException("数据错误");
        }
        input.setUpdater(loginUserId);
        input.setUpdateTime(new Date());
        tbCdcmrPopulationDataInfoMapper.update(input);
    }

    @Override
    public byte[] downloadTemplate(String loginUserName) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Map<Integer, List<String>> selectMap = new LinkedHashMap<>();
        LoginUserAreaVO loginUserAreaVO = streetInfoService.loadCurrUserArea(loginUserName);
        selectMap.put(0, new ArrayList<>(loginUserAreaVO.getProvinceCodeMap().values()));
        selectMap.put(1, new ArrayList<>(loginUserAreaVO.getCityCodeMap().values()));
        selectMap.put(2, new ArrayList<>(loginUserAreaVO.getDistrictCodeMap().values()));
        selectMap.put(3, new ArrayList<>(loginUserAreaVO.getStreetCodeMap().values()));
        // 街道数据过多，不设置下拉框，仅手填
        EasyExcel.write(outputStream, PopulationDataInfoImportDTO.class).registerWriteHandler(new SelectDataSheetWriteHandler(selectMap)).sheet().doWrite(new ArrayList<>());
        return outputStream.toByteArray();
    }

    @Override
    public List<PopulationDataInfoVO> listByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        setAreaLevelAndCodes(queryDTO);
        if (queryDTO.getAreaLevel() == null || CollectionUtils.isEmpty(queryDTO.getAreaCodes())){
            return new ArrayList<>();
        }
        return tbCdcmrPopulationDataInfoMapper.listByAreaCodes(queryDTO);
    }

    @Override
    public List<PopulationDataInfoVO> listByEachAreaCode(TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(queryDTO.getProvinceCodes()) &&
                CollectionUtils.isEmpty(queryDTO.getCityCodes()) &&
                CollectionUtils.isEmpty(queryDTO.getDistrictCodes()) &&
                CollectionUtils.isEmpty(queryDTO.getStreetCodes())) {
            return new ArrayList<>();
        }
        return tbCdcmrPopulationDataInfoMapper.listByEachAreaCodes(queryDTO);
    }

    @Override
    public List<PopulationDataInfoVO> listByAreaCodesAndTime(TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        setAreaLevelAndCodes(queryDTO);
        if (queryDTO.getAreaLevel() == null || CollectionUtils.isEmpty(queryDTO.getAreaCodes())){
            return new ArrayList<>();
        }
        List<PopulationDataInfoVO> retList = new ArrayList<>();
        List<PopulationDataInfoVO> startList = tbCdcmrPopulationDataInfoMapper.listByArea(queryDTO);
        Map<String, List<PopulationDataInfoVO>> listMap ;
        if (AreaLevelEnum.DISTRICT.getValue().equals(queryDTO.getAreaLevel())){
            listMap = startList.stream().collect(Collectors.groupingBy(PopulationDataInfoVO::getDistrictCode));
        } else if (AreaLevelEnum.CITY.getValue().equals(queryDTO.getAreaLevel())){
            listMap = startList.stream().collect(Collectors.groupingBy(PopulationDataInfoVO::getCityCode));
        } else {
            listMap = startList.stream().collect(Collectors.groupingBy(PopulationDataInfoVO::getProvinceCode));
        }

        List<Date> dateList = queryDTO.getDateList();
        PopulationDataInfoVO vo ;
        for (Date date : dateList) {
            vo = new PopulationDataInfoVO();
            vo.setQueryDate(date);
            int residentCnt = 0;
            for (Map.Entry<String, List<PopulationDataInfoVO>> entry : listMap.entrySet()) {
                final List<PopulationDataInfoVO> value = entry.getValue();
                //最近一条记录
                int lastCnt = 0;
                //按统计时候排序
                List<PopulationDataInfoVO> vos = value.stream().sorted(Comparator.comparing(PopulationDataInfoVO::getStatDate)).collect(Collectors.toList());
                for (PopulationDataInfoVO infoVO : vos) {
                    if (date.compareTo(infoVO.getStatDate()) < 1){
                        //覆盖取最近的一个
                        lastCnt = infoVO.getResidentPopulation();
                    }
                }
                if (lastCnt == 0 && !org.springframework.util.CollectionUtils.isEmpty(vos)){
                    lastCnt = vos.get(vos.size()-1).getResidentPopulation();
                }

                //每组的最近一个数量相加
                residentCnt = residentCnt + lastCnt;
            }
            vo.setResidentPopulation(residentCnt);
            retList.add(vo);
        }
        return retList;
    }

    @Override
    public Map<String, PopulationDataInfoVO> groupByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        return listByAreaCodes(queryDTO).stream().collect(Collectors.toMap(PopulationDataInfoVO::getAreaCode, t -> t));
    }

    @Override
    public PopulationDataInfoVO statByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        setAreaLevelAndCodes(queryDTO);
        return tbCdcmrPopulationDataInfoMapper.statByAreaCodes(queryDTO);
    }

    /**
     * 设置区域级别区域编码
     */
    private void setAreaLevelAndCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO){
        if (queryDTO.getAreaLevel() == null || CollectionUtils.isEmpty(queryDTO.getAreaCodes())){
            Pair<Integer, List<String>> areaLevelAndCodes = AreaLevelEnum.getAreaLevelAndCodes(queryDTO.getProvinceCodes(),
                    queryDTO.getCityCodes(), queryDTO.getDistrictCodes(), queryDTO.getStreetCodes());
            queryDTO.setAreaLevel(areaLevelAndCodes.getKey());
            queryDTO.setAreaCodes(areaLevelAndCodes.getValue());
        }
    }

    /**
     * 读取文件
     */
    private List<TbCdcmrPopulationDataInfo> readFile(InputStream inputStream, String loginUserName){
        List<PopulationDataInfoImportDTO> list = EasyExcel.read(inputStream).head(PopulationDataInfoImportDTO.class).sheet().doReadSync();
        List<String> regionNames = new ArrayList<>();
        list.forEach(d -> {
            if (StringUtils.isNotEmpty(d.getProvinceName())){
                regionNames.add(d.getProvinceName());
            }
            if (StringUtils.isNotEmpty(d.getCityName())){
                regionNames.add(d.getCityName());
            }
            if (StringUtils.isNotEmpty(d.getDistrictName())){
                regionNames.add(d.getDistrictName());
            }
        });
        if (regionNames.isEmpty()) {
            throw new MedicalBusinessException("请填写省市区");
        }
        LoginUserAreaVO loginUserAreaVO = streetInfoService.loadCurrUserArea(loginUserName);
        List<TbCdcmrPopulationDataInfo> entities = new ArrayList<>();
        int index = 2;
        List<String> errorMessages = new ArrayList<>();
        for (PopulationDataInfoImportDTO dto : list){
            TbCdcmrPopulationDataInfo entity = new TbCdcmrPopulationDataInfo();
            if (StringUtils.isBlank(dto.getStatDate())) {
                errorMessages.add(String.format("第%s行未填写统计日期", index));
            }
            if (StringUtils.isBlank(dto.getProvinceName())) {
                errorMessages.add(String.format("第%s行未填写省", index));
            }
            String provinceCode = loginUserAreaVO.getProvinceCode(dto.getProvinceName());
            if (provinceCode == null) {
                errorMessages.add(String.format("第%s行未匹配到省，请检查省名称是否正确", index));
            }
            entity.setProvinceCode(provinceCode);
            entity.setProvinceName(dto.getProvinceName());

            if (StringUtils.isNotEmpty(dto.getCityName())){
                String cityCode = loginUserAreaVO.getCityCode(dto.getCityName());
                if (cityCode == null){
                    errorMessages.add(String.format("第%s行未匹配到市，请检查市名称是否正确", index));
                }
                entity.setCityCode(cityCode);
                entity.setCityName(dto.getCityName());
            }
            if (StringUtils.isNotEmpty(dto.getDistrictName())){
                String districtCode = loginUserAreaVO.getStandardDistrictByName(dto.getCityName(), dto.getDistrictName());
                if (districtCode == null){
                    errorMessages.add(String.format("第%s行未匹配到县/区，请检查县/区名称是否正确", index));
                }
                entity.setDistrictCode(districtCode);
                entity.setDistrictName(dto.getDistrictName());
            }
            if (StringUtils.isNotEmpty(dto.getStreetName())){
                String streetCode = loginUserAreaVO.getStreetCode(dto.getDistrictName(), dto.getStreetName());
                if (streetCode == null){
                    errorMessages.add(String.format("第%s行未匹配到街道，请检查街道名称是否正确", index));
                }
                entity.setStreetCode(streetCode);
                entity.setStreetName(dto.getStreetName());
            }
            if (dto.getStatDate() == null) {
                errorMessages.add(String.format("第%s行未填写统计日期", index));
            }
            try {
                entity.setResidentPopulation(Integer.valueOf(dto.getResidentPopulation()));
            }catch (NumberFormatException e){
                errorMessages.add(String.format("第%s行常住人口数数据格式错误，请填写正确的数字", index));
            }
            try {
                entity.setRegisteredPopulation(Integer.valueOf(dto.getRegisteredPopulation()));
            }catch (NumberFormatException e){
                errorMessages.add(String.format("第%s行户籍人口数数据格式错误，请填写正确的数字", index));
            }
            try {
                entity.setGdp(new BigDecimal(dto.getGdp()));
            }catch (Exception e){
                errorMessages.add(String.format("第%s行GDP数据数据格式错误，请填写正确的数字", index));
            }
            try {
                entity.setUrbanDpi(new BigDecimal(dto.getUrbanDpi()));
            }catch (Exception e){
                errorMessages.add(String.format("第%s行城镇居民可支配收入数据格式错误，请填写正确的数字", index));
            }
            try {
                entity.setRuralDpi(new BigDecimal(dto.getRuralDpi()));
            }catch (Exception e){
                errorMessages.add(String.format("第%s行农村居民可支配收入数据格式错误，请填写正确的数字", index));
            }
            try {
                entity.setStatDate(DateUtils.getDateFromString(dto.getStatDate(), DateUtils.SHORT_DATE_FORMAT));
            }catch (Exception e){
                errorMessages.add(String.format("第%s行统计日期数据格式错误，请填写正确的格式：yyyy/MM/dd, 例：2024/03/01", index));
            }
            setAreaCode(entity);
            entities.add(entity);
            index++;
        }
        if (!errorMessages.isEmpty()){
            throw new MedicalBusinessException(String.join("\n", errorMessages));
        }
        return entities;
    }

    /**
     * 设置业务区域的编码
     */
    private void setAreaCode(TbCdcmrPopulationDataInfo entity){
        if (StringUtils.isNotEmpty(entity.getStreetCode())) {
            entity.setAreaCode(entity.getStreetCode());
        } else if (StringUtils.isNotEmpty(entity.getDistrictCode())) {
            entity.setAreaCode(entity.getDistrictCode());
        } else if (StringUtils.isNotEmpty(entity.getCityCode())) {
            entity.setAreaCode(entity.getCityCode());
        } else {
            entity.setAreaCode(entity.getProvinceCode());
        }
    }

    @Override
    public List<TbCdcmrPopulationDataInfo> searchHistoricalStatisticsDate(PopulationDataHistoricalInfoQueryDTO queryDTO, String loginUserName) {
        return tbCdcmrPopulationDataInfoMapper.listByAreaAndDate(queryDTO);
    }

    @Override
    public void updateHistoricalStatisticsDate(List<TbCdcmrPopulationDataInfo> inputs, String loginUserId) {
        for (TbCdcmrPopulationDataInfo input : inputs){
            input.setUpdater(loginUserId);
            input.setUpdateTime(new Date());
        }
        tbCdcmrPopulationDataInfoMapper.updateHistoricalStatistics(inputs);
    }
}
