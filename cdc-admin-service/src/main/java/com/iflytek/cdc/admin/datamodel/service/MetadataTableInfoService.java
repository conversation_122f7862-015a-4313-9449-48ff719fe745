package com.iflytek.cdc.admin.datamodel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableSqlLog;
import com.iflytek.cdc.admin.datamodel.model.dto.MetadataTableInfoQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.TableColumnInfoExcelVO;
import com.iflytek.cdc.admin.outbound.util.Response;
import com.iflytek.cdc.admin.service.ICdcService;

import java.io.InputStream;
import java.util.List;

public interface MetadataTableInfoService extends ICdcService<TbCdcdmMetadataTableInfo> {

    /**
     * 创建实体表
     */
    void createEntityTable(String id);

    /**
     * 同步实体表
     */
    void syncEntityTable(String id);

    /**
     * 模板下载
     */
    byte[] downloadTemplate();

    /**
     * 模板读取
     */
    List<TableColumnInfoExcelVO> readExcel(InputStream inputStream);

    void importByFile(InputStream inputStream);



    /**
     * 获取表信息的分页数据
     */
    PageInfo<TbCdcdmMetadataTableInfo> pageList(MetadataTableInfoQueryDTO queryDTO);

    void updateSyncStatus(List<TbCdcdmMetadataTableSqlLog> sqlLogs, String status);

}
