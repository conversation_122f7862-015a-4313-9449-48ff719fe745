package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.CustomizedLogicFieldVO;
import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarnVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedEventTypeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedLogicField;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.service.CustomizedWarnService;
import com.iflytek.cdc.admin.service.CustomizedWarningRuleService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CustomizedWarnServiceImpl implements CustomizedWarnService {

    @Resource
    TbCdcmrCustomizedWarnMapper tbCdcmrCustomizedWarnMapper;
    @Resource
    TbCdcmrCustomizedWarnItemMapper tbCdcmrCustomizedWarnItemMapper;
    @Resource
    TbCdcmrCustomizedWarningRuleMapper tbCdcmrCustomizedWarningRuleMapper;
    @Resource
    CustomizedWarningRuleService customizedWarningRuleService;
    @Resource
    TbCdcmrCustomizedWarningTaskMapper tbCdcmrCustomizedWarningTaskMapper;
    @Resource
    TbCdcmrCustomizedWarningRuleItemMapper tbCdcmrCustomizedWarningRuleItemMapper;
    @Resource
    BatchUidService batchUidService;

    @Override
    public PageInfo<TbCdcmrCustomizedWarn> getCustomizedWarnPageList(CustomizedWarnQueryDTO queryDTO, String loginUserId) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrCustomizedWarnMapper.getList(queryDTO));
    }

    @Override
    @Transactional
    public void addCustomizedWarn(CustomizedWarnVO customizedWarnVO, String loginUserId) {
        if (tbCdcmrCustomizedWarnMapper.getByName(customizedWarnVO.getName()) != null) {
            throw new MedicalBusinessException("已存在相同自定义名称！!");
        }
        customizedWarnVO.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warn")));
        customizedWarnVO.setCode(customizedWarnVO.getId());
        customizedWarnVO.setCreateTime(new Date());
        customizedWarnVO.setCreator(loginUserId);
        customizedWarnVO.setUpdater(loginUserId);
        customizedWarnVO.setUpdateTime(new Date());
        customizedWarnVO.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
        tbCdcmrCustomizedWarnMapper.insert(customizedWarnVO);
        customizedWarnVO.getItemList().forEach(e -> {
            e.setCustomizedWarnId(customizedWarnVO.getId());
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warn_item")));
        });
        tbCdcmrCustomizedWarnItemMapper.batchInsert(customizedWarnVO.getItemList());
        TbCdcmrCustomizedWarningRule warningRule = new TbCdcmrCustomizedWarningRule();
        warningRule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warning_rule")));
        warningRule.setCustomizedWarnId(customizedWarnVO.getId());
        warningRule.setCreateTime(new Date());
        warningRule.setCreator(loginUserId);
        warningRule.setUpdater(loginUserId);
        warningRule.setUpdateTime(new Date());
        warningRule.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
        warningRule.setStatus(Constants.STATUS_OFF);
        customizedWarningRuleService.addCustomizedWarningRule(warningRule);
    }

    @Override
    @Transactional
    public void deleteCustomizedWarn(String id) {
        TbCdcmrCustomizedWarningRule warnRule = tbCdcmrCustomizedWarningRuleMapper.selectByWarnId(id);
        if (!CollectionUtil.isEmpty(tbCdcmrCustomizedWarningRuleItemMapper.selectByRuleId(warnRule.getId()))) {
            throw new MedicalBusinessException("还有在使用的规则，请先删除规则后再进行删除！");
        }
        if (!CollectionUtil.isEmpty(tbCdcmrCustomizedWarningTaskMapper.selectByWarnRuleId(id))) {
            throw new MedicalBusinessException("还有已配置的监测，请先删除监测后再进行删除！");
        }
        tbCdcmrCustomizedWarnMapper.deleteByPrimaryKey(id);
        tbCdcmrCustomizedWarnItemMapper.deleteByWarnId(id);
        customizedWarningRuleService.deleteCustomizedWarningRule(id);
    }

    @Override
    @Transactional
    public void updateCustomizedWarn(CustomizedWarnVO customizedWarnVO, String loginUserId) {
        customizedWarnVO.setCreateTime(new Date());
        customizedWarnVO.setCreator(loginUserId);
        customizedWarnVO.setUpdater(loginUserId);
        customizedWarnVO.setUpdateTime(new Date());
        customizedWarnVO.getItemList().forEach(e -> {
            e.setCustomizedWarnId(customizedWarnVO.getId());
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warn_item")));
        });
        tbCdcmrCustomizedWarnMapper.updateByPrimaryKeySelective(customizedWarnVO);
        tbCdcmrCustomizedWarnItemMapper.deleteByWarnId(customizedWarnVO.getId());
        tbCdcmrCustomizedWarnItemMapper.batchInsert(customizedWarnVO.getItemList());
        tbCdcmrCustomizedWarningRuleItemMapper.deleteByRuleId(tbCdcmrCustomizedWarningRuleMapper.selectByWarnId(customizedWarnVO.getId()).getId());
    }

    @Override
    public CustomizedWarnVO getCustomizedWarnById(String id) {
        CustomizedWarnVO result = tbCdcmrCustomizedWarnMapper.selectByPrimaryKey(id);
        result.setItemList(tbCdcmrCustomizedWarnItemMapper.getByWarnId(id));
        return result;
    }

    @Override
    public List<CustomizedWarnVO> getAllEnabledWarn(String warningType) {
        List<CustomizedWarnVO> customizedWarnVOList = tbCdcmrCustomizedWarnMapper.getEnabledWarn(warningType);
        customizedWarnVOList.forEach(e -> {
            e.setItemList(tbCdcmrCustomizedWarnItemMapper.getByWarnId(e.getId()));
        });
        return customizedWarnVOList;
    }

    @Override
    public void updateCustomizedWarnStatus(Integer status, String id, String loginUserId) {
        CustomizedWarnVO customizedWarnVO = new CustomizedWarnVO();
        customizedWarnVO.setId(id);
        customizedWarnVO.setUpdater(loginUserId);
        customizedWarnVO.setUpdateTime(new Date());
        customizedWarnVO.setStatus(status);
        tbCdcmrCustomizedWarnMapper.updateByPrimaryKeySelective(customizedWarnVO);
    }

    @Override
    public Boolean checkCustomizedWarnByName(String name) {
        return tbCdcmrCustomizedWarnMapper.getByName(name) != null;
    }

    @Override
    public List<CustomizedLogicFieldVO> listLogicFieldByTypeAndSource(String warningType, String dataSource) {
        return groupLogicField(tbCdcmrCustomizedWarnMapper.listLogicFieldBy(warningType, dataSource, null));

    }

    @Override
    public List<TbCdcmrCustomizedEventTypeConfig> listEventTypeConfigByTypeAndSource(String warningType, String dataSource) {
        return tbCdcmrCustomizedWarnMapper.listEventTypeConfigByTypeAndSource(warningType, dataSource);
    }

    @Override
    public List<CustomizedLogicFieldVO> listNeedConfigField(String warningType, String dataSource) {
        return groupLogicField(tbCdcmrCustomizedWarnMapper.listLogicFieldBy(warningType, dataSource, false));
    }

    private List<CustomizedLogicFieldVO> groupLogicField(List<TbCdcmrCustomizedLogicField> logicFields){
        Map<String, List<TbCdcmrCustomizedLogicField>> logicFieldVOListMap = logicFields.stream().collect(Collectors.groupingBy(t -> t.getMedicalType() + t.getDataSource() + t.getWarningType() + t.getName()));
        List<CustomizedLogicFieldVO> fieldVOS = new ArrayList<>();

        logicFieldVOListMap.forEach((k, v) -> {
            CustomizedLogicFieldVO customizedLogicFieldVO = CustomizedLogicFieldVO.of(v.get(0));
            customizedLogicFieldVO.setFieldSettings(v.stream().map(setting -> {
                CustomizedLogicFieldVO.FieldSetting fieldSetting = new CustomizedLogicFieldVO.FieldSetting();
                fieldSetting.setFieldName(setting.getFieldName());
                fieldSetting.setColumnFlag(setting.isColumnFlag());
                fieldSetting.setPrefix(setting.getPrefix());
                fieldSetting.setSplitter(setting.getSplitter());
                return fieldSetting;
            }).collect(Collectors.toList()));
            fieldVOS.add(customizedLogicFieldVO);
        });
        fieldVOS.sort(Comparator.comparing(CustomizedLogicFieldVO::getSequence));
        return fieldVOS;
    }


}
