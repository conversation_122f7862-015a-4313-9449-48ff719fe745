package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.InspectionStandardQueryParam;
import com.iflytek.cdc.admin.dto.InspectionStandardVO;
import com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard;
import com.iflytek.cdc.admin.mapper.TbCdcmrInspectionStandardMapper;
import com.iflytek.cdc.admin.service.InspectionStandardService;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageRequest;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InspectionStandardServiceImpl implements InspectionStandardService {
    @Resource
    TermDictApi termDictApi;
    @Resource
    TbCdcmrInspectionStandardMapper tbCdcmrInspectionStandardMapper;

    @Override
    //@Scheduled(fixedRate = 3600000)
    public void syncInspectionStandard() {
        log.info("开始同步mdm检验标准字典");
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        int pageSize = 10;
        //定义查询页码
        pageRequest.setPageNumber(1);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode("404132");
        pageRequest.setFilter(filter);
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> tempResult = termDictApi.getTermCodedValueList(pageRequest);
        if (tempResult.getNext().getTotal() > pageSize) {
            pageRequest.setPageSize(tempResult.getNext().getTotal().intValue());
            tempResult = termDictApi.getTermCodedValueList(pageRequest);
        }
        List<TermCodedValueInfo> resultInfoList = tempResult.getEntities();
        List<TbCdcmrInspectionStandard> existList = tbCdcmrInspectionStandardMapper.getList();
        List<String> codeList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(resultInfoList)) {
            codeList = resultInfoList.stream().map(TermCodedValueInfo::getCodedValue).collect(Collectors.toList());
        }
        List<String> deleteList = new ArrayList<>();
        for (TbCdcmrInspectionStandard standard : existList) {
            if (!codeList.contains(standard.getInspectionItemCode())) {
                deleteList.add(standard.getInspectionItemCode());
            }
        }
        List<List<String>> splitDeleteList = CollectionUtil.splitList(deleteList, 100);
        splitDeleteList.forEach(e -> tbCdcmrInspectionStandardMapper.deleteByPrimaryKeyList(e));
        tbCdcmrInspectionStandardMapper.upsertList(resultInfoList.stream().map(e -> {
            TbCdcmrInspectionStandard standard = new TbCdcmrInspectionStandard();
            standard.setInspectionItem(e.getDescription());
            standard.setInspectionItemCode(e.getCodedValue());
            standard.setStatus(Constants.STATUS_ON);
            return standard;
        }).collect(Collectors.toList()));
    }

    @Override
    public void updateInspectionStandard(TbCdcmrInspectionStandard tbCdcmrInspectionStandard) {
        tbCdcmrInspectionStandard.setUpdateTime(new Date());
        tbCdcmrInspectionStandardMapper.updateByPrimaryKeySelective(tbCdcmrInspectionStandard);
    }

    @Override
    public PageInfo<InspectionStandardVO> getInspectionStandardList(InspectionStandardQueryParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        return new PageInfo<>(tbCdcmrInspectionStandardMapper.getListByParam(param));
    }

    @Override
    public List<TbCdcmrInspectionStandard> getInspectionStandardList(String keyword) {
        return tbCdcmrInspectionStandardMapper.getList().stream().filter(e -> e.getInspectionItem().contains(keyword)).collect(Collectors.toList());
    }
}
