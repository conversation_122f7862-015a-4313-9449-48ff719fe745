package com.iflytek.cdc.admin.service.province;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.EmergingWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.vo.EmergingProcessScopeVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;

import java.util.List;

public interface EmergingDiseaseWarningRuleService extends IService<TbCdcmrEmergingDiseaseWarningRule>{


    PageInfo<TbCdcmrEmergingDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod, String followStatus);

    void savaOrUpdateRule(TbCdcmrEmergingDiseaseWarningRule rule);

    List<TreeNode> getEmergingTree(EmergingWaringRuleQueryDTO dto);
}
