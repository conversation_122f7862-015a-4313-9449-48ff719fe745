package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import com.iflytek.cdc.admin.service.DataAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "数据权限查询接口")

public class DataAuthController {

    @Resource
    DataAuthService dataAuthService;

    @GetMapping("/pt/v1/dataAuth/getSyndromeDataAuthByLoginUserId")
    @ApiOperation("查询用户症候群数据权限")
    List<TbCdcmrUserDataAuth> getSyndromeDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getSyndromeDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getInfectedDataAuthByLoginUserId")
    @ApiOperation("查询用户传染病数据权限")
    List<TbCdcmrUserDataAuth> getInfectedDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getInfectedDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getSchoolSymptomDataAuthByLoginUserId")
    @ApiOperation("查询用户学校症状数据权限")
    List<TbCdcmrUserDataAuth> getSchoolSymptomDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getSchoolSymptomDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getPoisonDataAuthByLoginUserId")
    @ApiOperation("查询用户中毒类数据权限")
    List<TbCdcmrUserDataAuth> getPoisonDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getPoisonDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getOutpatientDataAuthByLoginUserId")
    @ApiOperation("查询用户门诊类数据权限")
    List<TbCdcmrUserDataAuth> getOutpatientDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getOutpatientDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getUnknownReasonDataAuthByLoginUserId")
    @ApiOperation("查询用户不明原因类数据权限")
    List<TbCdcmrUserDataAuth> getUnknownReasonDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getUnknownReasonDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getPreventionControlDataAuthByLoginUserId")
    @ApiOperation("查询用户联防联控类数据权限")
    List<TbCdcmrUserDataAuth> getPreventionControlDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getPreventionControlDataAuthByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/dataAuth/getCustomizedDataAuthByLoginUserId")
    @ApiOperation("查询用户自定义数据权限")
    List<TbCdcmrUserDataAuth> getCustomizedDataAuthByLoginUserId(@RequestParam String loginUserId) {
        return dataAuthService.getCustomizedDataAuthByLoginUserId(loginUserId);
    }
}
