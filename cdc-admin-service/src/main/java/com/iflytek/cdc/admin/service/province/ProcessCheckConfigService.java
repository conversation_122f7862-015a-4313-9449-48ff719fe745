package com.iflytek.cdc.admin.service.province;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.admin.model.processCheck.dto.ProcessCheckQueryDTO;

import java.util.List;

public interface ProcessCheckConfigService extends IService<TbCdcmrCheckAuthorityConfig> {

    /**
     * 查询病例审核配置
     * */
    PageInfo<TbCdcmrCheckAuthorityConfig> getProcessCheckConfig(ProcessCheckQueryDTO dto);

    /**
     * 获取登录人的病例审核配置
     * */
    List<TbCdcmrCheckAuthorityConfig> getCheckConfigByLoginUser(String loginUserId, String diseaseType);

    /**
     * 编辑审核配置
     * */
    void editCheckConfig(TbCdcmrCheckAuthorityConfig config);

    /**
     * 获取配置的症候群亚组
     * */
    List<String> getSyndromeSubgroup(String id);

}
