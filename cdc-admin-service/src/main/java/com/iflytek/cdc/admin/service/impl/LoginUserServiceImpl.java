package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.enums.AreaLevelEnum;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.UserConfigDto;
import com.iflytek.cdc.admin.mapper.UserConfigMapper;
import com.iflytek.cdc.admin.model.mr.vo.UserInfoVO;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import com.iflytek.cdc.admin.service.LoginUserService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgPo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LoginUserServiceImpl implements LoginUserService {

    private static final String EI_POSITION_SWITCH_CODE = "cdc-platform-service:ei-position-switch";

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private UserConfigMapper userConfigMapper;

    @Resource
    private ParamConfigService paramConfigService;

    @Override
    public UserInfoVO getUserInfo(String loginUserId) {
        UserInfoVO vo = new UserInfoVO();
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        BeanUtils.copyProperties(user, vo);
        vo.setUsername(user.getName());
        vo.setPhone(user.getPhone());
        UapOrgPo org = uapServiceApi.getUserOrg(user.getLoginName());
        vo.setProvinceCode(org.getProvinceCode());
        vo.setCityCode(org.getCityCode());
        vo.setDistrictCode(org.getDistrictCode());
        vo.setProvinceName(org.getProvince());
        vo.setCityName(org.getCity());
        vo.setDistrictName(org.getDistrict());
        vo.setOrgName(org.getName());
        vo.setOrgId(org.getId());

        //判断是否脱敏用户 以及用户的脱敏情况
        vo.setDesensitization(isDesensitization(loginUserId));
        vo.setDesensitizationUser(uapServiceApi.checkUserIsDesensitizationUser(loginUserId));

        ParamInfoFilter filter = new ParamInfoFilter();
        filter.setOrgId(org.getId());
        filter.setConfigCode(EI_POSITION_SWITCH_CODE);
        ParamInfo paramInfo = paramConfigService.paramInfoByCode(filter);
        if (paramInfo != null){
            vo.setEiPositionSwitch(paramInfo.getConfigValue());
        }
        if (StringUtils.isNotEmpty(vo.getProvinceCode())){
            vo.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
            vo.setAreaCode(vo.getProvinceCode());
            vo.setAreaName(vo.getProvinceName());
        }
        if (StringUtils.isNotEmpty(vo.getCityCode())){
            vo.setAreaLevel(AreaLevelEnum.CITY.getValue());
            vo.setAreaCode(vo.getCityCode());
            vo.setAreaName(vo.getCityName());
        }
        if (StringUtils.isNotEmpty(vo.getDistrictCode())){
            vo.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
            vo.setAreaCode(vo.getDistrictCode());
            vo.setAreaName(vo.getDistrictName());
        }
        return vo;
    }

    /**
     * 根据用户以及用户配置的开关 判断用户是否脱敏
     * */
    private boolean isDesensitization(String loginUserId) {

        boolean isDesensitizationUser = uapServiceApi.checkUserIsDesensitizationUser(loginUserId);
        if (isDesensitizationUser) {
            return true;
        } else {
            UserConfigDto result = userConfigMapper.getUserConfig(loginUserId, Constants.DESENSITIZATION_TYPE);
            // 不脱敏角色 进入系统默认为开启脱敏
            return result == null || result.getStatus() > 0;
        }

    }


}
