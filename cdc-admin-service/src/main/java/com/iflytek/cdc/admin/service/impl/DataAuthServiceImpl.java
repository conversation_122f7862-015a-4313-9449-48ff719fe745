package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.SyndromeInfo;
import com.iflytek.cdc.admin.mapper.SyndromeInfoMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrUserDataAuthMapper;
import com.iflytek.cdc.admin.service.*;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DataAuthServiceImpl implements DataAuthService {

    @Resource
    BatchUidService batchUidService;
    @Resource
    TbCdcmrUserDataAuthMapper tbCdcmrUserDataAuthMapper;

    @Resource
    @Lazy
    SyndromeService syndromeService;
    @Resource
    InfectiousDiseasesApiService infectiousDiseasesApiService;
    @Resource
    SchoolSymptomService schoolSymptomService;
    @Resource
    PoisoningWarnService poisoningWarnService;
    @Resource
    OutpatientWarnService outpatientWarnService;

    @Resource
    UnknownReasonWarnService unknownReasonWarnService;
    @Resource
    PreventionControlWarnService preventionControlWarnService;

    @Resource
    CustomizedWarningTaskService customizedWarningTaskService;
    @Override
    public void add(DataAuthVO vo) {
        List<TbCdcmrUserDataAuth> tbCdcewPersonDataAuthList = new ArrayList<>();

        for (InfectedNameVO infectedNameVO : vo.getInfectedList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_INFECTIOUS_DISEASE);
            dataAuth.setAuthName(infectedNameVO.getInfectedName());
            dataAuth.setAuthId(infectedNameVO.getInfectedCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }
        for (SymptomNameVO symptomNameVO : vo.getSymptomList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_SYNDROME);
            dataAuth.setAuthName(symptomNameVO.getSymptomName());
            dataAuth.setAuthId(symptomNameVO.getSymptomCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }
        for (SympNameVO sympNameVO : vo.getSchoolSymptomList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_SCHOOL_SYMPTOM);
            dataAuth.setAuthName(sympNameVO.getSchoolSymptomName());
            dataAuth.setAuthId(sympNameVO.getSchoolSymptomCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }

        for (PoisonNameVO poisonNameVO : vo.getPoisonList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_POISON);
            dataAuth.setAuthName(poisonNameVO.getPoisonName());
            dataAuth.setAuthId(poisonNameVO.getPoisonCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }
        for (OutpatientNameVO outpatientNameVO : vo.getOutpatientList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_OUTPATIENT);
            dataAuth.setAuthName(outpatientNameVO.getOutpatientTypeName());
            dataAuth.setAuthId(outpatientNameVO.getOutpatientTypeCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }
        for (UnknownReasonNameVO unknownReasonNameVO : vo.getUnknownReasonList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_UNKNOWN_REASON);
            dataAuth.setAuthName(unknownReasonNameVO.getDiseaseName());
            dataAuth.setAuthId(unknownReasonNameVO.getDiseaseCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }
        for (PreventionControlNameVO preventionControlNameVO : vo.getPreventionControlList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_PREVENTION_CONTROL);
            dataAuth.setAuthName(preventionControlNameVO.getPreventionControlName());
            dataAuth.setAuthId(preventionControlNameVO.getPreventionControlCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }


        for (CustomizedNameVO customizedNameVO : vo.getCustomizedList()) {
            TbCdcmrUserDataAuth dataAuth = new TbCdcmrUserDataAuth();
            dataAuth.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_user_data_auth")));
            dataAuth.setLoginUserId(vo.getUserId());
            dataAuth.setDataType(Constants.DATA_AUTH_CUSTOMIZED);
            dataAuth.setAuthName(customizedNameVO.getCustomizedName());
            dataAuth.setAuthId(customizedNameVO.getCustomizedCode());
            tbCdcewPersonDataAuthList.add(dataAuth);
        }

        tbCdcmrUserDataAuthMapper.deleteByLoginUserId(vo.getUserId());
        if (!tbCdcewPersonDataAuthList.isEmpty()) {
            tbCdcmrUserDataAuthMapper.batchInsert(tbCdcewPersonDataAuthList);
        }
    }

    @Override
    public List<TbCdcmrUserDataAuth> getSyndromeDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_SYNDROME == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getInfectedDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_INFECTIOUS_DISEASE == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getSchoolSymptomDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_SCHOOL_SYMPTOM == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getPoisonDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_POISON == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getOutpatientDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_OUTPATIENT == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getUnknownReasonDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_UNKNOWN_REASON == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getPreventionControlDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_PREVENTION_CONTROL == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getCustomizedDataAuthByLoginUserId(String loginUserId) {
        List<TbCdcmrUserDataAuth> dataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserId(loginUserId);
        return dataAuths.stream().filter(TbCdcmrUserDataAuth -> Constants.DATA_AUTH_CUSTOMIZED == TbCdcmrUserDataAuth.getDataType()).collect(Collectors.toList());
    }

    @Override
    public List<TbCdcmrUserDataAuth> getDataAuthByLoginUserIdAndDataType(String loginUserId, Integer dataType) {
        return  tbCdcmrUserDataAuthMapper.getDataAuthByLoginUserIdAndDataType(loginUserId,dataType);
    }

    @Override
    public List<CascadeVO> getConfiguredDiseaseCodesByAuth(String loginUserId, Integer dataType) {
        List<TbCdcmrUserDataAuth> dataAuths = getDataAuthByLoginUserIdAndDataType(loginUserId, dataType);
        List<CascadeVO> allRetList = new ArrayList<>();
        if (Constants.DATA_AUTH_SYNDROME == dataType){
            allRetList = syndromeService.getSyndromeList();
        } else if (Constants.DATA_AUTH_INFECTIOUS_DISEASE == dataType) {
            allRetList = infectiousDiseasesApiService.getInfectedNameList();
            if (CollectionUtils.isEmpty(dataAuths)){
                return allRetList;
            }
            List<CascadeVO> resultList = new ArrayList<>();
            List<String> authIds = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
            allRetList.forEach(restVO -> {
                List<CascadeVO> children = restVO.getChildren();
                children = children.stream().filter(infectedCascadeVO -> authIds.contains(infectedCascadeVO.getValue())).collect(Collectors.toList());
                restVO.setChildren(children);
            });
            allRetList.forEach(restVO -> {
                if (!(restVO.getChildren() == null || restVO.getChildren().isEmpty())) {
                    resultList.add(restVO);
                }
            });
            return resultList;
        } else if (Constants.DATA_AUTH_SCHOOL_SYMPTOM == dataType){
            allRetList = schoolSymptomService.getSymptoms();
        } else if (Constants.DATA_AUTH_POISON == dataType){
            allRetList = poisoningWarnService.getPoisonNameList();
//            if (CollectionUtils.isEmpty(dataAuths)){
//                return allRetList;
//            }
//            Set<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toSet());
//            return CascadeVO.filterCascadeVOByCode(allRetList, collect);

        } else if (Constants.DATA_AUTH_OUTPATIENT == dataType) {
            allRetList = outpatientWarnService.getOutpatientType();
            Collections.sort(allRetList);
        } else if (Constants.DATA_AUTH_UNKNOWN_REASON == dataType){
            allRetList = unknownReasonWarnService.getUnknownReason();
            Collections.sort(allRetList);
        } else if (Constants.DATA_AUTH_PREVENTION_CONTROL == dataType) {
            allRetList = preventionControlWarnService.getPreventionControl();
            Collections.sort(allRetList);
        } else if (Constants.DATA_AUTH_CUSTOMIZED == dataType){
            allRetList = customizedWarningTaskService.getCustomizedNameList();
        }
        else {
            throw new MedicalBusinessException("11441017", "未知的dataType");
        }



        //表中无数据权限 拥有所有权限
        if (CollectionUtils.isEmpty(dataAuths)){
            return allRetList;
        }

        //表中有数据，取表中的
        List<String> authIds = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
        return allRetList.stream()
                .filter(cascadeVO -> authIds.contains(cascadeVO.getValue())).collect(Collectors.toList());

    }
}
