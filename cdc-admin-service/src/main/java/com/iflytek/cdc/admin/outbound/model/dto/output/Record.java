package com.iflytek.cdc.admin.outbound.model.dto.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 返回记录信息对象
 * <AUTHOR>
 */
@Data
@Builder
public class Record {

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("居民姓名")
    private String dwellerName;

    @ApiModelProperty("手机号")
    private String telephone;

    @ApiModelProperty("任务状态: 0：成功,2：通话中,3：无法接通,4：关机,5：用户正忙," +
                      "6：空号,7：号码错误,9：停机,15：客户接听后并主动挂机,20：用户未接," +
                      "22：来电提醒,22：转来电提醒,23：呼入限制,26：网络故障,28：线路故障,30：呼叫失败,300 ：任务执行失败")
    private String resultCode;

    @ApiModelProperty("接通状态: 1：正常回答,2：自动留言,3：接听不便,4：居民已死亡,5：家属不能代答,6：不愿配合,7：号码错误,8：中断")
    private String endNode;

    @ApiModelProperty("电话任务状态描述")
    private String callResult;

    @ApiModelProperty("电话拨打时间")
    private Long callTime;

    @ApiModelProperty("拨打次数")
    private String actualCallCount;

    @ApiModelProperty("通话时长（秒）")
    private String callTimeLength;

    @ApiModelProperty("接通状态: 1：正常回答,2：自动留言,3：接听不便,4：居民已死亡,5：家属不能代答,6：不愿配合,7：号码错误,8：中断")
    private String connectStatus;

    @ApiModelProperty("第三方ID")
    private String relationId;

    @ApiModelProperty("身份证")
    private String idCard;

    ////////////////TODO 动态参数添加////////////////
    /**
     * 当前话术返回状态：1有、2没有、3不确定、4没看过病、5无关回答
     * 若话术发生改变，以上状态可能也不一样
     */
    private String wetherDiarrhea;
    private String wetherDiarrheaConvent;
    private String durationTimeConvent;

    /**
     * 当前话术返回状态：1有、2没有、3不确定、4无关回答
     * 若话术发生改变，以上状态可能也不一样
     */
    private String diarrheaCrowd;
    private String diarrheaFrequencyConvent;

    /**
     * 回答问题1
     */
    private String extractText1;

    /**
     * 回答问题2
     */
    private String extractText2;

    /**
     * 回答问题3
     */
    private String extractText3;

}
