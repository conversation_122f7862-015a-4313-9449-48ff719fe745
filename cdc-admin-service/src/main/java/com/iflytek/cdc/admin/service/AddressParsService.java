package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ExportAddressDTO;
import com.iflytek.cdc.admin.dto.SearchAddressDTO;
import com.iflytek.cdc.admin.entity.AddressStandard;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.RelationAddress;
import com.iflytek.cdc.admin.entity.StandardAddress;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/27 14:23
 **/
public interface AddressParsService {

    /**
     * 分页查询地址详情
     * @param sd
     * @return
     **/
    PageInfo<StandardAddress> queryStandardAddressList(SearchAddressDTO  sd);

    /**
     * 根据标准分页查询映射
     * @param sd
     * @return
     **/
    PageInfo<RelationAddress>  queryRelationAddressList(SearchAddressDTO  sd);

    /**
     * 删除关联地址
     * @param id
     * @return
     **/
    void deleteRelationAddress(String  id);

    /**
     * 导出标准地址数据
     * @param ed
     * @param response
     * @return
     **/
    void exportAddress(ExportAddressDTO  ed, HttpServletResponse response);

    List<CascadeVO> getAllRegion();


}
