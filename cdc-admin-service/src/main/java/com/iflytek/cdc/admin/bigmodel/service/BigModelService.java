package com.iflytek.cdc.admin.bigmodel.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.bigmodel.model.BigModelUrlAndParamVo;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 星火大模型
 * @author: shenghuang
 * @create: 2023/12/6 13:50
 **/
@Slf4j
@Service
@RefreshScope
public class BigModelService {
    @Value("${big.model.url:https://spark-api.xf-yun.com/v3.1/chat}")
    private String hostUrl;
    @Value("${big.model.appId:0cf2b70f}")
    private String appId;
    @Value("${big.model.apiSecret:Yjg5Yzc3Mzg0MTdiOWI1NTQ1MGI4ZGI2}")
    private String apiSecret;
    @Value("${big.model.apiKey:4ec0e453457b6ca07aef4032e792bfbe}")
    private String apiKey;
    @Value("${big.model.domain:generalv3}")
    private String domain;
    @Value("${big.model.temperature:0.5}")
    private float temperature;
    @Value("${big.model.top_k:4}")
    private float topk;
    @Value("${big.model.maxTokens:8192}")
    private int maxTokens;
    @Value("${big.model.maxQuestions:100}")
    private int maxQuestions;
    @Value("${medical.big.model.appKey:zscdc82783a35e11eb9df5fa163e7dscdc}")
    private String medicalBigModelAppKey;
    @Value("${medical.big.model.appSecret:424c129b2857437bbe8642626139f053}")
    private String medicalBigModelAppSecret;
    @Value("${medical.big.model.authUrl:http://*************:19002/v2.2/auth/oauth/token}")
    private String medicalBigModelAuthUrl;
    @Value("${medical.big.model.gptUrl:wss://*************:19002/v2.2/medical-gpt-core/gpt-ws/text}")
    private String medicalBigModelGptUrl;

    public static final Gson gson = new Gson();

    private static final String FORM_QUESTION = "您是一个阅读理解大师，请阅读文本并结合上下文提取以下字段内容:\n" +
            "${\n" +
            "提取要求:\n" +
            "1.返回格式参考(姓名：张三\n性别：男)，字段名称必须保持一致\n" +
            "2.提取不到不返回\n" +
            "3.不要返回与提取无关的信息\n" +
            "4.提取字段涉及日期的格式化为'yyyy-mm-dd',返回示例(2024年1月1日转为2024-01-01)\n" +
            "5.提取字段涉及时间的格式化为'yyyy-mm-dd HH-mm',返回示例(2024年1月1日 1时1分1秒转为2024-01-01 01:01)\n" +
            "}\n\n%s" + "\n\n文本内容如下：%s";;

    private static final RestTemplate restTemplate = new RestTemplate();

    public BigModelUrlAndParamVo getBigModelFormQuestion(String templateQuestion) {
        BigModelUrlAndParamVo bigModelUrlAndParamVo = new BigModelUrlAndParamVo();
        try {
            // 构建鉴权url
            String authUrl = getAuthUrl(hostUrl, apiKey, apiSecret);
            String url = authUrl.replace("http://", "ws://").replace("https://", "wss://");
            bigModelUrlAndParamVo.setWsUrl(url);
            String params = getSparkParamsByContent(String.format(FORM_QUESTION, templateQuestion, "{{}}"));
            bigModelUrlAndParamVo.setParam(params);
        } catch (Exception e) {
            log.error("获取星火大模型文本提取url和参数异常", e);
        }
        return bigModelUrlAndParamVo;
    }


    private List<Map<String, Object>> getWebSocket(OkHttpClient client, Request request, String txt) {
        final boolean[] wsCloseFlag = {false};
        final String[] totalAnswer = {""};
        try {
            WebSocket webSocket = client.newWebSocket(request, new WebSocketListener() {
                @Override
                public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                    super.onClosed(webSocket, code, reason);
                }

                @Override
                public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                    super.onClosing(webSocket, code, reason);
                }

                @Override
                public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
                    super.onFailure(webSocket, t, response);
                    try {
                        if (null != response) {
                            int code = response.code();
                            System.out.println("onFailure code:" + code);
                            System.out.println("onFailure body:" + response.body().string());
                            if (101 != code) {
                                System.out.println("connection failed");
                                System.exit(0);
                            }
                        }
                    } catch (IOException e) {
                        log.error("获取星火大模型结果异常", e);
                    }
                }

                @Override
                public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                    JsonParse myJsonParse = gson.fromJson(text, JsonParse.class);
                    if (myJsonParse.header.code != 0) {
                        System.out.println("发生错误，错误码为：" + myJsonParse.header.code);
                        System.out.println("本次请求的sid为：" + myJsonParse.header.sid);
                        webSocket.close(1000, "");
                    }
                    List<Text> textList = myJsonParse.payload.choices.text;
                    for (Text temp : textList) {
                        System.out.print(temp.content);
                        totalAnswer[0] = totalAnswer[0] + temp.content;
                    }
                    if (myJsonParse.header.status == 2) {
                        // 可以关闭连接，释放资源
                        System.out.println();
                        System.out.println("*************************************************************************************");
                        wsCloseFlag[0] = true;
                    }
                }

                @Override
                public void onMessage(@NotNull WebSocket webSocket, @NotNull ByteString bytes) {
                    super.onMessage(webSocket, bytes);
                }

                @Override
                public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
                    super.onOpen(webSocket, response);
                }
            });
            webSocket.send(txt);
            // 等待服务端返回完毕后关闭
            while (true) {
                Thread.sleep(10);
                if (wsCloseFlag[0]) {
                    break;
                }
            }
            webSocket.close(1000, "");
            if (StrUtil.isNotEmpty(totalAnswer[0])) {
                String[] split = totalAnswer[0].split("\\n");
                return Arrays.stream(split).map(m -> {
                    Map<String, Object> map = new HashMap<>();
                    String[] split1 = m.split("：");
                    if (split1.length > 1) {
                        map.put("key", split1[0]);
                        if ((split1[0].contains("时间") || split1[0].contains("日期")) && split1[1].contains("年") && split1[1].contains("月")) {
                            split1[1] = split1[1].replace("年", "-").replace("月", "-").replace("号", "").replace("日", "");
                            split1[1] = DateUtil.parse(split1[1], "yyyy-MM-dd").toDateStr();
                        }
                        map.put("value", split1[1]);
                        if (!"无".equals(split1[1]) && !"暂无信息".equals(split1[1]) && !"未知".equals(split1[1])
                                && !"无法获取".equals(split1[1]) && !"无法提供".equals(split1[1]) && !"未提供".equals(split1[1])
                                && !split1[1].contains("未提供") && !split1[1].contains("未找到")) {
                            return map;
                        }
                    }
                    return null;
                }).filter(f -> f != null).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取星火大模型结果异常", e);
        }
        return new ArrayList<>();
    }

    /**
     * 鉴权方法
     *
     * @param hostUrl
     * @param apiKey
     * @param apiSecret
     * @return
     */
    private String getAuthUrl(String hostUrl, String apiKey, String apiSecret) {
        try {
            URL url = new URL(hostUrl);
            // 时间
            SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("GMT"));
            String date = format.format(new Date());
            // 拼接
            String preStr = "host: " + url.getHost() + "\n" +
                    "date: " + date + "\n" +
                    "GET " + url.getPath() + " HTTP/1.1";
            // System.err.println(preStr);
            // SHA256加密
            Mac mac = Mac.getInstance("hmacsha256");
            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "hmacsha256");
            mac.init(spec);

            byte[] hexDigits = mac.doFinal(preStr.getBytes(StandardCharsets.UTF_8));
            // Base64加密
            String sha = Base64.getEncoder().encodeToString(hexDigits);
            // 拼接
            String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"", apiKey, "hmac-sha256", "host date request-line", sha);
            // 拼接地址
            HttpUrl httpUrl = Objects.requireNonNull(HttpUrl.parse("https://" + url.getHost() + url.getPath())).newBuilder().//
                    addQueryParameter("authorization", Base64.getEncoder().encodeToString(authorization.getBytes(StandardCharsets.UTF_8))).//
                    addQueryParameter("date", date).//
                    addQueryParameter("host", url.getHost()).//
                    build();

            return httpUrl.toString();
        } catch (Exception e) {
            log.error("星火大模型获取鉴权异常", e);
        }
        return StrUtil.EMPTY;
    }

    private String getSparkParamsByContent(String content) {
        JSONObject requestJson = new JSONObject();

        JSONObject header = new JSONObject();  // header参数
        header.put("app_id", appId);
        header.put("uid", UUID.randomUUID().toString().substring(0, 10));

        JSONObject parameter = new JSONObject(); // parameter参数
        JSONObject chat = new JSONObject();
        chat.put("domain", domain);
        chat.put("temperature", temperature);
        chat.put("top_k", topk);
        chat.put("max_tokens", maxTokens);
        parameter.put("chat", chat);

        JSONObject payload = new JSONObject(); // payload参数
        JSONObject message = new JSONObject();
        JSONArray text = new JSONArray();

//        RoleContent sysContent = new RoleContent();
//        sysContent.role = "system";
//        sysContent.content = "请根据输入信息参考以下模版输出一篇调查报告,并替换报告中所有【】的值,请将报告中“病例分布数据、时间分布数据、空间分布数据、年龄分布数据、实验室分布数据”转换为Markdown表格的格式";
//        text.add(sysContent);

        // 最新问题
        RoleContent roleContent = new RoleContent();
        roleContent.role = "user";
        roleContent.content = content;
        text.add(roleContent);


        message.put("text", text);
        payload.put("message", message);


        requestJson.put("header", header);
        requestJson.put("parameter", parameter);
        requestJson.put("payload", payload);
        System.out.println(requestJson.toString());
        return requestJson.toString();
    }

    private String getMedicalAuthToken() {
        String url = medicalBigModelAuthUrl + "?grant_type=client_credentials&client_id=" + medicalBigModelAppKey + "&client_secret=" + medicalBigModelAppSecret;
        HttpEntity<String> entity = new HttpEntity<>(new HttpHeaders());
        String response;
        try {
            response = restTemplate.postForObject(url, entity, String.class);
        } catch (Exception e) {
            log.error("获取医疗大模型的token失败", e);
            throw new MedicalBusinessException("获取医疗大模型的token失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        if (jsonObject != null) {
            return jsonObject.getString("access_token");
        }
        return response;
    }


    private String getSimpleAudioFinalText(String audioFinalText) {
        JSONArray audioFinalTextArray = JSON.parseArray(audioFinalText);
        JSONArray simpleTextArray = new JSONArray();
        for (int i = 0; i < audioFinalTextArray.size(); i++) {
            JSONObject jsonObject = audioFinalTextArray.getJSONObject(i);
            JSONObject newJsonObject = new JSONObject();
            //去除bg,rl,ed字段
            for (String key : jsonObject.keySet()) {
                if (!key.equals("bg") && !key.equals("rl") && !key.equals("ed")) {
                    newJsonObject.put(key, jsonObject.get(key));
                }
            }
            simpleTextArray.add(newJsonObject);
        }
        return simpleTextArray.toJSONString();
    }

    //返回的json结果拆解
    static class JsonParse {
        Header header;
        Payload payload;
    }

    static class Header {
        int code;
        int status;
        String sid;
    }

    static class Payload {
        Choices choices;
    }

    static class Choices {
        List<Text> text;
    }

    static class Text {
        String role;
        String content;
    }

    static class RoleContent {
        String role;
        String content;

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
