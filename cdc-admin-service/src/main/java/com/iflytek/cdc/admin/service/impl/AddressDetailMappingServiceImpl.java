package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.entity.AddressDetailMapping;
import com.iflytek.cdc.admin.entity.AddressStandard;
import com.iflytek.cdc.admin.mapper.AddressDetailMappingMapper;
import com.iflytek.cdc.admin.mapper.AddressStandardMapper;
import com.iflytek.cdc.admin.service.AddressDetailMappingService;
import org.simmetrics.StringMetric;
import org.simmetrics.metrics.StringMetrics;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Service
public class AddressDetailMappingServiceImpl extends ServiceImpl<AddressDetailMappingMapper, AddressDetailMapping> implements AddressDetailMappingService {

    @Value("${fpva.cdc.address.similarity:0.8}")
    private double similarityThreshold;

    @Resource
    AddressDetailMappingMapper addressDetailMappingMapper;

    @Resource
    AddressStandardMapper addressStandardMapper;

    @Override
    public void saveOrUpdateAddressDetailMapping(AddressDetailMapping addressDetailMapping) {
        addressDetailMappingMapper.saveOrUpdateAddressDetailMapping(addressDetailMapping);
    }

    @Override
    public void manualSimilarity(Date startDate, Date endDate, Integer status, Double similarity) {
        int pageSize = 1000;

        // 默认做多处理100w条数据
        for (int pageIndex = 1; pageIndex <= 1000; pageIndex++) {
            List<AddressDetailMapping> addressDetailMappings = addressDetailMappingMapper.selectByEtlTimeLimit(startDate,
                                                                                                               endDate,
                                                                                                               status,
                                                                                                               similarity,
                                                                                                               pageIndex,
                                                                                                               pageSize);
            if (CollectionUtil.isEmpty(addressDetailMappings)) {
                break;
            }

            List<String> standardIds = addressDetailMappings.stream()
                                                            .map(AddressDetailMapping::getAddressStandardId)
                                                            .collect(Collectors.toList());

            Map<String, AddressStandard> addressStandardMap = addressStandardMapper.selectByIds(standardIds)
                                                                                   .stream()
                                                                                   .collect(Collectors.toMap(
                                                                                           AddressStandard::getId,
                                                                                           Function.identity()));

            List<AddressDetailMapping> updateList = new ArrayList<>(addressDetailMappings.size());
            for (AddressDetailMapping addressDetailMapping : addressDetailMappings) {
                AddressStandard addressStandard = addressStandardMap.get(addressDetailMapping.getAddressStandardId());
                if (Objects.nonNull(addressStandard)) {
                    this.mappingSimilarity(addressStandard, addressDetailMapping);
                    updateList.add(addressDetailMapping);
                }
            }
            addressDetailMappingMapper.batchUpdateSimilarity(updateList);
        }

    }

    @Override
    public void mappingSimilarity(AddressStandard addressStandard, AddressDetailMapping addressDetailMapping) {

        StringMetric smithWatermanGotoh = StringMetrics.smithWatermanGotoh();

        String stdAddressDetailDesc = addressStandard.getAddressDetailDesc();
        String stdAddressName = addressStandard.getAddressName();

        String addressDetail = addressDetailMapping.getAddressDetail();

        double nameCompare = smithWatermanGotoh.compare(addressDetail, stdAddressName);
        double descCompare = smithWatermanGotoh.compare(addressDetail, stdAddressDetailDesc);

        addressDetailMapping.setSimilarity(Math.max(nameCompare, descCompare));
        if (Math.max(nameCompare, descCompare) >= similarityThreshold) {
            addressDetailMapping.setStatus(1);
        } else {
            // 未匹配
            addressDetailMapping.setStatus(0);
        }
    }
}
