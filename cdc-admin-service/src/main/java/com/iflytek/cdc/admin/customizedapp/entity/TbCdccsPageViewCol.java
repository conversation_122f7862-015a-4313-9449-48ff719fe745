package com.iflytek.cdc.admin.customizedapp.entity;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 列表显示配置;
 * <AUTHOR> fengwang35
 * @date : 2024-9-9
 */
@ApiModel(value = "列表显示配置")
@TableName("tb_cdccs_page_view_col")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsPageViewCol extends BaseEntity implements Serializable{
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id ;
    /** 页面id */
    @ApiModelProperty(value = "页面id")
    private String pageId ;
    /** 页面名称 */
    @ApiModelProperty(value = "页面名称")
    private String pageName ;

    @ApiModelProperty(value = "表标识")
    private String tableKey ;
    /** 列标识 */
    @ApiModelProperty(value = "列标识")
    private String colCode ;
    /** 列名称 */
    @ApiModelProperty(value = "列名称")
    private String colName ;
    /** 是否隐藏 */
    @ApiModelProperty(value = "是否隐藏")
    private String isHidden ;
    /** 可排序 */
    @ApiModelProperty(value = "可排序")
    private String isSort ;
    /** 是否是查询字段 */
    @ApiModelProperty(value = "是否是查询字段")
    private String isQuery ;
    /** 查询默认值 */
    @ApiModelProperty(value = "查询默认值")
    private String queryDefaultValue ;
    /** 查询操作符 */
    @ApiModelProperty(value = "查询操作符")
    private String queryOp ;
    /** 显示类型 */
    @ApiModelProperty(value = "显示类型")
    private String showType ;
    /** 扩展属性json */
    @ApiModelProperty(value = "扩展属性json")
    private String extendJson ;
    @ApiModelProperty(value = "模型字段fieldId")
    private String fieldId;
    @ApiModelProperty(value = "是否主键")
    private String isPrimaryKey;
    @ApiModelProperty(value = "sort")
    private Integer sort;
    @ApiModelProperty(value = "是否APP显示")
    private String appShow;
    @ApiModelProperty(value = "app端是否是查询字段")
    private String isAppQuery ;


}