package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.admin.datamodel.es.EsModelUtils;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.admin.datamodel.model.dto.DataFormTemplateQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelTemplateDTO;
import com.iflytek.cdc.admin.datamodel.service.DataFormTemplateService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Date;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class DataFormTemplateServiceImpl implements DataFormTemplateService {

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private EsModelUtils esModelUtils;

    @Resource
    private BatchUidService batchUidService;

    private static final String TB_CDCDM_DATA_FORM_TEMPLATE = "tb_cdcdm_data_form_template";

    @Override
    public TbCdcdmDataFormTemplate getDataFormTemplateById(String formTemplateDetailId) {

        return templateMapper.queryById(formTemplateDetailId);
    }

    @Override
    public void addDataFormTemplate(DataModelTemplateDTO dto) {

        UapUserPo uapUserPo = USER_INFO.get();
        String id = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_FORM_TEMPLATE));
        TbCdcdmDataFormTemplate formTemplate = new TbCdcdmDataFormTemplate();
        BeanUtils.copyProperties(dto, formTemplate);
        formTemplate.setFormTemplateDetailId(id);
        formTemplate.setCreator(uapUserPo == null ? null : uapUserPo.getName());
        formTemplate.setCreateTime(new Date());
        formTemplate.setUpdater(uapUserPo == null ? null : uapUserPo.getName());
        formTemplate.setUpdateTime(new Date());
        formTemplate.setIsEnable(Constants.STATUS_ON);
        formTemplate.setIsDeleted(Constants.NOT_DELETED);

        if (StrUtil.isNotBlank(formTemplate.getEsIndexName())) {
            String esTemplate = esModelUtils.buildEsIndexTemplate(formTemplate.getModelId(),
                    formTemplate.getModelVersionId(), formTemplate.getEsIndexName());
            formTemplate.setEsIndexTemplate(esTemplate);
        }
        templateMapper.insert(formTemplate);
    }

    @Override
    public void updateDataFormTemplate(DataModelTemplateDTO dto) {

        UapUserPo uapUserPo = USER_INFO.get();
        TbCdcdmDataFormTemplate formTemplate = new TbCdcdmDataFormTemplate();
        BeanUtils.copyProperties(dto, formTemplate);
        //将code作为唯一键存到id字段
        formTemplate.setMasterTableId(dto.getMasterTableCode());
        formTemplate.setUpdateTime(new Date());
        formTemplate.setUpdater(uapUserPo == null ? null : uapUserPo.getName());

        if (StrUtil.isNotBlank(formTemplate.getEsIndexName())) {
            String esTemplate = esModelUtils.buildEsIndexTemplate(formTemplate.getModelId(),
                    formTemplate.getModelVersionId(), formTemplate.getEsIndexName());
            formTemplate.setEsIndexTemplate(esTemplate);
        }
        templateMapper.update(formTemplate);
    }

    @Override
    public PageInfo<TbCdcdmDataFormTemplate> getAllModelTemplate(DataFormTemplateQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(templateMapper.getAllModelTemplate(dto));
    }

    @Override
    public String getConfigInfoByDiseaseCode(String diseaseCode) {

        return templateMapper.getConfigInfoByDiseaseCode(diseaseCode);
    }
}
