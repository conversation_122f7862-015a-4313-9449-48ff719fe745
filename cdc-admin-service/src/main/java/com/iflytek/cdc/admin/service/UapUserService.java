package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.common.vo.uap.UapUserSearchQueryDTO;
import com.iflytek.cdc.admin.dto.BusinessPersonQueryVo;
import com.iflytek.cdc.admin.dto.outcall.BusinessPersonResult;
import com.iflytek.cdc.admin.entity.UapAuthNode;
import com.iflytek.cdc.admin.entity.UapUserExtend;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/12 10:33
 **/
@Deprecated
public interface UapUserService {


    UapUserExtend getUserInfo(String loginUserId);

    List<UapAuthNode> getBigScreenMenu();

    BusinessPersonResult getUapUserList(String loginUserId, String loginOrgId, BusinessPersonQueryVo queryVo);

    /**
     * 根据orgId 查询机构用户
     */
     PageInfo<UapOrgUser> searchUapUserByOrgId(UapUserSearchQueryDTO queryDTO, String loginUserId);

     List<UapOrgUser> listByOrgId(String orgId);
}
