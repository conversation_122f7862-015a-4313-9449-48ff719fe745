package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportTaskDTO;
import com.iflytek.cdc.admin.dto.ExportTaskQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;

/**
 * <p>
 * 导出记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface ExportTaskService extends IService<TbCdcmrExportTask> {

    TbCdcmrExportTask add(ExportTaskDTO exportTask, String loginUserId);

    int getProceedCount();

    TbCdcmrExportTask getProceedDoneTask(String taskParams, String taskUrl, String userId);
    TbCdcmrExportTask checkExistTask(ExportTaskDTO exportTask, String loginUserId);

    /**
     * true 可以继续执行导出
     * false 无法继续导出 数量超过了
     * @return
     */
    Boolean checkExportTaskCount();

    PageInfo<TbCdcmrExportTask> queryList(ExportTaskQueryDTO queryDTO);

    /**
     * 任务变更
     */
    void updateTask(ExportTaskDTO taskDTO);

    PageInfo<ExportApplicationListDTO> queryExportRecordList(ExportTaskQueryDTO queryDTO);
}
