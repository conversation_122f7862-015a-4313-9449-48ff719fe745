package com.iflytek.cdc.admin.customizedapp.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.constants.TaskSettingConstants;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsReminderTaskSettingDetail;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.customizedapp.model.dto.TaskSettingQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.ReminderTaskSettingDetailService;
import com.iflytek.cdc.admin.customizedapp.service.TaskSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "任务配置接口")
@RequestMapping("/pt/{version}/app/taskSetting")
public class TaskSettingController {
    @Resource
    private TaskSettingService taskSettingService;
    
    @Resource
    private ReminderTaskSettingDetailService reminderTaskSettingDetailService;

    @PostMapping("/add")
    @ApiOperation("添加任务配置")
    public void add(@RequestBody TbCdccsTaskSetting input){
        taskSettingService.create(input);
    }

    @PostMapping("/update")
    @ApiOperation("修改任务配置")
    public void update(@RequestBody TbCdccsTaskSetting input){
        taskSettingService.update(input);
    }


    @GetMapping("/load")
    @ApiOperation("分页查询")
    public TbCdccsTaskSetting load(@RequestParam String id){
        return taskSettingService.loadCascade(id);
    }
//    @GetMapping("/listByPageId")
//    @ApiOperation("根据pageId 查询")
//    public List<TbCdccsTaskSetting> listByPageId(@RequestParam String pageId){
//        return taskSettingService.listByPageId(pageId);
//    }

    @PostMapping("/listCascade")
    @ApiOperation("查询所有的任务配置，关系任务详情配置")
    public List<TbCdccsTaskSetting> listCascadeAll(@RequestBody TaskSettingQueryDTO queryDTO){
        return taskSettingService.listCascade(queryDTO);
    }

    @PostMapping("/listDetailByIds")
    @ApiOperation("根据ids 查询任务详情")
    public List<TbCdccsReminderTaskSettingDetail> listDetailByIds(@RequestBody List<String> ids){
        return reminderTaskSettingDetailService.listByIds(ids);
    }


    @PostMapping("/pageList")
    @ApiOperation("分页查询")
    public PageInfo<TbCdccsTaskSetting> pageList(@RequestBody TaskSettingQueryDTO queryDTO){
        return taskSettingService.pageList(queryDTO);
    }


    @PostMapping("/delete")
    @ApiOperation("删除")
    public void delete(@RequestParam String id){
        taskSettingService.deleteById(id);
    }

    @GetMapping("/getConstants")
    @ApiOperation("获取常量")
    public Object getConstants(){
        return TaskSettingConstants.taskSettingConstants;
    }


}
