package com.iflytek.cdc.admin.datamodel.model.vo;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据模型信息 以及 对应的表单配置信息
 * */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataModelFormConfigVO {

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型名称
     */
    @ApiModelProperty(name = "数据模型名称")
    private String modelName ;

    /**
     * 数据模型分类
     */
    @ApiModelProperty(name = "数据模型分类")
    private String modelType ;

    /**
     * 是否是系统内置 1-是，0-否，默认0
     */
    @ApiModelProperty(name = "是否是系统内置 1-是，0-否，默认0")
    private Integer isBuiltIn ;

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "数据模型版本ID")
    private String modelVersionId ;

    /**
     * 数据模型标签
     */
    @ApiModelProperty(name = "数据模型标签")
    private String modelLabel ;

    /**
     * 版本
     */
    @ApiModelProperty(name = "版本")
    private String version ;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    private String note ;

    @ApiModelProperty(value = "表单配置")
    List<TbCdcdmDataModelForm> formList;
}
