//package com.iflytek.cdc.admin.interceptor;
//
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.annotation.Resource;
//
///**
// * 拦截器注册
// *
// * <AUTHOR>
// * @date 2022-06-16 14:18
// */
//@Component
//public class WebMvcConfig implements WebMvcConfigurer {
//
//    @Resource
//    UserInfoInterceptor userInfoInterceptor;
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(userInfoInterceptor);
//    }
//
//}
