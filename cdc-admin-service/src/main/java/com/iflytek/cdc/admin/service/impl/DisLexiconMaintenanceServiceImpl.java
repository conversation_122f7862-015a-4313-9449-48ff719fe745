package com.iflytek.cdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo;
import com.iflytek.cdc.admin.enums.AssociationTypeEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseInfoMapper;
import com.iflytek.cdc.admin.service.DisLexiconMaintenanceService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.QueryAssociationPageVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;
@Service
public class DisLexiconMaintenanceServiceImpl extends AbstractSyncMdmDataService implements DisLexiconMaintenanceService {
    @Autowired
    private DisLexiconMapper disLexiconMapper;
    @Autowired
    private DiseaseDiagnosisLexiconMapper diseaseDiagnosisLexiconMapper;
    @Autowired
    private LexiconAssociationMapper lexiconAssociationMapper;
    @Autowired
    public InfectiousDiseasesMapper infectiousDiseasesMapper;
    @Autowired
    LexiconAssociationDirectoryMapper lexiconAssociationDirectoryMapper;

    @Autowired
    private TbCdcmrInfectedDiseaseInfoMapper tbCdcmrInfectedDiseaseInfoMapper;

    @Override
    public PageData<InfectiousDiseases> queryInfecPageInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        PageMethod.startPage(searchInfecInfoDTO.getPageIndex(), searchInfecInfoDTO.getPageSize());
        List<InfectiousDiseases> infectiousDiseases = infectiousDiseasesMapper.queryInfecInfo2(searchInfecInfoDTO);
        PageInfo<InfectiousDiseases> pageInfo = new PageInfo<>(infectiousDiseases);
        PageData<InfectiousDiseases> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    public DisLexiconMaintenanceServiceImpl(UapUserApi uapUserApi, MdmDataSyncService mdmDataSyncService, BatchUidService batchUidService) {
        super(uapUserApi, mdmDataSyncService, batchUidService);
    }

    @Override
    public PageData<DisLexicon> getDiseaseLexiconPage(SearchDiseaseLexiconDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<DisLexicon> infectDisLexicon = disLexiconMapper.getDiseaseLexiconPage(dto);
        PageInfo<DisLexicon> pageInfo = new PageInfo<>(infectDisLexicon);
        PageData<DisLexicon> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public PageData<QueryAssociationPageVO> getDiseaseLexiconAssociationPage(QueryAssociationPageDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());

        SearchDiseaseLexiconDTO searchDiseaseLexiconDTO = new SearchDiseaseLexiconDTO();
//        如果值含义不为空
        if (StringUtils.isNotBlank(dto.getName())) {
            searchDiseaseLexiconDTO.setDiseasesName(dto.getName());
        }
//        如果值编码不为空
        if (StringUtils.isNotBlank(dto.getCode())) {
            searchDiseaseLexiconDTO.setDiseaseCode(dto.getCode());
        }
        List<DisLexicon> infectDisLexiconList = disLexiconMapper.getDiseaseLexiconPage(searchDiseaseLexiconDTO);
        List<QueryAssociationPageVO> vo = new ArrayList<>();
//        如果是左边列表
        if (StringUtils.isBlank(dto.getLeftId())) {
// 遍历每个DisLexicon，查询是否已关联
            for (DisLexicon disLexicon : infectDisLexiconList) {
                String leftId = disLexicon.getId();
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setId(disLexicon.getId());
                associationList.setName(disLexicon.getDiseasesName());
                associationList.setCode(disLexicon.getDiseasesCode());
                // 查询该左词库的关联状态
                List<LexiconAssociation> associations = lexiconAssociationMapper.findByLeftLexiconId(leftId, dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        //            如果是右边列表
        else{
            for (DisLexicon disLexicon : infectDisLexiconList) {
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setName(disLexicon.getDiseasesName());
                associationList.setCode(disLexicon.getDiseasesCode());
                associationList.setId(disLexicon.getId());
                // 根据关联类型查询关联状态
                List<LexiconAssociation> associations = getAssociation(dto.getLeftId(), disLexicon.getId(), dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        PageInfo<QueryAssociationPageVO> pageInfo = new PageInfo<>(vo);
        PageData<QueryAssociationPageVO> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public PageData<QueryAssociationPageVO> getDiseaseDiagnosisAssociationPage(QueryAssociationPageDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        SearchDiagnosisLexiconDTO searchDiseaseDiagnosisLexiconDTO = new SearchDiagnosisLexiconDTO();
        if (StringUtils.isNotBlank(dto.getName())) {
            searchDiseaseDiagnosisLexiconDTO.setDiagnosisName(dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getCode())) {
            searchDiseaseDiagnosisLexiconDTO.setDiagnosisCode(dto.getCode());
        }
        List<DiseaseDiagnosisLexicon> diagnosisLexicons = diseaseDiagnosisLexiconMapper.getDiagnosisLexiconPage(searchDiseaseDiagnosisLexiconDTO);
        List<QueryAssociationPageVO> vo = new ArrayList<>();
        //        如果是左边列表
        if (StringUtils.isBlank(dto.getLeftId())) {
// 遍历每个DisLexicon，查询是否已关联
            for (DiseaseDiagnosisLexicon diseaseDiagnosisLexicon : diagnosisLexicons) {
                String leftId = diseaseDiagnosisLexicon.getId();
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setId(diseaseDiagnosisLexicon.getId());
                associationList.setName(diseaseDiagnosisLexicon.getDiagnosisName());
                associationList.setCode(diseaseDiagnosisLexicon.getDiagnosisCode());
                // 查询该左词库的关联状态
                List<LexiconAssociation> associations = lexiconAssociationMapper.findByLeftLexiconId(leftId, dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        //            如果是右边列表
        else{
            for (DiseaseDiagnosisLexicon diseaseDiagnosisLexicon : diagnosisLexicons) {
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setId(diseaseDiagnosisLexicon.getId());
                associationList.setName(diseaseDiagnosisLexicon.getDiagnosisName());
                associationList.setCode(diseaseDiagnosisLexicon.getDiagnosisCode());
                // 根据关联类型查询关联状态
                List<LexiconAssociation> associations = getAssociation(dto.getLeftId(), diseaseDiagnosisLexicon.getId(), dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        PageInfo<QueryAssociationPageVO> pageInfo = new PageInfo<>(vo);
        PageData<QueryAssociationPageVO> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;

    }

    @Override
    public PageData<QueryAssociationPageVO> getInfectiousAssociationPage(QueryAssociationPageDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        SearchInfecInfoDTO searchInfecInfoDTO = new SearchInfecInfoDTO();
        if (StringUtils.isNotBlank(dto.getName())) {
            searchInfecInfoDTO.setDiseasesName(dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getCode())) {
            searchInfecInfoDTO.setDiseaseCode(dto.getCode());
        }
        List<TbCdcmrInfectedDiseaseInfo> infectiousDiseases = tbCdcmrInfectedDiseaseInfoMapper.queryInfecInfo(searchInfecInfoDTO);
        List<QueryAssociationPageVO> vo = new ArrayList<>();
        //        如果是左边列表
        if (StringUtils.isBlank(dto.getLeftId())) {
// 遍历每个DisLexicon，查询是否已关联
            for (TbCdcmrInfectedDiseaseInfo infectiousDisease : infectiousDiseases) {
                String leftId = infectiousDisease.getId();
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setId(infectiousDisease.getId());
                associationList.setName(infectiousDisease.getDiseaseName());
                associationList.setCode(infectiousDisease.getId());
                // 查询该左词库的关联状态
                List<LexiconAssociation> associations = lexiconAssociationMapper.findByLeftLexiconId(leftId, dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        //            如果是右边列表
        else{
            for (TbCdcmrInfectedDiseaseInfo infectiousDisease : infectiousDiseases) {
                QueryAssociationPageVO associationList = new QueryAssociationPageVO();
                associationList.setName(infectiousDisease.getDiseaseName());
                associationList.setCode(infectiousDisease.getId());
                associationList.setId(infectiousDisease.getId());
                // 根据关联类型查询关联状态
                List<LexiconAssociation> associations = getAssociation(dto.getLeftId(), infectiousDisease.getId(), dto.getAssociationType());
                // 判断是否关联
                setAssociationStatus(associationList, associations);
                // 按照dto的associationFilter过滤
                if (StringUtils.isNotBlank(dto.getAssociationFilter())) {
                    if (! associationList.getIsAssociation().equals(dto.getAssociationFilter())) {
                        continue;
                    }
                }
                vo.add(associationList);
            }
        }
        PageInfo<QueryAssociationPageVO> pageInfo = new PageInfo<>(vo);
        PageData<QueryAssociationPageVO> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    private void setAssociationStatus(QueryAssociationPageVO associationList, List<LexiconAssociation> associations) {
        if (associations != null && !associations.isEmpty()) {
            associationList.setIsAssociation(Constants.COMMON_STRNUM_ONE);
        } else {
            associationList.setIsAssociation(Constants.COMMON_STRNUM_ZERO);
        }
    }
    /**
     * 根据关联类型查询单个关联数据
     */
    private List<LexiconAssociation> getAssociation(String leftId, String rightId, String associationType) {
        // 创建查询条件
        QueryWrapper<LexiconAssociation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("left_lexicon_id", leftId);
        queryWrapper.eq("right_lexicon_id", rightId);
        queryWrapper.eq("association_type", associationType);
        queryWrapper.eq("is_association", Constants.COMMON_STRNUM_ONE);
        return lexiconAssociationMapper.selectList(queryWrapper);
    }

    @Transactional
    @Override
    public void updateDiagnosisLexicon(DiagnosisLexiconDTO dto) {
         DiseaseDiagnosisLexicon diseaseDiagnosisLexicon = new DiseaseDiagnosisLexicon();
//         复制dto到diseaseDiagnosisLexicon
        BeanUtils.copyProperties(dto,diseaseDiagnosisLexicon);
        diseaseDiagnosisLexicon.setUpdateUser(USER_INFO.get().getName());
        diseaseDiagnosisLexicon.setUpdateTime(new Date());
        diseaseDiagnosisLexiconMapper.updateById(diseaseDiagnosisLexicon);
    }

    @Override
    public PageData<DiseaseDiagnosisLexicon> getDiagnosisLexiconPage(SearchDiagnosisLexiconDTO dto) {
        PageMethod.startPage(dto.getPageIndex(), dto.getPageSize());
        List<DiseaseDiagnosisLexicon> diagnosisLexicons = diseaseDiagnosisLexiconMapper.getDiagnosisLexiconPage(dto);
        PageInfo<DiseaseDiagnosisLexicon> pageInfo = new PageInfo<>(diagnosisLexicons);
        PageData<DiseaseDiagnosisLexicon> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }
    @Transactional
    @Override
    public void deleteDiseaseLexicon(DiseaseLexiconDTO dto) {
        UpdateWrapper<DisLexicon> qw = new UpdateWrapper<>();
        qw.set("is_delete", DeleteFlagEnum.YES.getCode());
        qw.eq("id", dto.getId());
        disLexiconMapper.update(null, qw);
    }

    @Transactional
    @Override
    public ApiResult addAssociationDirectory(LexiconAssociationDirectory dto) {
        // 判断目录名称是否为空
        if (StringUtils.isEmpty(dto.getDirectoryName())) {
            return ApiResult.failed("目录名称不能为空");
        }
//        目录类型校验
        if (!EnumUtils.isValidEnum(AssociationTypeEnum.class, dto.getAssociationType())) {
            return ApiResult.failed("无效的目录类型");
        }
//        判断目录名称是否重复
        QueryWrapper<LexiconAssociationDirectory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("directory_name", dto.getDirectoryName());
        List<LexiconAssociationDirectory> list = lexiconAssociationDirectoryMapper.selectList(queryWrapper);
        if (list != null && list.size() > 0) {
            return ApiResult.failed("目录名称已存在");
        }
        dto.setId(String.valueOf(batchUidService.getUid(TableName.LEXICON_ASSOCIATION_DIRECTORY)));
        lexiconAssociationDirectoryMapper.insert(dto);
        return ApiResult.ok();
    }

    @Transactional
    @Override
    public ApiResult addDiseaseLexicon(DiseaseLexiconDTO dto) {
//        判断病种编码是否为空
        if (StringUtils.isBlank(dto.getDiseasesCode())) {
            return ApiResult.failed("病种编码不能为空");
        }
//        判断病种名称是否为空
        if (StringUtils.isBlank(dto.getDiseasesName())) {
            return ApiResult.failed("病种名称不能为空");
        }
// 查询是否存在相同的 diseases_code
        QueryWrapper<DisLexicon> queryWrapperCode = new QueryWrapper<>();
        queryWrapperCode.eq("diseases_code", dto.getDiseasesCode());
        DisLexicon codeResult = disLexiconMapper.selectOne(queryWrapperCode);
        if (codeResult != null) {
            return ApiResult.failed("病种编码已存在");
        }

// 查询是否存在相同的 diseases_name
        QueryWrapper<DisLexicon> queryWrapperName = new QueryWrapper<>();
        queryWrapperName.eq("diseases_name", dto.getDiseasesName());
        DisLexicon nameResult = disLexiconMapper.selectOne(queryWrapperName);
        if (nameResult != null) {
            return ApiResult.failed("病种名称已存在");
        }
//        插入新纪录
        DisLexicon disLexicon = new DisLexicon();
        BeanUtils.copyProperties(dto, disLexicon);
        Date date = new Date();
        disLexicon.setId(String.valueOf(batchUidService.getUid(TableName.INFECTIOUS_DISASES_LEXICON)));
        disLexicon.setCreateTime(date);
        disLexicon.setCreateUser(USER_INFO.get().getName());
        disLexicon.setUpdateUser(USER_INFO.get().getName());
        disLexicon.setUpdateTime(date);
        disLexicon.setIsDelete(Constants.COMMON_STRNUM_ZERO);
        disLexiconMapper.insert(disLexicon);
        return ApiResult.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApiResult associateLexicon(LexiconAssociationDTO dto) {
//        校验关联类型
        if (!dto.isAssociationTypeValid()) {
            return ApiResult.failed("关联类型错误");
        }
//        关联操作
        if (dto.getIsAssociation().equals(Constants.COMMON_STRNUM_ONE)) {
            // 根据关联类型判断
            boolean isLeftParent = isLeftParent(dto.getAssociationType());

            // 如果是左边病种，则检查左边是否为父级
            if (isLeftParent) {
                // 获取左侧病种（父级病种）ID的所有子级病种
                List<String> childDiseases = getChildDiseases(dto.getLeftLexiconId());

                // 检查这些子级病种是否都已关联
                if (childDiseases.isEmpty() || areAllChildrenAssociated(childDiseases, dto.getRightLexiconId(), dto.getAssociationType())) {
                    LexiconAssociation lexiconAssociation = new LexiconAssociation();
//                    将dto的值复制到lexiconAssociation对象中
                    BeanUtils.copyProperties(dto, lexiconAssociation);
                    lexiconAssociation.setId(String.valueOf(batchUidService.getUid(TableName.LEXICON_ASSOCIATION)));
                    // 如果所有子级病种都已关联，则允许父级病种进行关联
                    lexiconAssociationMapper.insert(lexiconAssociation);
                } else {
                    return ApiResult.failed("父级病种的子级病种没有完全关联，不能进行关联！");
                }
            } else {
                // 获取右侧病种（父级病种）ID的所有子级病种
                List<String> childDiseases = getChildDiseases(dto.getRightLexiconId());
                // 检查这些子级病种是否都已关联
                if (childDiseases.isEmpty() || areAllChildrenAssociated(childDiseases, dto.getLeftLexiconId(), dto.getAssociationType())) {
                    LexiconAssociation lexiconAssociation = new LexiconAssociation();
//                    将dto的值复制到lexiconAssociation对象中
                    BeanUtils.copyProperties(dto, lexiconAssociation);
                    lexiconAssociation.setId(String.valueOf(batchUidService.getUid(TableName.LEXICON_ASSOCIATION)));
                    // 如果所有子级病种都已关联，则允许父级病种进行关联
                    lexiconAssociationMapper.insert(lexiconAssociation);
                } else {
                    return ApiResult.failed("父级病种的子级病种没有完全关联，不能进行关联！");
                }
            }
        } else {
//            取消关联操作
//            通过dto.leftLexiconId和dto.rightLexiconId查询关联id
            QueryWrapper<LexiconAssociation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("left_lexicon_id", dto.getLeftLexiconId());
            queryWrapper.eq("right_lexicon_id", dto.getRightLexiconId());
            queryWrapper.eq("association_type", dto.getAssociationType());
            LexiconAssociation association = lexiconAssociationMapper.selectOne(queryWrapper);
            if (association == null) {
                return ApiResult.failed("取消关联失败");
            }
            association.setIsAssociation(Constants.COMMON_STRNUM_ZERO);
            lexiconAssociationMapper.updateById(association);
        }
        return ApiResult.ok();
    }

    // 判断该关联类型是左边还是右边需要检查父级病种
    private boolean isLeftParent(String associationType) {
        return associationType.equals("DISEASE_TO_DIAGNOSIS") || associationType.equals("DISEASE_TO_INFECTIOUS");
    }

    // 根据父级病种的ID获取所有子级病种
    private List<String> getChildDiseases(String parentDiseaseId) {
        // 查询数据库获取所有子级病种ID
        return disLexiconMapper.getChildDiseases(parentDiseaseId);
    }

    // 检查所有子级病种是否都已关联
    private boolean areAllChildrenAssociated(List<String> childDiseases, String rightLexiconId, String associationType) {
        for (String leftLexiconId : childDiseases) {
            // 查询每个子级病种是否已经关联
            boolean isAssociated = lexiconAssociationMapper.isDiseaseAssociated(leftLexiconId, rightLexiconId, associationType);
            if (!isAssociated) {
                return false; // 如果有任何一个子级没有关联，返回false
            }
        }
        return true; // 所有子级都已关联
    }

    @Transactional
    @Override
    public void updateDiseaseLexicon(DiseaseLexiconDTO dto) {
        DisLexicon diseaseLexicon = new DisLexicon();
        BeanUtils.copyProperties(dto,diseaseLexicon);
        diseaseLexicon.setUpdateUser(USER_INFO.get().getName());
        diseaseLexicon.setUpdateTime(new Date());
        disLexiconMapper.updateById(diseaseLexicon);
    }


    @Override
    public ApiResult exportAssociationDirectory(AssociationExportDTO dto) throws Exception {
        List<LexiconAssociationList> exportList = new ArrayList<>();
// 校验 associationType
        if (dto.getAssociationTypes() == null || dto.getAssociationTypes().isEmpty()) {
            return ApiResult.failed("关联类型不能为空");
        }
        for (String type : dto.getAssociationTypes()) {
            if (!EnumUtils.isValidEnum(AssociationTypeEnum.class, type)) {
                return ApiResult.failed("无效的关联类型");
            }
        }
        // 导出 Excel
        return exportToExcel(dto.getAssociationTypes());
    }

    /**
     * 根据不同的关联类型构建 LexiconAssociationList
     */
    private LexiconAssociationList buildAssociationList(String associationType, LexiconAssociation lexiconAssociation) {
        String leftId = lexiconAssociation.getLeftLexiconId();
        String rightId = lexiconAssociation.getRightLexiconId();
        LexiconAssociationList lexiconAssociationList = new LexiconAssociationList();
        AssociationTypeEnum associationTypeEnum = AssociationTypeEnum.valueOf(associationType);
        switch (associationTypeEnum) {
            case DISEASE_TO_DIAGNOSIS:
                DisLexicon left1 = disLexiconMapper.selectById(leftId);
                DiseaseDiagnosisLexicon right1 = diseaseDiagnosisLexiconMapper.selectById(rightId);
                if (left1 != null && right1 != null) {
                    lexiconAssociationList.setLeftLexiconName(left1.getDiseasesName());
                    lexiconAssociationList.setLeftLexiconCode(left1.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconCode(right1.getDiagnosisCode());
                    lexiconAssociationList.setRightLexiconName(right1.getDiagnosisName());
                }
                break;
            case DIAGNOSIS_TO_DISEASE:
                DiseaseDiagnosisLexicon left2 = diseaseDiagnosisLexiconMapper.selectById(leftId);
                DisLexicon right2 = disLexiconMapper.selectById(rightId);
                if (left2 != null && right2 != null) {
                    lexiconAssociationList.setLeftLexiconName(left2.getDiagnosisName());
                    lexiconAssociationList.setLeftLexiconCode(left2.getDiagnosisCode());
                    lexiconAssociationList.setRightLexiconCode(right2.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconName(right2.getDiseasesName());
                }
                break;
            case DISEASE_TO_INFECTIOUS:
                DisLexicon left3 = disLexiconMapper.selectById(leftId);
                InfectiousDiseases right3 = infectiousDiseasesMapper.selectByPrimaryKey(rightId);
                if (left3 != null && right3 != null) {
                    lexiconAssociationList.setLeftLexiconName(left3.getDiseasesName());
                    lexiconAssociationList.setLeftLexiconCode(left3.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconCode(right3.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconName(right3.getDiseasesName());
                }
                break;
            case INFECTIOUS_TO_DISEASE:
                InfectiousDiseases left4 = infectiousDiseasesMapper.selectByPrimaryKey(leftId);
                DisLexicon right4 = disLexiconMapper.selectById(rightId);
                if (left4 != null && right4 != null) {
                    lexiconAssociationList.setLeftLexiconName(left4.getDiseasesName());
                    lexiconAssociationList.setLeftLexiconCode(left4.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconCode(right4.getDiseasesCode());
                    lexiconAssociationList.setRightLexiconName(right4.getDiseasesName());
                }
                break;
            default:
                return null;  // 未匹配到有效的关联类型，返回null
        }
        return lexiconAssociationList;
    }
    private ApiResult exportToExcel(List<String> associationTypes) throws Exception {
        // 使用 Apache POI 导出数据为 Excel 文件
        HSSFWorkbook wb = new HSSFWorkbook();
        for (String associationType : associationTypes) {
            QueryWrapper<LexiconAssociation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("association_type", associationType);
            queryWrapper.eq("is_association", Constants.COMMON_STRNUM_ONE);
            List<LexiconAssociation> associationList = lexiconAssociationMapper.selectList(queryWrapper);

            List<LexiconAssociationList> exportList = new ArrayList<>();
            for (LexiconAssociation lexiconAssociation : associationList) {
                LexiconAssociationList lexiconAssociationList = buildAssociationList(associationType, lexiconAssociation);
                if (lexiconAssociationList != null) {
                    exportList.add(lexiconAssociationList);
                }
            }

            // 创建 Sheet
            HSSFSheet sheet = wb.createSheet(AssociationTypeEnum.getByType(associationType).getDescription());
            sheet.setDefaultColumnWidth(16);
            sheet.setColumnWidth(2, 10000);

            // 创建表头
            HSSFRow header = sheet.createRow(0);
            header.createCell(0).setCellValue("值代码");
            header.createCell(1).setCellValue("值含义");
            header.createCell(2).setCellValue("关联值代码");
            header.createCell(3).setCellValue("关联值含义");

            // 填充数据
            int rowIndex = 1;
            for (LexiconAssociationList association : exportList) {
                HSSFRow row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(association.getLeftLexiconCode());
                row.createCell(1).setCellValue(association.getLeftLexiconName());
                row.createCell(2).setCellValue(association.getRightLexiconCode());
                row.createCell(3).setCellValue(association.getRightLexiconName());
            }
        }

        // 设置响应头部，返回 Excel 文件
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        if (response != null) {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("关联目录导出", "UTF-8") + ".xls");
            response.setCharacterEncoding("utf-8");
        }

        try (OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
            wb.close();
        } catch (IOException e) {
            e.printStackTrace();
            return ApiResult.failed("Excel导出失败");
        }

        return ApiResult.ok("Excel导出成功");
    }
}
