package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.*;
import com.iflytek.cdc.admin.service.AmapService;
import com.iflytek.cdc.admin.util.BeanCombineUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * @ClassName AddressStandardizeController
 * @Description 模糊地址标准化接口
 * <AUTHOR>
 * @Date 2021/7/14 9:59
 * @Version 1.0
 */
@Api(value = "/", description = "地址智能解析 相关接口", tags = "地址智能解析 接口")
@RestController
@Slf4j
@RequestMapping("/v1/pb/address/standardize")
public class AddressStandardizeController {

    @Autowired
    private AmapService amapService;


    @ApiOperation(
            value = "地址智能解析",
            notes = "地址智能解析,通过解析 医疗机构所在地 所归属的标准行政区划 缩小对模糊地址的检索范围"
    )
    @PostMapping("/fuzzyAddressNormalization")
    public AddressStandardVo fuzzyAddressNormalization(@RequestBody @Valid AddressFuzzyNormalizationDTO addressFuzzyNormalizationLatestDtoParams) {
        AddressFuzzyNormalizationDTO addressFuzzyNormalizationDto = null;
        try {
            // Bean对象去除属性NULL值并赋予默认值
            addressFuzzyNormalizationDto = BeanCombineUtil.setFieldValueNotNull(addressFuzzyNormalizationLatestDtoParams);
        } catch (Exception e) {
            log.error("{}", e);
        }
        AddressStandardVo addressStandardVo = amapService.fuzzyAddressNormalizationSimple(addressFuzzyNormalizationDto, addressFuzzyNormalizationLatestDtoParams.getLoginUserId());
        try {
            // 替换 属性默认值 "unknown" 为 ""
            BeanCombineUtil.setFieldValueReplace(addressStandardVo, "unknown", "");
        } catch (Exception e) {
            log.info("替换属性默认值异常:{}", e);
        }
        return addressStandardVo;
    }

    @ApiOperation(value = "公司地址智能解析")
    @PostMapping("/fuzzyCompanyNormalization")
    public AddressStandardVo fuzzyCompanyNormalization(@RequestBody @Valid AddressFuzzyNormalizationDTO companyFuzzyNormalizationDtoParams) {
        AddressFuzzyNormalizationDTO addressFuzzyNormalizationDto = null;
        try {
            // Bean对象去除属性NULL值并赋予默认值
            addressFuzzyNormalizationDto = BeanCombineUtil.setFieldValueNotNull(companyFuzzyNormalizationDtoParams);
        } catch (Exception e) {
            log.error("替换属性默认值异常", e);
        }
        AddressStandardVo addressStandardVo = amapService.fuzzyCompanyNormalization(
                addressFuzzyNormalizationDto, companyFuzzyNormalizationDtoParams.getLoginUserId());
        try {
            // 替换 属性默认值 "unknown" 为 ""
            BeanCombineUtil.setFieldValueReplace(addressStandardVo, "unknown", "");
        } catch (Exception e) {
            log.info("替换属性默认值异常", e);
        }
        return addressStandardVo;
    }

    @ApiOperation(
            value = "地址智能解析分页",
            notes = "地址智能解析,通过解析 患者手机号 患者身份证 医疗机构所在地 所归属的标准行政区划  缩小对模糊地址的检索范围"
    )
    @PostMapping("/fuzzyAddressNormalizationLatest")
    public PageData<AddressStandardLatestVo> fuzzyAddressNormalizationLatest(@RequestBody @Valid AddressFuzzyNormalizationLatestDTO addressFuzzyNormalizationLatestDtoParams) {
        AddressFuzzyNormalizationLatestDTO addressFuzzyNormalizationLatestDto = null;
        try {
            //Bean对象去除属性NULL值并赋予默认值
            addressFuzzyNormalizationLatestDto = BeanCombineUtil.setFieldValueNotNull(addressFuzzyNormalizationLatestDtoParams);
        } catch (Exception e) {
            log.error("{}", e);
        }
        assert addressFuzzyNormalizationLatestDto != null;

        return amapService.fuzzyAddressNormalizationLatest(addressFuzzyNormalizationLatestDtoParams);
    }

    @ApiOperation(
            value = "根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码",
            notes = "根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码"
    )
    @PostMapping("/getAreaInfoByLongitudeAndLatitude")
    public AddressStandardLatestVo getAreaInfoByLongitudeAndLatitude(@RequestBody @Valid LongitudeAndLatitudeDto longitudeAndLatitudeDto) {

        return amapService.getAreaInfoByLongitudeAndLatitude(longitudeAndLatitudeDto);
    }
}
