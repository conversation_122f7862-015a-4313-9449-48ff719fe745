package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.outbound.entity.OutboundPerson;
import com.iflytek.cdc.admin.outbound.mapper.OutboundPersonMapper;
import com.iflytek.cdc.admin.outbound.model.vo.OutboundPersonVO;
import com.iflytek.cdc.admin.outbound.service.OutboundPersonService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OutboundPersonServiceImpl extends CdcServiceBaseImpl<OutboundPersonMapper, OutboundPerson> implements OutboundPersonService {
    @Resource
    private OutboundPersonMapper outboundPersonMapper;

    @Override
    public List<OutboundPersonVO> listByRecordIds(List<String> recordIds) {
        return outboundPersonMapper.listByRecordIds(recordIds);
    }

    @Override
    public void updateManualResult(String personId, String result) {
        OutboundPerson byId = getById(personId);
        byId.setManualResult(result);
        updateById(byId);
    }

}
