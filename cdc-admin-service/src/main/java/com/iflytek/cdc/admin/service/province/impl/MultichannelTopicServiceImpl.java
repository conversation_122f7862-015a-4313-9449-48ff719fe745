package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopic;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicWarningRule;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrPathogenInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicConfigMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrMultichannelTopicWarningRuleMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrPathogenInfoMapper;
import com.iflytek.cdc.admin.model.mr.dto.TopicConfigEditDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicQueryDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicWarningRuleEditDTO;
import com.iflytek.cdc.admin.model.mr.vo.MultichannelTopicInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TopicConfigInfoVO;
import com.iflytek.cdc.admin.service.province.MultichannelTopicService;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelDetailService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class MultichannelTopicServiceImpl implements MultichannelTopicService {

    public static final String TB_CDCMR_MULTICHANNEL_TOPIC = "tb_cdcmr_multichannel_topic";

    public static final String TB_CDCMR_MULTICHANNEL_TOPIC_CONFIG = "tb_cdcmr_multichannel_topic_config";

    public static final String TB_CDCMR_MULTICHANNEL_TOPIC_WARNING_RULE = "tb_cdcmr_multichannel_topic_warning_rule";

    public static final String TB_CDCMR_WARNING_RISK_LEVEL_DETAIL = "tb_cdcmr_warning_risk_level_detail";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrMultichannelTopicMapper topicMapper;

    @Resource
    private TbCdcmrMultichannelTopicConfigMapper topicConfigMapper;

    @Resource
    private TbCdcmrMultichannelTopicWarningRuleMapper topicWarningRuleMapper;

    @Resource
    private TbCdcmrPathogenInfoMapper tbCdcmrPathogenInfoMapper;

    @Resource
    private WarningRiskLevelDetailService warningRiskLevelDetailService;

    @Override
    public PageInfo<MultichannelTopicInfoVO> getTopicList(TopicQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<MultichannelTopicInfoVO> topicInfoVOList = topicMapper.getTopicList(dto);

        if(CollectionUtil.isNotEmpty(topicInfoVOList)) {
            List<String> idList = topicInfoVOList.stream().map(MultichannelTopicInfoVO::getTopicId).collect(Collectors.toList());
            //查询专题的风险等级
            List<TbCdcmrWarningRiskLevelDetail> detailList = warningRiskLevelDetailService.getRiskLevelDetailBy(idList);
            Map<String, TbCdcmrWarningRiskLevelDetail> detailMap = detailList.stream()
                                                                             .collect(Collectors.toMap(TbCdcmrWarningRiskLevelDetail::getDiseaseId,
                                                                                                       Function.identity(),
                                                                                                       (oldValue, newValue) -> newValue));
            //设置风险等级
            topicInfoVOList.forEach(e -> {
                TbCdcmrWarningRiskLevelDetail detail = detailMap.get(e.getTopicId());
                if(detail != null){
                    e.setRiskLevelDetailId(detail.getId());
                }
            });
        }
        return new PageInfo<>(topicInfoVOList);
    }

    @Override
    @Transactional
    public String editTopic(TbCdcmrMultichannelTopic topic) {

        UapUserPo uapUserPo = USER_INFO.get();
        topic.setCreateTime(new Date());
        topic.setUpdateTime(new Date());
        if(Objects.nonNull(uapUserPo)){
            topic.setCreator(uapUserPo.getName());
            topic.setCreatorId(uapUserPo.getId());
            topic.setUpdater(uapUserPo.getName());
            topic.setUpdaterId(uapUserPo.getId());
        }
        //不传为新增
        if(StringUtils.isBlank(topic.getId())) {
            //新增时 先判断专题名称是否重复
            List<String> topicNameList = topicMapper.listAllTopicName();
            if(CollUtil.isNotEmpty(topicNameList) && topicNameList.contains(topic.getTopicName())){
                throw new RuntimeException("专题名称重复");
            }
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_MULTICHANNEL_TOPIC));
            topic.setId(id);
            topic.setStatus(Constants.STATUS_ON);
            topic.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            //新增专题时直接新增一条风险等级详情数据
            this.saveRiskLevelDetail(topic);
        }
        topicMapper.editTopic(topic);
        return topic.getId();
    }

    /**
     * 多渠道新增专题 初始化关于该专题的一条风险等级详情数据
     */
    private void saveRiskLevelDetail(TbCdcmrMultichannelTopic topic){

        TbCdcmrWarningRiskLevelDetail detail = new TbCdcmrWarningRiskLevelDetail();
        BeanUtils.copyProperties(topic, detail);
        detail.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_WARNING_RISK_LEVEL_DETAIL)));
        detail.setDiseaseId(topic.getId());
        detail.setDiseaseCode(topic.getTopicCode());
        detail.setDiseaseName(topic.getTopicName());
        detail.setWarningType(WarningTypeProEnum.MULTICHANNEL.getCode());
        warningRiskLevelDetailService.save(detail);
    }

    @Override
    public TopicConfigInfoVO getTopicConfig(String topicId, String topicName) {
        TopicConfigInfoVO topicConfigInfoVO = new TopicConfigInfoVO();
        if (StringUtils.isBlank(topicId) && StringUtils.isBlank(topicName)){
            return topicConfigInfoVO;
        }
        //查询该专题信息
        TbCdcmrMultichannelTopic topic = topicMapper.queryByTopicId(topicId, topicName);
        if(topic == null) {
            return null;
        }
        //查询专题配置信息
        List<TbCdcmrMultichannelTopicConfig> topicConfigList = topicConfigMapper.queryByTopicId(topic.getId(), null);
        Map<String, List<TbCdcmrMultichannelTopicConfig>> topicConfigMap = topicConfigList.stream()
                                                                                          .filter(e -> StringUtils.isNotBlank(e.getChannelType()))
                                                                                          .collect(Collectors.groupingBy(TbCdcmrMultichannelTopicConfig::getChannelType));
        topicConfigInfoVO.setTopicId(topic.getId());
        topicConfigInfoVO.setTopicName(topic.getTopicName());
        topicConfigInfoVO.setNotes(topic.getNotes());
        topicConfigInfoVO.setTopicConfigMap(topicConfigMap);
        return topicConfigInfoVO;
    }

    @Override
    @Transactional
    public void editTopicConfig(TopicConfigEditDTO dto) {

        //清除该专题的配置
        topicConfigMapper.updateByTopicId(dto.getTopicId());
        if(CollectionUtil.isEmpty(dto.getTopicConfigList())){
            return;
        }
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        //批量处理编辑的配置信息
        List<TbCdcmrMultichannelTopicConfig> topicConfigList = dto.getTopicConfigList();
        topicConfigList.forEach(e -> {
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_MULTICHANNEL_TOPIC_CONFIG));
            e.setId(id);
            e.setTopicId(dto.getTopicId());
            e.setStatus(Constants.STATUS_ON);
            e.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            e.setCreateTime(new Date());
            e.setUpdateTime(new Date());
            e.setCreator(uapUserPo.getName());
            e.setCreatorId(uapUserPo.getId());
            e.setUpdater(uapUserPo.getName());
            e.setUpdaterId(uapUserPo.getId());
        });
        //批量插入配置信息
        topicConfigMapper.insertBatch(topicConfigList);
        //更新专题维表的 更新时间
        this.updateTopic(uapUserPo, dto.getTopicId());
    }

    @Override
    public TbCdcmrMultichannelTopicWarningRule getTopicWarningRule(String topicId) {

        return topicWarningRuleMapper.getWarningRuleByTopicId(topicId);
    }

    @Override
    @Transactional
    public void editTopicWarningRule(TopicWarningRuleEditDTO dto) {

        //将该专题下的规则做软删除
        topicWarningRuleMapper.updateRuleByTopicId(dto.getTopicId());
        TbCdcmrMultichannelTopicWarningRule rule = dto.getRule();
        if(rule == null) {
            return;
        }
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        if(StringUtils.isBlank(rule.getId())){
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_MULTICHANNEL_TOPIC_WARNING_RULE));
            rule.setId(id);
        }
        rule.setTopicId(dto.getTopicId());
        rule.setStatus(Constants.STATUS_ON);
        rule.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        rule.setCreateTime(new Date());
        rule.setUpdateTime(new Date());
        rule.setCreator(uapUserPo.getName());
        rule.setCreatorId(uapUserPo.getId());
        rule.setUpdater(uapUserPo.getName());
        rule.setUpdaterId(uapUserPo.getId());
        //插入或更新规则
        topicWarningRuleMapper.insertOrUpdate(rule);
        //更新专题维表的 更新时间
        this.updateTopic(uapUserPo, dto.getTopicId());
    }

    /**
     * 操作专题配置/专题规则时 更新专题维表中的更新时间
     * */
    private void updateTopic(UapUserPo uapUserPo, String topicId){

        //更新专题的 更新时间
        TbCdcmrMultichannelTopic topic = TbCdcmrMultichannelTopic.builder()
                                                                 .id(topicId)
                                                                 .updater(uapUserPo.getName())
                                                                 .updaterId(uapUserPo.getId())
                                                                 .updateTime(new Date())
                                                                 .build();
        topicMapper.editTopic(topic);
    }

    @Override
    public List<TbCdcmrMultichannelTopicConfig> listPathogenByTopicId(String topicId, String channelType) {

        return topicConfigMapper.queryByTopicId(topicId, channelType);
    }

    @Override
    public List<TbCdcmrPathogenInfo> getPathogenInfo() {

        return tbCdcmrPathogenInfoMapper.listAll();
    }
}
