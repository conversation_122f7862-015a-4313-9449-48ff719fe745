package com.iflytek.cdc.admin.datamodel.es;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.LifecycleOperationMode;
import co.elastic.clients.elasticsearch.ilm.ElasticsearchIlmClient;
import co.elastic.clients.elasticsearch.ilm.PutLifecycleResponse;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.DeleteIndexResponse;
import co.elastic.clients.elasticsearch.indices.ElasticsearchIndicesClient;
import co.elastic.clients.elasticsearch.indices.RolloverResponse;
import co.elastic.clients.elasticsearch.ingest.ElasticsearchIngestClient;
import co.elastic.clients.elasticsearch.ingest.Processor;
import co.elastic.clients.elasticsearch.ingest.PutPipelineResponse;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.transport.endpoints.BooleanResponse;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.config.EsModelConfigProperties;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelFormQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.FormGroupTmpl;
import com.iflytek.cdc.admin.datamodel.model.vo.ModelFormListVO;
import com.iflytek.cdc.admin.datamodel.service.DataModelService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * elastic search 检索模型工具类
 */
@Component
@Slf4j
public class EsModelUtils {

    @Resource
    private EsModelConfigProperties esModelConfigProperties;

    @Resource
    @Lazy
    private DataModelService dataModelService;

    @Autowired(required = false)
    private ElasticsearchClient client;

    @Autowired(required = false)
    @Qualifier("esRestTemplate")
    private RestTemplate esRestTemplate;

    /**
     * 为了滚动，新建索引名字设置为 <索引别名-{now/d}-000001>
     * <br/>
     * 备注：在数据湖里手动滚动，或者通过 ILM 自动滚动。
     *
     * @param indexAlias 索引别名
     * @return 初始化索引名称，定义了滚动名规则
     */
    private static String buildCreateIndexName(String indexAlias, boolean encode) {
        if (encode) {
            return "%3C" + indexAlias + "-%7Bnow%2Fd%7D-000001%3E";
        } else {
            return "<" + indexAlias + "-{now/d}-000001>";
        }
    }

    /**
     * 判断是否嵌套节点。
     * <p/>
     * 20250123: 数仓 fs 表最新逻辑，模型根节点是 {}，表单、分组节点都是 []，和是否重复无关；所以这里加一个配置项
     *
     * @param isRepeat 模型定义中节点是否重复
     * @return 是否嵌套节点
     */
    private boolean shouldNested(Integer isRepeat) {
        return esModelConfigProperties.getTemplate().isForceNested() || (isRepeat != null && isRepeat == 1);
    }

    /**
     * 解析字段对应的 es mapping field type
     *
     * @param type 模型定义中节点类型
     * @return 是否嵌套节点
     */
    private NodeType parseLeafNodeType(String type) {
        if (StrUtil.contains(type, "date")) {
            return NodeType.DATE;
        }
        if (StrUtil.contains(type, "number")) {
            return NodeType.NUMERIC;
        }
        return NodeType.TEXT;
    }

    /**
     * 检查是否启用了 es 检索。不启用检索也可以生成索引模板，但是不能自动创建索引模板或策略。
     *
     * @return 是否启用了 es 检索
     */
    private boolean isEsSearchEnabled() {
        return client != null && esModelConfigProperties.isEnabled();
    }

    /**
     * 生成包含嵌套结构的 es 索引模板，方便数据湖脚本滚动生成索引
     *
     * @param modelId        模型ID
     * @param modelVersionId 模型版本ID
     * @return 索引模板
     */
    public String buildEsIndexTemplate(String modelId, String modelVersionId, String esIndexName) {
        if (StrUtil.isBlank(esIndexName)) {
            return null;
        }
        List<ModelFormListVO> modelForms = this.getModelFormGroups(modelId, modelVersionId);
        if (CollUtil.isEmpty(modelForms)) {
            return null;
        }
        // 构建字段映射大 json
        JSONObject properties = this.buildPropertiesMapping(modelId, modelForms);

        EsModelConfigProperties.Template esConfig = esModelConfigProperties.getTemplate();
        // 构建索引模板脚本 json
        String templateJson = IndexTemplateBuilder.of(esIndexName)
                .meta("modelId", modelId)
                .meta("modelVersionId", modelVersionId)
                .numberOfShards(esConfig.getNumberOfShards())
                .numberOfReplicas(esConfig.getNumberOfReplicas())
                .refreshInterval(esConfig.getRefreshInterval())
                .totalFieldsLimit(esConfig.getTotalFieldsLimit())
                .nestedFieldsLimit(esConfig.getNestedFieldsLimit())
                .nestedObjectsLimit(esConfig.getNestedObjectsLimit())
                .defaultPipeline(esConfig.getDefaultPipeline())
                .lifecycleName(esConfig.getLifecyclePolicy())
                .dynamic(esConfig.getDynamic())
                .source(esConfig.isSourceEnabled())
                .properties(properties)
                .build();
        // 启用了 es 检索则自动初始化 es 配置
        if (isEsSearchEnabled()) {
            ensureEsConfig(esIndexName, templateJson);
        } else {
            log.debug("未启用 es 检索，跳过 es 配置自动初始化！");
        }
        return templateJson;
    }

    private JSONObject buildPropertiesMapping(String modelId, List<ModelFormListVO> modelForms) {
        MappingNode modelNode = new MappingNode(false, NodeType.DEFAULT);

        final Gson gson = new Gson();
        for (ModelFormListVO modelForm : modelForms) {
            MappingNode formNode = new MappingNode(false, shouldNested(modelForm.getIsRepeat()) ? NodeType.NESTED : NodeType.DEFAULT);
            modelNode.addChild(modelForm.getModelFormId(), formNode);

            FormGroupTmpl[] groups = gson.fromJson(modelForm.getFormJson(), FormGroupTmpl[].class);
            for (FormGroupTmpl group : groups) {
                MappingNode groupNode = new MappingNode(false, shouldNested(group.getRepeat()) ? NodeType.NESTED : NodeType.DEFAULT);
                formNode.addChild(group.getField(), groupNode);

                for (FormGroupTmpl.FieldRule field : group.getProps().getRules()) {
                    MappingNode fieldNode = new MappingNode(true, parseLeafNodeType(field.getType()));
                    groupNode.addChild(field.getField(), fieldNode);
                }
            }
        }
        // 根节点本身是从 properties 内部开始的，当叶子处理
        JSONObject root = new JSONObject(true);
        root.set(modelId, modelNode);
        return root;
    }

    private List<ModelFormListVO> getModelFormGroups(String modelId, String modelVersionId) {
        // 构建入参保证取全部
        DataModelFormQueryDTO dto = new DataModelFormQueryDTO();
        dto.setModelId(modelId);
        dto.setModelVersionId(modelVersionId);
        dto.setPageSize(Integer.MAX_VALUE);
        // 获取模型表单，这里拿到的是表单 json；因为需要遍历嵌套结构，自己拿 json 再根据层级解析
        return dataModelService.getModelFormList(dto).getList();
    }

    /**
     * 执行 es 索引配置初始化或者更新工作。
     * 初始化时直接创建 es 策略、索引模板和初始化滚动索引；更新时则只更新索引模板。
     * <p>
     * 该操作失败不影响上层逻辑。
     *
     * @param indexAlias 索引别名
     */
    private void ensureEsConfig(String indexAlias, String indexTemplate) {
        try {
            BooleanResponse ping = client.ping();
            if (ping.value()) {
                ensurePolicy();
                ensurePipeline();
                ensureIndexTemplate(indexAlias, indexTemplate);
                ensureRollingIndex(indexAlias);
            } else {
                log.error("es ping 失败，请检查配置！");
            }
        } catch (Exception e) {
            log.error("调用 es 失败", e);
        }
    }

    /**
     * ILM 策略只新增，不更新
     *
     * @throws IOException es 操作失败异常
     */
    private void ensurePolicy() throws IOException {
        ElasticsearchIlmClient ilm = client.ilm();
        if (!LifecycleOperationMode.Running.equals(ilm.getStatus().operationMode())) {
            log.error("es ilm 未开启，请先开启！");
            return;
        }
        final String lifecycleName = esModelConfigProperties.getTemplate().getLifecyclePolicy();
        JsonData actions = JsonData.fromJson("{\"rollover\": {\"max_age\": \"1d\",\"max_primary_shard_size\": \"50gb\"}}");
        PutLifecycleResponse putResp = ilm.putLifecycle(req -> req.name(lifecycleName)
                .policy(po -> po.phases(ph -> ph.hot(h -> h.actions(actions)))));
        log.info("es ilm policy [{}] 配置结束: {}", lifecycleName, putResp.acknowledged());
    }

    /**
     * 管道只新增，不更新
     *
     * @throws IOException es 操作失败异常
     */
    private void ensurePipeline() throws IOException {
        ElasticsearchIngestClient ingest = client.ingest();
        final String pipelineName = esModelConfigProperties.getTemplate().getDefaultPipeline();
        Processor timestampProcessor = Processor.of(pp -> pp.set(fn -> fn
                .description("Index the ingest timestamp rolling index for sort")
                .field("_ingest_time")
                .copyFrom("_ingest.timestamp")));
        PutPipelineResponse putResp = ingest.putPipeline(req -> req.id(pipelineName).version(1L)
                .description("Add common fields to cdc search model.")
                .processors(timestampProcessor));
        log.info("es pipeline [{}] 配置结束: {}", pipelineName, putResp.acknowledged());
    }

    /**
     * 新增或更新索引模板
     *
     * @param indexAlias    索引别名
     * @param indexTemplate 模板内容
     */
    private void ensureIndexTemplate(String indexAlias, String indexTemplate) {
        final String indexTemplateName = indexAlias + "-template";
        final String host = esModelConfigProperties.getEsHost();
        final String url = String.format("%s/_index_template/%s", host, indexTemplateName);
        // 直接覆盖
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        esRestTemplate.put(url, new HttpEntity<>(indexTemplate, headers));
    }

    /**
     * 初始化索引只更新，不新增
     *
     * @param indexAlias 索引别名
     */
    private void ensureRollingIndex(String indexAlias) throws IOException {
        ElasticsearchIndicesClient indices = client.indices();
        BooleanResponse existed = indices.exists(req -> req.index(indexAlias));
        if (existed.value()) {
            log.debug("es 索引或别名 [{}] 已存在", indexAlias);
            //  更新了索引结构，强制滚动
            RolloverResponse resp = indices.rollover(req -> req.alias(indexAlias));
            log.info("es 别名 [{}] 滚动结果: {}", indexAlias, resp.acknowledged());
        } else {
            String createIndexName = buildCreateIndexName(indexAlias, false);
            CreateIndexResponse resp = indices.create(req -> req.index(createIndexName).aliases(indexAlias, alias -> alias.isWriteIndex(true)));
            log.info("es 索引 [{}] 初始化结果: {}", createIndexName, resp.acknowledged());
        }
    }

    /**
     * 删除具体索引，不能使用别名或通配符
     *
     * @param concreteIndex 具体索引名称
     * @throws IOException IO异常
     */
    public void deleteConcreteIndex(String concreteIndex) throws IOException {
        ElasticsearchIndicesClient indices = client.indices();
        DeleteIndexResponse resp = indices.delete(req -> req.index(concreteIndex));
        log.info("es 索引 [{}] 删除结果: {}", concreteIndex, resp.acknowledged());
    }

    @Getter
    private enum NodeType {

        /**
         * 默认类型不设置，自适应；模型根节点一般用这个
         */
        DEFAULT(null, null),

        /**
         * 嵌套类型；表单和分组一般用这个
         */
        NESTED("nested", null),

        /**
         * 字符串类型；一般字段用这个
         */
        TEXT("text", null),

        /**
         * 日期类型；日期字段用这个
         */
        DATE("date", "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis||strict_date_optional_time"),

        /**
         * 数字类型；数字字段用这个
         */
        NUMERIC("double", null);

        private final String esType;

        private final String esFormat;

        NodeType(String esType, String esFormat) {
            this.esType = esType;
            this.esFormat = esFormat;
        }
    }

    /**
     * 自定义的索引模板构建工具，避免使用复杂 es client 对象
     */
    private static class IndexTemplateBuilder {

        private final JSONObject metadata;

        private final JSONObject settings;

        private final JSONObject mappings;

        private final String indexAlias;

        private IndexTemplateBuilder(@NonNull String indexAlias) {
            metadata = new JSONObject(true);
            settings = new JSONObject(true);
            mappings = new JSONObject(true);
            this.indexAlias = indexAlias;
        }

        public static IndexTemplateBuilder of(@NonNull String indexAlias) {
            return new IndexTemplateBuilder(indexAlias);
        }

        public IndexTemplateBuilder meta(String key, String value) {
            if (StrUtil.isNotBlank(key) && StrUtil.isNotBlank(value)) {
                metadata.set(key, value);
            }
            return this;
        }

        public IndexTemplateBuilder numberOfShards(int numberOfShards) {
            if (numberOfShards > 0) {
                settings.set("number_of_shards", numberOfShards);
            }
            return this;
        }

        public IndexTemplateBuilder numberOfReplicas(int numberOfReplicas) {
            if (numberOfReplicas > -1) {
                settings.set("number_of_replicas", numberOfReplicas);
            }
            return this;
        }

        public IndexTemplateBuilder refreshInterval(int refreshInterval) {
            if (refreshInterval >= -1) {
                settings.set("index.refresh_interval", refreshInterval + "s");
            }
            return this;
        }

        public IndexTemplateBuilder totalFieldsLimit(int limit) {
            settings.set("index.mapping.total_fields.limit", limit);
            return this;
        }

        public IndexTemplateBuilder nestedFieldsLimit(int limit) {
            settings.set("index.mapping.nested_fields.limit", limit);
            return this;
        }

        public IndexTemplateBuilder nestedObjectsLimit(int limit) {
            settings.set("index.mapping.nested_objects.limit", limit);
            return this;
        }

        public IndexTemplateBuilder lifecycleName(String lifecycleName) {
            if (StrUtil.isNotBlank(lifecycleName)) {
                // 创建模板时进行绑定 ILM
                settings.set("index.lifecycle.name", lifecycleName);
                settings.set("index.lifecycle.rollover_alias", indexAlias);
            }
            return this;
        }

        public IndexTemplateBuilder defaultPipeline(@NonNull String defaultPipeline) {
            // 创建模板时进行绑定默认管道，用于附加公共字段
            settings.set("index.default_pipeline", defaultPipeline);
            return this;
        }

        public IndexTemplateBuilder source(boolean enabled) {
            if (!enabled) {
                JSONObject source = new JSONObject();
                source.set("enabled", false);
                mappings.set("_source", source);
            }
            return this;
        }

        public IndexTemplateBuilder dynamic(String dynamic) {
            if (StrUtil.isNotBlank(dynamic) && !"true".equals(dynamic)) {
                mappings.set("dynamic", dynamic);
            }
            return this;
        }

        /**
         * 附加公共字段，并放入字段映射。
         * <p/>
         * 公共字段参见 <code>cdc-data-service</code> 的 <code>ElasticsearchUtils</code> 类。
         *
         * @param properties 字段映射
         * @return 当前构造器
         */
        public IndexTemplateBuilder properties(@NonNull JSONObject properties) {
            // 1. 数据主键和数据时间，排序和去重使用
            properties.set("ingest_id", new JSONObject().set("type", "keyword"));
            properties.set("_ingest_time", new JSONObject().set("type", "date"));
            // 2. 全文检索字段
            properties.set("_all_field_val", new JSONObject().set("type", "text").set("store", false));
            // 3. 疾病字段
            properties.set("_disease_code", new JSONObject().set("type", "keyword"));
            properties.set("_disease_name", new JSONObject().set("type", "keyword"));
            properties.set("_process_type", new JSONObject().set("type", "keyword"));
            properties.set("_delete_flag", new JSONObject().set("type", "keyword"));
            // 4. 数据权限字段
            properties.set("_permit_living_province_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_living_city_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_living_district_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_living_street_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_company_province_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_company_city_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_company_district_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_company_street_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_org_province_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_org_city_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_org_district_code", new JSONObject().set("type", "keyword"));
            properties.set("_permit_org_street_code", new JSONObject().set("type", "keyword"));

            mappings.set("properties", properties);
            return this;
        }

        public String build() {
            if (StrUtil.isBlank(indexAlias)) {
                throw new MedicalBusinessException("必须定义索引别名");
            }
            JSONObject template = new JSONObject(true);
            template.set("settings", settings);
            template.set("mappings", mappings);

            // 记录初始化索引名到模板 meta 里方便复制到 cURL 中
            metadata.set("create_index_name", buildCreateIndexName(indexAlias, true));

            JSONObject root = new JSONObject(true);
            root.set("index_patterns", new String[]{indexAlias + "*"});
            root.set("_meta", metadata);
            root.set("priority", 1);
            root.set("template", template);
            return root.toStringPretty();
        }
    }

    /**
     * 自定义的映射节点类，用于构建嵌套结构
     */
    private static class MappingNode extends JSONObject {

        private final JSONObject properties = new JSONObject(true);

        private final boolean isLeaf;

        /**
         * 默认带顺序
         */
        public MappingNode(boolean isLeaf, NodeType nodeType) {
            if (nodeType != null) {
                this.set("type", nodeType.getEsType());
                if (nodeType.getEsFormat() != null) {
                    this.set("format", nodeType.getEsFormat());
                }

                if (NodeType.TEXT.equals(nodeType)) {
                    JSONArray copyTo = new JSONArray();
                    copyTo.set("_all_field_val");
                    this.set("copy_to", copyTo);
                }
            }
            this.isLeaf = isLeaf;
            if (isLeaf) {
                JSONObject fields = new JSONObject();
                this.set("fields", fields);

                JSONObject keyword = new JSONObject();
                fields.set("keyword", keyword);

                keyword.set("type", "keyword");
                keyword.set("ignore_above", 256);
            } else {
                this.set("properties", properties);
            }
        }

        public void addChild(String key, MappingNode value) {
            if (isLeaf) {
                throw new MedicalBusinessException("叶子节点不能添加子节点");
            }
            properties.set(key, value);
        }

        @Override
        public JSONObject set(String key, Object value) throws JSONException {
            if (isLeaf && value instanceof MappingNode) {
                throw new MedicalBusinessException("叶子节点不能添加子节点");
            }
            return super.set(key, value);
        }
    }
}
