package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 传播动力学请求参数
 */
@Data
public class DynamicsRequest {

    private String startDay;//疫情开始日

    private String endDay;//疫情结束日

    private Integer popSize;//总人口数

    private Integer popInfected;//初始感染数

    private Double beta;//感染率

    private Double asympFactor;//无症状感染率
}
