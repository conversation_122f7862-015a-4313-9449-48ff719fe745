package com.iflytek.cdc.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.entity.TbCdcmrDiseaseMonitorRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrDiseaseMonitorRuleMapper;
import com.iflytek.cdc.admin.service.MonitorConfigService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class MonitorConfigServiceImpl extends ServiceImpl<TbCdcmrDiseaseMonitorRuleMapper, TbCdcmrDiseaseMonitorRule> implements MonitorConfigService {

    @Resource
    private TbCdcmrDiseaseMonitorRuleMapper tbCdcmrDiseaseMonitorRuleMapper;

    @Autowired
    private BatchUidService batchUidService;

    @Override
    public List<TbCdcmrDiseaseMonitorRule> getMonitorConfigMsg(String diseaseCode,String diseaseName ,String configType) {
        return tbCdcmrDiseaseMonitorRuleMapper.getMonitorConfigMsg(diseaseCode,diseaseName, configType);
    }

    @Override
    public List<TbCdcmrDiseaseMonitorRule> getMonitorConfigByIds(List<String> ids) {
        return baseMapper.selectBatchIds(ids);
    }

    @Override
    public void updateMonitorConfig(String loginUserId, TbCdcmrDiseaseMonitorRule tbCdcmrDiseaseMonitorRule) {

        LocalDateTime timeNow = LocalDateTime.now();
        final TbCdcmrDiseaseMonitorRule rule = baseMapper.selectById(tbCdcmrDiseaseMonitorRule.getId());
        if (rule == null || rule.getDeleteFlag().intValue() == 1) {
            throw new MedicalBusinessException("数据已被更新，请刷新页面!");
        }
        //单个监测规则 进行全删全增
        //根据规则id对该规则进行逻辑删除
        tbCdcmrDiseaseMonitorRuleMapper.deleteByLogic(tbCdcmrDiseaseMonitorRule.getId(), loginUserId, timeNow);
        //插入新的监测维护规则
        tbCdcmrDiseaseMonitorRule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_disease_monitor_rule")));
        tbCdcmrDiseaseMonitorRule.setCreator(loginUserId);
        tbCdcmrDiseaseMonitorRule.setUpdator(loginUserId);
        tbCdcmrDiseaseMonitorRule.setCreateDatetime(timeNow);
        tbCdcmrDiseaseMonitorRule.setUpdateDatetime(timeNow);
        baseMapper.insert(tbCdcmrDiseaseMonitorRule);
    }
}
