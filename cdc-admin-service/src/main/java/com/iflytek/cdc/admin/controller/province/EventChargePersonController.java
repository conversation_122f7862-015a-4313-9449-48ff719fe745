package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.EventChargePersonDTO;
import com.iflytek.cdc.admin.dto.EventChargePersonQuery;
import com.iflytek.cdc.admin.model.mr.dto.DealPersonQueryDTO;
import com.iflytek.cdc.admin.service.EventChargePersonService;
import com.iflytek.cdc.admin.vo.EventChargePersonVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "事件处理人配置相关接口")
public class EventChargePersonController {
    @Resource
    private EventChargePersonService eventChargePersonService;
    /**
     * 事件处理配置-修改状态
     */
    @ApiOperation("事件处理配置-修改状态")
    @OperationLogAnnotation(operationName = "信号处理人配置-修改状态")
    @PostMapping("/{version}/pt/eventChargePerson/changeStatus")
    public void changeStatus(@RequestBody EventChargePersonDTO input, @RequestParam String loginUserId) {
        eventChargePersonService.changeStatus(input);
    }
    @ApiOperation("事件处理配置-批量保存")
    @OperationLogAnnotation(operationName = "信号处理人配置-批量保存")
    @PostMapping("/{version}/pt/eventChargePerson/batchSave")
    public void batchSave(@RequestBody List<EventChargePersonDTO> inputs, @RequestParam String loginUserId) {
        eventChargePersonService.batchSave(inputs);
    }
    @ApiOperation("事件处理配置-疾病查询")
    @PostMapping("/{version}/pt/eventChargePerson/listDiseaseConfig")
    public PageInfo<EventChargePersonVO> listDiseaseConfig(@RequestBody EventChargePersonQuery input) {
        return eventChargePersonService.listDiseaseConfig(input);
    }
    @ApiOperation("根据初步诊断、事件严重等级以及事件类型 批量查询对应的处理人信息")
    @PostMapping("/{version}/pt/eventChargePerson/getDealPersonInfoBy")
    public List<EventChargePersonVO> getDealPersonInfoBy(@RequestBody List<EventChargePersonQuery> dtoList){

        return eventChargePersonService.getDealPersonInfoBy(dtoList);
    }
}
