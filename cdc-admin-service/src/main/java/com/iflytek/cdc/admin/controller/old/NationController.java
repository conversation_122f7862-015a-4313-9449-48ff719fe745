package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.UapNation;
import com.iflytek.cdc.admin.entity.NationInfo;
import com.iflytek.cdc.admin.service.NationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/7/8 14:22
 **/
@RestController
@Api(tags = "行政区划通用接口")
public class NationController {

    private final NationService nationService;

    public NationController(NationService nationService) {
        this.nationService = nationService;
    }

    @GetMapping("/{version}/pt/nation/getNationByCode")
    @ApiOperation("根据区域编码获取下级的行政区划")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public NationInfo getNationByCode(@PathVariable String version, String code) {
        return nationService.queryAllNationInfo(code);
    }

    @GetMapping("/{version}/pt/nation/getNationByUserId")
    @ApiOperation("根据用户Id获取下级的行政区划")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public NationInfo getNationByUserId(@PathVariable String version, String userId) {
        return nationService.queryAllNationInfoByUserId(userId);
    }

    @GetMapping("/{version}/pt/nation/getNationByCodeLevel")
    @ApiOperation("根据区域编码和子级Level获取到该level所有行政区划")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UapNation getNationByCodeLevel(@PathVariable String version, String code, int level) {
        return nationService.getNations(code, AreaLevelEnum.level(level));
    }
}