package com.iflytek.cdc.admin.vo.region;

import lombok.Data;

import java.util.Date;

/**
 * 区划信息维表
 */
@Data
public class RegionVO {
    /**
     * 主键id
     */
    private String id;

    /**
     * 区划层级:1-省;2-市;3-区/县;4-街道/乡镇;5-社区/村;6-小区/组;99-详细地址
     */
    private Integer regionLevel;

    /**
     * 区划代码
     */
    private String regionCode;

    /**
     * 区划名称
     */
    private String regionName;

    /**
     * 该区划别名
     */
    private String aliasName;

    /**
     * 父级区划代码
     */
    private String parentRegionCode;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 街道编码
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 社区/村编码
     */
    private String villageCode;

    /**
     * 社区/村名称
     */
    private String villageName;

    /**
     * 小区/组编码
     */
    private String groupCode;

    /**
     * 小区/组名称
     */
    private String groupName;

    /**
     * 城乡分类代码
     */
    private String urTypeCode;

    /**
     * 城乡分类名词
     */
    private String urTypeName;

    /**
     * 详细地区名称/地址
     */
    private String addressDetail;

    /**
     * 高德地图地点
     */
    private String amapAreaCode;

    /**
     * 高德地图地点
     */
    private String amapAreaName;

    /**
     * 中心点经度
     */
    private Double longitude;

    /**
     * 中心点纬度
     */
    private Double latitude;

//    /**
//     * 来源:1.uap, 2.built_in(系统内置), 3.edit(用户上传/编辑), 4.external(外部系统接口新增)
//     */
//    private String dataSource;

//    /**
//     * 源数据id
//     */
//    private String sourceId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 是否已审核启用
     */
    private Boolean isAuditEnable;

    /**
     * 创建时间
     */
    private Date createDatetime;

    /**
     * 更新时间
     */
    private Date updateDatetime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

}