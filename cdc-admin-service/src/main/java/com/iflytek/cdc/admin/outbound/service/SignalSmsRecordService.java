package com.iflytek.cdc.admin.outbound.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.outbound.entity.SignalSmsRecord;

import java.util.List;

/**
 * 信号短信记录服务接口
 *
 * <AUTHOR>
 */
public interface SignalSmsRecordService extends IService<SignalSmsRecord> {

 
    /**
     * 保存短信记录
     *
     * @param signalSmsRecord 短信记录
     * @return 是否保存成功
     */
    boolean saveSignalSmsRecord(SignalSmsRecord signalSmsRecord);

    /**
     * 批量保存短信记录
     *
     * @param signalSmsRecords 短信记录列表
     * @return 是否保存成功
     */
    boolean saveBatchSignalSmsRecord(List<SignalSmsRecord> signalSmsRecords);


} 