package com.iflytek.cdc.admin.vo.brief;

import com.iflytek.cdc.admin.entity.brief.BriefTakeApply;
import lombok.Data;

import java.util.Date;

@Data
public class BriefTakeApplyVo {

    private String id;

    private BriefTakeApply.UserData applyUser;

    private String templateId;

    private String statisticsCycle;

    private String briefReportType;

    private String templateTitle;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String applyReason;

    private Integer approveStatus;

    private String approveMark;


    private BriefTakeApply.UserData approver;

    private Date approveTime;

    private Date applyTime;
}
