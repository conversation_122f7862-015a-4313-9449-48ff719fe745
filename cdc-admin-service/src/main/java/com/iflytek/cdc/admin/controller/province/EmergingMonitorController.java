package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingMonitorConfig;
import com.iflytek.cdc.admin.model.mr.dto.EmergingMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EmergingDiseaseInfoVO;
import com.iflytek.cdc.admin.service.EmergingProcessMonitorService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新发突发 圈定病例范围
 * */
@Api(tags = "省统筹 - 监测新发突发 监测病例定义")
@RequestMapping("/pt/{version}/emergingMonitor")
@RestController
public class EmergingMonitorController {

    @Resource
    private EmergingProcessMonitorService emergingProcessMonitorService;

    @PostMapping("/getEmergingTreeInfo")
    @ApiOperation("查询新发突发结构树")
    public List<EmergingDiseaseInfoVO> getEmergingTreeInfo(){

        return emergingProcessMonitorService.getEmergingTreeInfo();
    }

    @PostMapping("/editSubEmergingInfo")
    @ApiOperation("编辑子类新发突发传染病信息")
    public String editSubEmergingInfo(@RequestBody TbCdcmrEmergingDiseaseInfo EmergingDiseaseInfo){

        return emergingProcessMonitorService.editSubEmergingInfo(EmergingDiseaseInfo);
    }

    @GetMapping("/deleteSubEmergingInfo")
    @ApiOperation("删除子类新发突发传染病信息")
    public void deleteSubEmergingInfo(@RequestParam String id){

        emergingProcessMonitorService.deleteSubEmergingInfo(id);
    }

    @PostMapping("/getEmergingInfoConfigs")
    @ApiOperation("查询该新发突发下监测病例定义配置")
    public List<TbCdcmrEmergingMonitorConfig> getEmergingInfoConfigs(@RequestBody EmergingMonitorConfigQueryDTO queryDTO){

        return emergingProcessMonitorService.getEmergingInfoConfigs(queryDTO);
    }

    @PostMapping("/editEmergingProcessDefinition")
    @ApiOperation("编辑新发突发传染病病例定义配置")
    @OperationLogAnnotation(operationName = "编辑症候群病例定义配置")
    public void editEmergingProcessDefinition(@RequestParam String diseaseInfoId,
                                              @RequestBody List<TbCdcmrEmergingMonitorConfig> EmergingMonitorConfigs){

        emergingProcessMonitorService.editEmergingProcessDefinition(diseaseInfoId, EmergingMonitorConfigs);
    }

    @GetMapping("/getEmergingDiseaseCodeByName")
    @ApiOperation("根据新发突发传染病名称获取code以及其子类code")
    public List<String> getEmergingDiseaseCodeByName(@RequestParam String diseaseName){

        return emergingProcessMonitorService.getEmergingDiseaseCodeByName(diseaseName);
    }

}
