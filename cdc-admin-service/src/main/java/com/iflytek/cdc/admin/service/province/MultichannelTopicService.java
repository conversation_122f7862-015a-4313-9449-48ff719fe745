package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopic;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicWarningRule;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrPathogenInfo;
import com.iflytek.cdc.admin.model.mr.dto.TopicConfigEditDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicQueryDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicWarningRuleEditDTO;
import com.iflytek.cdc.admin.model.mr.vo.MultichannelTopicInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TopicConfigInfoVO;

import java.util.List;

public interface MultichannelTopicService {

    /**
     * 获取多渠道专题列表
     * */
    PageInfo<MultichannelTopicInfoVO> getTopicList(TopicQueryDTO dto);

    /**
     * 编辑专题（新增、编辑、删除）
     * */
    String editTopic(TbCdcmrMultichannelTopic topic);

    /**
     * 查询某个专题的配置
     * */
    TopicConfigInfoVO getTopicConfig(String topicId, String topicName);

    /**
     * 编辑专题配置
     * */
    void editTopicConfig(TopicConfigEditDTO dto);

    /**
     * 查看专题的预警规则
     * */
    TbCdcmrMultichannelTopicWarningRule getTopicWarningRule(String topicId);

    /**
     * 编辑多渠道专题预警规则
     * */
    void editTopicWarningRule(TopicWarningRuleEditDTO dto);

    /**
     * 根据专题id查询改专题下配置的病原信息
     * */
    List<TbCdcmrMultichannelTopicConfig> listPathogenByTopicId(String topicId, String channelType);

    /**
     * 查看病原信息
     * */
    List<TbCdcmrPathogenInfo> getPathogenInfo();
}
