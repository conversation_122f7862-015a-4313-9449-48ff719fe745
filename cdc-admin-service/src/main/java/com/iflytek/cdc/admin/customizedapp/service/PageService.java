package com.iflytek.cdc.admin.customizedapp.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppPageQueryDTO;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface PageService extends ICdcService<TbCdccsPage> {

    /**
     * 通过code查询单条数据
     *
     * @param code 页面编码
     * @return 实例对象
     */
    TbCdccsPage loadCascadeByCode(String code);


    /**
     * 页面list查询
     * @return er模型实例
     */
    List<TbCdccsPage> list(AppPageQueryDTO queryDTO);

    /**
     * 分页数据查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    PageInfo<TbCdccsPage> pageList(AppPageQueryDTO queryDTO);

    TbCdcdmEntityErModel loadErModelByPageId(String pageId);

    /**
     * 根据appId 删除
     */
    void deleteByAppId(String appId);

    /**
     * 根据编码进行保存
     */
    void saveByCode(TbCdccsPage page);


}
