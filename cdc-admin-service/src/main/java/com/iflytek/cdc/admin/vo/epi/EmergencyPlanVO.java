package com.iflytek.cdc.admin.vo.epi;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
* 应急预案管理
*/
@Data
public class EmergencyPlanVO  implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "应急名称不能为空")
    @Length(min = 0, max = 64, message = "应急名称长度最大64")
    @ApiModelProperty(value = "应急名称")
    private String name;

    @NotBlank(message = "版本号不能为空")
    @Length(min = 0, max = 64, message = "版本号长度最大64")
    @ApiModelProperty(value = "版本号")
    private String versionFlag;

    @NotBlank(message = "预案类型不能为空")
    @Length(min = 0, max = 64, message = "预案类型长度最大64")
    @ApiModelProperty(value = "预案类型")
    private String taskType;

    @NotBlank(message = "预案类型名称不能为空")
    @Length(min = 0, max = 64, message = "预案类型名称长度最大64")
    @ApiModelProperty(value = "预案类型名称")
    private String taskTypeName;

    @Length(min = 0, max = 200, message = "致病因素编码数组长度最大200")
    @ApiModelProperty(value = "致病因素编码数组")
    private String diseaseCodeArray;

    @Length(min = 0, max = 200, message = "致病因素名称数组长度最大200")
    @ApiModelProperty(value = "致病因素名称数组")
    private String diseaseNameArray;

    @Length(min = 0, max = 100, message = "致病因素文本长度最大100")
    @ApiModelProperty(value = "致病因素文本")
    private String diseaseName;

    @NotBlank(message = "预案等级不能为空")
    @Length(min = 0, max = 64, message = "预案等级编码长度最大64")
    @ApiModelProperty(value = "预案等级编码")
    private String levelCode;

    @NotBlank(message = "预案等级名称不能为空")
    @Length(min = 0, max = 64, message = "预案等级名称长度最大64")
    @ApiModelProperty(value = "预案等级名称")
    private String levelName;

    @Length(min = 0, max = 64, message = "机构编码长度最大64")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @Length(min = 0, max = 64, message = "机构名称长度最大64")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @NotBlank(message = "预案描述不能为空")
    @Length(min = 0, max = 200, message = "预案描述长度最大200")
    @ApiModelProperty(value = "预案描述")
    private String description;

    @Length(min = 0, max = 2, message = "预案状态 1待提交 2待审核 3已通过 4已驳回长度最大2")
    @ApiModelProperty(value = "预案状态 1待提交 2待审核 3已通过 4已驳回")
    private String status;

    @NotBlank(message = "预案内容不能为空")
    @ApiModelProperty(value = "文件内容")
    private String content;

    @Length(min = 0, max = 64, message = "审核人ID长度最大64")
    @ApiModelProperty(value = "审核人ID")
    private String reviewUserId;

    @Length(min = 0, max = 64, message = "审核人长度最大64")
    @ApiModelProperty(value = "审核人")
    private String reviewUser;

    @ApiModelProperty(value = "审核时间")
    private String reviewTime;

    @Length(min = 0, max = 200, message = "审核意见长度最大200")
    @ApiModelProperty(value = "审核意见")
    private String remark;

    @Length(min = 0, max = 2, message = "短信通知长度最大64")
    @ApiModelProperty(value = "短信通知 0-否 1-是")
    private String messageOn;

    @Length(min = 0, max = 2, message = "是否启用长度最大64")
    @ApiModelProperty(value = "是否启用 0-否 1-是")
    private String enabled;

//    @ApiModelProperty(value = "应急附件",hidden = true)
//    @TableField(exist = false)
//    private TbCdcAttachment attachment;

    @ApiModelProperty(value = "省编码")
    @TableField(exist = false)
    private String provinceCode;

    @ApiModelProperty(value = "市编码")
    @TableField(exist = false)
    private String cityCode;

    @ApiModelProperty(value = "区编码")
    @TableField(exist = false)
    private String districtCode;
}
