package com.iflytek.cdc.admin.controller.old;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.util.cas.utils.XmlAnalysis;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RefreshScope
@Slf4j
public class NoSecretRedirectController {
    @Resource
    RestTemplate restTemplate;


    @Value("${cas.authserver:https://suzhou.thdata.qq.com/cas/api/user_auth/cas_service_validate}")
    String casAuthServer1;
    @Value("${cas.authserver:https://suzhou.thdata.qq.com/cas/api/user_auth/cas_service_validate}")
    String casAuthServer2;

    @Value("${cas.sturl:https://suzhou.thdata.qq.com/cas/api/user_auth/cas_service_validate}")
    String casStUrl;

    @Value("${cas.channel:test}")
    String casChannel;

    @Value("${cas.secretKey:testSecret}")
    String casSecretKey;

    @Value("${cas.screenUrl1:http://************:19002/cdc/screen/}")
    String casScreenUrl1;
    @Value("${cas.screenUrl2:http://************:19002/cdc/screen/}")
    String casScreenUrl2;


    @Value("${cas.uapUrl1:http://************:19002/gateway/uap-service-ext-service/}")
    String casUapUrl1;
    @Value("${cas.uapUrl2:http://************:19002/gateway/uap-service-ext-service/}")
    String casUapUrl2;

    @Value("${cas.adminUrl1:http://************:19002/gateway/cdc-admin-service/v1/pb/getRedirect}")
    String casAdminUrl1;
    @Value("${cas.adminUrl2:http://************:19002/gateway/cdc-admin-service/v1/pb/getRedirect}")
    String casAdminUrl2;

    @Value("${cas.checkIp:10.}")
    String checkIp;

    @GetMapping("/{version}/pb/getRedirect")
    @ApiOperation("免密登录")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void getRedirect(HttpServletResponse response, HttpServletRequest request) throws IOException, URISyntaxException {


        //验证服务登录状态
        String authserver = casAuthServer1;
        //校验st
        String sturl = casStUrl;
        String casUapUrl = casUapUrl1;
        String casScreenUrl = casScreenUrl1;

        String ipAddress = request.getHeader("X-Real-IP");
        log.info("--------");
        String url = casAdminUrl1;
        if (ipAddress.contains(checkIp)) {
            authserver = casAuthServer2;
            url = casAdminUrl2;
            casUapUrl = casUapUrl2;
            casScreenUrl = casScreenUrl2;
        }
        log.info("获取到的访问的地址是：" + ipAddress);
        log.info("返回地址：" + url);
        log.info("--------");

        String ticket = request.getParameter("ticket");
        log.info("requestUrl:{}", url);
        //ticket不为空
        if (StrUtil.isNotBlank(ticket)) {
            log.info("ticket:{}", ticket);
            //请求参数
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("service", url);
            paramMap.put("ticket", ticket);
            //请求头
            Map<String, String> headers = new HashMap<String, String>();
            headers.put(Header.CONTENT_TYPE.getValue(), ContentType.FORM_URLENCODED.getValue());
            //执行请求并获得返回值
            String result = HttpRequest.get(sturl).addHeaders(headers).form(paramMap).execute().body();
            JSONObject resultJson = JSONUtil.parseObj(result);
            //请求cas的RESTful成功
            if (StrUtil.isNotBlank(result) && resultJson.getInt("code") == 200) {

                //获取返回结果
                String data = resultJson.getStr("data");
                //实例化解析类
                XmlAnalysis xmlAnalysis = new XmlAnalysis();
                //获取用户
                String user = xmlAnalysis.parsePrincipalFromResponse(data);
                String channel = casChannel;
                String secretKey = casSecretKey;
                String timestamp = String.valueOf(new Date().getTime());
                String screenUrl = casScreenUrl;
                String userid = user;
                String checkResult = restTemplate.getForObject("http://uap-service-ext-service/v1/pv/TUapUser/withPassword/" + channel + "_" + user, String.class);
                if (StringUtils.isBlank(checkResult)) {
                    throw new MedicalBusinessException("不存在对应账号！");
                }
                String sign = MD5.create().digestHex(channel + timestamp + screenUrl + userid + secretKey);
                String requestUrl = casUapUrl + "v1/pb/authProxy/redirect?" +
                        "channel=" + channel + "&sign=" + sign + "&timestamp=" + timestamp + "&url=" + URLEncoder.encode(screenUrl, "UTF-8") + "&userid=" + userid;
                log.info("请求地址:{}", requestUrl);
                response.sendRedirect(requestUrl);

            } else {////请求cas的RESTful失败
                request.setAttribute("code", 500);
                request.setAttribute("data", "failed call cas_service_validate, cas http status code:" + resultJson.get("data"));
                response.sendRedirect(authserver + url);
            }
        } else {
            response.sendRedirect(authserver + url);
        }
    }

}