package com.iflytek.cdc.admin.controller.old;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.AdministrativeRegionQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo;
import com.iflytek.cdc.admin.service.AdministrativeRegionService;
import com.iflytek.cdc.admin.util.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
@RestController
@Api(tags = "街道信息")
@Deprecated
public class AdministrativeRegionController {

    @Resource
    AdministrativeRegionService administrativeRegionService;

    @PostMapping("/{version}/pt/administrative/maintenance")
    @ApiOperation("街道信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageData<TbCdcmrStreetInfo> regionMaintenance(@RequestBody AdministrativeRegionQueryDTO administrativeRegionQueryDTO,
                                                         @PathVariable String version) {
        return administrativeRegionService.getAdministrativeRegionDetail(administrativeRegionQueryDTO);
    }


    @PostMapping("/{version}/pt/administrative/exportAdministrativeRegionList")
    @ApiOperation("导出街道信息列表")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ResponseEntity<byte[]> exportRegionList(@RequestBody AdministrativeRegionQueryDTO administrativeRegionQueryDTO,
                                                   @PathVariable String version, @RequestParam("loginUserId") String loginUserId) throws UnsupportedEncodingException {

        byte[] bytes = administrativeRegionService.exportRegionList(administrativeRegionQueryDTO);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(Constants.SHORT_DATE_FORMAT);
        String strDate = sdf.format(date);
        String fileName = Constants.ADMINISTRATIVE_REGION_EXPORT_NAME + "-" + strDate + ".xlsx";
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileName);
        return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.CREATED);
    }

    @GetMapping("/{version}/pt/administrative/syncResult")
    @ApiOperation("同步街道信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void syncResult(@PathVariable String version,
                           @RequestParam(required = false) String startDate,
                           @RequestParam(required = false) String endDate) {
        Date startTime = StringUtils.isBlank(startDate) ? DateUtil.beginOfDay(new Date()) : DateUtil.parseDateTime(startDate);
        Date endTime = StringUtils.isBlank(startDate) ? DateUtil.endOfDay(new Date()) : DateUtil.parseDateTime(endDate);
        administrativeRegionService.syncResult(startTime, endTime);
    }

    @GetMapping("/{version}/pt/administrative/streetAddressStd")
    @ApiOperation("同步街道信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void streetAddressStd(@PathVariable String version,
                                 @RequestParam(required = false) String startDate,
                                 @RequestParam(required = false) String endDate) {
        Date startTime = StringUtils.isBlank(startDate) ? DateUtil.beginOfDay(new Date()) : DateUtil.parseDateTime(startDate);
        Date endTime = StringUtils.isBlank(startDate) ? DateUtil.endOfDay(new Date()) : DateUtil.parseDateTime(endDate);
        administrativeRegionService.streetAddressStd(startTime, endTime);
    }
}
