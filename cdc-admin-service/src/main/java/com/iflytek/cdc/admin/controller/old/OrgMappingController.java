package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.MatchOrgDTO;
import com.iflytek.cdc.admin.dto.OrgMatchResultDTO;
import com.iflytek.cdc.admin.dto.SearchHisOrgDTO;
import com.iflytek.cdc.admin.entity.ExcelUploadResult;
import com.iflytek.cdc.admin.entity.FailUpload;
import com.iflytek.cdc.admin.entity.HisOrg;
import com.iflytek.cdc.admin.entity.UapOrganization;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import com.iflytek.cdc.admin.service.OrgMappingService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * 机构映射controller
 *
 * <AUTHOR>
 * @date 2021/6/30 10:21
 **/

@Slf4j
@Api(tags = "机构映射接口")
@RestController
public class OrgMappingController {

    private final OrgMappingService oService;



    public OrgMappingController(OrgMappingService oService) {
        this.oService = oService;
    }



    @PostMapping("/{version}/pt/org/mapping/queryHisOrgInfo")
    @ApiOperation("查询his机构列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<HisOrg> queryHisOrgInfo(@PathVariable String version, @RequestBody SearchHisOrgDTO hisOrgDTO) {
        return oService.queryHisOrg(hisOrgDTO);
    }


    @GetMapping("/{version}/pt/org/mapping/downExcelTemplate")
    @ApiOperation("下载his机构导入模板")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void downExcelTemplate(@PathVariable String version, HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        XSSFWorkbook wb = null;
        OutputStream out = null;
        try {
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + Constants.EXCEL_TEMPLATE_NAME);
            //获取模板存放的路径
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(Constants.EXCEL_TEMPLATE_PATH);
            //生成excel
            if (is != null) {
                wb = new XSSFWorkbook(is);
                out = response.getOutputStream();
                wb.write(out);
                //关闭流
                out.flush();
            }
        } catch (Exception e) {
            log.error("his机构导入模板下载异常:{}", e);
            throw new MedicalBusinessException("模板下载异常");
        } finally {
            try {
                wb.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @PostMapping("/{version}/pt/org/mapping/downHisOrgFail")
    @ApiOperation("下载导入失败数据")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void downHisOrgFail(@PathVariable String version, @RequestBody FailUpload fu, HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        oService.exportFail(fu.getFailResult(), response);
    }

    @PostMapping(value = "/{version}/pt/org/mapping/uploadHisOrg", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("his机构数据导入")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ExcelUploadResult exportHisOrg(@PathVariable String version, @RequestPart("file") MultipartFile file, @RequestParam String loginUserId) {
        return oService.readUploadHisOrg(file, loginUserId);
    }

    @GetMapping(value = "/{version}/pt/org/mapping/deleteHisOrg")
    @ApiOperation("his机构数据删除")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteHisOrg(@PathVariable String version, @RequestParam String hisOrgId) {
        oService.deleteHisOrg(hisOrgId);
    }

    @PostMapping(value = "/{version}/pt/org/mapping/queryUapOrgInfo")
    @ApiOperation("uap机构数据分页查询")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<UapOrganization> queryUapOrgInfo(@PathVariable String version, @RequestBody SearchHisOrgDTO uapOrg, @RequestParam String loginUserId) {
        return oService.queryUapOrg(uapOrg, loginUserId);
    }

    @PostMapping(value = "/{version}/pt/org/mapping/matchOrg")
    @ApiOperation("机构映射一键匹配")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public OrgMatchResultDTO matchOrg(@PathVariable String version, @RequestBody MatchOrgDTO mo, @RequestParam String loginUserId) {
       return  oService.matchOrg(mo, loginUserId);
    }

    @PostMapping(value = "/{version}/pt/org/mapping/unbindOrgMatch")
    @ApiOperation("机构映射解绑")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void unbindOrgMatch(@PathVariable String version, @RequestBody MatchOrgDTO mo, @RequestParam String loginUserId) {
        oService.unbindOrgMatch(mo, loginUserId);
    }

    @PostMapping(value = "/{version}/pt/org/mapping/uapOrgByHis")
    @ApiOperation("根据his机构编码查询uap机构信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public UapOrgInfo uapOrgByHis(@PathVariable String version, @RequestBody HisInfoFilter  hisInfoFilter){
       return  oService.queryUapOrgByHis(hisInfoFilter);
    }

    @GetMapping(value = "/{version}/pt/org/mapping/hisOrgByUap")
    @ApiOperation("根据uapId查询his机构信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public HisOrgInfo  hisOrgByUap(@RequestParam String uapOrgId, @PathVariable String version){
      return oService.queryHisOrgByUap(uapOrgId);
    }



}
