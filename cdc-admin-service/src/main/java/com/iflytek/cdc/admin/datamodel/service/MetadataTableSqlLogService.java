package com.iflytek.cdc.admin.datamodel.service;

import cn.hutool.core.lang.Pair;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableSqlLog;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface MetadataTableSqlLogService extends ICdcService<TbCdcdmMetadataTableSqlLog> {

    /**
     * 生成创建表的sql
     */
    void createTableSql(TbCdcdmMetadataTableInfo tableInfo);

    /**
     * 生成变更表的sql
     */
    void createAlertSql(Pair<TbCdcdmMetadataTableInfo, TbCdcdmMetadataTableInfo> tbCdcdmMetadataTableInfo,
                        List<Pair<TbCdcdmMetadataTableColumnInfo, TbCdcdmMetadataTableColumnInfo>> updates,
                        List<TbCdcdmMetadataTableColumnInfo> inserts);

    /**
     * 启动执行
     */
    void startToExecute(TbCdcdmMetadataTableInfo tbCdcdmMetadataTableInfo);

    /**
     * 获取待执行的sql
     */
    List<TbCdcdmMetadataTableSqlLog> listToExecute();

    /**
     * 状态变更
     */
    void update(List<TbCdcdmMetadataTableSqlLog> logs);

}
