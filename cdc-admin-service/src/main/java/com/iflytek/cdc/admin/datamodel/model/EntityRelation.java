package com.iflytek.cdc.admin.datamodel.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("实体关系")
public class EntityRelation {
    @ApiModelProperty("表标识")
    private String tableKey;
    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("子表")
    private List<EntityRelation> children;
    @ApiModelProperty("关系")
    private Relationship relationship;

    @Data
    public static class Relationship{
        @ApiModelProperty("当前表字段")
        private String fromKey;
        @ApiModelProperty("类型")
        private String type;
        @ApiModelProperty("目标表字段")
        private String toKey;
    }

}
