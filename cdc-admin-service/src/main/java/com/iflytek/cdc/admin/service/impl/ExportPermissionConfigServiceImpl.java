package com.iflytek.cdc.admin.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrExportPermissionConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrExportPermissionOrg;
import com.iflytek.cdc.admin.entity.TbCdcmrOrgApprove;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionConfigMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionOrgMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrOrgApproveMapper;
import com.iflytek.cdc.admin.service.ExportPermissionConfigService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.record.DVALRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ExportPermissionConfigServiceImpl implements ExportPermissionConfigService {

    @Resource
    private TbCdcmrExportPermissionConfigMapper exportPermissionConfigMapper;

    @Resource
    private UapServiceApi uapServiceApi;

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    private TbCdcmrExportPermissionOrgMapper tbCdcmrExportPermissionOrgMapper;
    @Autowired
    public void setTbCdcmrExportPermissionOrgMapper(TbCdcmrExportPermissionOrgMapper tbCdcmrExportPermissionOrgMapper) {
        this.tbCdcmrExportPermissionOrgMapper = tbCdcmrExportPermissionOrgMapper;
    }

    private TbCdcmrOrgApproveMapper tbCdcmrOrgApproveMapper;

    @Autowired
    public void setTbCdcmrOrgApproveMapper(TbCdcmrOrgApproveMapper tbCdcmrOrgApproveMapper) {
        this.tbCdcmrOrgApproveMapper = tbCdcmrOrgApproveMapper;
    }

    // 查询导出审核配置
    @Override
    public PageInfo<ExportConfigDTO> queryExportConfigs(ExportConfigQueryDTO queryDTO,String loginUserId) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        // 进行机构拆分 进行预设数据
        fillExportPermissionOrgData(queryDTO,loginUserId);
        // 执行查询
        List<ExportConfigDTO> exportConfigs = exportPermissionConfigMapper.queryExportConfigs(queryDTO);
        // 获取分页信息
        return new PageInfo<>(exportConfigs);
    }


    // 更新审核权限
    @Override
    public boolean updateApprovalRequired(String loginUserId,String id, boolean approvalRequired) {
        TbCdcmrExportPermissionOrg tbCdcmrExportPermissionOrg = tbCdcmrExportPermissionOrgMapper.selectById(id);
        if (tbCdcmrExportPermissionOrg == null){
            log.error("更新审核权限出错,未查询到权限配置. id: {}", id);
            return false;
        }
        tbCdcmrExportPermissionOrg.setApprovalRequired(approvalRequired);
        tbCdcmrExportPermissionOrg.setUpdaterId(loginUserId);
        tbCdcmrExportPermissionOrg.setUpdater(uapServiceApi.getUser(loginUserId).getName());
        tbCdcmrExportPermissionOrg.setUpdaterTime(new Date());
        return tbCdcmrExportPermissionOrgMapper.updateById(tbCdcmrExportPermissionOrg) > 0;
    }

    // 查询导出权限配置
    @Override
    public ExportPermissionConfigDTO getExportPermissionConfig(String idOrCode) {
        TbCdcmrExportPermissionConfig config = exportPermissionConfigMapper.selectById(idOrCode);
        if (config == null) {
            config = exportPermissionConfigMapper.selectByExportCode(idOrCode);
        }
        if (config == null) {
            return null;
        }

        // 映射导出权限配置
        ExportPermissionConfigDTO exportPermissionConfigDTO = new ExportPermissionConfigDTO();
        exportPermissionConfigDTO.setExportRange(config.getExportRange());
        exportPermissionConfigDTO.setUserRange(config.getUserRange());

        // 解析 allowedUsers
        List<AllowedUsersDTO> allowedUsers = parseAllowedUsers(config.getAllowedUsers());
        exportPermissionConfigDTO.setAllowedUsers(allowedUsers);

        return exportPermissionConfigDTO;
    }

    // 查询审核权限配置
    @Override
    public ApprovalPermissionConfigDTO getApprovalPermissionConfig(String id) {

        TbCdcmrExportPermissionOrg tbCdcmrExportPermissionOrg = tbCdcmrExportPermissionOrgMapper.selectById(id);
        if (tbCdcmrExportPermissionOrg == null){
            return null;
        }
        Integer approvalLevels = tbCdcmrExportPermissionOrg.getApprovalLevels();
        Integer approvalTimeLimit = tbCdcmrExportPermissionOrg.getApprovalTimeLimit();

        List<ApproverDTO> approverList = tbCdcmrOrgApproveMapper.selectApproveByPermissionOrgId(id);
        ApprovalPermissionConfigDTO approvalPermissionConfig = new ApprovalPermissionConfigDTO();
        approvalPermissionConfig.setApprovalTimeLimit(approvalTimeLimit);
        approvalPermissionConfig.setApprovalLevels(approvalLevels);
        approvalPermissionConfig.setApprovers(approverList);
        return approvalPermissionConfig;
    }

    //更新导出权限配置
    @Override
    public boolean updateExportPermissionConfig(String id, ExportPermissionConfigDTO exportPermissionConfig, String loginUserId) {
        TbCdcmrExportPermissionConfig config = new TbCdcmrExportPermissionConfig();
        config.setId(id);
        config.setExportRange(exportPermissionConfig.getExportRange());
        config.setUserRange(exportPermissionConfig.getUserRange());
        config.setUpdaterId(loginUserId);
        config.setUpdater(uapServiceApi.getUser(loginUserId).getName());
        config.setUpdaterTime(new Date());

        // 将 allowedUsers 转换为 JSON 字符串
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String allowedUsersJson = objectMapper.writeValueAsString(exportPermissionConfig.getAllowedUsers());
            config.setAllowedUsers(allowedUsersJson);
        } catch (Exception e) {
            log.error("更新导出权限配置用户查询异常", e);
            throw new MedicalBusinessException("更新导出权限配置用户查询异常");
        }

        return exportPermissionConfigMapper.updateById(config) > 0;
    }

    //更新审核权限配置
    @Override
    public boolean updateApprovalPermissionConfig(String id, ApprovalPermissionConfigDTO approvalPermissionConfig, String loginUserId) {

        TbCdcmrExportPermissionOrg tbCdcmrExportPermissionOrg = tbCdcmrExportPermissionOrgMapper.selectById(id);
        if (tbCdcmrExportPermissionOrg == null){
            log.error("更新审核权限配置异常,未查询到权限配置");
            throw new MedicalBusinessException("更新审核权限配置异常");
        }
        try {
            String loginUserName = uapServiceApi.getUser(loginUserId).getName();
            tbCdcmrExportPermissionOrg.setApprovalTimeLimit(approvalPermissionConfig.getApprovalTimeLimit());
            tbCdcmrExportPermissionOrg.setApprovalLevels(approvalPermissionConfig.getApprovalLevels());
            tbCdcmrExportPermissionOrg.setUpdaterId(loginUserId);
            tbCdcmrExportPermissionOrg.setUpdater(loginUserName);
            tbCdcmrExportPermissionOrg.setUpdaterTime(new Date());
            tbCdcmrExportPermissionOrgMapper.updateById(tbCdcmrExportPermissionOrg);

            List<ApproverDTO> approverList = approvalPermissionConfig.getApprovers();
            LambdaQueryWrapper<TbCdcmrOrgApprove> tbCdcmrOrgApproveLambdaQueryWrapper = Wrappers.<TbCdcmrOrgApprove>lambdaQuery().eq(TbCdcmrOrgApprove::getPermissionOrgId, id);
            tbCdcmrOrgApproveMapper.delete(tbCdcmrOrgApproveLambdaQueryWrapper);


            if (approverList != null && !approverList.isEmpty()){
                List<TbCdcmrOrgApprove> tbCdcmrOrgApproveList = Lists.newArrayList();
                approverList.forEach(approverDTO -> {
                    TbCdcmrOrgApprove tbCdcmrOrgApprove = new TbCdcmrOrgApprove();
                    tbCdcmrOrgApprove.setId(String.valueOf(batchUidService.getUid(TbCdcmrOrgApprove.TABLE_NAME)));
                    tbCdcmrOrgApprove.setPermissionOrgId(id);
                    tbCdcmrOrgApprove.setUserId(approverDTO.getId());
                    tbCdcmrOrgApprove.setUserName(approverDTO.getName());
                    tbCdcmrOrgApprove.setLevel(tbCdcmrExportPermissionOrg.getApprovalLevels());
                    tbCdcmrOrgApprove.setCreatorId(loginUserId);
                    tbCdcmrOrgApprove.setCreator(loginUserName);
                    tbCdcmrOrgApprove.setCreatorTime(new Date());
                    tbCdcmrOrgApprove.setUpdaterId(loginUserId);
                    tbCdcmrOrgApprove.setUpdater(loginUserName);
                    tbCdcmrOrgApprove.setUpdaterTime(new Date());
                    tbCdcmrOrgApproveList.add(tbCdcmrOrgApprove);
                });
                if (!tbCdcmrOrgApproveList.isEmpty()){
                    Lists.partition(tbCdcmrOrgApproveList, 500).forEach(tbCdcmrOrgApproveMapper::saveOrgApprove);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新审核权限配置异常",e);
            throw new MedicalBusinessException("更新审核权限配置异常");
        }
    }

    //删除导出审核权限配置
    @Override
    public boolean deleteExportPermissionConfig(String id) {
        return exportPermissionConfigMapper.deleteById(id) > 0;
    }

    private List<AllowedUsersDTO> parseAllowedUsers(String allowedUsersJson) {
        //使用json解析库
        List<AllowedUsersDTO> allowedUsers = new ArrayList<>();
        if (allowedUsersJson != null && !allowedUsersJson.isEmpty()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                allowedUsers = objectMapper.readValue(allowedUsersJson, new TypeReference<List<AllowedUsersDTO>>() {
                });
            } catch (Exception e) {
                log.error("导出权限配置用户查询异常", e);
                throw new MedicalBusinessException("导出权限配置用户查询异常");
            }
        }
        return allowedUsers;
    }

    private List<ApproverDTO> parseApprovers(String Approvers) {

        List<ApproverDTO> approvers = new ArrayList<>();
        if (Approvers != null && !Approvers.isEmpty()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                approvers = objectMapper.readValue(Approvers, new TypeReference<List<ApproverDTO>>() {
                });
            } catch (Exception e) {
                log.error("审核权限配置用户查询异常", e);
                throw new MedicalBusinessException("审核权限配置用户查询异常");
            }
        }
        return approvers;
    }

    private void fillExportPermissionOrgData(ExportConfigQueryDTO queryDTO, String loginUserId) {
        // 机构Id
        String orgId = queryDTO.getOrgId();
        if (StrUtil.isBlank(orgId)){
            log.error("导出权限配置查询异常,缺少机构Id");
            throw new MedicalBusinessException("导出权限配置查询异常");
        }
        // 应用类型
        String moduleType = queryDTO.getModuleType();
        // 查询机构未配置的导出权限配置 主要是为了预设数据
        List<String> exportIdList = exportPermissionConfigMapper.selectNotExportConfig(moduleType, orgId);

        if (exportIdList != null && !exportIdList.isEmpty()){
            String loginUserName = uapServiceApi.getUser(loginUserId).getName();
            List<TbCdcmrExportPermissionOrg> tbCdcmrExportPermissionOrgList = Lists.newArrayList();
            // 说明需要进行预设数据
            exportIdList.forEach(exportId -> {
                TbCdcmrExportPermissionOrg tbCdcmrExportPermissionOrg = new TbCdcmrExportPermissionOrg();
                tbCdcmrExportPermissionOrg.setId(String.valueOf(batchUidService.getUid(TbCdcmrExportPermissionOrg.TABLE_NAME)));
                tbCdcmrExportPermissionOrg.setExportId(exportId);
                tbCdcmrExportPermissionOrg.setOrgId(orgId);
                tbCdcmrExportPermissionOrg.setApprovalRequired(true);
                tbCdcmrExportPermissionOrg.setApprovalTimeLimit(24);
                tbCdcmrExportPermissionOrg.setApprovalLevels(1);
                tbCdcmrExportPermissionOrg.setCreatorId(loginUserId);
                tbCdcmrExportPermissionOrg.setCreatorTime(new Date());
                tbCdcmrExportPermissionOrg.setCreator(loginUserName);
                tbCdcmrExportPermissionOrg.setUpdaterId(loginUserId);
                tbCdcmrExportPermissionOrg.setUpdater(loginUserName);
                tbCdcmrExportPermissionOrg.setUpdaterTime(new Date());
                tbCdcmrExportPermissionOrgList.add(tbCdcmrExportPermissionOrg);
            });
            if (!tbCdcmrExportPermissionOrgList.isEmpty()){
                Lists.partition(tbCdcmrExportPermissionOrgList, 500).forEach(tbCdcmrExportPermissionOrgMapper::saveExportPermissionOrg);
            }
        }

    }

}