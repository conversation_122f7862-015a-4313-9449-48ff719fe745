package com.iflytek.cdc.admin.customizedapp.controller;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsAppCategory;
import com.iflytek.cdc.admin.customizedapp.service.AppCategoryService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/customizedApp/category")
public class AppCategoryController {

    @Resource
    private AppCategoryService tbCdccsAppCategoryService;

    /**
     * 通过ID查询单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation("通过ID查询单条数据")
    public TbCdccsAppCategory queryById(@RequestParam String id){
        return tbCdccsAppCategoryService.getById(id);
    }


    /**
     * 查询所有分类
     */
    @GetMapping("/listAll")
    @ApiOperation("查询所有分类")
    public List<TbCdccsAppCategory> listAll(){
        return tbCdccsAppCategoryService.listAll();
    }

    /**
     * 新增数据
     */
    @PostMapping("/add")
    @ApiOperation("新增数据")
    public TbCdccsAppCategory addCategory(@RequestBody TbCdccsAppCategory tbCdccsAppCategory){
        return tbCdccsAppCategoryService.create(tbCdccsAppCategory);
    }

    /**
     * 更新数据
     */
    @PostMapping("/update")
    @ApiOperation("更新数据")
    public TbCdccsAppCategory update(@RequestBody TbCdccsAppCategory tbCdccsAppCategory){
        return tbCdccsAppCategoryService.update(tbCdccsAppCategory);
    }

    /**
     * 通过主键删除数据
     */
    @PostMapping("/deleteById")
    @ApiOperation("通过主键删除数据")
    public void deleteById(@RequestParam String id){
        tbCdccsAppCategoryService.deleteById(id);
    }
}
