package com.iflytek.cdc.admin.controller.province;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ExportApplicationDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationQueryDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationRecordDTO;
import com.iflytek.cdc.admin.service.ExportApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "导出任务")
public class ExportApplicationController {
    @Resource
    private ExportApplicationService exportApplicationService;

    @PostMapping("/pt/{version}/exportApplication/submit")
    @ApiOperation("提交导出申请")
    @Transactional(rollbackFor = Exception.class)
    public ExportApplicationDTO submit(@RequestParam String loginUserId, @RequestBody ExportApplicationDTO exportApplicationDTO) {
        return exportApplicationService.submit(exportApplicationDTO, loginUserId);
    }


    @PostMapping("/pt/{version}/exportApplication/queryList")
    @ApiOperation("获取导出申请列表")
    public PageInfo<ExportApplicationListDTO> queryList(@RequestParam String loginUserId, @RequestBody ExportApplicationQueryDTO queryDTO) {
        queryDTO.setLoginUserId(loginUserId);
        return exportApplicationService.queryApplicationExportList(queryDTO);
    }

    @GetMapping("/pt/{version}/exportApplication/detail")
    @ApiOperation("查询导出记录详情")
    public ExportApplicationRecordDTO getExportApplicationRecord(@RequestParam String id, @RequestParam(name = "approvalId", required = false) String approvalId) {
        return exportApplicationService.getExportRecord(id, approvalId);
    }
}
