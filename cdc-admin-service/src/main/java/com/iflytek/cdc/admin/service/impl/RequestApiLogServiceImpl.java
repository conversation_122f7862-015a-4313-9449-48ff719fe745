package com.iflytek.cdc.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.entity.TbCdcmrRequestApiLog;
import com.iflytek.cdc.admin.mapper.TbCdcMrRequestApiLogMapper;
import com.iflytek.cdc.admin.service.RequestApiLogService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class RequestApiLogServiceImpl extends ServiceImpl<TbCdcMrRequestApiLogMapper, TbCdcmrRequestApiLog> implements RequestApiLogService {

    @Resource
    private BatchUidService batchUidService;

    @Override
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveLog(String requestId, 
                        String requestUrl,
                        String status,
                        Object requestParam,
                        Object response,
                        String loginUserId) {
        TbCdcmrRequestApiLog log = new TbCdcmrRequestApiLog();
        log.setId(String.valueOf(batchUidService.getUid(TbCdcmrRequestApiLog.TABLE_NAME)));
        log.setRequestId(requestId);
        log.setRequestUrl(requestUrl);
        log.setRequestParam(JSONUtil.toJsonStr(requestParam));
        log.setResponse(JSONUtil.toJsonStr(response));
        log.setStatus(status);
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        log.setCreatorId(loginUserId);
        save(log);
    }


}
