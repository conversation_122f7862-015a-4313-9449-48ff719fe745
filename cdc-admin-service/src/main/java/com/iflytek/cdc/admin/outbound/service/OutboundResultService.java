package com.iflytek.cdc.admin.outbound.service;

import com.google.gson.Gson;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.outbound.entity.OutboundBatchExeResult;
import com.iflytek.cdc.admin.outbound.entity.OutboundRecord;
import com.iflytek.cdc.admin.outbound.entity.OutboundResultDetail;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundRecordQueryDTO;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundResultDetailQueryDTO;
import com.iflytek.cdc.admin.outbound.model.dto.input.BatchList;
import com.iflytek.cdc.admin.outbound.model.dto.output.BatchDetailRs;
import com.iflytek.cdc.admin.outbound.model.dto.output.BatchListRs;
import com.iflytek.cdc.admin.outbound.model.dto.output.RecordDetailRs;
import com.iflytek.cdc.admin.outbound.util.Response;
import com.iflytek.cdc.admin.service.FileService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.iflytek.cdc.admin.outbound.entity.OutboundRecord.Type.PHONE;

@Service
@Slf4j
public class OutboundResultService {

    @Resource
    private OutboundRecordService outboundRecordService;

    @Resource
    private QueryOutBoundService queryOutBoundService;
    
    @Resource
    private OutboundResultDetailService outboundResultDetailService;
    
    @Resource
    private OutboundBatchExeResultService batchExeResultService;
    
    @Resource
    private FileService fileService;

    @Transactional
    public void saveOutboundResult(OutboundRecordQueryDTO queryDTO){
        List<OutboundRecord> outboundRecords = outboundRecordService.listRecords(queryDTO);
        List<OutboundBatchExeResult> outboundBatchExeResults = new ArrayList<>();
        List<OutboundResultDetail> resultDetails = new ArrayList<>();
        Gson gson = new Gson();
        List<OutboundRecord> updates = new ArrayList<>();
        //暂时只处理电话类型的，短信类型的一直显示外呼失败
        outboundRecords.stream().filter(o -> StringUtils.isNotEmpty(o.getBatchNo()) && PHONE.getCode().equals(o.getType())).forEach(o -> {
            try {
                Response<BatchDetailRs> batchDetailRsResponse = queryOutBoundService.queryBatchDetail(o.getBatchNo());
                if (batchDetailRsResponse.isSuccess()){
                    BatchDetailRs data = batchDetailRsResponse.getData();
                    if (data.isFinished()){
                        OutboundRecord update = new OutboundRecord();
                        update.setId(o.getId());
                        update.setStatus(OutboundRecord.CallStatus.CALLED.getCode());
                        updates.add(update);
                    }
                    OutboundBatchExeResult outboundBatchExeResult = new OutboundBatchExeResult();
                    BeanUtils.copyProperties(data, outboundBatchExeResult);
                    outboundBatchExeResults.add(outboundBatchExeResult);
                    BatchList batchList = BatchList.builder()
                            .batch(o.getBatchNo())
                            .build();
                    Response<BatchListRs> batchListRsResponse = queryOutBoundService.queryBatchList(batchList);
                    if (batchDetailRsResponse.isSuccess()){
                        BatchListRs batchListRs = batchListRsResponse.getData();
                        batchListRs.getData().forEach(d -> {
                            OutboundResultDetail resultDetail = new OutboundResultDetail();
                            resultDetail.setBatchId(o.getBatchNo());
                            BeanUtils.copyProperties(d, resultDetail);
                            resultDetail.setFieldListJson(gson.toJson(batchListRs.getFieldList()));
                            resultDetails.add(resultDetail);
                        });
                    }
                }else if (batchDetailRsResponse.isSystemError()){
                    throw new MedicalBusinessException("获取外呼结果系统错误，请联系管理员获取");
                }
            }catch (Exception e){
                log.error(e.getLocalizedMessage(), e);
                OutboundRecord update = new OutboundRecord();
                update.setId(o.getId());
                update.setStatus(OutboundRecord.CallStatus.FAILED.getCode());
                updates.add(update);
            }
            
        });
        
        if (outboundBatchExeResults.size() > 0) {
            batchExeResultService.save(outboundBatchExeResults);
        }
        
        if (resultDetails.size() > 0){
            outboundResultDetailService.save(resultDetails);
        }
        outboundRecordService.saveStatus(updates);
    }

    @Transactional
    public void updateResultDetail(OutboundResultDetailQueryDTO queryDTO){
        List<OutboundResultDetail> resultDetails = outboundResultDetailService.listDetails(queryDTO);
        resultDetails.forEach(r -> {
            Response<RecordDetailRs> recordDetailRsResponse = queryOutBoundService.queryRecordDetail(r.getRecordId());
            if (recordDetailRsResponse.isSuccess()){
                RecordDetailRs data = recordDetailRsResponse.getData();
                BeanUtils.copyProperties(data, r);
                r.setStatus(Constants.COMMON_STRNUM_ONE);
                try {
                    if (StringUtils.isNotEmpty(data.getLocalUrl())){
                        String attachmentId = fileService.upload(data.getLocalUrl(), r.getId());
                        r.setAttachmentId(attachmentId);
                        r.setAudioUrl(fileService.getPathByAttachmentId(attachmentId));
                    }
                }catch (Exception e){
                    log.error(e.getLocalizedMessage(), e);
                    r.setStatus("error");
                }

            }
        });
        if (resultDetails.size() > 0){
            outboundResultDetailService.updateDetail(resultDetails);
        }
    }


}
