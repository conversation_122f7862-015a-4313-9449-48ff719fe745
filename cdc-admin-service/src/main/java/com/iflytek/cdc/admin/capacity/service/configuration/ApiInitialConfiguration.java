package com.iflytek.cdc.admin.capacity.service.configuration;


import com.iflytek.cdc.admin.capacity.api.algorithm.AlgorithmApi;
import com.iflytek.cdc.admin.capacity.api.algorithm.config.AlgorithmApiConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * api初始化配置 部署
 */
@Configuration
public class ApiInitialConfiguration {
    @Bean("thirdPartyRestTemplate")
    public RestTemplate requestTemplate(){
        return new RestTemplate();
    }

    @Bean
    @ConfigurationProperties("algorithm")
    public AlgorithmApiConfig algorithmApiConfig(){
        return new AlgorithmApiConfig();
    }
    
    @Bean
    public AlgorithmApi algorithmApi(RestTemplate thirdPartyRestTemplate, AlgorithmApiConfig algorithmApiConfig){
        return  new AlgorithmApi(thirdPartyRestTemplate, algorithmApiConfig);
    }
}
