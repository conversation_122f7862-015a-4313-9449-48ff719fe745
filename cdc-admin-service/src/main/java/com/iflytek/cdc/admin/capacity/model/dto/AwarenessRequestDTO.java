package com.iflytek.cdc.admin.capacity.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
public class AwarenessRequestDTO {
    @ApiModelProperty(value = "唯一标识")
    @NotBlank(message = "唯一标识不能为空")
    private String taskID; // 用户ID，唯一标识

    @ApiModelProperty(value = "疫情开始日，日期统一格式例如：2022-03-01")
    @NotBlank(message = "疫情开始日不能为空")
    private String startDay; // 疫情开始日，日期统一格式例如：2022-03-01

    @ApiModelProperty(value = "疫情结束日")
    @NotBlank(message = "疫情结束日不能为空")
    private String endDay; // 疫情结束日

    @ApiModelProperty(value = "总人口数")
    @NotNull(message = "总人口数不能为空")
    private Integer popSize; // 总人口数

    @ApiModelProperty(value = "初始感染数")
    @NotNull(message = "初始感染数不能为空")
    private Integer popInfected; // 初始感染数

    @ApiModelProperty(value = "感染率")
    @NotNull(message = "感染率不能为空")
    private Double beta; // 传染率，即一个易感者接触到一个携带者，被传染的概率，0~1之间

    @ApiModelProperty(value = "无症状者传染率相对水平，0~1之间")
    @NotNull(message = "无症状者感染率相对水平不能为空")
    private Double asympFactor; // 无症状者传染率相对水平，0~1之间

    @ApiModelProperty(value = "参数校准：是否做参数校准")
    private boolean ifCalibrate; // 参数校准：是否做参数校准

    @ApiModelProperty(value = "校准数据")
    private List<CalibrationData> calibrationData; // date和new_diagnoses，分别代表日期和新增病例数，用于“校准”参数

    @ApiModelProperty(value = "疫情态势推演的结束日，用于“校准”后或“政策仿真”的疫情态势推演")
    private String predDay; // 疫情态势推演的结束日，用于“校准”后或“政策仿真”的疫情态势推演

    @ApiModelProperty(value = "政策辅助推荐：是否绘制等高线")
    private boolean ifContour; // 政策辅助推荐：是否绘制等高线

    @ApiModelProperty(value = "学校政策")
    private InterventionPolicy schoolPolicy; // 学校政策

    @ApiModelProperty(value = "工作场所政策")
    private InterventionPolicy workDaysPolicy; // 工作场所政策

    @ApiModelProperty(value = "公共场所政策")
    private InterventionPolicy communityPolicy; // 公共场所政策

    @ApiModelProperty(value = "已发生的干预：是否包含简单疫苗")
    private boolean simpleVaccinePre; // 已发生的干预：是否包含简单疫苗

    @ApiModelProperty(value = "已发生的干预：接种疫苗的概率，0~1之间")
    private Double vaccineProbPre; // 已发生的干预：接种疫苗的概率，0~1之间

    @ApiModelProperty(value = "降低易感")
    private Double vaccineRelSusPre;//降低易感

    @ApiModelProperty(value = "已发生的干预：降低发病")
    private Double vaccineRelSympPre; // 降低发病

    @ApiModelProperty(value = "潜伏期 正数")
    private Double latency;//潜伏期 正数
    
    @ApiModelProperty(value = "有症状感染者比例")
    private Double mySympProb;//有症状感染者比例,0~1之间, mySympProb+mySevereProb+myDeathProb<=1, mySympProb>=mySevereProb>=myDeathProb
   
    @ApiModelProperty(value = "重症患者比例")
    private Double mySevereProb;//重症患者比例,0~1之间, mySympProb+mySevereProb+myDeathProb<=1, mySympProb>=mySevereProb>=myDeathProb
    
    @ApiModelProperty(value = "重症患者隔离天数")
    private Double quarPeriod;//重症患者隔离天数，正整数
    
    @ApiModelProperty(value = "重症患者病死率")
    private Double myDeathProb;//重症患者病死率


    @ApiModelProperty(value = "口罩政策")
    private InterventionPolicy masklPolicy; // 口罩政策

    @ApiModelProperty(value = "社交距离政策")
    private InterventionPolicy socialDistancePolicy; // 社交距离政策

    @ApiModelProperty(value = "人群聚集政策")
    private InterventionPolicy crowdGatherPolicy; // 人群聚集政策

    @ApiModelProperty(value = "消杀政策")
    private InterventionPolicy disinfectionDaysPolicy; // 消杀政策

    /**
     * 干预政策
     */
    @Data
    public static class InterventionPolicy{
        private String startDate;
        private String endDate;
        private Double rate;

    }

    /**
     * 参数校准数据
     */
    @Data
    public static class CalibrationData{

        private String date;//日期
        private Integer newInfections;//新增感染数
        private Integer cumInfections;//累积感染数
        private Integer newDiagnoses;//新增病例数
        private Integer cumDiagnoses;//累积病例数
        private Integer newDeaths;//新增死亡数
        private Integer cumDeaths;//累计死亡数
    }
}
