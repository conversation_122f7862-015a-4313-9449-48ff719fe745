package com.iflytek.cdc.admin.vo.epi;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class DutyUserVO implements Serializable {

   private static final long serialVersionUID = 1L;

    @NotBlank(message = "机构编码不能为空")
    @Length(max = 64, message = "机构编码长度最大64")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @NotBlank(message = "机构名称不能为空")
    @Length(max = 64, message = "机构名称长度最大64")
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @NotBlank(message = "值班人员ID不能为空")
    @Length(max = 64, message = "值班人员ID长度最大64")
    @ApiModelProperty(value = "值班人员ID")
    private String dutyUserId;

    @NotBlank(message = "值班人员姓名不能为空")
    @Length(max = 64, message = "值班人员姓名长度最大64")
    @ApiModelProperty(value = "值班人员姓名")
    private String dutyUser;

    @NotBlank(message = "值班人员电话不能为空")
    @Length(max = 13, message = "值班人员电话长度最大13")
    @ApiModelProperty(value = "值班人员电话")
    private String dutyPhone;

    @Length(max = 50, message = "职位名称长度最大50")
    @ApiModelProperty(value = "职位名称")
    private String jobTitleName;

    @Length(max = 50, message = "工号长度最大50")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    @NotBlank(message = "值班人员账号不能为空")
    @Length(max = 50, message = "值班人员账号长度最大50")
    @ApiModelProperty(value = "值班人员账号")
    private String loginName;

    @Length(max = 50, message = "部门编码长度最大50")
    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @Length(max = 50, message = "部门名称长度最大50")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @TableField(exist = false)
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @TableField(exist = false)
    @ApiModelProperty(value = "区编码")
    private String districtCode;
}
