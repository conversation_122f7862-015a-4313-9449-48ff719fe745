package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.model.mr.vo.InfectedDiseaseWarningVO;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseWarningMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseWarningRuleMapper;
import com.iflytek.cdc.admin.model.mr.dto.InfectedWarningRuleQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.model.mr.vo.TreeNodeProperty;
import com.iflytek.cdc.admin.service.province.InfectedDiseaseWarningService;
import com.iflytek.cdc.admin.vo.InfectedWarningRuleDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 传染病预警-预警病种对象表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class InfectedDiseaseWarningServiceImpl implements InfectedDiseaseWarningService {

    @Resource
    private TbCdcmrDiseaseWarningMapper tbCdcmrDiseaseWarningMapper;

    @Resource
    private TbCdcmrInfectedDiseaseWarningRuleMapper tbCdcmrInfectedDiseaseWarningRuleMapper;

    @Override
    public List<TreeNode> getInfectedTree(InfectedWarningRuleQueryDTO dto) {

        //疾病列表
        List<InfectedDiseaseWarningVO> diseaseWarningList = tbCdcmrDiseaseWarningMapper.listAll(null, dto.getDiseaseTypeCode());
        //疾病规则数量
        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> ruleCount = this.getDiseaseRuleCount();

        //法定传染病 - 从疾病第二层构建树结构
        List<TreeNode> secondNode = TreeNode.buildTreeByNodeList(diseaseWarningList,
                                                                 InfectedDiseaseWarningVO::getId,
                                                                 InfectedDiseaseWarningVO::getParentDiseaseCode,
                                                                 InfectedDiseaseWarningVO::getDiseaseName,
                                                                 InfectedDiseaseWarningVO::getId);
        //构建病种结构
        Map<Pair<String, String>, List<InfectedDiseaseWarningVO>> infectedClassMap = diseaseWarningList.stream()
                                                                                                            .filter(e -> StringUtils.isNotBlank(e.getDiseaseTypeCode()) && StringUtils.isNotBlank(e.getDiseaseTypeName()))
                                                                                                            .collect(Collectors.groupingBy(InfectedDiseaseWarningVO::getDiseaseClass));

        List<TreeNode> rootNode = new ArrayList<>();
        for(Map.Entry<Pair<String, String>, List<InfectedDiseaseWarningVO>> entry : infectedClassMap.entrySet()){

            //该病种类型的code-value
            Pair<String, String> diseaseClassPair = entry.getKey();
            //该病种下的疾病
            List<InfectedDiseaseWarningVO> diseaseWarnings = entry.getValue();
            TreeNode root = TreeNode.from(diseaseClassPair.getFirst(), diseaseClassPair.getSecond(), diseaseClassPair.getFirst());
            Set<TreeNode> childNodes = new HashSet<>();
            //用第二层的疾病 匹配该病种下的疾病
            secondNode.forEach(e -> {
                for (InfectedDiseaseWarningVO disease : diseaseWarnings) {
                    if(Objects.equals(e.getId(), disease.getDiseaseCode()) || Objects.equals(e.getId(), disease.getParentDiseaseCode())){
                        childNodes.add(e);
                    }
                }
            });
            root.setChildren(new ArrayList<>(childNodes));
            rootNode.add(root);
        }

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);
        //将规则数量添加到对应的节点
        TreeNodeProperty.mergeTreeProperty(rootNode, ruleCount);
        //按照id排序
        rootNode = rootNode.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());

        return rootNode;
    }

    /**
     * 查询每个疾病下 规则数
     * */
    private Map<String, List<TreeNodeProperty.RiskRuleCountVO>> getDiseaseRuleCount(){

        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> res = new HashMap<>();
        //查询传染病 每个风险等级下 规则的数量
        List<InfectedWarningRuleDetailVO> riskRuleCountList = tbCdcmrInfectedDiseaseWarningRuleMapper.getRiskRuleCountBy();
        //按照疾病进行分组
        Map<String, List<InfectedWarningRuleDetailVO>> listMap = riskRuleCountList.stream()
                                                                                  .collect(Collectors.groupingBy(InfectedWarningRuleDetailVO::getId));
        listMap.forEach((k,v) ->{
            List<TreeNodeProperty.RiskRuleCountVO> riskLevelList = new ArrayList<>();
            for (InfectedWarningRuleDetailVO detailVO : v) {
                TreeNodeProperty.RiskRuleCountVO riskLevelVO = new TreeNodeProperty.RiskRuleCountVO();
                if(StrUtil.isNotBlank(detailVO.getRiskLevel())){
                    riskLevelVO.setRiskLevelCount(detailVO.getRiskLevelCount());
                    riskLevelVO.setRiskLevel(detailVO.getRiskLevel());
                    riskLevelList.add(riskLevelVO);
                }
            }
            res.put(k, riskLevelList);
        });
        return res;
    }

}
