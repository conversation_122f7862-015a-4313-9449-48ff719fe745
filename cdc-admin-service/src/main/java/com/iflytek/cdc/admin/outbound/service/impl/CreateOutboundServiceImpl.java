package com.iflytek.cdc.admin.outbound.service.impl;

import com.google.gson.reflect.TypeToken;

import com.iflytek.cdc.admin.outbound.common.executor.OutboundExecutor;
import com.iflytek.cdc.admin.outbound.constant.OutboundConfig;
import com.iflytek.cdc.admin.outbound.model.dto.SignDTO;
import com.iflytek.cdc.admin.outbound.model.dto.input.CallTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.PlanTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.SmsTask;
import com.iflytek.cdc.admin.outbound.model.dto.output.CreateCallRs;
import com.iflytek.cdc.admin.outbound.service.CreateOutboundService;
import com.iflytek.cdc.admin.outbound.util.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 执行外呼服务实现类
 * <AUTHOR>
 */
@Service
public class CreateOutboundServiceImpl implements CreateOutboundService {

    @Resource
    private OutboundConfig outboundConfig;

    @Resource
    private OutboundExecutor outboundExecutor;

    @Override
    public Response<CreateCallRs> execPlanTask(PlanTask planTask) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getPlanTask(), planTask, new TypeToken<Response<CreateCallRs>>() {}.getType());
    }

    @Override
    public Response<CreateCallRs> execCallTask(CallTask callTask) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getCallTask(), callTask, new TypeToken<Response<CreateCallRs>>() {}.getType());
    }

    @Override
    public Response<CreateCallRs> execSmsTask(SmsTask smsTask) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getSmsTask(), smsTask, new TypeToken<Response<CreateCallRs>>() {}.getType());
    }

    @Override
    public SignDTO getSign() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        return SignDTO.builder().timestamp(timestamp).sign(outboundExecutor.getSign(outboundConfig, timestamp)).build();
    }

}
