package com.iflytek.cdc.admin.controller.province;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportTaskDTO;
import com.iflytek.cdc.admin.dto.ExportTaskQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;
import com.iflytek.cdc.admin.service.ExportTaskService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 导出记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@RestController
public class ExportTaskController {
    @Resource
    private ExportTaskService exportTaskService;

    @PostMapping("/pt/{version}/exportTask/add")
    @ApiOperation("新增导出任务")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public TbCdcmrExportTask add(@PathVariable String version, @RequestParam String loginUserId,
                    @RequestBody ExportTaskDTO exportTask){
        return exportTaskService.add(exportTask,loginUserId);
    }
    @PostMapping("/pt/{version}/exportTask/updateById")
    @ApiOperation("更新导出任务")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public Boolean updateById(@PathVariable String version,
                                 @RequestBody TbCdcmrExportTask exportTask){
        return exportTaskService.updateById(exportTask);
    }
    @PostMapping("/pt/{version}/exportTask/checkExportTaskCount")
    @ApiOperation("检查任务是否可以导出，任务数量限制")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public Boolean checkExportTaskCount(@PathVariable String version,
                                        @RequestParam String loginUserId){
        return exportTaskService.checkExportTaskCount();
    }

    @PostMapping("/pt/{version}/exportTask/checkExistTask")
    @ApiOperation("检查任务是否已经存在")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public TbCdcmrExportTask checkExistTask(@PathVariable String version,
                                            @RequestParam String loginUserId,
                                        @RequestBody ExportTaskDTO exportTask){
        return exportTaskService.checkExistTask(exportTask,loginUserId);
    }
    @PostMapping("/pt/{version}/exportTask/queryList")
    @ApiOperation("查询导出任务List")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<TbCdcmrExportTask> queryList(@PathVariable String version,
                                                 @RequestParam String loginUserId,
                                                 @RequestBody ExportTaskQueryDTO queryDTO){
        queryDTO.setLoginUserId(loginUserId);
        return exportTaskService.queryList(queryDTO);
    }

    /**
     * 查询导出记录list
     */
    @PostMapping("/pt/{version}/exportTask/queryExportRecordList")
    @ApiOperation("查询导出记录list")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<ExportApplicationListDTO> queryExportRecordList(@PathVariable String version,
                                                                    @RequestParam String loginUserId,
                                                                    @RequestBody ExportTaskQueryDTO queryDTO) {
        queryDTO.setLoginUserId(loginUserId);
        return exportTaskService.queryExportRecordList(queryDTO);
    }
    @PostMapping("/pt/{version}/exportTask/updateTask")
    @ApiOperation("变更任务的status, attachmentId, attachmentUrl")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateTask(@RequestBody ExportTaskDTO updateTask){
         exportTaskService.updateTask(updateTask);
    }
}

