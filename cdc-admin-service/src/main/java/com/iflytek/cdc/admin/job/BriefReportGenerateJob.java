package com.iflytek.cdc.admin.job;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.entity.brief.*;
import com.iflytek.cdc.admin.mapper.brief.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.apiservice.CdcDataServiceApi;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.brief.DimensionList;
import com.iflytek.cdc.admin.dto.brief.IndicatorSearchDto;
import com.iflytek.cdc.admin.service.brief.PushRecordService;
import com.iflytek.cdc.admin.util.brief.CycleTimeUtil;
import com.iflytek.cdc.admin.vo.brief.IndicatorVo;
import com.iflytek.cdc.admin.vo.brief.TemplateRuleDetailVo;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 生成简报任务
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BriefReportGenerateJob {

    private static final int BATCH_SIZE = 1000;

    private final BatchUidService batchUidService;
    private final TemplateRuleMapper templateRuleMapper;
    private final Executor asyncExecutor;
    private final BriefTemplateMapper briefTemplateMapper;
    private final BriefIndicatorsMapper briefIndicatorsMapper;
    private final BriefInfoMapper briefInfoMapper;
    private final PushRecordService pushRecordService;
    private final CdcDataServiceApi cdcDataServiceApi;

    private final BriefTakeApplyMapper briefTakeApplyMapper;

    /**
     * 执行
     *
     * @return {@link ReturnT}<{@link String}>
     */
    @XxlJob(value = "generateBriefReport")
    public ReturnT<String> execute(String param) {
        long startTime = SystemClock.now();
        log.info("开始执行生成简报任务");
        // 按当前日期查询待生成的模板规则
        String currentDate = StrUtil.isBlank(param) ? DateUtil.format(new Date(), "yyyy-MM-dd") : param;
        List<TemplateRuleDetailVo> templateRuleList = templateRuleMapper.selectByDate(currentDate);
        if (CollUtil.isEmpty(templateRuleList)) {
            log.info("没有待生成的模板规则");
            return ReturnT.SUCCESS;
        }
        Map<String, List<TemplateRuleDetailVo>> templateRuleMap = templateRuleList.stream()
                .collect(Collectors.groupingBy(TemplateRuleDetailVo::getBusinessType));
        templateRuleMap.forEach(this::executePerBusinessType);
        log.info("结束执行生成简报任务，耗时：{}ms", SystemClock.now() - startTime);
        return ReturnT.SUCCESS;
    }

    private void executePerBusinessType(String businessType, List<TemplateRuleDetailVo> templateRuleList) {
        if (CollUtil.isEmpty(templateRuleList)) {
            return;
        }
        String businessTypeDesc = Constants.BriefReport.getBusinessTypeDesc(businessType);
        log.info("{}模块简报生成任务开始", businessTypeDesc);
        long startTime = SystemClock.now();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        Lists.partition(templateRuleList, 5).forEach(item -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> asyncBriefInfo(item, businessType),
                    asyncExecutor);
            futures.add(future);
        });
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (CompletionException e) {
            log.error("{}模块简报生成出现异常：{}", businessTypeDesc, e.getMessage(), e);
        }
        log.info("{}模块简报生成任务结束，耗时：{}ms", businessTypeDesc, SystemClock.now() - startTime);
    }

    private void asyncBriefInfo(List<TemplateRuleDetailVo> rules, String businessType) {
        if (CollUtil.isEmpty(rules)) {
            return;
        }
        rules.forEach(rule -> {
            // 查询指标集合
            List<IndicatorsEntity> listIndicators = briefTemplateMapper.listIndicators(rule.getTemplateId());
            List<IndicatorsEntity> zwIndicators = listIndicators.stream()
                    .filter(e -> Constants.BriefReport.INDEX_SCOPE_ZW.equals(e.getIndexScope()))
                    .collect(Collectors.toList());
            List<IndicatorsEntity> fjIndicators = listIndicators.stream()
                    .filter(e -> Constants.BriefReport.INDEX_SCOPE_FJ.equals(e.getIndexScope()) &&
                            Constants.BriefReport.LOCATION_TYPE_TEXT.equals(e.getLocation()))
                    .collect(Collectors.toList());
            List<IndicatorsEntity> fjHeadIndicators = listIndicators.stream()
                    .filter(e -> Constants.BriefReport.INDEX_SCOPE_FJ.equals(e.getIndexScope()) &&
                            Constants.BriefReport.LOCATION_TYPE_HEAD.equals(e.getLocation()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(zwIndicators)) {
                log.info("模板规则：{}，指标集合为空", rule.getTemplateId());
                return;
            }
            // 调用指标查询接口获取数据
            String briefId = handleZWIndicatorData(rule, zwIndicators, businessType);
            if (CollUtil.isEmpty(fjIndicators)) {
                log.info("附件模板规则：{}，指标集合为空", rule.getTemplateId());
                return;
            }
            if (CollUtil.isNotEmpty(fjHeadIndicators)) {
                persistIndicatorValues(fjHeadIndicators, briefId);
            }
            handleFJIndicatorData(rule, fjIndicators, businessType, briefId);
        });
    }

    /**
     * 处理指标数据
     *
     * @param rule         规则
     * @param zwIndicators zw指标
     * @return 简报id
     */
    private String handleZWIndicatorData(TemplateRuleDetailVo rule, List<IndicatorsEntity> zwIndicators,
            String businessType) {
        // 组装接口入参
        List<IndicatorSearchDto> indicatorSearchDtoList = Lists.newArrayList();
        zwIndicators.forEach(e -> {
            IndicatorSearchDto indicatorSearchDto = new IndicatorSearchDto();
            indicatorSearchDto.setIndicatorList(Lists.newArrayList(e.getIndexId()));
            buildDate(rule, indicatorSearchDto);
            // 省市区
            indicatorSearchDto.setProvince(rule.getProvinceCode());
            indicatorSearchDto.setCity(rule.getCityCode());
            indicatorSearchDto.setDistrict(rule.getDistrictCode());
            DimensionList dimensionList = new DimensionList();
            dimensionList.setDimensionId(e.getParentDimensionId());
            dimensionList.setValues(Lists.newArrayList(e.getDimensionName()));
            indicatorSearchDto.setDimensionList(Lists.newArrayList(dimensionList));
            indicatorSearchDtoList.add(indicatorSearchDto);
        });
        // 调用接口获取指标数据值
        log.info("正文调用接口获取指标数据值，入参：{},规则id：{}", JSONUtil.toJsonStr(indicatorSearchDtoList), rule.getId());
        List<IndicatorVo> indicatorVoList = cdcDataServiceApi.getIndicatorValuesBy(indicatorSearchDtoList,
                businessType);
        log.info("正文调用接口获取指标数据值，返回：{},规则id：{}", JSONUtil.toJsonStr(indicatorVoList), rule.getId());

        // 预先生成简报信息ID，因为存简报
        String briefId = String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_BRIEF_INFO));
        // 组装指标数据，更新指标数据表
        handleIndicatorDataResponse(zwIndicators, indicatorVoList, briefId);
        // 生成简报信息
        generateBriefReportInfo(rule, zwIndicators, briefId);
        // 生成推送记录
        generatePushRecord(rule, briefId);
        return briefId;
    }

    private void handleFJIndicatorData(TemplateRuleDetailVo rule, List<IndicatorsEntity> fjIndicators,
            String businessType, String briefId) {
        // 组装接口入参
        List<IndicatorSearchDto> indicatorSearchDtoList = Lists.newArrayList();
        IndicatorSearchDto indicatorSearchDto = new IndicatorSearchDto();
        buildDate(rule, indicatorSearchDto);
        // 构建地区维度
        indicatorSearchDto.setProvince(rule.getProvinceCode());
        indicatorSearchDto.setCity(rule.getCityCode());
        indicatorSearchDto.setDistrict(rule.getDistrictCode());
        indicatorSearchDto.setIndicatorList(
                fjIndicators.stream().map(IndicatorsEntity::getIndexId).distinct().collect(Collectors.toList()));
        DimensionList dimensionList = new DimensionList();
        dimensionList.setDimensionId(fjIndicators.get(0).getParentDimensionId());
        dimensionList.setValues(
                fjIndicators.stream().map(IndicatorsEntity::getDimensionName).distinct().collect(Collectors.toList()));
        indicatorSearchDto.setDimensionList(Lists.newArrayList(dimensionList));
        indicatorSearchDtoList.add(indicatorSearchDto);

        // 调用接口获取指标数据值
        log.info("附件调用接口获取指标数据值，入参：{},规则id：{}", JSONUtil.toJsonStr(indicatorSearchDtoList), rule.getId());
        List<IndicatorVo> indicatorVoList = cdcDataServiceApi.getIndicatorValuesBy(indicatorSearchDtoList,
                businessType);
        log.info("附件调用接口获取指标数据值，返回：{},规则id：{}", JSONUtil.toJsonStr(indicatorVoList), rule.getId());
        // 组装指标数据，更新指标数据表
        handleIndicatorDataResponse(fjIndicators, indicatorVoList, briefId);
    }

    private static void buildDate(TemplateRuleDetailVo rule, IndicatorSearchDto indicatorSearchDto) {
        String time = rule.getStatisticsTime();
        switch (rule.getStatisticsCycle()) {
            case "year":
                indicatorSearchDto.setYear(time.substring(0, 4));
                break;
            case "month":
                indicatorSearchDto.setYear(time.substring(0, 4));
                indicatorSearchDto.setMonth(time.substring(time.indexOf("年") + 1, time.indexOf("月")));
                break;
            case "week":
                indicatorSearchDto.setYear(time.substring(0, 4));
                indicatorSearchDto.setWeek(time.substring(time.indexOf("第") + 1, time.indexOf("周")));
                break;
            case "day":
                indicatorSearchDto.setDay(CycleTimeUtil.convertChineseDate(time));
                break;
            default:
                break;
        }
    }

    /**
     * 生成推送记录
     *
     * @param rule    规则
     * @param briefId 简短id
     */
    private void generatePushRecord(TemplateRuleDetailVo rule, String briefId) {
        // 查询已配置的权限列表
        List<PushUserEntity> pushUserList = templateRuleMapper.pushUserList(rule.getRuleId(),null);
        if (CollUtil.isEmpty(pushUserList)) {
            log.info("模板规则：{}，推送用户为空", rule.getRuleId());
            return;
        }
        String briefReportType = rule.getBriefReportType();
        if (StrUtil.isNotBlank(briefReportType)){
            // 查询申请审核的
            List<PushUserEntity> pushUserApproveList = buildBriefTakeApplyUser(rule.getTemplateId());
            // 
        }
        List<PushRecordEntity> pushRecordList = Lists.newArrayList();
        pushUserList.forEach(e -> {
            PushRecordEntity pushRecord = new PushRecordEntity();
            pushRecord.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_PUSH_RECORD)));
            pushRecord.setBriefId(briefId);
            pushRecord.setPushPersonId(e.getUapUserId());
            pushRecord.setPushPersonName(e.getUapUserName());
            pushRecord.setPushPersonPhone(e.getPhone());
            pushRecord.setProvinceCode(rule.getProvinceCode());
            pushRecord.setProvinceName(rule.getProvinceName());
            pushRecord.setCityCode(rule.getCityCode());
            pushRecord.setCityName(rule.getCityName());
            pushRecord.setDistrictCode(rule.getDistrictCode());
            pushRecord.setDistrictName(rule.getDistrictName());
            pushRecord.setBusinessType(rule.getBusinessType());
            pushRecord.setCreateTime(new Date());
            pushRecordList.add(pushRecord);
        });
        Lists.partition(pushRecordList, BATCH_SIZE).forEach(pushRecordService::saveBatch);
    }

    private List<PushUserEntity> buildBriefTakeApplyUser(String templateId) {
        LambdaQueryWrapper<BriefTakeApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BriefTakeApply::getTemplateId,templateId);
        List<BriefTakeApply> briefTakeApplyList = briefTakeApplyMapper.selectList(wrapper);
        if (briefTakeApplyList == null || briefTakeApplyList.isEmpty()){
            return new ArrayList<>();
        }
        List<PushUserEntity> list = new ArrayList<>();
        for (BriefTakeApply briefTakeApply : briefTakeApplyList) {
            BriefTakeApply.UserData applyUser = briefTakeApply.getApplyUser();
            PushUserEntity pushUserEntity = new PushUserEntity();
            pushUserEntity.setUapUserId(applyUser.getUserId());
            pushUserEntity.setUapUserName(applyUser.getUserName());
            pushUserEntity.setPhone(applyUser.getPhoneNum());
        }
        return list;
    }

    /**
     * 生成简要报告信息
     *
     * @param rule       规则
     * @param indicators
     * @return {@link String }
     */
    private void generateBriefReportInfo(TemplateRuleDetailVo rule, List<IndicatorsEntity> indicators, String briefId) {
        // 创建指标占位符映射
        Map<String, String> placeHolderMap = indicators.stream()
                .collect(Collectors.toMap(IndicatorsEntity::getPlaceHolder, IndicatorsEntity::getIndexValue, (oldValue, newValue) -> newValue));
        // 遍历替换内容占位符
        String content = rule.getContent();
        content = replacePlaceholders(content, placeHolderMap);

        BriefInfoEntity briefInfo = new BriefInfoEntity();
        briefInfo.setId(briefId);
        // 标题，时间+省市区
        briefInfo.setTitle(rule.getStatisticsTime() + rule.getProvinceName() + rule.getCityName()
                + rule.getDistrictName() + rule.getContentTitle());
        briefInfo.setAnalysisObject(rule.getAnalysisObject());
        briefInfo.setContent(rule.getStatisticsTime() + "(" + formatDateStr(rule.getStartDate()) + "0时至"
                + formatDateStr(rule.getEndDate()) + "24时)"
                + rule.getProvinceName() + rule.getCityName() + rule.getDistrictName() + content);
        briefInfo.setBusinessType(rule.getBusinessType());
        briefInfo.setProvinceCode(rule.getProvinceCode());
        briefInfo.setProvinceName(rule.getProvinceName());
        briefInfo.setCityCode(rule.getCityCode());
        briefInfo.setCityName(rule.getCityName());
        briefInfo.setDistrictCode(rule.getDistrictCode());
        briefInfo.setDistrictName(rule.getDistrictName());
        briefInfo.setStatisticsTime(rule.getStatisticsTime());
        briefInfo.setStatisticsCycle(rule.getStatisticsCycle());
        if (Constants.BriefReport.ATTACHMENT_FLAG_YES.equals(rule.getAttachmentFlag())) {
            briefInfo.setAttachmentTitle(rule.getStatisticsTime() + rule.getProvinceName() + rule.getCityName()
                    + rule.getDistrictName() + rule.getAttachmentTitle());
        }
        briefInfo.setTemplateId(rule.getTemplateId());
        briefInfo.setCreateTime(new Date());
        briefInfoMapper.insert(briefInfo);
    }

    /**
     * 使用正则表达式替换占位符
     *
     * @param content      原始字符串模板
     * @param placeholders 占位符和对应的值
     * @return 替换后的字符串
     */
    public static String replacePlaceholders(String content, Map<String, String> placeholders) {
        // 正则表达式匹配占位符，例如 $_1, $_10
        Pattern pattern = Pattern.compile("\\$_(\\d+)");
        Matcher matcher = pattern.matcher(content);

        // 使用 StringBuffer 存储替换后的结果
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            // 获取匹配的占位符，例如 $_1
            String placeholder = matcher.group();
            // 获取占位符对应的值
            String replacement = placeholders.getOrDefault(placeholder, "");
            // 替换占位符
            matcher.appendReplacement(result, replacement);
        }
        // 添加剩余部分
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 日期格式化中文
     *
     * @return {@link String }
     */
    private static String formatDateStr(LocalDate localDate) {
        // 创建中文日期格式器
        DateTimeFormatter chineseDateFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy年MM月dd日")
                .toFormatter(Locale.CHINA);
        // 格式化日期为中文格式
        return localDate.format(chineseDateFormatter);
    }

    /**
     * 处理指标数据响应
     *
     * @param indicators   指标
     * @param responseData 响应数据
     */
    private void handleIndicatorDataResponse(List<IndicatorsEntity> indicators, List<IndicatorVo> indicatorVoList,
            String briefId) {
        String globalNullValue = null;
        if (indicatorVoList.size() == 1) {
            globalNullValue = indicatorVoList.get(0).getNullValue();
        }
        // 遍历集合获取指标值
        int indicatorPos = 0;
        for (IndicatorsEntity indicator : indicators) {
            indicatorPos++;
            int voPos = 0;
            String nullValue = globalNullValue;
            for (IndicatorVo indicatorVo : indicatorVoList) {
                voPos++;
                if (indicatorPos == voPos) {
                    nullValue = indicatorVo.getNullValue();
                }
                List<Map<String, String>> responseItem = indicatorVo.getData();
                for (Map<String, String> data : responseItem) {
                    String parentDimensionId = indicator.getParentDimensionId();
                    String dimensionName = indicator.getDimensionName();
                    String mdmDimensionName = data.get("dim_" + parentDimensionId);
                    if (StrUtil.isBlank(mdmDimensionName) || !dimensionName.equals(mdmDimensionName)) {
                        continue;
                    }
                    String indexValue = data.get("ind_" + indicator.getIndexId());
                    if (StrUtil.isNotBlank(indexValue)) {
                        indicator.setIndexValue(indexValue);
                    }
                }
                if (StrUtil.isNotBlank(indicator.getIndexValue())) {
                    break;
                }
            }
            if (StrUtil.isBlank(indicator.getIndexValue())) {
                indicator.setIndexValue(StrUtil.nullToEmpty(nullValue));
            }
        }
        // 保存更新后的指标集合
        persistIndicatorValues(indicators, briefId);
    }

    private void persistIndicatorValues(List<IndicatorsEntity> indicators, String briefId) {
        if (CollUtil.isEmpty(indicators)) {
            return;
        }
        List<BriefIndicatorsEntity> briefIndicators = indicators.stream()
                .map(indicator -> {
                    BriefIndicatorsEntity briefIndicator = new BriefIndicatorsEntity();
                    BeanUtils.copyProperties(indicator, briefIndicator);
                    briefIndicator.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_BRIEF_INDICATORS)));
                    briefIndicator.setBriefId(briefId);
                    return briefIndicator;
                })
                .collect(Collectors.toList());
        Lists.partition(briefIndicators, BATCH_SIZE).forEach(briefIndicatorsMapper::insertBatch);
    }
}
