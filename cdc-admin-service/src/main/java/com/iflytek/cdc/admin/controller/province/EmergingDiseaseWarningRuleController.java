package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.EmergingWarningRuleConstants;
import com.iflytek.cdc.admin.constant.SyndromeWarningRuleConstants;
import com.iflytek.cdc.admin.dto.EmergingWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.vo.EmergingProcessScopeVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.EmergingDiseaseWarningRuleService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Api(tags = "预警规则维护-新发突发传染病")
@RequestMapping("/pt/{version}/emergingDiseaseWarning")
public class EmergingDiseaseWarningRuleController {

    @Resource
    private EmergingDiseaseWarningRuleService emergingDiseaseWarningRuleService;

    @GetMapping("/getRuleDetail")
    @ApiOperation("预警规则详情")
    public PageInfo<TbCdcmrEmergingDiseaseWarningRule> getRuleDetail(@RequestParam String diseaseInfoId,
                                                                     @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                     @RequestParam(defaultValue = "20") Integer pageSize,
                                                                     @RequestParam(required = false) String riskLevel,
                                                                     @RequestParam(required = false) String warningMethod,
                                                                     @RequestParam(required = false) String followStatus){

        return emergingDiseaseWarningRuleService.getRuleDetail(diseaseInfoId, pageIndex, pageSize, riskLevel, warningMethod, followStatus);
    }


    @PostMapping("/getEmergingTree")
    @ApiOperation("获取新发突发传染病结构树")
    public List<TreeNode> getEmergingTree(@RequestBody EmergingWaringRuleQueryDTO dto){

        return emergingDiseaseWarningRuleService.getEmergingTree(dto);
    }

    @PostMapping("/savaOrUpdateRule")
    @ApiOperation("编辑新发突发传染病规则")
    @OperationLogAnnotation(operationName = "编辑新发突发传染病规则")
    public void savaOrUpdateRule(@RequestBody TbCdcmrEmergingDiseaseWarningRule rule){

        emergingDiseaseWarningRuleService.savaOrUpdateRule(rule);
    }

    @GetMapping("/getConstants")
    @ApiOperation("常量获取")
    public Object getConstants(){

        return EmergingWarningRuleConstants.getInstance();
    }
    
}

