package com.iflytek.cdc.admin.outbound.model.dto.input;

import com.iflytek.cdc.admin.outbound.model.dto.input.param.Dweller;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 外呼开放接口（用方案）
 * <AUTHOR>
 */
@Data
public class PlanTask {

    @ApiModelProperty(value = "方案ID（讯飞随访平台提供）", required = true)
    private String planId;

    @ApiModelProperty(value = "要外呼的居民信息（居民数不超过500）", required = true)
    private List<Dweller> dwellers;

}
