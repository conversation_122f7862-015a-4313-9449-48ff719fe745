package com.iflytek.cdc.admin.dto.brief;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 简报概况搜索dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OverviewSearchDto extends BaseInfoDto {

    /**
     * 统计周期，仅支持日周月年，参考 DateUnitEnum
     */
    @ApiModelProperty("统计周期 日day周week月month年year")
    private String statisticsCycle;

    @ApiModelProperty("被推送人")
    private String pushUserName;

    @ApiModelProperty("报告类型")
    private String briefReportType;
}
