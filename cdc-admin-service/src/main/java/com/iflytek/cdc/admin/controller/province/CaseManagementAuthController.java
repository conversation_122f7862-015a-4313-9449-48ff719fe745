package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;
import com.iflytek.cdc.admin.service.province.CaseManagementAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "病例权限管理")
@RequestMapping("/pt/{version}/caseManagement/auth")
@RestController
public class CaseManagementAuthController {

    @Autowired
    private CaseManagementAuthService caseManagementAuthService;

    @ApiOperation("/校验查看权限")
    @PostMapping("/validViewAuth")
    public EDRAuthResultVO validViewAuth(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrCaseManagementPermissionRecord permissionRecord) {
        return caseManagementAuthService.validViewAuth(loginUserId, permissionRecord);
    }

    @ApiOperation("/新增权限申请记录")
    @PostMapping("/addAuthApplyRecord")
    public EDRAuthResultVO addAuthApplyRecord(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrCaseManagementPermissionRecord permissionRecord) {
        caseManagementAuthService.addAuthApplyRecord(loginUserId, permissionRecord);
        return EDRAuthResultVO.success();
    }

    @ApiOperation("/变更权限状态")
    @PostMapping("/changeAuthStatus")
    public EDRAuthResultVO changeAuthStatus(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrCaseManagementPermissionRecord permissionRecord) {
        caseManagementAuthService.changeAuthStatus(loginUserId, permissionRecord);
        return EDRAuthResultVO.success();
    }

    @ApiOperation("/查询权限申请记录列表")
    @PostMapping("/getAuthApplyRecordList")
    public PageInfo<TbCdcmrCaseManagementPermissionRecord> getAuthApplyRecordList(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrCaseManagementPermissionRecord permissionRecord) {
        return caseManagementAuthService.getAuthApplyRecordList(loginUserId, permissionRecord);
    }

    @ApiOperation("/根据id查询权限申请记录")
    @GetMapping("/getPermissionRecordInfoById/{id}")
    public TbCdcmrCaseManagementPermissionRecord getPermissionRecordInfoById(@PathVariable String id) {
        return caseManagementAuthService.getPermissionRecordInfoById(id);
    }

    @ApiOperation("/查询申请人列表")
    @GetMapping("/getCreateUserList")
    public List<TbCdcmrCaseManagementPermissionRecord> getCreateUserList(@RequestParam String loginUserId,@RequestParam String moduleType) {
        return caseManagementAuthService.getCreateUserList(loginUserId,moduleType);
    }

    @ApiOperation("/查询审批人列表")
    @GetMapping("/getUpdateUserList")
    public List<TbCdcmrCaseManagementPermissionRecord> getUpdateUserList(@RequestParam String loginUserId,@RequestParam String moduleType) {
        return caseManagementAuthService.getUpdateUserList(loginUserId,moduleType);
    }

}
