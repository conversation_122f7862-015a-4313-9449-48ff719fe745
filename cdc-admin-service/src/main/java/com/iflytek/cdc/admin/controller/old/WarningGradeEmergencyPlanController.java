package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.WarningGradeEmergencyPlanVO;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.service.WarningGradeEmergencyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "预警分级应急预案维护")
public class WarningGradeEmergencyPlanController {
    @Resource
    WarningGradeEmergencyPlanService warningGradeEmergencyPlanService;

    @ApiOperation("预警分级应急预案维护-查询应急预案列表")
    @GetMapping("/{version}/pt/warningGradeEmergencyPlan/getByConfigId")
    public List<WarningGradeEmergencyPlanVO> getByConfigId(@RequestParam String configId) {
        return warningGradeEmergencyPlanService.getByConfigId(configId);
    }

    @ApiOperation("预警分级应急预案维护-查询应急预案列表")
    @GetMapping("/{version}/pt/warningGradeEmergencyPlan/getByTypeAndDisease")
    public List<TbCdcAttachment> getByTypeAndGrade(@RequestParam String configType, @RequestParam String diseaseCode) {
        return warningGradeEmergencyPlanService.getByTypeAndDisease(configType, diseaseCode);
    }
}
