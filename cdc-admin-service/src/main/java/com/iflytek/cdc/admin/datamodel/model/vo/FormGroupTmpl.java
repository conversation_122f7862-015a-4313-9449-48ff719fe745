package com.iflytek.cdc.admin.datamodel.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class FormGroupTmpl {


    private String type;

    private String field;

    private String title;

    private Integer repeat;

    private GroupProps props;

    @Data
    public static class GroupProps {
        private Integer max;
        private List<FieldRule> rules;
    }

    @Data
    public static class FieldRule {
        private String type;
        private String field;
        private String title;
        private Integer maxLength;
        private String placeholder;
        private Object props;
        private Object databaseOption;
        private String optionField;
        //字段类型
        private String columnType;

        private List<String> databaseField;
    }
}


