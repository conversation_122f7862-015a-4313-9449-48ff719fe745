package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.enums.VersionStatusEnum;
import com.iflytek.cdc.admin.customizedapp.enums.VersionTypeEnum;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsMenuMapper;
import com.iflytek.cdc.admin.customizedapp.model.dto.MenuGroupDTO;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import com.iflytek.cdc.admin.customizedapp.service.MenuService;
import com.iflytek.cdc.admin.customizedapp.service.VersionService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MenuServiceImpl extends CdcServiceBaseImpl<TbCdccsMenuMapper, TbCdccsMenu> implements MenuService {

    @Resource
    private VersionService tbCdccsVersionService;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private AppService tbCdccsAppService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Override
    public void create(MenuGroupDTO menuGroup) {
        String groupId = getId();
        tbCdccsVersionService.create(groupId, menuGroup.getAppId(), menuGroup.getGroupName(), VersionTypeEnum.APP_MENU);
        menuGroup.getMenuList().forEach(m -> {
            m.setGroupId(groupId);
            m.setAppId(menuGroup.getAppId());
            if (StringUtils.isEmpty(m.getUrl())){
                throw new MedicalBusinessException(m.getMenuName() + "路径不能为空");
            }
        });
        saveBatch(menuGroup.getMenuList());
    }

    @Override
    public void update(MenuGroupDTO menuGroupDTO) {
        TbCdccsVersion tbCdccsVersion = tbCdccsVersionService.loadByBusinessId(menuGroupDTO.getGroupId(), VersionTypeEnum.APP_MENU);
        //已发布的菜单创建新的版本
        if (VersionStatusEnum.PUBLISHED.getCode().equals(tbCdccsVersion.getStatus())){
            Map<String, TbCdccsMenu> oldIdMap = new HashMap<>();
            menuGroupDTO.getMenuList().forEach(tbCdccsMenu -> {
                String oldId = tbCdccsMenu.getId();
                fulfillCreateProperty(tbCdccsMenu);
                tbCdccsMenu.setId(getId());
                tbCdccsMenu.setAppId(tbCdccsVersion.getBusinessKey());
                oldIdMap.put(oldId, tbCdccsMenu);
                if (StringUtil.isNotEmpty(tbCdccsMenu.getParentId())){
                    TbCdccsMenu parent = oldIdMap.get(tbCdccsMenu.getParentId());
                    tbCdccsMenu.setParentId(parent.getId());
                    tbCdccsMenu.setParentName(parent.getParentName());
                }
            });
            create(menuGroupDTO);
        }else {
            List<TbCdccsMenu> menuList = menuGroupDTO.getMenuList();
            List<String> ids = menuList.stream().map(TbCdccsMenu::getId).collect(Collectors.toList());
            List<TbCdccsMenu> tbCdccsMenus = listByGroupId(menuGroupDTO.getGroupId());
            Map<String, TbCdccsMenu> existedMenuMap = tbCdccsMenus.stream().collect(Collectors.toMap(TbCdccsMenu::getId, t -> t));
            List<TbCdccsMenu> inserts = new ArrayList<>();
            List<TbCdccsMenu> updates = new ArrayList<>();
            menuList.forEach(m -> {
                m.setGroupId(menuGroupDTO.getGroupId());
                if (StringUtils.isEmpty(m.getUrl())){
                    throw new MedicalBusinessException(m.getMenuName() + "路径不能为空");
                }
                if (existedMenuMap.containsKey(m.getId())){
                    updates.add(m);
                    existedMenuMap.remove(m.getId());
                }else {
                    inserts.add(m);
                }
            });
            if (inserts.size() > 0){
                saveBatch(inserts);
            }
            if (updates.size() > 0){
                updateBatchById(updates);
            }
            if (existedMenuMap.size() > 0){
                batchDeleteByIds(existedMenuMap.keySet());
            }

        }
    }

    @Override
    public String getId() {
        return String.valueOf(batchUidService.getUid(TbCdccsMenu.TABLE_NAME));
    }

    @Override
    public List<TbCdccsMenu> listByAppId(String appId) {
        String groupId = tbCdccsVersionService.loadPublishedBusinessId(appId, VersionTypeEnum.APP_MENU);
        return listByGroupId(groupId);
    }

    @Override
    public List<TbCdccsMenu> listByGroupId(String groupId) {
        LambdaQueryWrapper<TbCdccsMenu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdccsMenu::getGroupId, groupId);
        queryWrapper.orderByAsc(TbCdccsMenu::getSort);
        return list(queryWrapper);
    }

    @Override
    public List<TbCdccsVersion> listGroup(String appId) {
        return tbCdccsVersionService.listByBusinessKey(appId, VersionTypeEnum.APP_MENU);
    }

    @Override
    @Transactional
    public void deleteGroup(String groupId) {
        TbCdccsVersion tbCdccsVersion = tbCdccsVersionService.loadByBusinessId(groupId, VersionTypeEnum.APP_MENU);
        tbCdccsVersionService.deleteById(tbCdccsVersion.getId());
        deleteByGroupId(groupId);
    }

    @Override
    public List<String> listAllContainsDeletedCode(String appId) {
        return baseMapper.listAllContainsDeletedCode(appId);
    }

    /**
     * 根据groupId删除
     */
    private void deleteByGroupId(String groupId){
        remove(lambdaQueryWrapper().eq(TbCdccsMenu::getGroupId, groupId));
    }
}
