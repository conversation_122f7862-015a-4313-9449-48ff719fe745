package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.admin.model.processCheck.dto.ProcessCheckQueryDTO;
import com.iflytek.cdc.admin.service.province.ProcessCheckConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "病例审核配置")
@RequestMapping("/pt/{version}/processCheckConfig")
public class ProcessCheckConfigController {
    
    @Resource
    private ProcessCheckConfigService checkConfigService;
    
    @PostMapping("/getProcessCheckConfig")
    @ApiOperation("获取病例审核配置")
    public PageInfo<TbCdcmrCheckAuthorityConfig> getProcessCheckConfig(@RequestBody ProcessCheckQueryDTO dto){

        return checkConfigService.getProcessCheckConfig(dto);
    }

    @GetMapping("/getCheckConfigByLoginUser")
    @ApiOperation("获取登录人的病例审核配置")
    public List<TbCdcmrCheckAuthorityConfig> getCheckConfigByLoginUser(@RequestParam String loginUserId,
                                                                       @RequestParam String diseaseType){

        return checkConfigService.getCheckConfigByLoginUser(loginUserId, diseaseType);
    }

    @PostMapping("/editCheckConfig")
    @ApiOperation("编辑审核配置")
    public void editCheckConfig(@RequestBody TbCdcmrCheckAuthorityConfig config){

        checkConfigService.editCheckConfig(config);
    }

    @GetMapping("/getSyndromeSubgroup")
    @ApiOperation("获取配置的症候群亚组")
    public List<String> getSyndromeSubgroup(@RequestParam String id){

        return checkConfigService.getSyndromeSubgroup(id);
    }

}
