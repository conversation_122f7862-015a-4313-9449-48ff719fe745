package com.iflytek.cdc.admin.service.province.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsAppCategory;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsAppCategoryMapper;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrPathogenInfo;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrPathogenInfoMapper;
import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.cdc.admin.service.province.IMasterDataService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 病原主数据
 * */
@Service("pathogenMasterDataCategory")
public class PathogenMasterDataServiceImpl extends CdcServiceBaseImpl<TbCdcmrPathogenInfoMapper, TbCdcmrPathogenInfo> implements IMasterDataService {

    @Override
    public List<TreeNode> getMasterData(MasterDataQueryDTO dto) {

        LambdaQueryWrapper<TbCdcmrPathogenInfo> queryWrapper = lambdaQueryWrapper();
        List<TbCdcmrPathogenInfo> pathogenInfoList = list(queryWrapper);
        //构建树形结构
        List<TreeNode> root = TreeNode.buildTreeByNodeList(pathogenInfoList,
                                                           TbCdcmrPathogenInfo::getId,
                                                           TbCdcmrPathogenInfo::getParentId,
                                                           TbCdcmrPathogenInfo::getPathogenName,
                                                           TbCdcmrPathogenInfo::getId);
        root = root.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());
        //将所有叶子节点的children设为null
        TreeNode.setChildrenToNull(root);
        return root;
    }

    @Override
    public TreeNode getMasterDataByRootId(String rootId) {
        return null;
    }

    @Override
    public <T extends CommonMasterData> T getMasterDataConfigInfo(MasterDataQueryDTO dto) {
        return null;
    }

    @Override
    public String addMasterData(MasterDataRecordDTO dto) {
        return null;
    }

    @Override
    public void editMasterDataBaseInfo(MasterDataEditRecordDTO dto) {

    }

    @Override
    public void deleteMasterDataBaseInfo(MasterDataDeleteDTO dto) {

    }

    @Override
    public void syncMasterData() {

    }

    @Override
    public void uploadFile(MultipartFile file) {

    }

    @Override
    public CommonMasterData getDiseaseInfoByCode(String masterDataCode) {

        return null;
    }

}
