package com.iflytek.cdc.admin.capacity.model.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * 普通态势推演
 */
@Data
public class AwarenessResultVO {

    /**
     * 原始推演结果
     */
    private List<Result> originalResult;

    /**
     * 原始推演结果
     */
    private List<Result> calibrateResult;

    /**
     * 等高线
     */
    private Contour contour;


    @Data
    public static final class Result{
        private String date;

        private Double infectiousRate;//发病率，例如：0.001，值域在0~1之间
        private Double deathRate;//死亡率，例如：0.001，值域在0~1之间

        private Integer newDiagnoses;//新增确诊数
        private Integer cumDiagnoses;//累积确诊数

        private Integer newInfectious;//新增病例数
        private Integer cumInfectious;//累积病例数

        private Integer newDeaths;//新增死亡数，例如：[10，20] ，自然数
        private Integer cumDeaths;//累积死亡数，例如：[10，20] ，自然数


        private Integer newRecoveries;//新增治愈数，例如：[10，20] ，自然数
        private Integer cumRecoveries;//累积治愈数，例如：[10，20] ，自然数

        private Integer curInfectious;//现存发病数，例如：[10，20] ，自然数
        private Integer curDiagnoses;//现存确诊数，例如：[10，20] ，自然数


        private Double mxNewInfectious;//新增病例数峰值
        private String mxdNewInfectious;//新增病例数峰值日期
        private Double mxCumInfectious;//累积病例数峰值
        private String mxdCumInfectious;//累积病例数峰值日期

        private Double mxNewDeaths;//新增死亡数峰值
        private String mxdNewDeaths;//新增死亡数峰值日期
        private Double mxCumDeaths;//累积死亡数峰值
        private String mxdCumDeaths;//累积死亡数峰值日期

        private Double mxNewRecoveries;//新增治愈数峰值
        private String mxdNewRecoveries;//新增治愈数峰值日期
        private Double mxCumRecoveries;//累积治愈数峰值
        private String mxdCumRecoveries;//累积治愈数峰值日期

        private Double mxNewDiagnoses;//新增确诊数峰值
        private String mxdNewDiagnoses;//新增确诊数峰值日期
        private Double mxCumDiagnoses;//累积确诊数峰值
        private String mxdCumDiagnoses;//累积确诊数峰值日期

        private Double mxCurInfectious;//现存发病数峰值
        private String mxdCurInfectious;//现存发病数值日期
        private Double mxCurDiagnoses;//现存确诊数峰值
        private String mxdCurDiagnoses;//现存确诊数峰值日期

        private Mmc newDiagnosesMMC;//新增确诊数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】
        private Mmc cumDiagnosesMMC;//累积确诊数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】

        private Mmc newInfectiousMMC;//新增病例数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】
        private Mmc cumInfectiousMMC;//累积病例数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】

        private Mmc newDeathsMMC;//新增死亡数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】
        private Mmc cumDeathsMMC;//累积死亡数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】

        private Mmc newRecoveriesMMC;//新增治愈数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】
        private Mmc cumRecoveriesMMC;//累积治愈数的【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】

        private Mmc curInfectiousMMC;//现存发病数【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】
        private Mmc curDiagnosesMMC;//现存确诊数【均值、中位数、95%置信区间(下限, 上限)、5%置信区间(下限, 上限)】

        private CalibrateIndex indexes;//疫情参数校准后的参数

        private Double rtValue;//实时再生数-平均值
        private Double rtCiLowerBound;//实时再生数-置信区间最小值
        private Double rtCiUpperBound;//实时再生数-置信区间最大值
    }

    @Data
    public static class Contour{
        private List<Integer> xIndex;//政策辅助推荐的等高线的x轴：降低传播率政策的开始日，例如：[5, 10, 15, 20, 25, 30]，自然数
        private List<Double> yIndex;//政策辅助推荐的等高线的y轴：降低传播率政策的力度，例如：[0, 0.2, 0.4, 0.6, 0.8, 1]，浮点数
        private List<List<Double>> data;//政策辅助推荐的等高线的结果数据，例如：[0.2, 0.4, 0.6, 0.8, 1， 1.5]，浮点数
    }

    @Data
    public static class Mmc {
        @ApiModelProperty(value = "均值")
        private Double mean;//均值
        @ApiModelProperty(value = "中位数")
        private Double median;//中位数
        @ApiModelProperty(value = "95%置信区间(下限, 上限)")
        private List<Double> ci_95;//95%置信区间(下限, 上限)
        @ApiModelProperty(value = "5%置信区间(下限, 上限)")
        private List<Double> ci_5;//5%置信区间(下限, 上限)
    }

    @Data
    public static class CalibrateIndex{
        private Double r0;//疫情参数校准后的基本再生数
        private List<List<Double>> rt;//疫情参数校准后的有效再生数
        private Double mx;//疫情参数校准后的疫情峰值
        private String mxd;;//疫情参数校准后的疫情峰值日期
        private Double db;//疫情参数校准后的倍增时间
        private Double gen;//疫情参数校准后的代际时间
        private Double ic;//疫情参数校准后的潜伏期
    }
}
