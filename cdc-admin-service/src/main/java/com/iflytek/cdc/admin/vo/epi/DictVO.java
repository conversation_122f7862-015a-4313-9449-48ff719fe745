package com.iflytek.cdc.admin.vo.epi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("字典值对象")
@Data
public class DictVO {
    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("编码")
    private String code;
    @ApiModelProperty("父节点id")
    private String parentId;
    @ApiModelProperty("值")
    private String value;
    @ApiModelProperty("子节点")
    private List<DictVO> children;
}
