package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 事件分级请求
 */
@Data
public class EventClassifyRequest {
    private Integer jiekouType;//接口类型， 0 单个， 1 多个
    private String keyword;//关键词
    private String addressCode;//事件的地址等级
    private Integer diseaseNumber;//事件的患病/发病人数，缺失填-1
    private Integer deathNumber;//事件的死亡人数，缺失填-1


//    public static enum AddressCode{
//
//
//        private String value;
//
//
//    }
}
