package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import com.iflytek.cdc.admin.service.WarningGradeConfigRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "预警分级配置规则维护")
public class WarningGradeConfigRuleController {
    @Resource
    WarningGradeConfigRuleService warningGradeConfigRuleService;

    @ApiOperation("预警分级配置规则维护-预警分级配置规则查询")
    @GetMapping("/{version}/pt/warningGradeConfigRule/getByConfigId")
    public List<TbCdcmrWarningGradeConfigRule> getByConfigId(@RequestParam String configId) {
        return warningGradeConfigRuleService.getByConfigId(configId);
    }


}
