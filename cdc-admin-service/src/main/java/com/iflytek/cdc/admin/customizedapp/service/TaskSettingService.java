package com.iflytek.cdc.admin.customizedapp.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.customizedapp.model.dto.TaskSettingQueryDTO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

/**
 * 任务配置服务
 */
public interface TaskSettingService extends ICdcService<TbCdccsTaskSetting> {

    /**
     * 查询所有的任务配置
     */
    List<TbCdccsTaskSetting> listCascade(TaskSettingQueryDTO queryDTO);

    /**
     * 任务配置分页查询
     */
    PageInfo<TbCdccsTaskSetting> pageList(TaskSettingQueryDTO queryDTO);
}
