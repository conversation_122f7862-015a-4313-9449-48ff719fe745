package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.CustomizedWarnVO;
import com.iflytek.cdc.admin.dto.CustomizedWarningTaskQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO;
import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningTaskAreaMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningTaskMapper;
import com.iflytek.cdc.admin.service.CustomizedWarnService;
import com.iflytek.cdc.admin.service.CustomizedWarningTaskService;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CustomizedWarningTaskServiceImpl implements CustomizedWarningTaskService {
    @Resource
    TbCdcmrCustomizedWarningTaskMapper tbCdcmrCustomizedWarningTaskMapper;
    @Resource
    TbCdcmrCustomizedWarningTaskAreaMapper tbCdcmrCustomizedWarningTaskAreaMapper;
    @Resource
    CustomizedWarnService customizedWarnService;
    @Resource
    BatchUidService batchUidService;
    @Resource
    DataAuthService dataAuthService;
    @Resource
    UapUserService uapUserService;

    @Override
    public void addCustomizedWarningTask(CustomizedWarningTaskVO taskVO, String loginUserId, String loginUserName) {
        if (tbCdcmrCustomizedWarningTaskMapper.getByName(taskVO.getTaskName()) != null) {
            throw new MedicalBusinessException("已存在相同自定义名称！!");
        }
        taskVO.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warning_task")));
        taskVO.setUpdateTime(new Date());
        taskVO.setCreateTime(new Date());
        taskVO.setUpdater(loginUserId);
        taskVO.setUpdaterName(uapUserService.getUserInfo(loginUserId).getName());
        taskVO.setCreator(loginUserId);
        taskVO.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
        taskVO.setStatus(Constants.STATUS_OFF);
        tbCdcmrCustomizedWarningTaskMapper.insert(taskVO);
        taskVO.getAreaList().forEach(e -> {
            e.setTaskId(taskVO.getId());
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warning_task_area")));
        });
        tbCdcmrCustomizedWarningTaskAreaMapper.batchInsert(taskVO.getAreaList());
    }

    @Override
    public void updateCustomizedWarningTask(CustomizedWarningTaskVO taskVO, String loginUserId, String loginUserName) {
        taskVO.setUpdateTime(new Date());
        taskVO.setUpdater(loginUserId);
        taskVO.setUpdaterName(uapUserService.getUserInfo(loginUserId).getName());
        taskVO.getAreaList().forEach(e -> {
            e.setTaskId(taskVO.getId());
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warning_task_area")));
        });
        tbCdcmrCustomizedWarningTaskMapper.updateByPrimaryKeySelective(taskVO);
        tbCdcmrCustomizedWarningTaskAreaMapper.deleteByTaskId(taskVO.getId());
        tbCdcmrCustomizedWarningTaskAreaMapper.batchInsert(taskVO.getAreaList());
    }

    @Override
    public void deleteCustomizedWarningTask(String id, String loginUserId, String loginUserName) {
        tbCdcmrCustomizedWarningTaskMapper.deleteByPrimaryKey(id);
    }

    @Override
    public PageInfo<CustomizedWarningTaskVO> getCustomizedWarningTaskList(CustomizedWarningTaskQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrCustomizedWarningTaskMapper.getList(queryDTO));
    }

    @Override
    public CustomizedWarningTaskVO getCustomizedWarningTaskById(String id) {
        CustomizedWarningTaskVO result = tbCdcmrCustomizedWarningTaskMapper.selectByPrimaryKey(id);
        result.setAreaList(tbCdcmrCustomizedWarningTaskAreaMapper.selectByTaskId(id));
        return result;
    }

    @Override
    public List<CustomizedWarningTaskVO> getAllEnabledWarningTask() {
        List<CustomizedWarningTaskVO> customizedWarningTaskVOList = tbCdcmrCustomizedWarningTaskMapper.getEnabledWarn();
        customizedWarningTaskVOList.forEach(e -> {
            e.setAreaList(tbCdcmrCustomizedWarningTaskAreaMapper.selectByTaskId(e.getId()));
        });
        return customizedWarningTaskVOList;
    }

    @Override
    public void updateCustomizedWarningTaskStatus(Integer status, String id, String loginUserId, String loginUserName) {
        CustomizedWarningTaskVO customizedWarningTaskVO = new CustomizedWarningTaskVO();
        customizedWarningTaskVO.setId(id);
        customizedWarningTaskVO.setStatus(status);
        customizedWarningTaskVO.setUpdater(loginUserId);
        customizedWarningTaskVO.setUpdaterName(uapUserService.getUserInfo(loginUserId).getName());
        customizedWarningTaskVO.setUpdateTime(new Date());
        tbCdcmrCustomizedWarningTaskMapper.updateByPrimaryKeySelective(customizedWarningTaskVO);
    }

    @Override
    public Boolean checkCustomizedWarnByName(String name) {
        return tbCdcmrCustomizedWarningTaskMapper.getByName(name) != null;
    }

    @Override
    public List<CascadeVO> getCustomizedNameList() {
        List<CustomizedWarningTaskVO> customizedWarningTaskVOList = tbCdcmrCustomizedWarningTaskMapper.getEnabledWarn();
        return customizedWarningTaskVOList.stream()
                .collect(Collectors.toMap(CustomizedWarningTaskVO::getId, e -> e, (k1, k2) -> k1))
                .values().stream().map(e -> {
                    CascadeVO vo = new CascadeVO();
                    vo.setLabel(e.getTaskName());
                    vo.setValue(e.getId());
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<CascadeVO> getCustomizedNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_CUSTOMIZED);

//        List<CustomizedWarningTaskVO> customizedWarningTaskVOList = tbCdcmrCustomizedWarningTaskMapper.getEnabledWarn();
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getCustomizedDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        return customizedWarningTaskVOList.stream()
//                .collect(Collectors.toMap(CustomizedWarningTaskVO::getId, e -> e, (k1, k2) -> k1))
//                .values().stream().map(e -> {
//                    CascadeVO vo = new CascadeVO();
//                    vo.setLabel(e.getTaskName());
//                    vo.setValue(e.getId());
//                    return vo;
//                }).filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
    }

    @Override
    public CustomizedWarnVO getWarningRuleByTaskId(String taskId) {
        String id = tbCdcmrCustomizedWarningTaskMapper.getWarningIdByTaskId(taskId);
        if(StringUtils.isEmpty(id)){
            return null;
        }
        return customizedWarnService.getCustomizedWarnById(id);
    }

    @Override
    public String getWarningRuleIdByTaskId(String taskId) {
        String id = tbCdcmrCustomizedWarningTaskMapper.getWarningIdByTaskId(taskId);
        if(StringUtils.isEmpty(id)){
            return null;
        }
        return id;
    }
}
