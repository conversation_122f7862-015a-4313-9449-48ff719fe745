package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPageButtonSetting;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsPageButtonSettingMapper;
import com.iflytek.cdc.admin.customizedapp.service.PageButtonSettingService;
import com.iflytek.cdc.admin.entity.BaseEntity;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PageButtonSettingServiceImpl extends CdcServiceBaseImpl<TbCdccsPageButtonSettingMapper, TbCdccsPageButtonSetting> implements PageButtonSettingService {
    @Override
    public void saveByPage(TbCdccsPage page, List<TbCdccsPageButtonSetting> pageViewColList) {
        batchSaveByForeignKey(page, TbCdccsPage::getId,
                TbCdccsPageButtonSetting::getPageId,
                (e, t) ->{
                    t.setPageName(e.getPageName());
                    t.setPageId(e.getId());
                },
                pageViewColList);
    }

    @Override
    public List<TbCdccsPageButtonSetting> listByPageId(String pageId) {
        LambdaQueryWrapper<TbCdccsPageButtonSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdccsPageButtonSetting::getPageId, pageId);
        return list(queryWrapper);
    }

    @Override
    public void deleteByPageId(String pageId) {
        LambdaUpdateChainWrapper<TbCdccsPageButtonSetting> updateChainWrapper = lambdaUpdate();
        updateChainWrapper.set(TbCdccsPageButtonSetting::getDeleteFlag, DeleteFlagEnum.YES.getCode())
                .eq(TbCdccsPageButtonSetting::getPageId, pageId)
                .update();
    }

}
