package com.iflytek.cdc.admin.datamodel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.customizedapp.model.dto.EntityErQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcdmEntityErModelMapper extends BaseMapper<TbCdcdmEntityErModel> {

    /**
     * 分页查询
     */
    List<TbCdcdmEntityErModel> pageList(EntityErQueryDTO queryDTO);
}
