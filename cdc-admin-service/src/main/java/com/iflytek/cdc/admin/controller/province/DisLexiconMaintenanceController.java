package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.mapper.DisLexiconMapper;
import com.iflytek.cdc.admin.mapper.LexiconAssociationDirectoryMapper;
import com.iflytek.cdc.admin.service.DisLexiconMaintenanceService;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.QueryAssociationPageVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "疾病词库维护服务")
@AllArgsConstructor
public class DisLexiconMaintenanceController {
    @Resource
    private DisLexiconMaintenanceService disLexiconMaintenanceService;
    @Resource
    private LexiconAssociationDirectoryMapper lexiconAssociationDirectoryMapper;
    /**
     * 词库维护-疾病病种词库-新增。
     *
     * @param dto         需要新增的疾病兵种词库信息。
     */
    @PostMapping("/{version}/pt/lexicon/diseaseLesicon/add")
    @ApiOperation("词库维护-疾病病种词库-新增")
    public ApiResult addDiseaseLexicon(@RequestBody DiseaseLexiconDTO dto) {
        try {
            return disLexiconMaintenanceService.addDiseaseLexicon(dto);
        } catch (Exception e) {
            log.error("病种词库添加失败：{}", e.getMessage());
            return ApiResult.failed("添加失败");
        }
    }

    /**
     * 词库维护-疾病病种词库-编辑
     *
     * @param dto
     */
    @PostMapping("/{version}/pt/lexicon/diseaseLesicon/edit")
    @ApiOperation("词库维护-疾病病种词库-编辑")
    public ApiResult editDiseaseLexicon(@RequestBody DiseaseLexiconDTO dto) {
        try {
            disLexiconMaintenanceService.updateDiseaseLexicon(dto);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("病种词库编辑失败：{}", e.getMessage());
            return ApiResult.failed("保存失败");
        }
    }

    /**
     * 词库维护-疾病病种词库-删除。
     *
     * @param dto 需要删除的疾病词库数据传输对象。
     */
    @PostMapping("/{version}/pt/lexicon/diseaseLesicon/delete")
    @ApiOperation("词库维护-疾病病种词库-删除")
    public ApiResult deleteDiseaseLexicon(@RequestBody DiseaseLexiconDTO dto) {
        try {
            disLexiconMaintenanceService.deleteDiseaseLexicon(dto);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("病种词库删除失败：{}", e.getMessage());
            return ApiResult.failed("删除失败");
        }
    }

    /**
     * 词库维护-疾病病种词库-分页查询。
     *
     * @param dto 分页参数及查询条件。
     * @return 返回疾病兵种词库的分页列表。
     */
    @PostMapping("/{version}/pt/lexicon/diseaseLesicon/page")
    @ApiOperation("词库维护-疾病病种词库-分页查询")
    public PageData<DisLexicon> getDiseaseLexiconPage(@RequestBody SearchDiseaseLexiconDTO dto) {
      return disLexiconMaintenanceService.getDiseaseLexiconPage(dto);
    }
    /**
     * 词库维护-疾病诊断词库-分页查询
     */
    @PostMapping("/{version}/pt/lexicon/diagnosisLexicon/page")
    @ApiOperation("词库维护-疾病诊断词库-分页查询")
    public PageData<DiseaseDiagnosisLexicon> getDiagnosisLexiconPage(@RequestBody SearchDiagnosisLexiconDTO dto) {
       return disLexiconMaintenanceService.getDiagnosisLexiconPage(dto);
    }

    /**
     * 词库维护-疾病诊断词库-编辑
     */
    @PostMapping("/{version}/pt/lexicon/diagnosisLexicon/edit")
    @ApiOperation("词库维护-疾病诊断词库-编辑")
    public ApiResult editDiagnosisLexicon(@RequestBody DiagnosisLexiconDTO dto) {
        try {
            disLexiconMaintenanceService.updateDiagnosisLexicon(dto);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("疾病诊断词库编辑失败：{}", e.getMessage());
            return ApiResult.failed("保存失败");
        }
    }

    /**
     * 词库维护-词库关联维护-词库关联/取消关联
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/associate")
    @ApiOperation("词库维护-词库关联维护-词库关联/取消关联")
    public ApiResult associateLexicon(@RequestBody LexiconAssociationDTO dto) {
        try {
            return disLexiconMaintenanceService.associateLexicon(dto);
        } catch (Exception e) {
            log.error("操作失败：{}", e.getMessage());
            return ApiResult.failed("操作失败");
        }
    }
    /**
     * 词库维护-词库关联维护-新增关联目录
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/addAssociationDirectory")
    @ApiOperation("词库维护-词库关联维护-新增关联目录")
    public ApiResult addAssociationDirectory(@RequestBody LexiconAssociationDirectory dto) {
        try {
            return disLexiconMaintenanceService.addAssociationDirectory(dto);
        } catch (Exception e) {
            log.error("新增关联目录失败：{}", e.getMessage());
            return ApiResult.failed("添加失败");
        }
    }
    /**
     * 词库维护-词库关联维护-关联目录列表
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/queryAssociationDirectory")
    @ApiOperation("词库维护-词库关联维护-关联目录列表")
    public ApiResult<List<LexiconAssociationDirectory>> queryAssociationDirectory() {
        try {
//            返回所有目录记录列表
            List<LexiconAssociationDirectory> list = lexiconAssociationDirectoryMapper.selectAll();
            return ApiResult.ok(list);
        } catch (Exception e) {
            log.error("查询关联目录失败：{}", e.getMessage());
            return ApiResult.failed("查询失败");
        }
    }
    /**
     * 词库维护-词库关联维护-导出关联列表
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/export")
    @ApiOperation("词库维护-词库关联维护-导出关联列表")
    public ApiResult exportAssociationDirectory(@RequestBody AssociationExportDTO dto) {
        try {
            return disLexiconMaintenanceService.exportAssociationDirectory(dto);
        } catch (Exception e) {
            log.error("导出关联列表失败：{}", e.getMessage());
            return ApiResult.failed("导出失败");
        }
    }
    /**
     * 词库维护-词库关联维护-病种词库分页查询。
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/diseaseLexiconPage")
    @ApiOperation("词库维护-词库关联维护-病种词库分页查询")
    public PageData<QueryAssociationPageVO> diseaseLexiconPage(@RequestBody QueryAssociationPageDTO dto) {
        return disLexiconMaintenanceService.getDiseaseLexiconAssociationPage(dto);
    }
    /**
     * 词库维护-词库关联维护-诊断词库分页查询。
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/diseaseDiagnosisPage")
    @ApiOperation("词库维护-词库关联维护-诊断词库分页查询")
    public PageData<QueryAssociationPageVO> diseaseDiagnosisPage(@RequestBody QueryAssociationPageDTO dto) {
        return disLexiconMaintenanceService.getDiseaseDiagnosisAssociationPage(dto);
    }
    /**
     * 词库维护-词库关联维护-传染病主数据分页查询。
     */
    @PostMapping("/{version}/pt/lexicon/lexiconAssociation/infectiousMainPage")
    @ApiOperation("词库维护-词库关联维护-传染病主数据分页查询")
    public PageData<QueryAssociationPageVO> infectiousMainPage(@RequestBody QueryAssociationPageDTO dto) {
        return disLexiconMaintenanceService.getInfectiousAssociationPage(dto);
    }
}