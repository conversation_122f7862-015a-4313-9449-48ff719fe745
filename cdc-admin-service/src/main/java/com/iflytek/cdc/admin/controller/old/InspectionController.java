package com.iflytek.cdc.admin.controller.old;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.InspectionMappingPageVO;
import com.iflytek.cdc.admin.dto.InspectionMappingVO;
import com.iflytek.cdc.admin.dto.InspectionStandardQueryParam;
import com.iflytek.cdc.admin.dto.InspectionStandardVO;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard;
import com.iflytek.cdc.admin.enums.ClinicalTypeEnum;
import com.iflytek.cdc.admin.service.InspectionMappingService;
import com.iflytek.cdc.admin.service.InspectionStandardService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@RestController
public class InspectionController {

    @Resource
    MdmDataSyncService mdmDataSyncService;
    @Resource
    InspectionStandardService inspectionStandardService;
    @Resource
    InspectionMappingService inspectionMappingService;

    @GetMapping("/pt/v1/mdm/dictInfo")
    @ApiOperation("根据字典编码查询MDM字典内容:标本代码 404120,检验结果 404395")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<CascadeVO> dictInfo(@RequestParam String dictCode, @RequestParam(defaultValue = "") String keyword) {
        return mdmDataSyncService.getMdmDictResult(dictCode, keyword);
    }

    @PostMapping("/pt/v1/inspectionStandard/getPageList")
    @ApiOperation("查询检验标准分页列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<InspectionStandardVO> getPageList(@RequestBody InspectionStandardQueryParam param) {
        return inspectionStandardService.getInspectionStandardList(param);
    }

    @GetMapping("/pt/v1/inspectionStandard/getList")
    @ApiOperation("查询检验标准列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<TbCdcmrInspectionStandard> getList(@RequestParam(defaultValue = "") String keyword) {
        return inspectionStandardService.getInspectionStandardList(keyword);
    }

    @PostMapping("/pt/v1/inspectionStandard/updateStandard")
    @ApiOperation("更新检验标准内容")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateStandard(@RequestBody TbCdcmrInspectionStandard tbCdcmrInspectionStandard, @RequestParam String loginUserId) {
        tbCdcmrInspectionStandard.setUpdateUser(loginUserId);
        inspectionStandardService.updateInspectionStandard(tbCdcmrInspectionStandard);
    }

    @GetMapping("/pt/v1/inspectionMapping/getPageList")
    @ApiOperation("查询检验映射列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<InspectionMappingPageVO> getPageList(Integer pageIndex, Integer pageSize, String diseaseCode, String diseaseType) {
        return inspectionMappingService.getPageList(pageIndex, pageSize, diseaseCode, diseaseType);
    }


    @GetMapping("/pt/v1/inspectionMapping/getListByInfectedCode")
    @ApiOperation("查询检验映射列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<InspectionMappingVO> getListByInfectedCode(String infectedCode) {
        return inspectionMappingService.getList(infectedCode);

    }

    @PostMapping("/pt/v1/inspectionMapping/insert")
    @ApiOperation("插入检验标准列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void batchInsert(@RequestBody List<InspectionMappingVO> recordList, @RequestParam String loginUserId, @RequestParam String infectedCode) {
        inspectionMappingService.batchInsert(recordList, loginUserId, infectedCode);
    }

    @PostMapping("/pt/v1/inspectionMapping/clinicalTypeList")
    @ApiOperation("获取临床状态列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<Map<String, Object>> clinicalTypeList() {
        return ClinicalTypeEnum.getAllToList();
    }
}
