package com.iflytek.cdc.admin.datamodel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DataModelListVO {

    @ApiModelProperty(value = "数据模型id")
    private String modelId;

    @ApiModelProperty(value = "数据模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "er 模型id")
    private String erModelId;

    @ApiModelProperty(value = "数据模型名称")
    private String dataModelName;

    @ApiModelProperty(value = "数据模型分类")
    private String dataModelType;

    @ApiModelProperty(value = "数据模型标签")
    private String dataModelLabel;

    @ApiModelProperty(value = "发布状态")
    private String status;

    @ApiModelProperty(value = "配置")
    private String configInfo;

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String publishDate;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;
}
