package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.AdministrativeRegionQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo;

import java.util.Date;

/**
 * <AUTHOR>
public interface AdministrativeRegionService {

    PageData<TbCdcmrStreetInfo> getAdministrativeRegionDetail(AdministrativeRegionQueryDTO administrativeRegionQueryDTO);

    byte[] exportRegionList(AdministrativeRegionQueryDTO administrativeRegionQueryDTO);

    void syncResult(Date startTime, Date endTime);

    void streetAddressStd(Date startTime, Date endTime);

}
