package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.datamodel.entity.*;
import com.iflytek.cdc.admin.datamodel.es.EsModelUtils;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataModelFormMapper;
import com.iflytek.cdc.admin.datamodel.model.dto.*;
import com.iflytek.cdc.admin.datamodel.model.vo.*;
import com.iflytek.cdc.admin.datamodel.mapper.DataModelConfigMapper;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.admin.datamodel.service.DataFormTemplateService;
import com.iflytek.cdc.admin.datamodel.service.DataModelExtendService;
import com.iflytek.cdc.admin.datamodel.service.DataModelService;
import com.iflytek.cdc.admin.datamodel.service.MetaDataService;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcdmDataDictMapper;
import com.iflytek.cdc.admin.util.ExcelUtils;
import com.iflytek.cdc.admin.util.UUIDUtil;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.datamodel.model.dto.UploadDataModelInfo.JUDGE_BOOLEAN;
import static com.iflytek.cdc.admin.datamodel.model.dto.UploadDataModelInfo.SHOW_TYPE_MAP;

@Service
@Slf4j
public class DataModelServiceImpl implements DataModelService {

    private static final String TB_CDCDM_DATA_MODEL = "tb_cdcdm_data_model";

    private static final String TB_CDCDM_DATA_MODEL_VERSION = "tb_cdcdm_data_model_version";

    private static final String TB_CDCDM_DATA_FORM_TEMPLATE = "tb_cdcdm_data_form_template";

    private static final String TB_CDCDM_DATA_MODEL_FORM = "tb_cdcdm_data_model_form";

    private static final String TB_CDCDM_DATA_FORM_VALUE = "tb_cdcdm_data_form_value";

    @Resource
    private DataModelConfigMapper dataModelConfigMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcdmDataFormTemplateMapper formTemplateMapper;

    @Resource
    private EsModelUtils esModelUtils;

    @Resource
    private MetaDataService metaDataService;

    @Resource
    private DataModelExtendService dataModelExtendService;

    @Resource
    private DataFormTemplateService templateService;

    @Resource
    private TbCdcdmDataDictMapper dataDictMapper;

    @Resource
    private TbCdcdmDataModelFormMapper tbCdcdmDataModelFormMapper;

    @Override
    @Transactional
    public Map<String, String> editDataModel(DataModelValueDTO dto, String loginUserName) {

        Map<String, String> detailIdMap = new HashMap<>();
        //模型名称唯一
        String modelId = dataModelConfigMapper.getModelIdByModelName(dto.getModelName());
        //(没传modelId && 没有重名的模型) - 新增
        if (StringUtils.isBlank(dto.getModelId()) && StringUtils.isBlank(modelId)) {
            String id = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL));
            dto.setModelId(id);
            //新增数据模型时，直接初始化该模型的配置
            String formTemplateDetailId = this.initModelTemplate(id, dto.getModelName(), loginUserName);
            detailIdMap.put(Constants.FORM_TEMPLATE_DETAIL_ID, formTemplateDetailId);
        } else {
            //编辑或新增版本 设置当前模型id（当modelId为空，但传参有modelId时，例：修改模型名称）
            dto.setModelId(StringUtils.isNotBlank(modelId) ? modelId : dto.getModelId());
        }
        if (dto.getModelVersionId() == null) {
            dto.setModelVersionId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_VERSION)));
        }
        //查询当前版本状态
        String status = dataModelConfigMapper.getVersionStatusByVersionId(dto.getModelVersionId());
        //设置最新的版本
        String version = this.getDataModelVersion(dto.getModelId(), dto.getModelVersionId());

        //如果当前数据模型版本处于已发布或者已失效状态
        if (Constants.VERSION_STATUS_PUBLISHED.equals(status)
                || Constants.VERSION_STATUS_EXPIRED.equals(status)) {
            String oldModelVersionId = dto.getModelVersionId();
            dto.setModelVersionId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_VERSION)));
            //已发布和已失效的数据模型编辑时 需要复制一份之前的表单
            List<TbCdcdmDataModelForm> versionFormList = dataModelConfigMapper.getFormListByVersionId(Collections.singletonList(oldModelVersionId));

            versionFormList.forEach(e -> {
                e.setModelFormId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_FORM)));
                e.setModelVersionId(dto.getModelVersionId());
                e.setCreator(loginUserName);
                e.setCreateTime(new Date());
                e.setUpdater(loginUserName);
                e.setUpdateTime(new Date());
            });
            if (!versionFormList.isEmpty()) {
                dataModelConfigMapper.batchInsertFormList(versionFormList);
            }
        }
        //设置当前版本
        dto.setVersion(version);
        detailIdMap.put(Constants.MODEL_ID, dto.getModelId());
        detailIdMap.put(Constants.MODEL_VERSION_ID, dto.getModelVersionId());
        //数据模型实体类
        TbCdcdmDataModel tbCdcdmDataModel = createDataModelEntity(dto);
        //数据模型版本实体类
        TbCdcdmDataModelVersion tbCdcdmDataModelVersion = createDataModelVersionEntity(dto, loginUserName);

        //TODO： 新建版本时，数据模型的分类标签怎么变（随版本改动，每个版本不同？所有版本必须保持一致？）
        //保存对应的数据模型
        dataModelConfigMapper.insertIntoDataModel(tbCdcdmDataModel);
        //保存扩展
        dataModelExtendService.saveDataModelExtend(dto.getModelId(), tbCdcdmDataModelVersion.getModelVersionId(), dto.getErModelId());
        //保存对应数据模型的草稿
        dataModelConfigMapper.insertIntoDataModelVersion(tbCdcdmDataModelVersion);

        //如果是新建则返回初始化配置的id，否则返回空
        return detailIdMap;
    }

    /**
     * 模型版本更新规则
     * 新的数据模型是第一个版本
     * 如果存在已发布或者已失效的数据模型更新 - 版本号+1 ； 否则版本号不变
     * */
    private String getDataModelVersion(String modelId, String modelVersionId) {

        //查询该模型所有的版本
        List<TbCdcdmDataModel> modelList = dataModelConfigMapper.getModelInfoByModelId(modelId, null);
        if (CollectionUtil.isEmpty(modelList)) {
            //如果当前模型没有版本存在， 则为第一个版本
            return String.valueOf(1);
        }
        //获取当前数据模型的信息
        TbCdcdmDataModel currModel = dataModelConfigMapper.getDataModelInfoById(modelId);
        String status = dataModelConfigMapper.getVersionStatusByVersionId(modelVersionId);
        if (StringUtils.isBlank(status) || Constants.VERSION_STATUS_PUBLISHED.equals(status) || Constants.VERSION_STATUS_EXPIRED.equals(status)) {
            return String.valueOf(Integer.parseInt(currModel == null ? "0" : Optional.ofNullable(currModel.getLatestVersion()).orElse("0")) + 1);
        }
        //如果状态不属于上述几种，直接返回当前版本
        return dataModelConfigMapper.getVersionInfoByVersionId(modelVersionId).getVersion();
    }

    /**
     * 构建 数据模型 实体类
     * */
    private TbCdcdmDataModel createDataModelEntity(DataModelValueDTO dto) {

        return TbCdcdmDataModel.builder()
                               .modelId(dto.getModelId())
                               .modelName(dto.getModelName())
                               .modelType(dto.getModelType())
                               .note(dto.getDataModelNote())
                               .latestVersion(dto.getVersion())
                               .build();
    }

    /**
     * 构建 数据模型版本 实体类
     * */
    private TbCdcdmDataModelVersion createDataModelVersionEntity(DataModelValueDTO dto, String loginUserName) {

        return TbCdcdmDataModelVersion.builder()
                                      .modelVersionId(dto.getModelVersionId())
                                      .modelId(dto.getModelId())
                                      .modelLabel(dto.getModelLabel())
                                      .status(Constants.VERSION_STATUS_NOT_PUBLISHED)
                                      .version(dto.getVersion())
                                      .isEnable(Constants.STATUS_ON)
                                      .isDeleted(Constants.STATUS_OFF)
                                      .note(dto.getDataModelVersionNote())
                                      .createTime(new Date())
                                      .creator(loginUserName)
                                      .updateTime(new Date())
                                      .updater(loginUserName)
                                      .build();
    }

    @Override
    public PageInfo<DataModelListVO> getDataModelList(String status, String keyWord, String modelId, Integer pageIndex, Integer pageSize) {

        PageHelper.startPage(pageIndex, pageSize);
        DataModelValueDTO dto = new DataModelValueDTO();
        dto.setStatus(status);
        dto.setKeyWord(keyWord);
        dto.setModelId(modelId);
        return new PageInfo<>(dataModelConfigMapper.getDataModelList(dto));
    }

    @Override
    public PageInfo<DataModelListVO> getDataModelList(DataModelValueDTO dto, Integer pageIndex, Integer pageSize) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(dataModelConfigMapper.getDataModelList(dto));
    }

    @Override
    public List<DataModelListVO> allDataModelList(String status) {
        DataModelValueDTO dto = new DataModelValueDTO();
        dto.setStatus(status);
        return dataModelConfigMapper.getDataModelList(dto);
    }

    @Override
    @Transactional
    public void publishDataModel(String modelId, String modelVersionId, String loginUserName) {

        //发布新的数据模型时，该数据模型当前 已发布的版本变为已失效
        dataModelConfigMapper.updateDataModel(modelId, null, Constants.VERSION_STATUS_EXPIRED, Constants.VERSION_STATUS_PUBLISHED, loginUserName);
        //当前版本如果为已失效： 进行发布操作则新增一个版本，其他内容与该版本一致
        String status = dataModelConfigMapper.getVersionStatusByVersionId(modelVersionId);

        if (Constants.VERSION_STATUS_EXPIRED.equals(status)) {

            String version = this.getDataModelVersion(modelId, modelVersionId);
            //更新当前模型最新的版本
            dataModelConfigMapper.updateDataModelLatestVersion(modelId, version);
            //复制版本信息
            String newModelVersionId = this.copyVersionInfo(modelVersionId, loginUserName, version, Constants.VERSION_STATUS_PUBLISHED, null);
            //发布模型，更新配置信息
            updateTemplateConfig(modelId, newModelVersionId, loginUserName);
            return;
        }
        dataModelConfigMapper.publishDataModel(modelId, modelVersionId, Constants.VERSION_STATUS_PUBLISHED, loginUserName);
        //发布模型，更新配置信息
        updateTemplateConfig(modelId, modelVersionId, loginUserName);
    }

    /**
     * 修改数据模型的配置
     * */
    private void updateTemplateConfig(String modelId, String modelVersionId, String loginUserName){

        TbCdcdmDataFormTemplate formTemplate = formTemplateMapper.getConfigByModelId(modelId);
        // 模型在配置表中不存在配置时，新增一条配置信息
        if(Objects.isNull(formTemplate)){
            TbCdcdmDataModel dataModel = dataModelConfigMapper.getDataModelInfoById(modelId);
            this.initModelTemplate(modelId, dataModel.getModelName(), loginUserName);

        } else if (StrUtil.isNotBlank(formTemplate.getEsIndexName())) {
            // 如果已经存在并且有配置 es 索引，则重新生成 es 索引模板
            String esTemplate = esModelUtils.buildEsIndexTemplate(modelId, modelVersionId, formTemplate.getEsIndexName());
            formTemplateMapper.updateEsIndexTemplateById(formTemplate.getFormTemplateDetailId(), esTemplate);
        }
        //发布模型的新版本后，自动更新配置表中该模型对应的版本
        formTemplateMapper.updateModelVersionByModelId(modelId, modelVersionId);
    }

    /**
     * 复制某个版本的表单信息
     * */
    private String copyVersionInfo(String modelVersionId, String loginUserName, String version, String status, TbCdcdmDataModelForm modelForm) {

        //获取当前版本的所有状态，将其作为一个新的版本插入版本表，其对应的form表单也作为新的表单插入
        TbCdcdmDataModelVersion versionInfo = dataModelConfigMapper.getVersionInfoByVersionId(modelVersionId);
        List<TbCdcdmDataModelForm> versionFormList = dataModelConfigMapper.getFormListByVersionId(Collections.singletonList(modelVersionId));

        String newModelVersionId = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_VERSION));
        versionInfo.setModelVersionId(newModelVersionId);
        versionInfo.setStatus(status);
        versionInfo.setVersion(version);
        versionInfo.setCreator(loginUserName);
        versionInfo.setCreateTime(new Date());
        versionInfo.setUpdater(loginUserName);
        versionInfo.setUpdateTime(new Date());
        //复制当前版本进行操作时，根据新版本的状态决定发布人发布时间字段的更新
        if (status.equals(Constants.VERSION_STATUS_PUBLISHED)) {
            versionInfo.setPublisher(loginUserName);
            versionInfo.setPublishTime(new Date());
        } else {
            versionInfo.setPublisher(null);
            versionInfo.setPublishTime(null);
        }
        versionInfo.setInvalidator(null);
        versionInfo.setInvalidTime(null);
        dataModelConfigMapper.insertIntoDataModelVersion(versionInfo);

        //当有对表单做修改时，先处理表单修改，再将新版本插入数据库
        if(Objects.nonNull(modelForm)){
            this.editModelFormList(versionFormList, modelForm);
        }
        versionFormList.forEach(e -> {
            e.setModelFormId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_FORM)));
            e.setModelVersionId(newModelVersionId);
            e.setCreator(loginUserName);
            e.setCreateTime(new Date());
            e.setUpdater(loginUserName);
            e.setUpdateTime(new Date());
        });
        if (!versionFormList.isEmpty()) {
            dataModelConfigMapper.batchInsertFormList(versionFormList);
        }
        return newModelVersionId;
    }

    /**
     * 判断传入表单是否为新增表单
     * */
    private void editModelFormList(List<TbCdcdmDataModelForm> versionFormList,
                                   TbCdcdmDataModelForm modelForm){

        //为空则为新增表单
        if(StringUtils.isBlank(modelForm.getModelFormId())){
            if (modelForm.getIsDeleted() == null) {
                modelForm.setIsDeleted(Constants.NOT_DELETED);
            }
            versionFormList.add(modelForm);
        }else {
            //非空为修改表单
            versionFormList.forEach(e -> {
                if(Objects.equals(modelForm.getModelFormId(), e.getModelFormId())){
                    e.setFormName(modelForm.getFormName());
                    e.setIsRepeat(modelForm.getIsRepeat());
                    e.setMaxRepeatCnt(modelForm.getMaxRepeatCnt());
                    e.setFormIdentityColumn(modelForm.getFormIdentityColumn());
                    e.setFormJson(modelForm.getFormJson());
                    e.setIsDeleted(modelForm.getIsDeleted());
                }
            });
        }
    }

    @Override
    public void updateDataModel(String modelVersionId, String loginUserName) {

        String status = dataModelConfigMapper.getVersionStatusByVersionId(modelVersionId);
        //未发布数据模型作废后还处于未发布状态
        if (!Constants.VERSION_STATUS_NOT_PUBLISHED.equals(status)) {
            //作废数据模型
            dataModelConfigMapper.updateDataModel(null, modelVersionId, Constants.VERSION_STATUS_EXPIRED, null, loginUserName);
        }
    }

    @Override
    public void deleteDataModel(String modelVersionId, String loginUserName) {

        dataModelConfigMapper.deleteDataModel(modelVersionId, loginUserName);
    }

    @Override
    public PageInfo<ModelFormListVO> getModelFormList(DataModelFormQueryDTO dto) {

        //未传数据模型版本id的情况下 根据数据模型id查询已发布的版本
        if(StringUtils.isBlank(dto.getModelVersionId())){
            //根据model_id查询模型已发布版本信息
            List<TbCdcdmDataModelVersion> dataModelVersion = dataModelConfigMapper.getDataModelVersion(Collections.singletonList(Constants.VERSION_STATUS_PUBLISHED),
                                                                                                       dto.getModelId());
            if (CollectionUtil.isEmpty(dataModelVersion)){
                return new PageInfo<>(new ArrayList<>());
            }
            dto.setModelVersionId(dataModelVersion.get(0).getModelVersionId());
        }
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(dataModelConfigMapper.getModelFormList(dto));
    }

    @Override
    public PageInfo<DataModelVersionHistoryVO> getDataModelVersionHistory(String modelId, Integer pageIndex, Integer pageSize) {

        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(dataModelConfigMapper.getDataModelVersionHistory(modelId));
    }

    @Override
    @Transactional
    public String editModelForm(TbCdcdmDataModelForm tbCdcdmDataModelForm, String loginUserName) {

        //获取当前编辑表单 所处数据模型的版本信息
        TbCdcdmDataModelVersion versionInfo = dataModelConfigMapper.getVersionInfoByVersionId(tbCdcdmDataModelForm.getModelVersionId());
        tbCdcdmDataModelForm.setModelId(versionInfo.getModelId());
        List<TbCdcdmDataModel> modelList = dataModelConfigMapper.getModelInfoByModelId(tbCdcdmDataModelForm.getModelId(), Constants.VERSION_STATUS_PUBLISHED);
        //当前的编辑表单的数据模型版本处于已发布状态时 编辑该表单需要新增版本并复制其他表单到新版本
        if (Constants.VERSION_STATUS_PUBLISHED.equals(versionInfo.getStatus())) {
            //获取模型的最新版本号
            String version = this.getDataModelVersion(tbCdcdmDataModelForm.getModelId(), tbCdcdmDataModelForm.getModelVersionId());
            //复制当前版本 版本信息以及表单信息，修改的表单作为传参传入新的版本
            String newVersionId = this.copyVersionInfo(tbCdcdmDataModelForm.getModelVersionId(),
                                                       loginUserName,
                                                       version,
                                                       Constants.VERSION_STATUS_NOT_PUBLISHED,
                                                       tbCdcdmDataModelForm);
            //更新模型的最新版本号
            dataModelConfigMapper.updateDataModelLatestVersion(tbCdcdmDataModelForm.getModelId(), version);
            
            TbCdcdmDataModel curr = modelList.get(0);
            dataModelExtendService.saveDataModelExtend(curr.getModelId(), newVersionId, curr.getErModelId());
            return newVersionId;
        }
        //构建实体类
        this.createDataModelFormEntity(tbCdcdmDataModelForm, loginUserName);
        dataModelConfigMapper.editModelForm(tbCdcdmDataModelForm);
        return tbCdcdmDataModelForm.getModelVersionId();
    }

    private void createDataModelFormEntity(TbCdcdmDataModelForm modelForm, String loginUserName){

        if(modelForm.getModelFormId() == null){
            modelForm.setModelFormId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_MODEL_FORM)));
        }
        modelForm.setCreator(loginUserName);
        modelForm.setCreateTime(new Date());
        modelForm.setUpdater(loginUserName);
        modelForm.setUpdateTime(new Date());
        if (modelForm.getIsDeleted() == null) {
            modelForm.setIsDeleted(Constants.NOT_DELETED);
        }
    }

    @Override
    public TbCdcdmDataModelForm getFormInfoByFormId(String modelFormId) {

        return dataModelConfigMapper.getFormInfoByFormId(modelFormId);
    }

    @Override
    public void saveModelFormData(TbCdcdmDataFormValue tbCdcdmDataFormValue, String loginUserName) {

        if (tbCdcdmDataFormValue.getId() == null) {
            tbCdcdmDataFormValue.setId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_FORM_VALUE)));
        }
        tbCdcdmDataFormValue.setCreator(loginUserName);
        tbCdcdmDataFormValue.setUpdater(loginUserName);
        tbCdcdmDataFormValue.setCreateTime(new Date());
        tbCdcdmDataFormValue.setUpdateTime(new Date());
        dataModelConfigMapper.saveModelFormData(tbCdcdmDataFormValue);
    }

    @Override
    public DataModelFormConfigVO getDataModelFormsByModelId(String modelId) {

        DataModelFormConfigVO dataModelFormConfigVO = new DataModelFormConfigVO();
        //数据模型信息
        List<TbCdcdmDataModel> tbCdcdmDataModelList = dataModelConfigMapper.getModelInfoByModelId(modelId, Constants.VERSION_STATUS_PUBLISHED);
        if(CollectionUtil.isNotEmpty(tbCdcdmDataModelList)) {
            //一个数据模型 只有一个版本能处于发布状态
            TbCdcdmDataModel tbCdcdmDataModel = tbCdcdmDataModelList.get(0);
            BeanUtils.copyProperties(tbCdcdmDataModel, dataModelFormConfigVO);
            TbCdcdmDataModelVersion modelLatestVersion = dataModelConfigMapper.getDataModelVersion(Collections.singletonList(Constants.VERSION_STATUS_PUBLISHED),
                    modelId).get(0);
            BeanUtils.copyProperties(modelLatestVersion, dataModelFormConfigVO);
            //表单信息
            dataModelFormConfigVO.setFormList(dataModelConfigMapper.getFormListByModelId(modelId, Constants.VERSION_STATUS_PUBLISHED));
        }
        return dataModelFormConfigVO;
    }

    @Override
    public TbCdcdmDataFormValue getModelFormData(String id) {

        return dataModelConfigMapper.getModelFormData(id);
    }

    @Override
    public void editFormSequence(List<TbCdcdmDataModelForm> tbCdcdmDataModelForms, String loginUserName) {

        tbCdcdmDataModelForms.forEach(e -> {
            e.setUpdater(loginUserName);
            e.setUpdateTime(new Date());
        });
        dataModelConfigMapper.editFormSequence(tbCdcdmDataModelForms);
    }

    @Override
    public List<ModelFormFieldsVO> getModelFormFields() {

        List<ModelFormFieldsVO> modelFormFieldsVOList = new ArrayList<>();
        //数据模型配置
        List<DataModelFormConfigVO> formConfigVOList = dataModelConfigMapper.getModelFormListByVersion(Constants.VERSION_STATUS_PUBLISHED);
        //找到每个模型 已发布版本的id
        List<String> modelVersionIds = formConfigVOList.stream().map(DataModelFormConfigVO::getModelVersionId).collect(Collectors.toList());
        //根据已发布模型的版本id查询表单列表
        List<TbCdcdmDataModelForm> dataModelFormList = dataModelConfigMapper.getFormListByVersionId(modelVersionIds);
        //数据模型版本 和表单列表进行匹配
        formConfigVOList.forEach(elem -> {
            List<TbCdcdmDataModelForm> formList = dataModelFormList.stream()
                                                                   .filter(e -> Objects.equals(elem.getModelVersionId(), e.getModelVersionId()))
                                                                   .collect(Collectors.toList());
            elem.setFormList(formList);
        });

        for (DataModelFormConfigVO configVO : formConfigVOList) {

            ModelFormFieldsVO modelFormFieldsVO = new ModelFormFieldsVO();
            //复制基础信息（模型id、版本id等信息）
            BeanUtils.copyProperties(configVO, modelFormFieldsVO);
            List<TbCdcdmDataModelForm> modelForms = configVO.getFormList();
            //处理模型中表单的解析
            List<ModelFormFieldsVO.ModelFormVO> modelFormVOList = this.modelFormAnalysis(modelForms);
            modelFormVOList = modelFormVOList.stream()
                                             .sorted(Comparator.comparing(ModelFormFieldsVO.ModelFormVO::getOrderFlag,
                                                                          Comparator.nullsFirst(Integer::compareTo)).reversed())
                                             .collect(Collectors.toList());
            modelFormFieldsVO.setModelFormList(modelFormVOList);

            modelFormFieldsVOList.add(modelFormFieldsVO);
        }

        return modelFormFieldsVOList;
    }

    /**
     * 处理数据模型表单解析
     * */
    private List<ModelFormFieldsVO.ModelFormVO> modelFormAnalysis(List<TbCdcdmDataModelForm> modelForms) {

        List<ModelFormFieldsVO.ModelFormVO> modelFormVOList = new ArrayList<>();
        for (TbCdcdmDataModelForm modelForm : modelForms) {

            ModelFormFieldsVO.ModelFormVO modelFormVO = new ModelFormFieldsVO.ModelFormVO();
            //复制表单的基础信息
            BeanUtils.copyProperties(modelForm, modelFormVO);
            //设置表单中字段的解析结果
            List<ModelFormFieldsVO.FormFieldsVO> formFieldsVOList = this.formFieldsAnalysis(modelForm.getFormJson());
            modelFormVO.setFormFieldList(formFieldsVOList);

            modelFormVOList.add(modelFormVO);
        }
        return modelFormVOList;
    }

    /**
     * 处理表单中字段解析
     * */
    private List<ModelFormFieldsVO.FormFieldsVO> formFieldsAnalysis(String formJson) {

        List<ModelFormFieldsVO.FormFieldsVO> formFieldsVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(formJson)) {
            Gson gson = new Gson();
            //解析表单配置的字段属性
            List<FormGroupTmpl> formGroupTmplList = Arrays.asList(gson.fromJson(formJson, FormGroupTmpl[].class));
            if (CollectionUtil.isNotEmpty(formGroupTmplList)) {
                return this.getAllFieldInfoBy(formGroupTmplList);
            }
        }
        return formFieldsVOList;
    }

    /**
     * 解析某个表单中所有字段
     * */
    private List<ModelFormFieldsVO.FormFieldsVO> getAllFieldInfoBy(List<FormGroupTmpl> formGroupTmplList) {

        List<ModelFormFieldsVO.FormFieldsVO> formFieldsVOList = new ArrayList<>();

        for (FormGroupTmpl groupTmpl : formGroupTmplList) {

            List<FormGroupTmpl.FieldRule> fieldRuleList = groupTmpl.getProps().getRules();
            if (CollectionUtil.isNotEmpty(fieldRuleList)) {
                for (FormGroupTmpl.FieldRule fieldRule : fieldRuleList) {

                    ModelFormFieldsVO.FormFieldsVO formFieldsVO = new ModelFormFieldsVO.FormFieldsVO();
                    formFieldsVO.setTitle(fieldRule.getTitle());
                    formFieldsVO.setField(fieldRule.getField());
                    formFieldsVOList.add(formFieldsVO);
                }
            }
        }
        return formFieldsVOList;
    }

    /**
     * 新建模型时初始化template配置
     * */
    private String initModelTemplate(String modelId, String modelName, String loginUserName) {

        String formTemplateDetailId = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_FORM_TEMPLATE));
        TbCdcdmDataFormTemplate formTemplate = TbCdcdmDataFormTemplate.builder()
                                                                      .formTemplateDetailId(formTemplateDetailId)
                                                                      .modelId(modelId)
                                                                      .modelName(modelName)
                                                                      .isEnable(Constants.STATUS_ON)
                                                                      .isDeleted(Constants.NOT_DELETED)
                                                                      .creator(loginUserName)
                                                                      .createTime(new Date())
                                                                      .updater(loginUserName)
                                                                      .updateTime(new Date())
                                                                      .build();
        formTemplateMapper.insert(formTemplate);
        return formTemplateDetailId;
    }


    @Override
    public void uploadFile(MultipartFile file, String loginUserName) {
        String fileName = file.getOriginalFilename();
        try (InputStream in = file.getInputStream()) {

            List<UploadDataModelInfo> dataModelLoads = ExcelUtils.readExcel(UploadDataModelInfo.class, fileName, in);

            processDataModel(dataModelLoads, loginUserName);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Transactional
    public void processDataModel(List<UploadDataModelInfo> dataModelLoads, String loginUserName) {
        List<DataModelValueDTO> dataModels = new ArrayList<>();
        List<TbCdcdmDataModelForm> dataModelForms = new ArrayList<>();
        
        List<TbCdcdmDataFormTemplate> templates = formTemplateMapper.getAllModelTemplate(new DataFormTemplateQueryDTO());
        List<String> modelIds = templates.stream().map(TbCdcdmDataFormTemplate::getModelId).collect(Collectors.toList());

        Map<String, TbCdcdmMetadataTableInfo> tableInfos = metaDataService.getAllTable("").stream()
                                                                          .collect(Collectors.toMap(TbCdcdmMetadataTableInfo::getTableName,
                                                                                                    Function.identity(),
                                                                                                    (a, b) -> a));

        Map<String, List<UploadDataModelInfo>> modelMap = dataModelLoads.stream().collect(Collectors.groupingBy(UploadDataModelInfo::getModelName));
        for (Map.Entry<String, List<UploadDataModelInfo>> entry : modelMap.entrySet()) {
            String modelName = entry.getKey();
            DataModelValueDTO dataModel = new DataModelValueDTO();
            dataModel.setModelName(modelName);
            String modelId = UUIDUtil.generateMD5(modelName);
            String modelVersionId = UUIDUtil.generateMD5(modelName);

            dataModel.setModelId(modelId);
            dataModel.setModelVersionId(modelVersionId);
            //save
            dataModels.add(dataModel);

            int formIdx = 1;

            Map<String, List<UploadDataModelInfo>> formMap = entry.getValue()
                                                                  .stream()
                                                                  .collect(Collectors.groupingBy(UploadDataModelInfo::getFormName,
                                                                                                 LinkedHashMap::new,
                                                                                                 Collectors.toList()));

            for (Map.Entry<String, List<UploadDataModelInfo>> formEntry : formMap.entrySet()) {

                String formName = formEntry.getKey();
                List<UploadDataModelInfo> formList = formEntry.getValue();

                TbCdcdmDataModelForm form = new TbCdcdmDataModelForm();
                form.setModelId(modelId);
                form.setModelVersionId(modelVersionId);
                form.setModelFormId(UUIDUtil.generateMD5(modelName + formName));
                form.setFormName(formName);
                form.setOrderFlag(formIdx++); // 序号

                dataModelForms.add(form);

                UploadDataModelInfo formInfo = formList.get(0);

                String formRepeatFlag = formInfo.getFormRepeatFlag();
                String formRepeatCnt = formInfo.getFormRepeatCnt();

                form.setIsRepeat("是".equals(formRepeatFlag) ? 1 : 0);
                form.setMaxRepeatCnt(StringUtils.isBlank(formRepeatCnt) ? 1 : (int) Double.parseDouble(formRepeatCnt));

                Map<String, List<UploadDataModelInfo>> groupMap = formList.stream().collect(Collectors.groupingBy(UploadDataModelInfo::getGroupName,
                                                                                                                  LinkedHashMap::new,
                                                                                                                  Collectors.toList()));

                List<FormGroup> formGroups = new ArrayList<>();

                for (Map.Entry<String, List<UploadDataModelInfo>> groupEntry : groupMap.entrySet()) {
                    String groupName = groupEntry.getKey();
                    List<UploadDataModelInfo> columnList = groupEntry.getValue();
                    UploadDataModelInfo groupInfo = columnList.get(0);

                    FormGroup formGroup = new FormGroup();
                    formGroup.setType("group");
                    formGroup.setField(UUIDUtil.generateShortId());
                    formGroup.setTitle(groupName);

                    String groupRepeatFlag = groupInfo.getGroupRepeatFlag();
                    formGroup.setRepeat("是".equals(groupRepeatFlag) ? 1 : 0);
                    String groupRepeatCnt = groupInfo.getGroupRepeatCnt();
                    int max = StringUtils.isBlank(groupRepeatCnt) ? 1 : (int) Double.parseDouble(groupRepeatCnt);
                    formGroup.setMax(max);

                    FormGroup.Props props = new FormGroup.Props();
                    formGroup.setProps(props);

                    List<FormGroup.Rule> rules = new ArrayList<>();
                    props.setMax(max);
                    props.setRules(rules);

                    // 字段
                    for (UploadDataModelInfo columnInfo : columnList) {
                        String columnNameDesc = columnInfo.getColumnNameDesc();
                        FormGroup.Rule rule = new FormGroup.Rule();
                        rule.setTitle(columnNameDesc);
                        rule.setField(UUIDUtil.generateShortId());
                        rule.setPlaceholder(columnInfo.getDefaultText());
                        //组件选项
                        String type = SHOW_TYPE_MAP.getOrDefault(columnInfo.getComponent(), "input");
                        rule.setType(type);
                        //字段是否必填
                        rule.setRequired(JUDGE_BOOLEAN.getOrDefault(columnInfo.getRequireFlag(), false));
                        //字段是否只读 默认false
                        rule.setDisabled(false);
                        //字段配置值域
                        rule.setOptionField(dataDictMapper.getDictIdBy(columnInfo.getDataValue()));

                        HashMap<Object, Object> properties = new HashMap<>();
                        properties.put("minLength", 0);
                        properties.put("maxLength", StringUtils.isBlank(columnInfo.getTextLength()) ? 100 : columnInfo.getTextLength());
                        if(StringUtils.isNotBlank(columnInfo.getHasFutureFlag())){
                            properties.put("supportFuture", Objects.equals(columnInfo.getHasFutureFlag(), "是"));
                        }
                        String fieldProps = JSON.toJSONString(properties);
                        rule.setProps(fieldProps);

                        String adsColumn = columnInfo.getColumnName();
                        String adsTable = columnInfo.getTableName();
                        TbCdcdmMetadataTableInfo tableInfo = tableInfos.get(adsTable);

                        if (tableInfo != null) {
                            // 字段
                            String tableCode = tableInfo.getTableCode();
                            String columnCode = metaDataService.getFieldByTableName(tableInfo.getTableName(), adsColumn).stream()
                                                               .filter(t -> t.getColumnName().equals(adsColumn))
                                                               .findFirst()
                                                               .map(TbCdcdmMetadataTableColumnInfo::getColumnCode)
                                                               .orElse("");
                            if (StringUtils.isNotBlank(columnCode)) {
                                List<String> databaseField = Lists.newArrayList(tableCode, columnCode);
                                rule.setDatabaseField(databaseField);

                                FormGroup.DatabaseOption databaseOption = new FormGroup.DatabaseOption();
                                databaseOption.setLabel(adsColumn);
                                databaseOption.setValue(columnCode);
                                rule.setDatabaseOption(databaseOption);

                                List<String> databaseName = Lists.newArrayList(tableCode, columnCode);
                                rule.setDatabaseName(databaseName);
                            }
                        }

                        rules.add(rule);
                    }

                    formGroups.add(formGroup);
                }

                String formJson = new Gson().toJson(formGroups);
                form.setFormJson(formJson);
            }

        }

        for (DataModelValueDTO dataModel : dataModels) {
            editDataModel(dataModel, loginUserName);
        }

        int formSize = dataModelForms.size() + 1;
        for (TbCdcdmDataModelForm dataModelForm : dataModelForms) {
            dataModelForm.setOrderFlag(formSize - dataModelForm.getOrderFlag());
            this.editModelForm(dataModelForm, loginUserName);
        }

        //导入数据模型 每个模型新增一份配置(数量少暂时不批量插入)
        dataModels.forEach(e -> {
            if(!modelIds.contains(e.getModelId())) {
                DataModelTemplateDTO dto = new DataModelTemplateDTO();
                dto.setModelId(e.getModelId());
                dto.setModelName(e.getModelName());
                dto.setModelVersionId(e.getModelVersionId());
                templateService.addDataFormTemplate(dto);
            }
        });
    }

    /**
     * 将切换数据模型发布版本
     * */
    @Override
    public void updatePublishDataModel(String modelId, String modelVersionId, String status) {

        dataModelConfigMapper.updatePublishDataModel(modelId, modelVersionId, status);
    }

    @Override
    public List<TbCdcdmDataModelForm> getModelFormByConfigInfo(String configInfo) {

        LambdaQueryWrapper<TbCdcdmDataFormTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcdmDataFormTemplate::getConfigInfo, configInfo);
        queryWrapper.eq(TbCdcdmDataFormTemplate::getIsDeleted, Integer.valueOf(DeleteFlagEnum.NO.getCode()));
        TbCdcdmDataFormTemplate template = formTemplateMapper.selectOne(queryWrapper.last("limit 1"));

        LambdaQueryWrapper<TbCdcdmDataModelForm> formQueryWrapper = new LambdaQueryWrapper<>();
        formQueryWrapper.eq(TbCdcdmDataModelForm::getModelId, template.getModelId());
        formQueryWrapper.eq(TbCdcdmDataModelForm::getIsDeleted, Integer.valueOf(DeleteFlagEnum.NO.getCode()));
        return tbCdcdmDataModelFormMapper.selectList(formQueryWrapper);
    }
}
