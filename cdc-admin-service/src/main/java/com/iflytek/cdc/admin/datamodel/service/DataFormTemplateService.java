package com.iflytek.cdc.admin.datamodel.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.admin.datamodel.model.dto.DataFormTemplateQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelTemplateDTO;

public interface DataFormTemplateService {

    /**
     * 根据配置表id查询配置详情
     * */
    TbCdcdmDataFormTemplate getDataFormTemplateById(String formTemplateDetailId);

    /**
     * 新增一条配置信息
     * */
    void addDataFormTemplate(DataModelTemplateDTO dto);

    /**
     * 更新配置表
     * */
    void updateDataFormTemplate(DataModelTemplateDTO dto);

    /**
     * 查询所有的模型配置
     * */
    PageInfo<TbCdcdmDataFormTemplate> getAllModelTemplate(DataFormTemplateQueryDTO dto);

    /**
     * 根据疾病code获取配置的数据模型configInfo
     * 针对症候群每种疾病一个数据模型的问题
     * */
    String getConfigInfoByDiseaseCode(String diseaseCode);
}
