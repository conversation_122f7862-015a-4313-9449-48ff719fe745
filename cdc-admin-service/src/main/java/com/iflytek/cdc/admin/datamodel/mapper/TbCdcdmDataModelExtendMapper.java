package com.iflytek.cdc.admin.datamodel.mapper;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelExtend;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 数据模型扩展属性mapper 层
 */
@Mapper
public interface TbCdcdmDataModelExtendMapper extends BaseMapper<TbCdcdmDataModelExtend> {
    /**
     * 根据模型ID加载实体模型。
     * @return 返回根据模型ID加载的实体模型。
     */
    String loadErModelIdByModelId(@Param("modelId") String modelId);
}
