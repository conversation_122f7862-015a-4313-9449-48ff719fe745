package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;

import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease;
import com.iflytek.cdc.admin.service.UnknownReasonDiseaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "不明原因标准维护服务",description = "不明原因标准维护服务")
@AllArgsConstructor
public class UnknownReasonDisController {


    @Resource
    UnknownReasonDiseaseService unknownReasonDiseaseService;

    @ApiOperation("不明原因类维护-不明原因列表查询")
    @PostMapping("/{version}/pt/unknownReason/pageList")
    public PageInfo<TbCdcmrUnknownReasonDisease> getUnknownReasonDiseasePageList(@RequestBody UnknownReasonQueryDto dto) {
        return unknownReasonDiseaseService.getPageList(dto);
    }

    @ApiOperation("不明原因类维护-新增症状")
    @PostMapping("/{version}/pt/unknownReason/add")
    public void addUnknownReasonDisease(@RequestBody TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId) {
        unknownReasonDiseaseService.add(tbCdcmrUnknownReasonDisease, loginUserId);
    }

    @ApiOperation("不明原因类维护-删除症状")
    @PostMapping("/{version}/pt/unknownReason/delete")
    public void deleteUnknownReasonDisease(@RequestParam String id) {
        unknownReasonDiseaseService.delete(id);
    }

    @ApiOperation("不明原因类维护-编辑不明原因")
    @PostMapping("/{version}/pt/unknownReason/update")
    public void updateUnknownReasonDisease(@RequestBody TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId) {
        unknownReasonDiseaseService.update(tbCdcmrUnknownReasonDisease, loginUserId);
    }
}
