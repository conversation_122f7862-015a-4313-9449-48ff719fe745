package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ParamConfigDTO;
import com.iflytek.cdc.admin.dto.SearchParamDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.ParamOrg;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6 13:50
 **/
public interface ParamConfigService {

    /**
     * 查询参数维度参数列表
     *
     * @param sd
     * @param loginUserId
     * @return
     **/
    PageInfo<ParamConfig> queryParamConfig(SearchParamDTO sd, String loginUserId);


    /**
     * 查询参数维度参数列表
     *
     * @param sd
     * @param loginUserId
     * @return
     **/
    PageInfo<ParamOrg> queryParamOrg(SearchParamDTO sd, String loginUserId);

    /**
     * 根据机构id查询配置的参数
     *
     * @param isDelete
     * @param sd
     * @return
     **/
    PageInfo<ParamConfig> queryParamConfigById(String isDelete, SearchParamDTO sd);

    /**
     * 根据参数编码查询机构信息
     *
     * @param sd
     * @param loginUserId
     * @return
     **/
    List<ParamOrg> queryParamOrgByCode(SearchParamDTO sd, String loginUserId);

    /**
     * 添加参数配置信息
     *
     * @param pc
     * @param loginUserId
     * @return
     **/
    void insertParamConfig(ParamConfigDTO pc, String loginUserId);

    /**
     * 机构参数配置详情页面移除参数配置
     *
     * @param orgId
     * @param configCode
     * @param loginUserId
     * @return
     **/
    void removeOrgParamConfig(String orgId, String configCode, String loginUserId);

    /**
     * 查询单个参数配置详细信息
     *
     * @param sd
     * @param loginUserId
     * @return
     **/
    ParamConfigDTO queryParamConfigDetail(SearchParamDTO sd, String loginUserId);

    /**
     * 修改参数配置信息
     *
     * @param pc
     * @param loginUserId
     * @return
     **/
    void updateParamConfig(ParamConfigDTO pc, String loginUserId);


    /**
     * 修改参数配置信息
     *
     * @param id
     * @param loginUserId
     * @return
     **/
    void deleteParamConfig(String id, String loginUserId);

    /**
     * 查询参数信息
     *
     * @param paramInfoFilter
     * @return
     **/
    ParamInfo paramInfoByCode(ParamInfoFilter paramInfoFilter);

    /**
     * 批量查询参数信息
     *
     * @param filters
     * @return
     **/
    List<ParamInfo> paramListByCodeList(List<ParamInfoFilter> filters);

    /**
     * 根据参数分组查询下面所有分组的参数信息
     *
     * @param configGroup
     * @return
     **/
    List<ParamInfo> paramInfoByGroup(String configGroup);

    List<ParamConfig> queryParamConfigByKeyword(String keyword);

    /**
     * 获取导出最大数量
     * @return
     */
    int getExportMax();

    /**
     * 校验是否超出文件导出最大值
     */
    void checkExportMax(Collection collection);


    String getTrendForecastStatus(String configCode, String configGroup);

    String getStatusByCodeAndGroup(String configCode, String configGroup);

    void refreshRedisBy(String configCode, String configGroup);

}
