package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.ClientUpgradeParamDto;
import com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.service.ClientUpgradeUrlService;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.iflytek.cdc.admin.mapper.ClientUpgradeUrlMapper;


import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ClientUpgradeUrlService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/30 14:16
 * @Version 1.0
 */
@Service
@Slf4j
@AllArgsConstructor
public class ClientUpgradeUrlServiceImpl implements ClientUpgradeUrlService {

    @Resource
    private ClientUpgradeUrlMapper clientUpgradeUrlMapper;

    private  BatchUidService batchUidService;

    private  UapOrgService orgService;

    private  UapUserApi uapUserApi;

    @Override
    public PageData<ClientUpgradeUrlDto> queryClientUpgradeUrlDto( ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {
        PageMethod.startPage(clientUpgradeParamDto.getPageIndex(), clientUpgradeParamDto.getPageSize());
        List<String> orgCodes;
        if (!StringUtils.isEmpty(clientUpgradeParamDto.getOrgCode())) {
            List<String> dowOrgCodes = orgService.queryDownOrgCode(loginUserId);
            if(dowOrgCodes.contains(clientUpgradeParamDto.getOrgCode())){
                orgCodes = new ArrayList<String>();
                orgCodes.add(clientUpgradeParamDto.getOrgCode());
            }else{
               return null;
            }
        } else {
            orgCodes = orgService.queryDownOrgCode(loginUserId);
        }
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        List<ClientUpgradeUrlDto> clientUpgradeUrlDtos = clientUpgradeUrlMapper.queryClientUpgradeUrlDto(clientUpgradeParamDto);
        PageInfo<ClientUpgradeUrlDto> pageInfo = new PageInfo<>(clientUpgradeUrlDtos);
        PageData<ClientUpgradeUrlDto> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public int updateClientUpgradeUrlDto(ClientUpgradeUrlDto clientUpgradeUrlDto,String loginUserId) {
        TUapUser uapUser = getTuapUser(loginUserId);
        clientUpgradeUrlDto.setUpdateUser(uapUser.getId());
        return clientUpgradeUrlMapper.updateClientUpgradeUrlDto(clientUpgradeUrlDto);
    }

    @Override
    public int addClientUpgradeUrlDto(ClientUpgradeUrlDto clientUpgradeUrlDto,String loginUserId) {
        ClientUpgradeParamDto clientUpgradeParamDto = new  ClientUpgradeParamDto();
        List<String> orgCodes =new ArrayList<String>();
        orgCodes.add(clientUpgradeUrlDto.getOrgCode());
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        List<ClientUpgradeUrlDto> clientUpgradeUrlDtos = clientUpgradeUrlMapper.queryClientUpgradeUrlDto(clientUpgradeParamDto);
        if(clientUpgradeUrlDtos.size()>0){
            log.error("该机构升级服务已存在：{}",clientUpgradeUrlDto.getOrgCode());
            throw new MedicalBusinessException("该机构升级服务已存在");
        }
        clientUpgradeUrlDto.setId(String.valueOf(batchUidService.getUid(TableName.CLIENT_UPGRADE_URL)));
        clientUpgradeUrlDto.setIsDelete("1");
        TUapUser uapUser = getTuapUser(loginUserId);
        clientUpgradeUrlDto.setCreateUser(uapUser.getId());
        return clientUpgradeUrlMapper.addClientUpgradeUrlDto(clientUpgradeUrlDto);
    }

    private TUapUser getTuapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache= uapUserApi.getUserDetail(loginUserId).getUapUser();
        if(userCache ==null){
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }
}
