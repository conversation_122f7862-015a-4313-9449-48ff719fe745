package com.iflytek.cdc.admin.outbound.common.executor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.outbound.model.vo.ManualCallResultVo;
import com.iflytek.cdc.admin.util.StorageClientUtil;
import com.iflytek.cdc.admin.util.VerifyUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Service
@Slf4j
public class ManualCallPhoneExecutor {
    private static final RestTemplate restTemplate = new RestTemplate();

    @Value("${cdc.phone.url:http://172.30.32.235:9000/open-api}")
    private String phoneUrl;

    @Value("${cdc.phone.appId:1300545634336768}")
    private String appId;

    @Value("${cdc.phone.secretKey:wt5Oyxa53aj9OQRW}")
    private String secretKey;



    /**
     * 获取签名参数
     */
    private String getSignParams() {
        Long timestamp = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        sb.append(appId).append(timestamp).append(secretKey);
        String sign = SecureUtil.md5(sb.toString());
        return "appId=" + appId + "&timestamp=" + timestamp + "&sign=" + sign;
    }

    public String getAccount(String name, String telephone, String relationId) {
        try {
            if (StringUtils.isEmpty(name)) {
                throw new MedicalBusinessException("姓名不能为空！");
            }
            if (!VerifyUtil.verifyPhone(telephone)) {
                throw new MedicalBusinessException("电话格式不正确！");
            }
            if (StringUtils.isEmpty(relationId)) {
                throw new MedicalBusinessException("个案ID不能为空！");
            }
            String url = phoneUrl + "/v1/pb/openapi/manual-call/account?" + getSignParams() + "&name=" + name + "&telephone=" + telephone + "&relationId=" + relationId;
            JSONObject jsonObject = restTemplate.getForObject(url, JSONObject.class);
            if (jsonObject != null && jsonObject.get("data") != null) {
                log.info(jsonObject.get("message").toString());
                return jsonObject.get("data").toString();
            }else {
                if (jsonObject != null){
                    throw new MedicalBusinessException(jsonObject.get("message").toString());
                }
            }
        } catch (Exception e) {
            log.error("获取软段话配置并生成任务异常！", e);
            throw new MedicalBusinessException(e.getLocalizedMessage());
        }
        return StrUtil.EMPTY;
    }

    public String updateTime(String recordId) {
        try {
            String url = phoneUrl + "/v1/pb/openapi/manual-call/update?" + getSignParams() + "&recordId=" + recordId;
            JSONObject jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
            if (jsonObject != null && jsonObject.get("data") != null) {
                return jsonObject.get("data").toString();
            }
        } catch (Exception e) {
            log.error("更新软电话记录的更新时间异常！", e);
        }
        return StrUtil.EMPTY;
    }

    public String updateResult(String recordId, String relationId, ManualCallResultVo vo) {
        try {
            String url = phoneUrl + "/v1/pb/openapi/manual-call/result?" + getSignParams() + "&recordId=" + recordId;
            JSONObject jsonObject = restTemplate.postForObject(url, vo, JSONObject.class);
            if (jsonObject != null && jsonObject.get("data") != null) {
                return jsonObject.get("data").toString();
            }
        } catch (Exception e) {
            log.error("提交软电话记录的结果异常！", e);
        }
        return StrUtil.EMPTY;
    }

    public String getRecordUrl(String relationId) {
        try {
            //获取电话音频
            String url = phoneUrl + "/v1/pb/openapi/manual-call/record/url?" + getSignParams() + "&relationId=" + relationId;
            JSONObject jsonObject = restTemplate.postForObject(url, null, JSONObject.class);
            if (jsonObject != null && jsonObject.get("data") != null) {
                return jsonObject.get("data").toString();
            }
        } catch (Exception e) {
            log.error("获取软电话音频地址异常！", e);
        }
        return StrUtil.EMPTY;
    }

}
