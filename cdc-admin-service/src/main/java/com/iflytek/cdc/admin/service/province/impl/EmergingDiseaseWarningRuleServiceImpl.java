package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.EmergingWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseWarningRule;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingDiseaseWarningRuleMapper;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.model.mr.vo.TreeNodeProperty;

import com.iflytek.cdc.admin.vo.EmergingWarningRuleDetailVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.service.province.EmergingDiseaseWarningRuleService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class EmergingDiseaseWarningRuleServiceImpl extends ServiceImpl<TbCdcmrEmergingDiseaseWarningRuleMapper, TbCdcmrEmergingDiseaseWarningRule> implements EmergingDiseaseWarningRuleService {

    private static final String TB_CDCMR_EMERGING_DISEASE_WARNING_RULE = "tb_cdcmr_emerging_disease_warning_rule";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrEmergingDiseaseWarningRuleMapper emergingDiseaseWarningRuleMapper;

    @Resource
    private TbCdcmrEmergingDiseaseInfoMapper emergingDiseaseInfoMapper;

    @Override
    public PageInfo<TbCdcmrEmergingDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod, String followStatus) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(emergingDiseaseWarningRuleMapper.getListByEmergingDiseaseWarningId(diseaseInfoId, riskLevel, warningMethod, followStatus));
    }

    @Override
    public void savaOrUpdateRule(TbCdcmrEmergingDiseaseWarningRule rule) {
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        if (StringUtils.isBlank(rule.getId())) {
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_DISEASE_WARNING_RULE));
            rule.setId(id);
            rule.setCreatorId(uapUserPo.getId());
            rule.setCreateTime(new Date());
            rule.setCreator(uapUserPo.getName());
            rule.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            rule.setStatus(Integer.valueOf(StatusEnum.STATUS_ON.getCode()));
        }
        rule.setUpdaterId(uapUserPo.getId());
        rule.setUpdateTime(new Date());
        rule.setUpdater(uapUserPo.getName());
        //        设置关注状态
        if (StringUtils.isNotBlank(rule.getFollowStatus())) {
            rule.setFollowStatus(rule.getFollowStatus());
        }
        baseMapper.upsert(rule);
    }

    @Override
    public List<TreeNode> getEmergingTree(EmergingWaringRuleQueryDTO dto) {

        //查询新发突发传染病疾病列表
        List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = emergingDiseaseInfoMapper.listAll();

        //疾病规则数量
        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> ruleCount = this.getDiseaseRuleCount();

        //构建新发突发传染病树
        List<TreeNode> rootNode = TreeNode.buildTreeByNodeList(diseaseInfoList,
                TbCdcmrEmergingDiseaseInfo::getId,
                TbCdcmrEmergingDiseaseInfo::getDiseaseParentId,
                TbCdcmrEmergingDiseaseInfo::getDiseaseName,
                TbCdcmrEmergingDiseaseInfo::getDiseaseCode);

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);
        //将规则数量添加到对应的节点
        TreeNodeProperty.mergeTreeProperty(rootNode, ruleCount);
        //按照id排序
        rootNode = rootNode.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());

        if (StringUtils.isNotBlank(dto.getDiseaseInfoId())){
            return Collections.singletonList(TreeNode.getMatchNodeById(rootNode, t -> t.getId().equals(dto.getDiseaseInfoId())));
        }
        return rootNode;
    }

    /**
     * 查询每个疾病下 规则数
     * */
    private Map<String, List<TreeNodeProperty.RiskRuleCountVO>> getDiseaseRuleCount(){

        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> res = new HashMap<>();
        //查询传染病 每个风险等级下 规则的数量
        List<EmergingWarningRuleDetailVO> riskRuleCountList = emergingDiseaseWarningRuleMapper.getRiskRuleCountBy();
        //按照疾病进行分组
        Map<String, List<EmergingWarningRuleDetailVO>> listMap = riskRuleCountList.stream()
                .collect(Collectors.groupingBy(EmergingWarningRuleDetailVO::getId));
        listMap.forEach((k,v) ->{
            List<TreeNodeProperty.RiskRuleCountVO> riskLevelList = new ArrayList<>();
            for (EmergingWarningRuleDetailVO detailVO : v) {
                TreeNodeProperty.RiskRuleCountVO riskLevelVO = new TreeNodeProperty.RiskRuleCountVO();
                if(StrUtil.isNotBlank(detailVO.getRiskLevel())){
                    riskLevelVO.setRiskLevelCount(detailVO.getRiskLevelCount());
                    riskLevelVO.setRiskLevel(detailVO.getRiskLevel());
                    riskLevelList.add(riskLevelVO);
                }
            }
            res.put(k, riskLevelList);
        });
        return res;
    }

}
