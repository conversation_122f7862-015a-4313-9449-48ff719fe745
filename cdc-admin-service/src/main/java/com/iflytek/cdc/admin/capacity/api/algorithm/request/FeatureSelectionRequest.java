package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import com.iflytek.cdc.admin.dto.InfluenceFactorDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

@Slf4j
@lombok.Data
public class FeatureSelectionRequest implements Serializable {

    /**
     * 数据参数
     */
    private Data data;

    /**
     * 模型参数
     */
    private Model model;


    /**
     * 历史数据
     */
    @lombok.Data
    public static class Data{

        /**
         * 数据集路径
         */
        private String file_path;

        /**
         * 自定义使用的特征
         */
        private List<String> x_cols;

        private List<InfluenceFactorDTO> detailData;

        /**
         * 自定义使用的目标变量
         */
        private String y_cols;
    }

    @lombok.Data
    public static class Model{

        /**
         * 筛选特征方法
         */
        private String method;

        /**
         * 特征筛选的评估器
         */
        private String estimator;

        /**
         * 评估指标
         */
        private String metric;

        /**
         * 筛选后留下几个特征 5-10
         */
        private int n_features_to_select;

        /**
         * 每次移除几个特征 1-2
         */
        private int step;

        /**
         * 评估指标的筛选阈值 0-1
         */
        private double threshold;
    }
}
