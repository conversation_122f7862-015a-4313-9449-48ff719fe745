package com.iflytek.cdc.admin.datamodel.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.model.dm.dto.AddNewDataDictDTO;
import com.iflytek.cdc.admin.model.dm.dto.DataDictNameQueryDTO;
import com.iflytek.cdc.admin.model.dm.dto.DictValueQueryDTO;
import com.iflytek.cdc.admin.model.dm.dto.DragSortDataDTO;
import com.iflytek.cdc.admin.model.dm.vo.DataDictListVO;
import com.iflytek.cdc.admin.model.dm.vo.DataDictObjectVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ValueDomainService {

    /**
     * 查询值域列表
     * */
    PageInfo<DataDictListVO> getValueDomainList(String id, String name, Integer attrCount, Integer pageIndex, Integer pageSize);

    TbCdcdmDataDict getValueDomainDictById(String id);

    /**
     * 新增值域
     * */
    String editValueDomain(TbCdcdmDataDict tbCdcdmDataDict, UapUserPo uapUserPo);

    /**
     * 获取值域详情列表
     * */
    PageInfo<TbCdcdmDataDictValue> getValueDomainDetailList(String dataDictId, String type, Integer pageIndex, Integer pageSize);

    List<TbCdcdmDataDictValue> getDataDictValueList(String dataDictId);

    void convertToDictValueList(List<TreeNode> root, List<TbCdcdmDataDictValue> dictValues);

    /**
     * 根据id/code/name 查询值域详情
     * */
    List<TreeNode> getValueByKey(String id, String code, String name);

    List<TbCdcdmDataDictValue> getValueBy(String id, String code, String name, String type);

    /**
     * 根据name 查询对应的值域 或者 值
     * */
    DataDictObjectVO getDictValueBy(DictValueQueryDTO dto);

    /**
     * 新增值域详情
     * */
    void addValueDomainDetail(List<TbCdcdmDataDictValue> dictValues);

    /**
     * 更新值域详情
     * */
    void updateValueDomainDetail(List<TbCdcdmDataDictValue> dictValues);

    /**
     * 删除值域详情
     * */
    void deleteValueDomainDetail(String id, String attrId);

    /**
     * 根据code查询对应的name
     * */
    List<TbCdcdmDataDictValue> listNameByCode(DataDictNameQueryDTO dto);

    /**
     * 根据值域的Id集合查询
     */
    List<TbCdcdmDataDictValue> listByDataDictIds(List<String> dataDictIds);

    /**
     * 新增值域
     * */
    void addValueDomainBy(AddNewDataDictDTO dto);

    /**
     * 新增多层值域 - 研发使用
     * */
    void addMulValueDomainBy(MultipartFile file);

    /**
     * 保存拖动排序
     */
    void saveDragSort(DragSortDataDTO dto);

    /**
     * 生成值域导入模板
     * @return Excel 文件的字节数组
     */
    byte[] generateImportTemplate();
}
