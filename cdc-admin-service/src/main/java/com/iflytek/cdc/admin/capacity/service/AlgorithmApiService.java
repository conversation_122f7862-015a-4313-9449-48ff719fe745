package com.iflytek.cdc.admin.capacity.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.capacity.api.algorithm.AlgorithmApi;
import com.iflytek.cdc.admin.capacity.api.algorithm.config.AlgorithmApiConfig;
import com.iflytek.cdc.admin.capacity.api.algorithm.request.*;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.*;
import com.iflytek.cdc.admin.capacity.model.dto.*;
import com.iflytek.cdc.admin.capacity.model.vo.AwarenessResultVO;
import com.iflytek.cdc.admin.capacity.model.vo.DispositionAdviceResponseVO;
import com.iflytek.cdc.admin.capacity.model.vo.EventClassifyResponseVO;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.util.RequestApiUtils;
import com.iflytek.cdc.admin.util.UUIDUtil;
import com.iflytek.fpva.common.utils.DateUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.capacity.constants.AlgorithmApiConstants.*;

@Service
@Slf4j
public class AlgorithmApiService {
    @Resource
    private AlgorithmApi algorithmApi;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private AlgorithmApiConfig algorithmApiConfig;

    @Resource
    private RequestApiUtils requestApiUtils;


    /**
     * token 获取
     */
    public String getToken() {
        String token;
        Long expiresTime = stringRedisTemplate.opsForValue()
                .getOperations()
                .getExpire(getAccessTokenKey());
        log.info("开始获取缓存【key={}】的失效时间为{}秒", getAccessTokenKey(), expiresTime);
        try {
            if (expiresTime == null || expiresTime < algorithmApiConfig.getRefreshTime()) {
                log.info("开始续租token");
                try {
                    return tokenDeal(REFRESH_TOKEN);
                } catch (Exception e) {
                    log.info("续租失败，重新调用token生成接口");
                    return tokenDeal(ACCESS_TOKEN);
                }
            }
            token = stringRedisTemplate.opsForValue().get(getAccessTokenKey());
            if (StringUtils.isNotBlank(token)) {
                log.info("token已存在，值为={}", token);
                return token;
            }
            token = tokenDeal(ACCESS_TOKEN);
            return token;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            throw new MedicalBusinessException("Token 获取错误");
        }


    }

    /**
     * 删除Token
     */
    public void deleteToken() {
        stringRedisTemplate.delete(getRefreshTokenKey());
        stringRedisTemplate.delete(getAccessTokenKey());
    }

    private String getAccessTokenKey() {
        return REDIS_COMMON + ACCESS_TOKEN + ":" + algorithmApiConfig.getRootUrl();
    }

    private String getRefreshTokenKey() {
        return REDIS_COMMON + REFRESH_TOKEN + ":" + algorithmApiConfig.getRootUrl();
    }

//    /**
//     * 态势感知推演 无政策干预
//     */
//    public String awarenessNotIntervention(AwarenessRequestDTO awarenessRequest, String loginUserId){
//        AwarenessRequest request = new AwarenessRequest();
//        BeanUtils.copyProperties(awarenessRequest, request);
//        request.setIfContour(true);
//        if (awarenessRequest.getDatafile() == null || awarenessRequest.getDatafile().size() == 0){
//            request.setDatafile(null);
//            request.setIfCalibrate(false);
//        }
//        request.setSimpleVaccinePre(false);
//        request.setVaccineDaysPre(Collections.singletonList(0));
//        request.setSimpleVaccinePre(false);
//        request.setVaccineProbPre(0d);
//        request.setVaccineRelSympPre(0d);
//        String token = getToken();
//        return requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
//                algorithmApi.getFullUrlWithToken(algorithmApiConfig.getCdcPolicySimulationUrl(), token),
//                request,
//                loginUserId,
//                () -> algorithmApi.cdcPolicySimulation(getToken(), request)
//                ).getResult();
//    }

    /**
     * 态势感知推演 政策干预
     */
    public String awareness(AwarenessRequestDTO awarenessRequest, String loginUserId) {
        AwarenessRequest request = new AwarenessRequest();
        BeanUtils.copyProperties(awarenessRequest, request);
        if (awarenessRequest.getCalibrationData() == null || awarenessRequest.getCalibrationData().size() == 0) {
            request.setIfCalibrate(false);
        } else {
            request.setDatafile(convertToDataFile(awarenessRequest.getCalibrationData()));
            request.setIfCalibrate(true);
            request.setPredDay(awarenessRequest.getEndDay());
        }
        request.setIfContour(false);
        convertAndSetPolicy(awarenessRequest.getSchoolPolicy(), request, AwarenessRequest::setSchoolDaysPre, AwarenessRequest::setSchoolChangesPre);
        convertAndSetPolicy(awarenessRequest.getWorkDaysPolicy(), request, AwarenessRequest::setWorkDaysPre, AwarenessRequest::setWorkChangesPre);
        convertAndSetPolicy(awarenessRequest.getCommunityPolicy(), request, AwarenessRequest::setCommunityDaysPre, AwarenessRequest::setCommunityChangesPre);

        convertAndSetPolicy(awarenessRequest.getMasklPolicy(), request, AwarenessRequest::setMaskDaysPre, AwarenessRequest::setMaskChangesPre);
        convertAndSetPolicy(awarenessRequest.getSocialDistancePolicy(), request, AwarenessRequest::setSocialDistanceDaysPre, AwarenessRequest::setSocialDistanceChangesPre);
        convertAndSetPolicy(awarenessRequest.getCrowdGatherPolicy(), request, AwarenessRequest::setCrowdGatherDaysPre, AwarenessRequest::setCrowdGatherChangesPre);
        convertAndSetPolicy(awarenessRequest.getDisinfectionDaysPolicy(), request, AwarenessRequest::setDisinfectionDaysPre, AwarenessRequest::setDisinfectionChangesPre);
        String token = getToken();
        String requestId = UUIDUtil.generateUUID();
        request.setTaskID(requestId);
        log.info("调用算法服务esa接口请求参数：{}", JSONUtil.toJsonStr(request));
        return requestApiUtils.executeAndLog(requestId,
                algorithmApi.getFullUrlWithToken(algorithmApiConfig.getCdcPolicySimulationUrl(), token),
                request,
                loginUserId,
                () -> algorithmApi.cdcPolicySimulation(getToken(), request)
        ).getResult();
    }

    /**
     * 态势感知结果查询
     */
    public AwarenessResultVO loadAwarenessResult(String taskId, String loginUserId) {
        if (StringUtils.isBlank(taskId)) {
            throw new MedicalBusinessException("调用出错：任务ID不能为空");
        }
        AwarenessResultRequest request = new AwarenessResultRequest();
        request.setTaskId(taskId);
        String token = getToken();
        PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> awarenessResponsePlatformResponseBase = requestApiUtils.executeAndLog(taskId,
                algorithmApi.getFullUrlWithToken(algorithmApiConfig.getQueryCdcAwarenessUrl(), token),
                request,
                loginUserId,
                () -> algorithmApi.queryCdcAwareness(token, request),
                (d) -> d.getCode().equals(PlatformResponseBase.SUCCESS_CODE) || !d.getCode().equals(PlatformResponseBase.RUNNING_CODE));
        if (awarenessResponsePlatformResponseBase.getCode().equals(PlatformResponseBase.RUNNING_CODE)) {
            return null;
        }
        if (!awarenessResponsePlatformResponseBase.getCode().equals(PlatformResponseBase.SUCCESS_CODE)) {
            log.info("调用出错：{}", awarenessResponsePlatformResponseBase.getMessage());
            return null;
        }
        AlgorithmResponseBase<AwarenessResponse> response = awarenessResponsePlatformResponseBase.getResult();
        if (response == null) {
            log.info("调用出错：未获取到结果");
            return null;
        }
        if (!response.getCode().equals(PlatformResponseBase.SUCCESS_CODE)) {
            log.info("调用出错：{}", response.getMessage());
            return null;
        }
        AwarenessResultVO vo = new AwarenessResultVO();
        AwarenessResponse awarenessResponse = response.getResult();
        if (awarenessResponse == null) {
            return null;
        }
        AwarenessResponse.Result original = awarenessResponse.getOriginal();
        vo.setOriginalResult(convert(original));

        AwarenessResponse.Calibrate calibrate = awarenessResponse.getCalibrate();
        if (calibrate != null) {
            vo.setCalibrateResult(convert(calibrate));
        }

        AwarenessResultVO.Contour contour = new AwarenessResultVO.Contour();
        if (awarenessResponse.getContour() != null) {
            BeanUtils.copyProperties(awarenessResponse.getContour(), contour);
        }
        vo.setContour(contour);
        return vo;

    }

    /**
     * 风险等级研判
     */
    public EventClassifyResponseVO eventClassify(EventClassifyRequestDTO requestDTO, String loginUserId) {
        EventClassifyRequest eventClassifyRequest = new EventClassifyRequest();
        BeanUtils.copyProperties(requestDTO, eventClassifyRequest);
        eventClassifyRequest.setJiekouType(0);
        String token = getToken();
        log.info("风险等级研判请求参数：{}", eventClassifyRequest.toString());
        PlatformResponseBase<EventClassifyResponse> eventClassifyResponsePlatformResponseBase =
                requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                        algorithmApi.getFullUrlWithToken(algorithmApiConfig.getCdcEventClassifyUrl(), token),
                        eventClassifyRequest,
                        loginUserId,
                        () -> algorithmApi.cdcEventClassify(token, eventClassifyRequest)
                );
        EventClassifyResponseVO responseVO = new EventClassifyResponseVO();
        EventClassifyResponse result = eventClassifyResponsePlatformResponseBase.getResult();
        if (result == null || result.getBaseResult() == null) {
            responseVO.setBaseResult(Constants.NONE_CLASSIFY);
            return responseVO;
        }
        log.info("风险等级研判返回结果：{}", result.toString());
        BeanUtils.copyProperties(result, responseVO);
        responseVO.setBaseResult(result.getBaseResult().getEventRisk());
        return responseVO;
    }

    /**
     * 风险等级处置建议
     */
    public DispositionAdviceResponseVO dispositionAdvice(DispositionAdviceRequestDTO requestDTO, String loginUserId) {
        DispositionAdviceRequest request = new DispositionAdviceRequest();
        BeanUtils.copyProperties(requestDTO, request);
        String token = getToken();
        log.info("风险等级处置建议请求参数：{}", requestDTO.toString());
        PlatformResponseBase<AlgorithmResponseBase<DispositionAdviceResponse>> algorithmResponseBasePlatformResponseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApi.getFullUrlWithToken(algorithmApiConfig.getCdcDispositionAdviceUrl(), token), request, loginUserId,
                () -> algorithmApi.cdcDispositionAdvice(token, request));
        AlgorithmResponseBase<DispositionAdviceResponse> result = algorithmResponseBasePlatformResponseBase.getResult();
        if (result == null || result.getResult() == null) {
            return new DispositionAdviceResponseVO();
        }
        List<DispositionAdviceResponseVO.Materials> materials = new ArrayList<>();
        List<List<DispositionAdviceResponse.Materials>> emergencySupplies = result.getResult().getEmergencySupplies();
        if (CollUtil.isNotEmpty(emergencySupplies)) {
            for (List<DispositionAdviceResponse.Materials> list : emergencySupplies) {
                for (DispositionAdviceResponse.Materials material : list) {
                    if (material.get物资种类() != null) {
                        DispositionAdviceResponseVO.Materials materials1 = new DispositionAdviceResponseVO.Materials();
                        BeanUtils.copyProperties(material, materials1);
                        materials.add(materials1);
                    }
                }
            }
        }
        log.info("风险等级处置建议返回结果：{}", materials.toString());
        DispositionAdviceResponseVO responseVO = new DispositionAdviceResponseVO();
        BeanUtils.copyProperties(result.getResult(), responseVO);
        responseVO.setEmergencySupplies(materials);
        return responseVO;
    }

    /**
     * 预测未来的疾病趋势
     */
    public List<Integer> cdcDiseaseTrendPrediction(DiseaseTrendRequestDTO requestDTO, String loginUserId) {
        if (requestDTO.getHistoryData() == null) {
            throw new MedicalBusinessException("历史数据不能为空");
        }
        if (requestDTO.getHistoryData().size() != 4 && requestDTO.getHistoryData().size() != 7) {
            throw new MedicalBusinessException("历史数据的长度只能为4 或 7, 4 以周为单位,7 以天为单位");
        }
        DiseaseTrendRequest request = new DiseaseTrendRequest();
        BeanUtils.copyProperties(requestDTO, request);
        request.setHistoryDataGenders(Arrays.asList(requestDTO.getMaleNum() == null ? 0 : requestDTO.getMaleNum(), requestDTO.getFemaleNum() == null ? 0 : requestDTO.getFemaleNum()));
        request.setHistoryDataAges(Arrays.asList(requestDTO.getAgeGroup0To3Num(),
                requestDTO.getAgeGroup4To6Num(),
                requestDTO.getAgeGroup7To18Num(),
                requestDTO.getAgeGroup19To60Num(),
                requestDTO.getAgeGroupApprove60Num()));

        int historyDataSum = request.getHistoryData().stream().mapToInt(s -> s).sum();
        int genderDataSum = request.getHistoryDataGenders().stream().mapToInt(s -> s).sum();
        int ageDataSum = request.getHistoryDataAges().stream().mapToInt(s -> s).sum();
        if (!(historyDataSum == genderDataSum && genderDataSum == ageDataSum)) {
            throw new MedicalBusinessException("历史数据、性别数据和年龄数据的总和必须相等");
        }

        String token = getToken();
        PlatformResponseBase<AlgorithmResponseBase<List<Integer>>> algorithmResponseBasePlatformResponseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApi.getFullUrlWithToken(algorithmApiConfig.getCdcDiseaseTrendPredictionUrl(), token), request, loginUserId,
                () -> algorithmApi.cdcDiseaseTrendPrediction(token, request));
        if (algorithmResponseBasePlatformResponseBase != null
                && algorithmResponseBasePlatformResponseBase.getResult() != null) {
            AlgorithmResponseBase<List<Integer>> result = algorithmResponseBasePlatformResponseBase.getResult();
            return result.getResult();
        }
        return new ArrayList<>();
    }

    /**
     * 态势推演-时间序列模型
     */
    public List<Integer> arima(ArimaRequestDTO request, String loginUserId) {
        ArimaRequest arimaRequest = new ArimaRequest();
        List<String> timestamp = new ArrayList<>();
        List<Integer> quantity = new ArrayList<>();
        request.getHistoryDataList().forEach(d -> {
            timestamp.add(DateUtil.convertDateToString(d.getDate()));
            quantity.add(d.getQuantity());
        });
        ArimaRequest.HistoryData historyData = new ArimaRequest.HistoryData();
        historyData.setQuantity(quantity);
        historyData.setTimestamp(timestamp);
        arimaRequest.setHistoryData(historyData);
        arimaRequest.setStep(request.getStep());
        AlgorithmResponseBase<List<Integer>> responseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getArimaUrl(),
                arimaRequest,
                loginUserId,
                () -> algorithmApi.arima(arimaRequest));
        if (!responseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(responseBase.getMessage());
        }
        return responseBase.getResult();
    }

    /**
     * 态势推演-时空统计模型
     */
    public List<Integer> stgnn(STGNNRequestDTO request, String loginUserId) {
        STGNNRequest stgnnRequest = new STGNNRequest();
        Map<String, Integer> recordCount = request.getHistoryDataList().stream().collect(Collectors.toMap(d -> DateUtil.convertDateToString(d.getDate()), NonDynamicsRequestDTO.HistoryData::getQuantity, (o, n) -> n, LinkedHashMap::new));
        stgnnRequest.setRecordsCount(recordCount);
        stgnnRequest.setFutureDays(request.getStep());
        if (recordCount.size() < 14) {
            throw new MedicalBusinessException("至少需要14条历史数据");
        }
        AlgorithmResponseBase<List<Integer>> responseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getStgnnUrl(),
                stgnnRequest,
                loginUserId,
                () -> algorithmApi.stgnn(stgnnRequest));
        if (!responseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(responseBase.getMessage());
        }
        return responseBase.getResult();
    }

    /**
     * 态势推演-神经网络模型
     */
    public List<Integer> lstm(LSTMRequestDTO request, String loginUserId) {
        LSTMRequest lstmRequest = new LSTMRequest();
        Map<String, Integer> recordCount = request.getHistoryDataList().stream().collect(Collectors.toMap(d -> DateUtil.convertDateToString(d.getDate()), NonDynamicsRequestDTO.HistoryData::getQuantity, (o, n) -> n, LinkedHashMap::new));
        lstmRequest.setRecordsCount(recordCount);
        lstmRequest.setFutureDays(request.getStep());
        AlgorithmResponseBase<List<Integer>> responseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getLstmUrl(),
                lstmRequest,
                loginUserId,
                () -> algorithmApi.lstm(lstmRequest));
        if (!responseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(responseBase.getMessage());
        }
        return responseBase.getResult();
    }

    /**
     * 态势推演-传染病特定组合模型
     */
    public List<Integer> seir(SEIRRequestDTO request, String loginUserId) {
        SEIRRequest seirRequest = new SEIRRequest();
        BeanUtils.copyProperties(request, seirRequest);
        AlgorithmResponseBase<List<Integer>> responseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getSeirUrl(),
                seirRequest,
                loginUserId,
                () -> algorithmApi.seir(seirRequest));
        if (!responseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(responseBase.getMessage());
        }
        return responseBase.getResult();
    }

    /**
     * 态势推演-混合模型
     */
    public List<Integer> ensemble(EnsembleRequestDTO request, String loginUserId) {
        EnsembleRequest ensembleRequest = new EnsembleRequest();
        BeanUtils.copyProperties(request, ensembleRequest);
        AlgorithmResponseBase<List<Integer>> responseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getEnsembleUrl(),
                ensembleRequest,
                loginUserId,
                () -> algorithmApi.ensemble(ensembleRequest));
        if (!responseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(responseBase.getMessage());
        }
        return responseBase.getResult();
    }

    /**
     * 辅助决策——疾病影响因素分析
     */
    public InfluenceFactorResponse.Result.InfluenceFactor randomForest(FeatureSelectionRequest request, String loginUserId) {
        InfluenceFactorResponse influenceFactor = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getFeatureSelectionUrl(),
                request,
                loginUserId,
                () -> algorithmApi.randomForest(request));
        log.info("随机森林模型返回结果：{}", JSONUtil.toJsonStr(influenceFactor));
        if (!influenceFactor.getCode().equals("0")) {
            throw new MedicalBusinessException(influenceFactor.getMessage());
        }
        return influenceFactor.getResut().getSelected_features();
    }


    /**
     * 辅助大屏-获取街道预测
     */
    public Map<String, Map<String, Integer>> predictSpread(PredictSpreadDTO request, String loginUserId) {
        log.info("请求参数：{},+++{}", request.getRecordsCount(), request.getLongitudeAndLatitude().values().toString());
        AlgorithmResponseBase<Map<String, Map<String, Integer>>> mapAlgorithmResponseBase = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getSpreadEpidemicUrl(),
                request,
                loginUserId,
                () -> algorithmApi.spreadEpidemic(request));

        if (!mapAlgorithmResponseBase.getCode().equals("0")) {
            throw new MedicalBusinessException(mapAlgorithmResponseBase.getMessage());
        }
        log.info("返回结果：{},+++{}" + mapAlgorithmResponseBase.getMessage(), mapAlgorithmResponseBase.getResult().toString());
        return mapAlgorithmResponseBase.getResult();
    }


    /**
     * 辅诊大屏-风险评估
     */
    public RiskAssessResponse riskAssess(RiskAssessRequestDTO request, String loginUserId) {
        log.info("风险评估请求参数：{}", request.toString());
        RiskAssessResponse riskAssessResponse = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getRiskAssessUrl(),
                request,
                loginUserId,
                () -> algorithmApi.riskAssess(request));
        if (!riskAssessResponse.getCode().equals("0")) {
            throw new MedicalBusinessException(riskAssessResponse.getMessage());
        }
        log.info("风险评估返回结果：{}", riskAssessResponse.getMessage().toString());
        return riskAssessResponse;
    }

    /**
     * 辅诊大屏-风险评估
     */
    public KnowledgeBaseSearchResponse knowledgeBaseSearch(KnowledgeBaseSearchDTO request, String loginUserId) {
        log.info("事件知识库查询请求参数：{}", request.toString());
        KnowledgeBaseSearchResponse knowledgeBaseSearchResponse = requestApiUtils.executeAndLog(UUIDUtil.generateUUID(),
                algorithmApiConfig.getKnowledgeBaseSearchUrl(),
                request,
                loginUserId,
                () -> algorithmApi.knowledgeBaseSearch(request));
        if (!knowledgeBaseSearchResponse.getCode().equals("0")) {
            throw new MedicalBusinessException(knowledgeBaseSearchResponse.getMessage());
        }
        log.info("事件知识库查询返回结果：{}", knowledgeBaseSearchResponse.getMessage().toString());
        return knowledgeBaseSearchResponse;
    }

    /**
     * 结果转换
     */
    private List<AwarenessResultVO.Result> convert(AwarenessResponse.Result originalResult) {
        List<List<Integer>> date = originalResult.getDate();

        List<Double> infectiousRate = originalResult.getInfectiousRate();
        List<Double> deathRate = originalResult.getDeathRate();

        List<Integer> newInfectious = originalResult.getNewInfectious();
        List<Integer> cumInfectious = originalResult.getCumInfectious();

        List<Integer> newDiagnoses = originalResult.getNewDiagnoses();
        List<Integer> cumDiagnoses = originalResult.getCumDiagnoses();

        List<Integer> newDeaths = originalResult.getNewDeaths();
        List<Integer> cumDeaths = originalResult.getCumDeaths();

        List<Integer> newRecoveries = originalResult.getNewRecoveries();
        List<Integer> cumRecoveries = originalResult.getCumRecoveries();

        List<Integer> curInfectious = originalResult.getCurInfectious();
        List<Integer> curDiagnoses = originalResult.getCurDiagnoses();

        AwarenessResponse.Mmc newDiagnosesMMC = originalResult.getNewDiagnosesMMC();
        AwarenessResponse.Mmc cumDiagnosesMMC = originalResult.getCumDiagnosesMMC();

        AwarenessResponse.Mmc newInfectiousMMC = originalResult.getNewInfectiousMMC();
        AwarenessResponse.Mmc cumInfectiousMMC = originalResult.getCumInfectiousMMC();

        AwarenessResponse.Mmc newDeathsMMC = originalResult.getNewDeathsMMC();
        AwarenessResponse.Mmc cumDeathsMMC = originalResult.getCumDeathsMMC();

        AwarenessResponse.Mmc newRecoveriesMMC = originalResult.getNewRecoveriesMMC();
        AwarenessResponse.Mmc cumRecoveriesMMC = originalResult.getCumRecoveriesMMC();

        AwarenessResponse.Mmc curInfectiousMMC = originalResult.getCurInfectiousMMC();
        AwarenessResponse.Mmc curDiagnosesMMC = originalResult.getCurDiagnosesMMC();


        List<List<Double>> rtIndexes = null;
        if (originalResult instanceof AwarenessResponse.Calibrate) {
            AwarenessResponse.CalibrateIndex indexes = ((AwarenessResponse.Calibrate) originalResult).getIndexes();
            if (indexes != null) {
                rtIndexes = indexes.getRt();
            }
        }

        List<AwarenessResultVO.Result> results = new ArrayList<>();
        for (int i = 0; i <= date.size() - 1; i++) {
            List<Integer> dates = date.get(i);
            String dateStr = dates.get(0) + String.format("%02d", dates.get(1)) + String.format("%02d", dates.get(2));
            AwarenessResultVO.Result result = new AwarenessResultVO.Result();
            result.setDate(dateStr);
            result.setInfectiousRate(CollUtil.get(infectiousRate, i));
            result.setDeathRate(CollUtil.get(deathRate, i));

            result.setNewInfectious(CollUtil.get(newInfectious, i));
            result.setCumInfectious(CollUtil.get(cumInfectious, i));

            result.setNewDiagnoses(CollUtil.get(newDiagnoses, i));
            result.setCumDiagnoses(CollUtil.get(cumDiagnoses, i));

            result.setNewDeaths(CollUtil.get(newDeaths, i));
            result.setCumDeaths(CollUtil.get(cumDeaths, i));

            result.setNewRecoveries(CollUtil.get(newRecoveries, i));
            result.setCumRecoveries(CollUtil.get(cumRecoveries, i));

            result.setCurInfectious(CollUtil.get(curInfectious, i));
            result.setCurDiagnoses(CollUtil.get(curDiagnoses, i));

            result.setMxNewInfectious(originalResult.getMxNewInfectious());
            result.setMxdNewInfectious(originalResult.getMxdNewInfectious());
            result.setMxCumInfectious(originalResult.getMxCumInfectious());
            result.setMxdCumInfectious(originalResult.getMxdCumInfectious());

            result.setMxNewDeaths(originalResult.getMxNewDeaths());
            result.setMxdNewDeaths(originalResult.getMxdNewDeaths());
            result.setMxCumDeaths(originalResult.getMxCumDeaths());
            result.setMxdCumDeaths(originalResult.getMxdCumDeaths());

            result.setMxNewRecoveries(originalResult.getMxNewRecoveries());
            result.setMxdNewRecoveries(originalResult.getMxdNewRecoveries());
            result.setMxCumRecoveries(originalResult.getMxCumRecoveries());
            result.setMxdCumRecoveries(originalResult.getMxdCumRecoveries());

            result.setMxNewDiagnoses(originalResult.getMxNewDiagnoses());
            result.setMxdNewDiagnoses(originalResult.getMxdNewDiagnoses());
            result.setMxCumDiagnoses(originalResult.getMxCumDiagnoses());
            result.setMxdCumDiagnoses(originalResult.getMxdCumDiagnoses());


            result.setMxCurInfectious(originalResult.getMxCurInfectious());
            result.setMxdCurInfectious(originalResult.getMxdCurInfectious());
            result.setMxCurDiagnoses(originalResult.getMxCurDiagnoses());
            result.setMxdCurDiagnoses(originalResult.getMxdCurDiagnoses());

            result.setNewDiagnosesMMC(BeanUtil.copyProperties(newDiagnosesMMC, AwarenessResultVO.Mmc.class));
            result.setCumDiagnosesMMC(BeanUtil.copyProperties(cumDiagnosesMMC, AwarenessResultVO.Mmc.class));
            result.setNewInfectiousMMC(BeanUtil.copyProperties(newInfectiousMMC, AwarenessResultVO.Mmc.class));
            result.setCumInfectiousMMC(BeanUtil.copyProperties(cumInfectiousMMC, AwarenessResultVO.Mmc.class));
            result.setNewDeathsMMC(BeanUtil.copyProperties(newDeathsMMC, AwarenessResultVO.Mmc.class));
            result.setCumDeathsMMC(BeanUtil.copyProperties(cumDeathsMMC, AwarenessResultVO.Mmc.class));
            result.setNewRecoveriesMMC(BeanUtil.copyProperties(newRecoveriesMMC, AwarenessResultVO.Mmc.class));
            result.setCumRecoveriesMMC(BeanUtil.copyProperties(cumRecoveriesMMC, AwarenessResultVO.Mmc.class));
            result.setCurInfectiousMMC(BeanUtil.copyProperties(curInfectiousMMC, AwarenessResultVO.Mmc.class));
            result.setCurDiagnosesMMC(BeanUtil.copyProperties(curDiagnosesMMC, AwarenessResultVO.Mmc.class));

            if (originalResult instanceof AwarenessResponse.Calibrate) {
                AwarenessResponse.CalibrateIndex indexes = ((AwarenessResponse.Calibrate) originalResult).getIndexes();
                if (indexes != null) {
                    List<List<Double>> rtList = indexes.getRt();
                    if (CollUtil.isNotEmpty(rtList)) {
                        // 将列表中为null的值改为0
                        for (List<Double> rtChildList : rtList) {
                            for (int k = 0; k < rtChildList.size(); k++) {
                                if (rtChildList.get(k) == null) {
                                    rtChildList.set(k, 0d);
                                }
                            }
                        }
                    }
                }
                AwarenessResultVO.CalibrateIndex calibrateIndex = new AwarenessResultVO.CalibrateIndex();
                BeanUtils.copyProperties(indexes, calibrateIndex);
                result.setIndexes(calibrateIndex);
            }

            List<Double> rtIndex = CollUtil.get(rtIndexes, i);
            if (rtIndex != null && rtIndex.size() > 2) {
                result.setRtValue(rtIndex.get(0));
                result.setRtCiLowerBound(rtIndex.get(1));
                result.setRtCiUpperBound(rtIndex.get(2));
            }

            results.add(result);
        }
        return results;
    }

    /**
     * 校准数据转换
     */
    private AwarenessRequest.Datafile convertToDataFile(List<AwarenessRequestDTO.CalibrationData> calibrationData) {
        AwarenessRequest.Datafile datafile = new AwarenessRequest.Datafile();
        List<String> date = new ArrayList<>();
        List<Integer> newDiagnoses = new ArrayList<>();
        List<Integer> newInfected = new ArrayList<>();
        List<Integer> newDeaths = new ArrayList<>();
        List<Integer> cumDiagnoses = new ArrayList<>();
        List<Integer> cumInfected = new ArrayList<>();
        List<Integer> cumDeaths = new ArrayList<>();
        calibrationData.forEach(c -> {
            date.add(c.getDate());
            newDiagnoses.add(c.getNewDiagnoses());
            newInfected.add(c.getNewInfections());
            newDeaths.add(c.getNewDeaths());
            cumDiagnoses.add(c.getCumDiagnoses());
            cumInfected.add(c.getCumInfections());
            cumDeaths.add(c.getCumDeaths());
        });
        datafile.setDate(date);
        if (isNotAllZeroNullOrEmpty(newDiagnoses)) {
            datafile.setNew_diagnoses(newDiagnoses);
        }
        if (isNotAllZeroNullOrEmpty(newInfected)) {
            datafile.setNew_infectious(newInfected);
        }
        if (isNotAllZeroNullOrEmpty(newDeaths)) {
            datafile.setNew_deaths(newDeaths);
        }
        if (isNotAllZeroNullOrEmpty(cumDiagnoses)) {
            datafile.setCum_diagnoses(cumDiagnoses);
        }
        if (isNotAllZeroNullOrEmpty(cumInfected)) {
            datafile.setCum_infectious(cumInfected);
        }
        if (isNotAllZeroNullOrEmpty(cumDeaths)) {
            datafile.setCum_deaths(cumDeaths);
        }
        return datafile;
    }

    /**
     * 判断数组里是否全是0 或 空
     */
    private boolean isNotAllZeroNullOrEmpty(List<Integer> data) {
        if (data == null || data.isEmpty()) {
            return false;
        }
        long count = data.stream().filter(d -> d != null && d != 0).count();
        return !(count == 0);
    }

    private void convertAndSetPolicy(AwarenessRequestDTO.InterventionPolicy interventionPolicy,
                                     AwarenessRequest request,
                                     BiConsumer<AwarenessRequest, List<String>> daysPre,
                                     BiConsumer<AwarenessRequest, List<Double>> changesPre) {
        if (interventionPolicy == null) {
            return;
        }
        daysPre.accept(request, Arrays.asList(interventionPolicy.getStartDate(), interventionPolicy.getEndDate()));
        changesPre.accept(request, Arrays.asList(interventionPolicy.getRate(), 0d));
    }

    private String tokenDeal(String type) throws Exception {
        AuthRequest authRequest = new AuthRequest();
        authRequest.setGrantType(algorithmApiConfig.getGrantType());
        authRequest.setClientId(algorithmApiConfig.getClientId());
        authRequest.setClientSecret(algorithmApiConfig.getClientSecret());
        authRequest.setStateCode(algorithmApiConfig.getCityCenterCode());

        // 传入刷新token值进行刷新操作
        if (REFRESH_TOKEN.equals(type)) {
            String accessTokenOld = stringRedisTemplate.opsForValue().get(getAccessTokenKey());
            String refreshTokenOld = stringRedisTemplate.opsForValue().get(getRefreshTokenKey());
            // 失效无法刷新token，重新获取token
            if (StringUtils.isNotBlank(accessTokenOld) && StringUtils.isNotBlank(refreshTokenOld)) {
                authRequest.setAccessToken(accessTokenOld);
                authRequest.setRefreshToken(refreshTokenOld);
            }
        }
        try {
            String accessToken = "";
            AuthResponse token = algorithmApi.getToken(authRequest);
            if (token != null
                    && StringUtils.isNotEmpty(token.getAccessToken())
                    && StringUtils.isNotEmpty(token.getRefreshToken())) {
                accessToken = token.getAccessToken();
                stringRedisTemplate.opsForValue().set(getAccessTokenKey(),
                        accessToken, token.getExpiresIn(), TimeUnit.SECONDS);

                String refreshToken = token.getRefreshToken();
                stringRedisTemplate.opsForValue().set(getRefreshTokenKey(),
                        refreshToken, token.getExpiresIn(), TimeUnit.SECONDS);
            } else if (StringUtils.isNotEmpty(authRequest.getRefreshToken()) && REFRESH_TOKEN.equals(type)) {
                accessToken = tokenDeal(ACCESS_TOKEN);

            }
            return accessToken;
        } catch (Exception e) {
            log.error("获取token失败,{}", e.getMessage());
            throw new MedicalBusinessException("获取cdc 平台token失败");
        }
    }
}
