package com.iflytek.cdc.admin.vo.epi;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel("流调响应数据")
public class EpiResponseBaseVO<T>{
    public static final Integer SUCCESS_CODE = 200;
    private Integer code;
    private String message;
    private T data;
    
    public boolean success(){
        return  SUCCESS_CODE.equals(code);
    }
    
    public T getDataOrThrow(){
        if (!success()){
            throw new MedicalBusinessException(message);
        }
        return getData();
    }
}
