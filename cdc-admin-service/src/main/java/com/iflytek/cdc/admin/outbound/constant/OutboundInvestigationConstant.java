package com.iflytek.cdc.admin.outbound.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import com.iflytek.cdc.admin.enums.BaseEnum;
@Data
public class OutboundInvestigationConstant {
    
    public static final OutboundInvestigationConstant OUTBOUND_INVESTIGATION_CONSTANT = new OutboundInvestigationConstant();
    
    @ApiModelProperty("执行方式")
    private Object executionMethods = ExecutionMethod.values();

    @ApiModelProperty("执行时间类型")
    private Object executionTimeTypes = ExecutionTimeType.values();
    
    @ApiModelProperty("短信发送类型")
    private Object smsSendTypes = SmsSendType.values();
    

    @Getter
    public enum ExecutionMethod implements BaseEnum {
        PHONE("1", "电话通知"),
        SMS("2", "短信")
        ;
        private final String code;
        private final String desc;
        
        ExecutionMethod(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum ExecutionTimeType implements BaseEnum {
        NOW("1", "立即执行"),
        APPOINTED_TIME("2", "指定时间")
        ;
        private final String code;
        private final String desc;

        ExecutionTimeType(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 短信发送条件
     */
    @Getter
    public enum SmsSendType implements BaseEnum {
        NOT("0", "不发送短信"),
        NOT_COLLECTED("1", "未接通发送短信"),
        AFTER_COLLECTED("2", "接通后发送短信"),
        ALL("3", "所有人发短信"),
        ;
        private final String code;
        private final String desc;

        SmsSendType(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }
}
