package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleSaveDTO;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonQuery;
import com.iflytek.cdc.admin.dto.epi.AreaDTO;
import com.iflytek.cdc.admin.dto.epi.EmergencyPlanRequest;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson;
import com.iflytek.cdc.admin.enums.DealPersonTypeEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.model.mr.dto.DealPersonQueryDTO;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import com.iflytek.cdc.admin.vo.SignalPushConfigurationVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.cdc.admin.vo.epi.DictVO;
import com.iflytek.cdc.admin.vo.epi.DutyUserVO;
import com.iflytek.cdc.admin.vo.epi.EcdOrgUserVO;
import com.iflytek.cdc.admin.vo.epi.EmergencyPlanVO;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Slf4j
@RestController
@Api(tags = "信号处理人配置相关接口")
public class WarningChargePersonController {
    @Resource
    private WarningChargePersonService warningChargePersonService;

    @ApiOperation("信号处理人配置-保存")
    @OperationLogAnnotation(operationName = "信号处理人配置-保存")
    @PostMapping("/{version}/pt/warningChargePerson/save")
    public TbCdcmrWarningChargePerson save(@RequestBody WarningChargePersonDTO input, @RequestParam String loginUserId) {
        return warningChargePersonService.save(input);
    }

    /**
     * 信号处理人配置-修改状态
     */
    @ApiOperation("信号处理人配置-修改状态")
    @OperationLogAnnotation(operationName = "信号处理人配置-修改状态")
    @PostMapping("/{version}/pt/warningChargePerson/changeStatus")
    public void changeStatus(@RequestBody WarningChargePersonDTO input, @RequestParam String loginUserId) {
        warningChargePersonService.changeStatus(input);
    }

    @ApiOperation("信号处理人配置-批量保存")
    @OperationLogAnnotation(operationName = "信号处理人配置-批量保存")
    @PostMapping("/{version}/pt/warningChargePerson/batchSave")
    public void batchSave(@RequestBody List<WarningChargePersonDTO> inputs, @RequestParam String loginUserId) {
        warningChargePersonService.batchSave(inputs);
    }


    @ApiOperation("信号处理人配置-疾病查询")
    @PostMapping("/{version}/pt/warningChargePerson/listDiseaseConfig")
    public List<WarningChargePersonVO> listDiseaseConfig(@RequestBody WarningChargePersonQuery input) {

        //使用公共处理时限的预警类型 统一转化
        input.setTimeLimitType(WarningTypeProEnum.commonTimeLimitWarning.contains(input.getWarningType()) ?
                Constants.COMMON_TIME_LIMIT : input.getWarningType());
        return warningChargePersonService.listDiseaseConfig(input);
    }

    @ApiOperation("常量获取")
    @PostMapping("/{version}/pt/warningChargePerson/getConstants")
    public Object getConstants() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("dealPersonTypeEnum", DealPersonTypeEnum.mapValues());
        return resultMap;
    }

    @ApiOperation("根据疾病code以及风险等级 批量查询对应的处理人信息")
    @PostMapping("/{version}/pt/warningChargePerson/getDealPersonInfoBy")
    public List<WarningChargePersonVO> getDealPersonInfoBy(@RequestBody List<DealPersonQueryDTO> dtoList){

        return warningChargePersonService.getDealPersonInfoBy(dtoList);
    }

    @ApiOperation("查询症候群的处理人信息")
    @PostMapping("/{version}/pt/warningChargePerson/loadBySyndromeCode")
    public WarningChargePersonVO loadBySyndromeCode(@RequestBody DealPersonQueryDTO queryDTO){

        return warningChargePersonService.loadByDiseaseCode(queryDTO.getDiseaseCode(), WarningTypeProEnum.SYNDROME.getCode());
    }

    /**
     * 获取研判角色列表
     */
    @ApiOperation("获取研判角色列表")
    @GetMapping("/{version}/pt/warningChargePerson/getExpertTypeList")
    public List<DictVO> getExpertTypeList(){
        return warningChargePersonService.getExpertTypeList();
    }

    /**
     * 获取研判专家列表
     */
    @ApiOperation("获取研判专家列表")
    @GetMapping("/{version}/pt/warningChargePerson/getExpertList")
    public List<EcdOrgUserVO> getExpertList(@RequestParam(required = false) List<String> expertCategory){
        return warningChargePersonService.getExpertList(expertCategory);
    }

    /**
     * 获取应急值班人员
     */
    @ApiOperation("获取应急值班人员")
    @PostMapping("/{version}/pt/warningChargePerson/getDutyUserList")
    public List<DutyUserVO> getDutyUserList(@RequestBody List<AreaDTO> areaDTOS){
        return warningChargePersonService.getDutyUserList(areaDTOS);
    }

    /**
     * 获取应急预案
     */
    @ApiOperation("获取应急预案")
    @PostMapping("/{version}/pt/warningChargePerson/getEmergencyPlan")
    public PageData<EmergencyPlanVO> getEmergencyPlan(@RequestBody EmergencyPlanRequest request){
        return warningChargePersonService.getEmergencyPlan(request);
    }

    /**
     * 添加信号自动推送配置
     */
    @ApiOperation("添加/编辑信号自动推送配置")
    @PostMapping("/{version}/pt/warningChargePerson/saveOrUpdateAutoPushRule")
    public void saveAutoPushRule(@RequestBody List<SignalPushRuleSaveDTO> inputs, @RequestParam String loginUserId) {
        warningChargePersonService.saveOrUpdateAutoPushRule(inputs);
    }

    /**
     * 手动推送规则
     */
    @ApiOperation("根据推送规则添加到消息提醒")
    @PostMapping("/{version}/pt/warningChargePerson/savePushRule")
    public Integer savePushRule(@RequestBody List<SignalPushRuleDTO> inputs) {
        return warningChargePersonService.savePushRule(inputs);
    }
    /**
     * 查询信号自动推送配置
     */
    @ApiOperation("查询信号自动推送配置")
    @PostMapping("/{version}/pt/warningChargePerson/getSignalPushRule")
    public List<TbCdcmrSignalPushConfiguration> getSignalPushRule(@RequestBody List<SignalPushRuleDTO> inputs) {
        return warningChargePersonService.getSignalPushRule(inputs);
    }
    /**
     * 停止信号任务消息提醒
     */
    @ApiOperation("停止信号任务消息提醒")
    @PostMapping("/{version}/pt/warningChargePerson/stopSignalPushMessage")
    public Integer stopSignalPushRule(@RequestBody List<SignalPushRuleDTO> dtos) {
        return warningChargePersonService.stopSignalPushRuleMessage(dtos);
    }
}
