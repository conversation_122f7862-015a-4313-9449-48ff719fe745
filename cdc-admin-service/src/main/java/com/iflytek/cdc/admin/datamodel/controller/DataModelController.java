package com.iflytek.cdc.admin.datamodel.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormValue;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelFormQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelValueDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelFormConfigVO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelListVO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelVersionHistoryVO;
import com.iflytek.cdc.admin.datamodel.model.vo.ModelFormFieldsVO;
import com.iflytek.cdc.admin.datamodel.model.vo.ModelFormListVO;
import com.iflytek.cdc.admin.datamodel.service.DataModelExtendService;
import com.iflytek.cdc.admin.datamodel.service.DataModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据模型
 * 包含表单配置
 * */
@RestController
@Api(tags = "数据模型 & 表单配置")
public class DataModelController {

    @Resource
    private DataModelService dataModelService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private DataModelExtendService dataModelExtendService;

    @PostMapping("/pt/{version}/dataModel/editDataModel")
    @ApiOperation("编辑（新增or修改）数据模型")
    public Map<String, String> editDataModel(@RequestBody DataModelValueDTO dto,
                                             @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        return dataModelService.editDataModel(dto, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/getDataModelList")
    @ApiOperation("查询数据模型列表")
    public PageInfo<DataModelListVO> getDataModelList(@RequestParam(required = false) String status,
                                                      @RequestParam(required = false) String keyWord,
                                                      @RequestParam(required = false) String modelId,
                                                      @RequestParam(defaultValue = "1") Integer pageIndex,
                                                      @RequestParam(defaultValue = "20") Integer pageSize) {

        return dataModelService.getDataModelList(status, keyWord, modelId, pageIndex, pageSize);
    }

    @PostMapping("/pt/{version}/dataModel/getDataModelListByDto")
    @ApiOperation("查询数据模型列表使用dto")
    public PageInfo<DataModelListVO> getDataModelListByDto( @RequestParam(defaultValue = "1") Integer pageIndex,
                                                            @RequestParam(defaultValue = "20") Integer pageSize,
                                                            @RequestBody DataModelValueDTO dto) {

        return dataModelService.getDataModelList(dto, pageIndex, pageSize);
    }

    @GetMapping("/pt/{version}/dataModel/publishDataModel")
    @ApiOperation("发布数据模型")
    public void publishDataModel(@RequestParam String modelId,
                                 @RequestParam String modelVersionId,
                                 @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        dataModelService.publishDataModel(modelId, modelVersionId, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/updateDataModel")
    @ApiOperation("作废数据模型")
    public void updateDataModel(@RequestParam String modelVersionId,
                                @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        dataModelService.updateDataModel(modelVersionId, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/deleteDataModel")
    @ApiOperation("删除数据模型")
    public void deleteDataModel(@RequestParam String modelVersionId,
                                @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        dataModelService.deleteDataModel(modelVersionId, loginUserName);
    }

    @PostMapping("/pt/{version}/dataModel/getModelFormList")
    @ApiOperation("查询数据模型的 - 表单列表")
    public PageInfo<ModelFormListVO> getModelFormList(@RequestBody DataModelFormQueryDTO dto) {

        return dataModelService.getModelFormList(dto);
    }

    @GetMapping("/pt/{version}/dataModel/getDataModelVersionHistory")
    @ApiOperation("查询数据模型 - 历史版本列表")
    public PageInfo<DataModelVersionHistoryVO> getDataModelVersionHistory(@RequestParam String modelId,
                                                                          @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                          @RequestParam(defaultValue = "10") Integer pageSize) {

        return dataModelService.getDataModelVersionHistory(modelId, pageIndex, pageSize);
    }

    @PostMapping("/pt/{version}/dataModel/editModelForm")
    @ApiOperation("数据模型 - 表单编辑（新增、编辑、删除）")
    public String editModelForm(@RequestBody TbCdcdmDataModelForm tbCdcdmDataModelForm,
                              @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        return dataModelService.editModelForm(tbCdcdmDataModelForm, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/getFormInfoByFormId")
    @ApiOperation("根据表单id查询表单配置")
    public TbCdcdmDataModelForm getFormInfoByFormId(@RequestParam String modelFormId) {

        return dataModelService.getFormInfoByFormId(modelFormId);
    }

    @PostMapping("/pt/{version}/dataModel/saveModelFormData")
    @ApiOperation("数据模型 - 表单 - 数据存储")
    public void saveModelFormData(@RequestBody TbCdcdmDataFormValue tbCdcdmDataFormValue,
                                  @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        dataModelService.saveModelFormData(tbCdcdmDataFormValue, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/getDataModelFormsByModelId")
    @ApiOperation("根据数据模型的id 查看该数据模型 已发布版本的表单列表")
    public DataModelFormConfigVO getDataModelFormsByModelId(@RequestParam String modelId) {

        return dataModelService.getDataModelFormsByModelId(modelId);
    }

    @GetMapping("/pt/{version}/dataModel/getModelFormData")
    @ApiOperation("查看 数据模型 表单 的数据")
    public TbCdcdmDataFormValue getModelFormData(@RequestParam String id) {

        return dataModelService.getModelFormData(id);
    }

    @PostMapping("/pt/{version}/dataModel/editFormSequence")
    @ApiOperation("数据模型 - 模型中表单展示的顺序编辑")
    public void editFormSequence(@RequestBody List<TbCdcdmDataModelForm> tbCdcdmDataModelForms,
                                 @RequestParam String loginUserId) {

        String loginUserName = uapServiceApi.getUser(loginUserId).getName();
        dataModelService.editFormSequence(tbCdcdmDataModelForms, loginUserName);
    }

    @GetMapping("/pt/{version}/dataModel/getModelFormFields")
    @ApiOperation("查询数据模型中表单配置的字段（数据模型 - 表单 - 字段）")
    public List<ModelFormFieldsVO> getModelFormFields() {

        return dataModelService.getModelFormFields();
    }

    @GetMapping("/pt/{version}/dataModel/allDataModelList")
    @ApiOperation("查询数据模型列表")
    public List<DataModelListVO> allDataModelList(String status) {

        return dataModelService.allDataModelList(status);
    }

    @PostMapping("/pt/{version}/dataModel/upload")
    @ApiOperation("上传数据模型")
    public void uploadFile(@RequestParam MultipartFile file) {

        dataModelService.uploadFile(file, "admin");
    }

    @GetMapping("/pt/{version}/dataModel/loadErModelByModelId")
    @ApiOperation("查询er模型")
    public TbCdcdmEntityErModel loadErModelByModelId(@RequestParam String modelId){
        return dataModelExtendService.loadByModelId(modelId);
    }

    @GetMapping("/pt/{version}/dataModel/updatePublishDataModel")
    @ApiOperation("更新已发布数据模型的状态")
    public void updatePublishDataModel(@RequestParam String modelId,
                                       @RequestParam String modelVersionId,
                                       @RequestParam String status){

        dataModelService.updatePublishDataModel(modelId, modelVersionId, status);
    }

    @GetMapping("pt/{version}/dataModel/getModelFormByConfigInfo")
    @ApiOperation("根据configInfo查询表单信息")
    public List<TbCdcdmDataModelForm> getModelFormByConfigInfo(@RequestParam String configInfo) {

        return dataModelService.getModelFormByConfigInfo(configInfo);
    }

}
