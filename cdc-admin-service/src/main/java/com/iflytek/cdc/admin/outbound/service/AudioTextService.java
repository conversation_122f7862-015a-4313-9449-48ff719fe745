package com.iflytek.cdc.admin.outbound.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.outbound.mapper.CaseAudioMapper;
import com.iflytek.cdc.admin.util.DraftWithOrigin;
import com.iflytek.cdc.admin.util.EncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.WebSocket;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * 实时转写调用demo
 * 此demo只是一个简单的调用示例，不适合用到实际生产环境中
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class AudioTextService {
    @Value("${sound.appid:d8ee43b3}")
    private String APPID = "d8ee43b3";
    @Value("${sound.secretkey:40fd977aef6441c336280304e06fa17b}")
    private String SECRET_KEY;
    @Value("${sound.host:}")
    private String HOST;
    @Value("${sound.smooth:false}")
    private Boolean smooth;
    // 每次发送的数据大小 1280 字节
    private static final int CHUNCKED_SIZE = 1280;

    @Resource
    private CaseAudioMapper caseAudioMapper;

    @Async("asyncExecutor")
    public void getAudioText(MultipartFile file, String audioId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd HH:mm:ss.SSS");
        String BASE_URL = "ws://" + HOST;
        String ORIGIN = "http://" + HOST;
        try (InputStream inputStream = file.getInputStream()) {
            while (true) {
                URI url = new URI(BASE_URL + getHandShakeParams(APPID, SECRET_KEY));
                DraftWithOrigin draft = new DraftWithOrigin(ORIGIN);
                CountDownLatch handshakeSuccess = new CountDownLatch(1);
                CountDownLatch connectClose = new CountDownLatch(1);
                MyWebSocketClient client = new MyWebSocketClient(url, draft, handshakeSuccess, connectClose, audioId, smooth);
                client.connect();
                int i = 0;
                while (!client.getReadyState().equals(WebSocket.READYSTATE.OPEN)) {
                    log.info(getCurrentTimeStr() + "\t连接中");
                    Thread.sleep(1000);
                    i++;
                    if (i > 4) {
                        break;
                    }
                }
                //4秒都没连上 直接不再获取
                if (i > 4) {
                    log.error("4秒内未握手成功，放弃此次转写任务");
                    break;
                }
                // 等待握手成功
                handshakeSuccess.await();
                log.info(sdf.format(new Date()) + " 开始发送音频数据");
                // 发送音频
                byte[] bytes = new byte[CHUNCKED_SIZE];
                try {
                    int len = -1;
                    long lastTs = 0;
                    while ((len = inputStream.read(bytes)) != -1) {
                        if (len < CHUNCKED_SIZE) {
                            send(client, bytes = Arrays.copyOfRange(bytes, 0, len));
                            break;
                        }
                        long curTs = System.currentTimeMillis();
                        if (lastTs == 0) {
                            lastTs = System.currentTimeMillis();
                        }
                        send(client, bytes);
                        // 每隔40毫秒发送一次数据
                        Thread.sleep(10);
                    }
                    // 发送结束标识
                    send(client, "{\"end\": true}".getBytes());
                    log.info(getCurrentTimeStr() + "\t发送结束标识完成");
                    Thread.sleep(3000);
                    // 音频解析文本
                    if (CollUtil.isNotEmpty(client.getTextList())) {
                        caseAudioMapper.updateAudioFinalTextById(JSONObject.toJSONString(client.getTextList()), audioId);
                    }
                } catch (Exception e) {
                    log.error("语音转写异常", e);
                }
                // 等待连接关闭
                connectClose.await();
                break;
            }
        } catch (Exception e) {
            log.error("尝试获取音频转写结果失败", e);
        }
    }

    public String getHandShakeUrlAndParams(){
        return "ws://" + HOST + getHandShakeParams(APPID, SECRET_KEY);
    }

    // 生成握手参数
    public static String getHandShakeParams(String appId, String secretKey) {
        String ts = System.currentTimeMillis() / 1000 + "";
        String signa = "";
        try {
            signa = EncryptUtil.HmacSHA1Encrypt(EncryptUtil.MD5(appId + ts), secretKey);
            return "?appid=" + appId + "&ts=" + ts + "&signa=" + URLEncoder.encode(signa, "UTF-8");
        } catch (Exception e) {
            log.error("语音转写异常", e);
        }
        return "";
    }

    public static void send(WebSocketClient client, byte[] bytes) {
        if (client.isClosed()) {
            throw new RuntimeException("client connect closed!");
        }
        client.send(bytes);
    }

    public static String getCurrentTimeStr() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(new Date());
    }

    @Slf4j
    public static class MyWebSocketClient extends WebSocketClient {

        private CountDownLatch handshakeSuccess;
        private CountDownLatch connectClose;

        private List<JSONObject> textList = new ArrayList<>();
        private String audioId;
        private Boolean smooth;

        public List<JSONObject> getTextList() {
            return textList;
        }

        public MyWebSocketClient(URI serverUri, Draft protocolDraft, CountDownLatch handshakeSuccess, CountDownLatch connectClose, String audioId, Boolean smooth) {
            super(serverUri, protocolDraft);
            this.handshakeSuccess = handshakeSuccess;
            this.connectClose = connectClose;
            if (serverUri.toString().contains("wss")) {
                trustAllHosts(this);
            }
            this.audioId = audioId;
            this.smooth = smooth;
        }

        @Override
        public void onOpen(ServerHandshake handshake) {
            log.info(getCurrentTimeStr() + "\t连接建立成功！");
        }

        @Override
        public void onMessage(String msg) {
            //log.info("原始数据："+msg);
            JSONObject msgObj = JSON.parseObject(msg);
            String action = msgObj.getString("action");
            if (Objects.equals("started", action)) {
                // 握手成功
                log.info(getCurrentTimeStr() + "\t握手成功！sid: " + msgObj.getString("sid"));
                handshakeSuccess.countDown();
            } else if (Objects.equals("", action)) {
                // 转写结果
                JSONObject st = msgObj.getJSONObject("data").getJSONObject("cn").getJSONObject("st");
                if (st.getString("type").equals("0")) {
                    JSONArray rtList = st.getJSONArray("rt");
                    rtList.forEach(rt -> {
                        JSONObject rtItem = (JSONObject) rt;
                        JSONArray wsList = rtItem.getJSONArray("ws");
                        wsList.forEach(ws -> {
                            JSONObject wsItem = (JSONObject) ws;
                            wsItem.getJSONArray("cw").forEach(cw -> {
                                JSONObject cwItem = (JSONObject) cw;
                                //去掉顺滑词
                                String wp = cwItem.getString("wp");
                                if(smooth && "s".equalsIgnoreCase(wp)){
                                    return;
                                }
                                if (cwItem.getInteger("rl") != 0
                                       // && textList.stream().noneMatch(text -> text.getInteger("rl") == cwItem.getInteger("rl"))
                                ) {
                                    //新增一个对话人和句子
                                    JSONObject o = new JSONObject();
                                    o.put("rl", cwItem.getInteger("rl"));
                                    o.put("speaker", String.valueOf(cwItem.getInteger("rl")));
                                    o.put("bg", st.getString("bg"));
                                    o.put("ed", st.getString("ed"));
                                    o.put("paragraph", cwItem.getString("w"));
                                    textList.add(o);
                                }else{
                                    //往前追加
                                    if (!textList.isEmpty()) {
                                        String pre = textList.get(textList.size() - 1).getString("paragraph");
                                        textList.get(textList.size() - 1).put("paragraph", pre + cwItem.getString("w"));
                                    }
                                }

                                /*if (cwItem.getInteger("rl") == 0) {
                                    //还是之前人说的话 往前追加
                                    if (!textList.isEmpty()) {
                                        String pre = textList.get(textList.size() - 1).getString("paragraph");
                                        textList.get(textList.size() - 1).put("paragraph", pre + cwItem.getString("w"));
                                    }
                                }*/
                            });
                        });
                    });


                }

            } else if (Objects.equals("error", action)) {

            }
        }

        @Override
        public void onError(Exception e) {
            log.info(getCurrentTimeStr() + "\t连接发生错误：" + e.getMessage() + ", " + new Date());
        }

        @Override
        public void onClose(int arg0, String arg1, boolean arg2) {
            log.info(getCurrentTimeStr() + "\t链接关闭");
            connectClose.countDown();
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            try {
                log.info(getCurrentTimeStr() + "\t服务端返回：" + new String(bytes.array(), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("语音转写异常", e);
            }
        }

        public void trustAllHosts(MyWebSocketClient appClient) {
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[]{};
                }

                @Override
                public void checkClientTrusted(X509Certificate[] arg0, String arg1)  {

                }

                @Override
                public void checkServerTrusted(X509Certificate[] arg0, String arg1)  {

                }
            }};

            try {
                SSLContext sc = SSLContext.getInstance("TLS");
                sc.init(null, trustAllCerts, new java.security.SecureRandom());
                appClient.setSocket(sc.getSocketFactory().createSocket());
            } catch (Exception e) {
                log.error("语音转写异常", e);
            }
        }
    }

    // 把转写结果解析为句子
    public static String getContent(String message) {
        StringBuffer resultBuilder = new StringBuffer();
        try {
            JSONObject messageObj = JSON.parseObject(message);
            JSONObject cn = messageObj.getJSONObject("cn");
            JSONObject st = cn.getJSONObject("st");
            JSONArray rtArr = st.getJSONArray("rt");
            for (int i = 0; i < rtArr.size(); i++) {
                JSONObject rtArrObj = rtArr.getJSONObject(i);
                JSONArray wsArr = rtArrObj.getJSONArray("ws");
                for (int j = 0; j < wsArr.size(); j++) {
                    JSONObject wsArrObj = wsArr.getJSONObject(j);
                    JSONArray cwArr = wsArrObj.getJSONArray("cw");
                    for (int k = 0; k < cwArr.size(); k++) {
                        JSONObject cwArrObj = cwArr.getJSONObject(k);
                        String wStr = cwArrObj.getString("w");
                        resultBuilder.append(wStr);
                    }
                }
            }
        } catch (Exception e) {
            return message;
        }

        return resultBuilder.toString();
    }
}