package com.iflytek.cdc.admin.vo.brief;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;


@Data
public class TemplateExcelVo {

    @ExcelProperty("模板ID")
    private String id;

    @ExcelProperty("统计周期")
    private String statisticsCycleDesc;

    @ExcelProperty("模板标题")
    private String title;

    @ExcelProperty("分析对象")
    private String analysisObjectName;

    @ExcelProperty("最近更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ExcelProperty("最近更新人")
    private String updatorName;

}
