package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo;
import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface IMasterDataService {

    /**
     * 查询主数据的树形结构
     * */
    List<TreeNode> getMasterData(MasterDataQueryDTO dto);

    /**
     * 查询主数据的树形结构
     * */
    TreeNode getMasterDataByRootId(String rootId);

    /**
     * 获取主数据基础信息以及其配置信息
     * */
    <T extends CommonMasterData> T getMasterDataConfigInfo(MasterDataQueryDTO dto);

    /**
     * 新增主数据
     * */
    String addMasterData(MasterDataRecordDTO dto);

    /**
     * 编辑疾病信息
     * */
    void editMasterDataBaseInfo(MasterDataEditRecordDTO dto);

    /**
     * 删除疾病信息
     * */
    void deleteMasterDataBaseInfo(MasterDataDeleteDTO dto);

    /**
     * 批量同步主数据
     * */
    void syncMasterData();

    /**
     * 上传主数据 - 用于初始化数据
     * */
    void uploadFile(MultipartFile file);

    /**
     * 通过code获取疾病信息
     * */
    CommonMasterData getDiseaseInfoByCode(String masterDataCode);

}
