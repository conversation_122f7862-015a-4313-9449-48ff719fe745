package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.IllnessFileApprovalAuthDto;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFileApprovalAuth;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessRecordAuthority;
import com.iflytek.cdc.admin.enums.AuthClassEnum;
import com.iflytek.cdc.admin.model.mr.dto.EDRAuthQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;
import com.iflytek.cdc.admin.service.province.EDRAuthService;
import com.iflytek.cdc.admin.service.province.IllnessFileApprovalAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "edr - 权限管理")
@RequestMapping("/pt/{version}/edr/auth")
@RestController
public class EDRAuthController {

    @Resource
    private EDRAuthService edrAuthService;

    @Resource
    private IllnessFileApprovalAuthService illnessFileApprovalAuthService;

    @PostMapping("/listAuthBy")
    @ApiOperation("查看权限列表")
    public PageInfo<EDRAuthInfoVO> listAuthBy(@RequestBody EDRAuthQueryDTO dto) {

        return edrAuthService.listAuthBy(dto);
    }

    @PostMapping("/editEDRAuth")
    @ApiOperation("编辑edr权限")
    public void editEDRAuth(@RequestBody TbCdcmrIllnessRecordAuthority recordAuthority) {

        edrAuthService.editEDRAuth(recordAuthority);
    }
    
    @GetMapping("/getUserAuthInfo")
    @ApiOperation("根据登录人 获取用户操作权限")
    public Map<String, List<TbCdcmrIllnessRecordAuthority>> getUserAuthInfo(@RequestParam String userId) {

        return edrAuthService.getUserAuthInfo(userId);
    }

    @ApiOperation("常量获取")
    @GetMapping("/getConstants")
    public Object getConstants() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("authClassEnum", AuthClassEnum.mapValues());
        return resultMap;
    }

    @ApiOperation("/校验查看权限")
    @PostMapping("/validViewAuth")
    public EDRAuthResultVO validViewAuth(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        return edrAuthService.validViewAuth(loginUserId, permissionRecord);
    }

    @ApiOperation("/增加权限申请记录")
    @PostMapping("/addAuthApplyRecord")
    public EDRAuthResultVO addAuthApplyRecord(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        edrAuthService.addAuthApplyRecord(loginUserId, permissionRecord);
        return EDRAuthResultVO.success();
    }

    @ApiOperation("/修改权限申请记录")
    @PostMapping("/changeAuthStatus")
    public EDRAuthResultVO changeAuthStatus(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        edrAuthService.changeAuthStatus(loginUserId, permissionRecord);
        return EDRAuthResultVO.success();
    }

    @ApiOperation("/获取权限申请记录")
    @PostMapping("/getAuthApplyRecordList")
    public PageInfo<TbCdcmrIllnessFilePermissionRecord>  getAuthApplyRecordList(@RequestParam String loginUserId, @Validated @RequestBody TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        return edrAuthService.getAuthApplyRecordList(loginUserId, permissionRecord);
    }


    @ApiOperation("/获取权限申请记录详情")
    @GetMapping("/getPermissionRecordInfoById/{id}")
    public TbCdcmrIllnessFilePermissionRecord getPermissionRecordInfoById(@RequestParam String loginUserId,@PathVariable String id) {
        return edrAuthService.getPermissionRecordInfoById(id);
    }

    @ApiOperation("/保存审批权限")
    @PostMapping("/saveBatchApprovalAuth")
    public EDRAuthResultVO saveBatchApprovalAuth(@RequestParam String loginUserId, @Validated @RequestBody List<TbCdcmrIllnessFileApprovalAuth> list) {
        illnessFileApprovalAuthService.saveBatchApprovalAuth(loginUserId, list);
        return EDRAuthResultVO.success();
    }

    @ApiOperation("/根据机构id审批权限")
    @GetMapping("/getApprovalAuthByOrgId/{orgId}")
    public List<TbCdcmrIllnessFileApprovalAuth> getApprovalAuthByOrgId(@RequestParam String loginUserId, @PathVariable String orgId) {
        return illnessFileApprovalAuthService.getApprovalAuthByOrgId(orgId);
    }

    @ApiOperation("/修改审批权限")
    @PostMapping("/updateApprovalAuth")
    public EDRAuthResultVO updateApprovalAuth(@RequestParam String loginUserId, @Validated @RequestBody IllnessFileApprovalAuthDto authDto) {
        illnessFileApprovalAuthService.updateApprovalAuth(loginUserId, authDto);
        return EDRAuthResultVO.success();
    }


}
