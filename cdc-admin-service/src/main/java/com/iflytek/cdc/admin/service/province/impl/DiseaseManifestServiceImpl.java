package com.iflytek.cdc.admin.service.province.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.DiseaseManifestationEnum;
import com.iflytek.cdc.admin.enums.ManifestClassifyEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseManifestInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseManifestRuleMapper;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseManifestQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiseaseManifestRulesVO;
import com.iflytek.cdc.admin.service.province.DiseaseManifestService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class DiseaseManifestServiceImpl implements DiseaseManifestService {

    @Resource
    private TbCdcmrDiseaseManifestInfoMapper manifestInfoMapper;

    @Resource
    private TbCdcmrDiseaseManifestRuleMapper manifestRuleMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CommonUtils commonUtils;

    @Value("${generateStrLength:8}")
    private int length;

    private static final String TB_CDCMR_DISEASE_MANIFEST_INFO = "tb_cdcmr_disease_manifest_info";

    private static final String TB_CDCMR_DISEASE_MANIFEST_RULE = "tb_cdcmr_disease_manifest_rule";

    @Override
    public PageInfo<TbCdcmrDiseaseManifestInfo> getDiseaseManifestInfoList(DiseaseManifestQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcmrDiseaseManifestInfo> manifestInfoList = manifestInfoMapper.getDiseaseManifestInfoList(dto);
        manifestInfoList.forEach(e -> {
            e.setManifestClassifyName(Optional.ofNullable(ManifestClassifyEnum.getDescByCode(e.getManifestClassify())).orElse(""));
            e.setDiseaseManifestationName(Optional.ofNullable(DiseaseManifestationEnum.getDescByCode(e.getDiseaseManifestation())).orElse(""));
        });
        return new PageInfo<>(manifestInfoList);
    }

    @Override
    public void addDiseaseManifestInfo(TbCdcmrDiseaseManifestInfo manifestInfo) {

        manifestInfoMapper.insert(this.createManifestInfoEntity(manifestInfo));
    }

    private TbCdcmrDiseaseManifestInfo createManifestInfoEntity(TbCdcmrDiseaseManifestInfo manifestInfo){

        //查询已有疾病的code
        List<String> manifestCodeList = manifestInfoMapper.getAllManifestCode();

        UapUserPo uapUserPo = USER_INFO.get();
        TbCdcmrDiseaseManifestInfo info = new TbCdcmrDiseaseManifestInfo();
        BeanUtils.copyProperties(manifestInfo, info);
        info.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_DISEASE_MANIFEST_INFO)));
        info.setManifestCode(commonUtils.generateSyndromeCode(length, manifestCodeList));
        info.setStatus(StatusEnum.STATUS_ON.getCode());
        info.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        info.setCreateTime(new Date());
        info.setUpdateTime(new Date());
        if(Objects.nonNull(uapUserPo)){
            info.setCreator(uapUserPo.getName());
            info.setCreatorId(uapUserPo.getId());
            info.setUpdater(uapUserPo.getName());
            info.setUpdaterId(uapUserPo.getId());
        }
        return info;
    }

    @Override
    public void updateDiseaseManifestInfo(TbCdcmrDiseaseManifestInfo manifestInfo) {

        UapUserPo uapUserPo = USER_INFO.get();
        TbCdcmrDiseaseManifestInfo info = new TbCdcmrDiseaseManifestInfo();
        BeanUtils.copyProperties(manifestInfo, info);
        info.setUpdateTime(new Date());
        if(Objects.nonNull(uapUserPo)){
            info.setUpdater(uapUserPo.getName());
            info.setUpdaterId(uapUserPo.getId());
        }
        manifestInfoMapper.update(info);
    }

    @Override
    public List<DiseaseManifestRulesVO> getDiseaseManifestRules(String manifestId) {

        List<DiseaseManifestRulesVO> manifestRulesVOList = new ArrayList<>();
        //查询疾病信息
        List<TbCdcmrDiseaseManifestInfo> manifestInfo = manifestInfoMapper.selectManifestInfoBy(manifestId);
        //查询监测症状对应的规则列表
        List<TbCdcmrDiseaseManifestRule> manifestRules = manifestRuleMapper.selectManifestRuleBy(manifestId);

        manifestInfo.forEach(elem -> {
            List<TbCdcmrDiseaseManifestRule> ruleList = manifestRules.stream()
                                                                     .filter(e -> elem.getId().equals(e.getManifestId()))
                                                                     .collect(Collectors.toList());
            DiseaseManifestRulesVO rulesVO = this.createManifestRulesVO(elem, ruleList);
            manifestRulesVOList.add(rulesVO);
        });
        return manifestRulesVOList;
    }

    private DiseaseManifestRulesVO createManifestRulesVO(TbCdcmrDiseaseManifestInfo manifestInfo, List<TbCdcmrDiseaseManifestRule> manifestRules){

        return DiseaseManifestRulesVO.builder()
                                     .id(manifestInfo.getId())
                                     .manifestCode(manifestInfo.getManifestCode())
                                     .manifestName(manifestInfo.getManifestName())
                                     .manifestClassify(manifestInfo.getManifestClassify())
                                     .diseaseManifestation(manifestInfo.getDiseaseManifestation())
                                     .manifestClassifyName(ManifestClassifyEnum.getDescByCode(manifestInfo.getManifestClassify()))
                                     .diseaseManifestationName(DiseaseManifestationEnum.getDescByCode(manifestInfo.getDiseaseManifestation()))
                                     .manifestRuleList(manifestRules)
                                     .build();
    }

    @Override
    @Transactional
    public void editDiseaseManifestRules(String diseaseManifestId, List<TbCdcmrDiseaseManifestRule> manifestRules) {

        UapUserPo uapUserPo = USER_INFO.get();
        //软删除该疾病下的所有规则
        manifestRuleMapper.updateByDiseaseManifestId(diseaseManifestId);

        String loginUserId = null;
        String loginUserName = null;
        if(uapUserPo != null){
            loginUserId = uapUserPo.getId();
            loginUserName = uapUserPo.getName();
        }
        for (TbCdcmrDiseaseManifestRule rule : manifestRules) {
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_DISEASE_MANIFEST_RULE));
            //非空为更新，为空则新增
            rule.setId(rule.getId() == null ? id : rule.getId());
            //状态默认开启，默认未删除
            rule.setStatus(rule.getStatus() == null ? StatusEnum.STATUS_ON.getCode() : rule.getStatus());
            rule.setDeleteFlag(rule.getDeleteFlag() == null ? DeleteFlagEnum.NO.getCode() : rule.getDeleteFlag());
            rule.setCreateTime(new Date());
            rule.setCreator(loginUserName);
            rule.setCreatorId(loginUserId);
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserName);
            rule.setUpdaterId(loginUserId);
        }
        if(CollectionUtils.isNotEmpty(manifestRules)) {
            manifestRuleMapper.batchInsert(manifestRules);
        }
    }
}
