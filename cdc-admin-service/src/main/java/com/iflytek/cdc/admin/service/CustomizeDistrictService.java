package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.AreaInfoVO;
import com.iflytek.cdc.admin.entity.TbCdcmrConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface CustomizeDistrictService {


    List<TbCdcmrCustomizeDistrict> getCustomizeDistrictWithoutData();

    String getCustomizeDistrictSwitch();

    void updateCustomizeDistrictSwitch(TbCdcmrConfig tbCdcmrConfig);

    void deleteCustomizeDistrict(String districtCode);

    List<String> getJsonDataNameList();

    String getJsonDataByName(String jsonName);

    void saveCustomizeDistrict(TbCdcmrCustomizeDistrict tbCdcmrCustomizeDistrict);

    TbCdcmrCustomizeDistrict getCustomizeDistrict(String districtCode);

     AreaInfoVO findAreaAndLevelByUser(String loginUserId);

}
