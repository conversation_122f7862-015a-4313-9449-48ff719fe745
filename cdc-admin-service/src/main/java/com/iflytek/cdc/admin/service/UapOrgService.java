package com.iflytek.cdc.admin.service;

import com.iflytek.zhyl.uap.usercenter.pojo.UapMdmDataOrgDetailDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapMdmDataOrgDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgTreeNode;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 机构树接口
 * <AUTHOR>
 * @date 2021/6/24 15:12
 **/
public interface UapOrgService {

    /**
     * 查询本机构及其下级机构的id集合
     * @param loginUserId
     * @return
     **/
    List<String> queryDownOrgId(String  loginUserId);

    /**
     * 查询本机构及其下级机构的code集合
     * @param loginUserId
     * @return
     **/
    List<String> queryDownOrgCode(String  loginUserId);

    UapOrgTreeNode getOrgTree(String loginUserId);

    UapOrgTreeNode getCdcAndDeptOrg(String loginUserId);

    UapMdmDataOrgDetailDto getByOrgId(String orgId);

    /**
     * 业务人员维护的机构配置
     */
    UapOrgTreeNode getOrgForBusinessPerson(String loginUserId);

    List<UapOrgTreeNode> getOrgTreeV5(String loginUserId);

}
