package com.iflytek.cdc.admin.datamodel.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据模型扩展属性表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcdm_data_model_extend")
@ApiModel("数据模型扩展属性表")
public class TbCdcdmDataModelExtend extends BaseEntity {

    @ApiModelProperty("er 模型id")
    private String entityErModelId;

    @ApiModelProperty("模型版本id")
    private String modelVersionId;

    @ApiModelProperty("模型id")
    private String modelId;
}
