package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.OrgChangeDto;
import com.iflytek.cdc.admin.dto.PlunOrgChangeDto;
import com.iflytek.cdc.admin.dto.ResponseResult;

/**
 * nifi机构树更新服务
 * <AUTHOR>
 * @Date 2021/10/20
 **/
public interface PlunOrgUpdateService {

    /**
     *  插件机构改动同步修改
     * @param orgChangeDto
     * return ResponseResult
     */
    ResponseResult orgChangeMonitor(PlunOrgChangeDto orgChangeDto);

}
