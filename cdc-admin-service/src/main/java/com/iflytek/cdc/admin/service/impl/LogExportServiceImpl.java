package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.LogExportReqDto;
import com.iflytek.cdc.admin.entity.TbCdcmrLogExport;
import com.iflytek.cdc.admin.mapper.TbCdcmrLogExportMapper;
import com.iflytek.cdc.admin.service.LogExportService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date :2023/8/3 11:07
 * @description:LogExportServiceImpl
 */
@Service
@Slf4j
public class LogExportServiceImpl implements LogExportService {
    @Resource
    private TbCdcmrLogExportMapper logExportMapper;
    @Autowired
    private BatchUidService batchUidService;


    @Override
    public int insert(LogExportReqDto reqDto) {

        TbCdcmrLogExport record = new TbCdcmrLogExport();
        record.setQueryUrl(reqDto.getQueryUrl());
        record.setQueryParam(reqDto.getQueryParam());
        record.setCreator(reqDto.getLoginUserId());
        record.setFileSize(0L);
        record.setStatus(Constants.STATUS_OFF);
        if (reqDto.getStatus() != null && Constants.STATUS_ON.equals(reqDto.getStatus())) {
            record.setStatus(Constants.STATUS_ON);
        }
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_log_export")));
        record.setCreateTime(new Date());

        reqDto.setId(record.getId());
        return logExportMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(TbCdcmrLogExport record) {
        return logExportMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public TbCdcmrLogExport selectByPrimaryKey(String id) {
        return logExportMapper.selectByPrimaryKey(id);
    }
}
