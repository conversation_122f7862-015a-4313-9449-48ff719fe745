package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.AreaInfoVO;
import com.iflytek.cdc.admin.entity.TbCdcmrConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizeDistrict;
import com.iflytek.cdc.admin.service.CustomizeDistrictService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class CustomizeDistrictController {

    @Resource
    CustomizeDistrictService customizeDistrictService;


    @ApiOperation("列表页：功能区轮廓列表查询")
    @GetMapping("/pt/v1/customizeDistrict/getCustomizeDistrictWithoutData")
    public List<TbCdcmrCustomizeDistrict> getCustomizeDistrictWithoutData() {
        return customizeDistrictService.getCustomizeDistrictWithoutData();
    }

    @ApiOperation("列表页：功能开关查询")
    @GetMapping("/pt/v1/customizeDistrict/getCustomizeDistrictSwitch")
    public String getCustomizeDistrictSwitch() {
        return customizeDistrictService.getCustomizeDistrictSwitch();
    }

    @ApiOperation("列表页：功能开关修改")
    @PostMapping("/pt/v1/customizeDistrict/updateCustomizeDistrictSwitch")
    public void updateCustomizeDistrictSwitch(@RequestBody TbCdcmrConfig tbCdcmrConfig, @RequestParam String loginUserId) {
        tbCdcmrConfig.setUpdaterName(loginUserId);
        customizeDistrictService.updateCustomizeDistrictSwitch(tbCdcmrConfig);
    }

    @ApiOperation("列表页：功能区轮廓根据districtCode删除")
    @PostMapping("/pt/v1/customizeDistrict/deleteCustomizeDistrict")
    public void deleteCustomizeDistrict(@RequestParam String districtCode) {
        customizeDistrictService.deleteCustomizeDistrict(districtCode);
    }

    @ApiOperation("详情页：查询链家所有区县名称字典")
    @GetMapping("/pt/v1/customizeDistrict/getJsonDataNameList")
    public List<String> getJsonDataNameList() {
        return customizeDistrictService.getJsonDataNameList();
    }

    @ApiOperation("详情页：查询某个链家区县json数据")
    @GetMapping("/pt/v1/customizeDistrict/getJsonDataByName")
    public String getJsonDataByName(@RequestParam String jsonName) {
        return customizeDistrictService.getJsonDataByName(jsonName);
    }

    @ApiOperation("详情页：保存功能区数据")
    @PostMapping("/pt/v1/customizeDistrict/saveCustomizeDistrict")
    public void saveCustomizeDistrict(@RequestBody TbCdcmrCustomizeDistrict tbCdcmrCustomizeDistrict) {
        customizeDistrictService.saveCustomizeDistrict(tbCdcmrCustomizeDistrict);
    }

    @ApiOperation("详情页：查询功能区数据")
    @GetMapping("/pt/v1/customizeDistrict/getCustomizeDistrict")
    public TbCdcmrCustomizeDistrict getCustomizeDistrict(@RequestParam String districtCode) {
        return customizeDistrictService.getCustomizeDistrict(districtCode);
    }

    @ApiOperation("公共查询：查询区划(取决于开关)")
    @GetMapping("/pt/v1/customizeDistrict/areaInfo")
    public AreaInfoVO areaInfo(@RequestParam String loginUserId) {
        return customizeDistrictService.findAreaAndLevelByUser(loginUserId);
    }
}
