package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.AnalysisTargetDto;
import com.iflytek.cdc.admin.dto.IndicatorQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimension;
import com.iflytek.cdc.admin.entity.TbCdcmrIndicator;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrIndicatorMapper;
import com.iflytek.cdc.admin.service.TbCdcmrIndicatorService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.PageInfoConverter;
import com.iflytek.cdc.admin.util.SplitterUtil;
import com.iflytek.cdc.admin.vo.CdcmrIndicatorVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TbCdcmrIndicatorServiceImpl implements TbCdcmrIndicatorService {
    @Resource
    private TbCdcmrIndicatorMapper tbCdcmrIndicatorMapper;
    @Resource
    private BatchUidService batchUidService;
    @Resource
    private UapServiceApi uapServiceApi;

    private void checkDuplicateIndicatorName(String name, String existedId) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        if (tbCdcmrIndicatorMapper.countByNameNorId(name, existedId) > 0) {
            throw new MedicalBusinessException("名称重复，请重新定义指标");
        }
    }

    @Override
    public CdcmrIndicatorVO create(CdcmrIndicatorVO input, String loginUserId) {
        this.checkDuplicateIndicatorName(input.getIndicatorName(), null);
        this.checkAnalysisTargetExist(input.getAnalysisTargetList());
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        TbCdcmrIndicator entity = voToEntity(input);

        entity.setId(String.valueOf(batchUidService.getUid(TbCdcmrIndicator.TABLE_NAME)));
        entity.setCreator(loginUserId);
        entity.setCreatorName(user.getName());
        entity.setCreateTime(new Date());
        entity.setUpdater(loginUserId);
        entity.setUpdaterName(user.getName());
        entity.setUpdateTime(new Date());
        if (StringUtils.isEmpty(entity.getIndicatorLabel())) {
            generateAndSetLabel(entity);
        }
        entity.setDeleteFlag(Integer.valueOf(DeleteFlagEnum.NO.getCode()));
        labelDuplicationCheck(entity);
        tbCdcmrIndicatorMapper.insert(entity);

        return entityToVo(entity);
    }

    @Override
    public CdcmrIndicatorVO load(String id) {
        TbCdcmrIndicator entity = tbCdcmrIndicatorMapper.load(id);
        return entityToVo(entity);
    }

    @Override
    public void update(CdcmrIndicatorVO input, String loginUserId) {
        this.checkDuplicateIndicatorName(input.getIndicatorName(), input.getId());
        this.checkAnalysisTargetExist(input.getAnalysisTargetList());

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        CdcmrIndicatorVO curr = load(input.getId());
        if (curr == null) {
            throw new MedicalBusinessException("未找到对应指标");
        }
        TbCdcmrIndicator entity = voToEntity(input);

        entity.setUpdater(loginUserId);
        entity.setUpdaterName(user.getName());
        entity.setUpdateTime(new Date());
        labelDuplicationCheck(entity);
        tbCdcmrIndicatorMapper.update(entity);
    }

    @Override
    public PageInfo<CdcmrIndicatorVO> pageList(IndicatorQueryDto queryDto) {
        PageHelper.startPage(queryDto.getPageIndex(), queryDto.getPageSize());
        List<TbCdcmrIndicator> tbCdcmrIndicators = tbCdcmrIndicatorMapper.pageList(queryDto);
        // mapper 返回的是继承了 ArrayList 的 Page 对象，这里需要特殊处理
        return PageInfoConverter.convertPerItem(new PageInfo<>(tbCdcmrIndicators), TbCdcmrIndicatorServiceImpl::entityToVo);
    }

    @Override
    public CdcmrIndicatorVO loadByLabel(String label) {
        TbCdcmrIndicator entity = tbCdcmrIndicatorMapper.loadByLabel(label);
        return entityToVo(entity);
    }

    @Override
    public List<CdcmrIndicatorVO> listByLabels(List<String> labels) {
        List<TbCdcmrIndicator> tbCdcmrIndicators = tbCdcmrIndicatorMapper.listByLabels(labels);
        return tbCdcmrIndicators.stream().map(TbCdcmrIndicatorServiceImpl::entityToVo).collect(Collectors.toList());
    }

    @Override
    public void delete(String id) {
        tbCdcmrIndicatorMapper.delete(id);
    }

    /**
     * 生成标签
     */
    private void generateAndSetLabel(TbCdcmrIndicator entity){
        Pair<Integer, String> pair = generateLabel();
        entity.setNum(pair.getKey());
        entity.setIndicatorLabel(pair.getValue());
    }

    /**
     * 生成标签
     */
    private Pair<Integer, String> generateLabel(){
        Integer num = tbCdcmrIndicatorMapper.loadLastNum();
        if (num == null){
            num = 0;
        }
        String label = String.format("1%04d", ++num);
        while (loadByLabel(label) != null){
            num ++;
            label = String.format("1%04d", num);
        }
        return Pair.of(num, label);

    }

    /**
     * label重复校验
     */
    private void labelDuplicationCheck(TbCdcmrIndicator entity){
        TbCdcmrIndicator curr  = tbCdcmrIndicatorMapper.loadByLabel(entity.getIndicatorLabel());
        if (curr == null){
            return;
        }
        if (!curr.getId().equals(entity.getId())){
            throw new MedicalBusinessException("标签重复，请重新定义标签");
        }
    }

    public static CdcmrIndicatorVO entityToVo(TbCdcmrIndicator entity) {
        if (entity == null) {
            return null;
        }
        CdcmrIndicatorVO vo = new CdcmrIndicatorVO();
        BeanUtils.copyProperties(entity, vo);

        if (entity.getIndicatorAliasName() != null) {
            List<String> aliasNames = SplitterUtil.split(Constants.COMMA, entity.getIndicatorAliasName());
            vo.setIndicatorAliasNames(aliasNames);
        }

        CdcmrIndicatorVO.ApplicableDisease ad = new Gson().fromJson(entity.getApplicableDisease(), CdcmrIndicatorVO.ApplicableDisease.class);
        vo.setApplicableDisease(ad);

        String analysisTarget = entity.getAnalysisTarget();
        if (StrUtil.isNotBlank(analysisTarget)) {
            try {
                List<AnalysisTargetDto> analysisTargetList = new Gson().fromJson(
                        entity.getAnalysisTarget(),
                        new TypeToken<List<AnalysisTargetDto>>() {
                        }.getType()
                );
                vo.setAnalysisTargetList(analysisTargetList);
            } catch (JsonSyntaxException e) {
                vo.setAnalysisTargetList(null);
            }

        }

        return vo;
    }

    private static TbCdcmrIndicator voToEntity(CdcmrIndicatorVO vo) {
        if (vo == null) {
            return null;
        }
        TbCdcmrIndicator entity = new TbCdcmrIndicator();
        BeanUtils.copyProperties(vo, entity);

        // 将 List<String> 转换为逗号分隔的字符串
        if (vo.getIndicatorAliasNames() != null) {
            String aliasNames = String.join(Constants.COMMA, vo.getIndicatorAliasNames());
            entity.setIndicatorAliasName(aliasNames);
        }

        // 将 ApplicableDisease 对象转换为 JSON 字符串
        String applicableDiseaseJson = new Gson().toJson(vo.getApplicableDisease());
        entity.setApplicableDisease(applicableDiseaseJson);

        // 将 AnalysisTargetList 对象转换为 JSON 字符串
        List<AnalysisTargetDto> analysisTargetList = vo.getAnalysisTargetList();
        if (analysisTargetList != null && !analysisTargetList.isEmpty()){
            String analysisTargetJson = new Gson().toJson(analysisTargetList);
            entity.setAnalysisTarget(analysisTargetJson);
        }

        return entity;
    }


    private void checkAnalysisTargetExist(List<AnalysisTargetDto> analysisTargetList) {

        if (analysisTargetList == null || analysisTargetList.isEmpty()){
            throw new MedicalBusinessException("缺少分析对象");
        }

    }
}
