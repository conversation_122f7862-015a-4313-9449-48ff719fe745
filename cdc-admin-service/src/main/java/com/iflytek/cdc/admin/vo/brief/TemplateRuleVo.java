package com.iflytek.cdc.admin.vo.brief;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.enums.StatusEnum;

import lombok.Data;

/**
 * 模板规则vo
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_template_rule")
public class TemplateRuleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String id;
    /**
     * 模板id
     */
    private String templateId;
    /**
     * 起始时间
     */
    private String startTime;
    /**
     * 截止时间
     */
    private String endTime;
    /**
     * 是否重复
     */
    private String repeatFlag;
    /**
     * 自定义时间（逗号分割）
     */
    private String customTime;
    /**
     * 1启用0禁用
     */
    private String status = StatusEnum.STATUS_ON.getCode();
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 区名称
     */
    private String districtName;
    /**
     * 业务类型：传染病；XX症候群
     */
    private String businessType;
    /**
     * 修改者ID
     */
    private String creatorId;
    /**
     * 修改者
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改者ID
     */
    private String updatorId;
    /**
     * 修改者
     */
    private String updatorName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 标题
     */
    private String title;
    /**
     * 统计周期
     */
    private String statisticsCycle;


    /**
     * 报告类型
     */
    private String briefReportType;
}
