package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.entity.BaseEntity;
import com.iflytek.cdc.admin.service.ICdcService;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

public class CdcServiceBaseImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements ICdcService<T> {

    public Gson gson = new Gson();

    @Autowired
    private BatchUidService batchUidService;

    /**
     * 新增数据
     *
     * @param entity 实例对象
     * @return 实例对象
     */
    @Transactional
    public T create(T entity){
        beforeCreate(entity);
        save(entity);
        afterCreate(entity);
        return entity;
    }

    /**
     * 更新数据
     *
     * @param entity 实例对象
     * @return 实例对象
     */
    @Transactional
    public T update(T entity){
        getById(entity.getId());
        beforeUpdate(entity);
        updateById(entity);
        afterUpdate(entity);
        return entity;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     */
    @Transactional
    public void deleteById(String id){
        beforeDelete(id);
        removeById(id);
        afterDelete(id);
    }

    @Override
    public T loadCascade(String id) {
        T t = getById(id);
        fulfillChildProperty(t);
        return t;
    }

    /**
     * 创建前操作
     */
    public void beforeCreate(T entity){
        fulfillCreateProperty(entity);
    }

    /**
     * 修改前操作
     */
    public void beforeUpdate(T entity){
        fulfillUpdateProperty(entity);
    }

    /**
     * 删除前操作
     */
    public void beforeDelete(String id){}

    /**
     * 创建后操作
     */
    public void afterCreate(T entity){}

    /**
     * 修改后操作
     */
    public void afterUpdate(T entity){}

    /**
     * 删除后操作
     */
    public void afterDelete(String id){}

    /**
     * 查重
     * @param entity 输入的实体
     * @param errorMessage 报错信息
     * @param columns 需要判断的列
     */
    @SafeVarargs
    public final void duplicate(T entity,
                                String errorMessage,
                                SFunction<T, ?>... columns){
        if (columns.length == 0){
            throw new MedicalBusinessException("未传入列");
        }
        LambdaQueryChainWrapper<T> tLambdaQueryChainWrapper = lambdaQuery();
        for (SFunction<T, ?> column : columns){
            tLambdaQueryChainWrapper = tLambdaQueryChainWrapper.eq(column, column.apply(entity));
        }
        T one = tLambdaQueryChainWrapper
                .select(T::getDeleteFlag, T ::getId)
                .one();
        if (one != null && !one.getId().equals(entity.getId())){
            throw new MedicalBusinessException(errorMessage);
        }
    }
    /**
     * 根据id查询
     */
    public T getById(Serializable id){
        if (id == null){
            throw new MedicalBusinessException("id 不能为空");
        }
        LambdaQueryWrapper<T> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(T::getId, id);
        T curr = getOne(queryWrapper);
        if (curr == null){
            throw new MedicalBusinessException("数据错误，请联系管理员");
        }
        return curr;
    }

    public void fulfillChildProperty(T t){

    }

    /**
     * 根据id 批量查询
     * @param ids 主键id集合
     * @return 实体数据
     */
    public List<T> listByIds(List<String> ids){
        LambdaQueryWrapper<T> tLambdaQueryChainWrapper = lambdaQueryWrapper();
        tLambdaQueryChainWrapper.in(T :: getId, ids);
        return list(tLambdaQueryChainWrapper);
    }

    /**
     * 批量删除
     * @param ids 主键id集合
     */
    public void batchDeleteByIds(Set<String> ids){
        LambdaUpdateChainWrapper<T> update = lambdaUpdate();
        update.set(T :: getDeleteFlag, DeleteFlagEnum.YES.getCode()).in(T :: getId, ids).update();
    }

    @Override
    public boolean saveBatch(Collection<T> entityList, int batchSize) {
        entityList.forEach(this::fulfillCreateProperty);
        return super.saveBatch(entityList, batchSize);
    }

    @Override
    public boolean updateBatchById(Collection<T> entityList, int batchSize) {
        entityList.forEach(this::fulfillUpdateProperty);
        return super.updateBatchById(entityList, batchSize);
    }


    /**
     * 根据外键保存
     */
    public <F extends BaseEntity> void batchSaveByForeignKey(F foreignEntity,
                                                             Function<F, ?> foreignKeyValueGetter,
                                                             SFunction<T, ?> foreignKeyGetter,
                                                             BiConsumer<F, T> fulfillForeignValueConsumer,
                                                             List<T> entities){
        batchSaveByForeignKey(foreignEntity, foreignKeyValueGetter, foreignKeyGetter, fulfillForeignValueConsumer, null, null, entities);
    }


    /**
     * 根据外键保存
     */
    @Transactional
    public <F extends BaseEntity> void batchSaveByForeignKey(F foreignEntity,
                                                             Function<F, ?> foreignKeyValueGetter,
                                                             SFunction<T, ?> foreignKeyGetter,
                                                             BiConsumer<F, T> fulfillForeignValueConsumer,
                                                             Consumer<List<T>> insertConsumer,
                                                             Consumer<List<Pair<T, T>>> updateConsumer,
                                                             List<T> entities){
        List<T> existed = list(lambdaQueryWrapper().eq(foreignKeyGetter, foreignKeyValueGetter.apply(foreignEntity)));
        Map<String, T> existedMap = existed.stream().collect(Collectors.toMap(T :: getId, t -> t));
        List<T> inserts = new ArrayList<>();
        List<T> updates = new ArrayList<>();
        List<Pair<T, T>> updateParis = new ArrayList<>();
        for (T t: entities){
            if (fulfillForeignValueConsumer != null){
                fulfillForeignValueConsumer.accept(foreignEntity, t);
            }
            if (existedMap.containsKey(t.getId())){
                updates.add(t);
                updateParis.add(Pair.of(existedMap.get(t.getId()), t));
                existedMap.remove(t.getId());
            }else {
                inserts.add(t);
            }
        }
        if (insertConsumer != null){
            insertConsumer.accept(inserts);
        }
        if (updateConsumer != null){
            updateConsumer.accept(updateParis);
        }
        if (inserts.size() > 0){
            saveBatch(inserts);
        }
        if (updates.size() > 0){
            updateBatchById(updates);
        }
        if (existedMap.size() > 0){
            batchDeleteByIds(new HashSet<>(existedMap.keySet()));
        }
    }

    /**
     * 填充创建字段
     */
    public void fulfillCreateProperty(T entity){
        UapUserPo uapUserPo = USER_INFO.get() == null ? new UapUserPo() : USER_INFO.get();
        if (StringUtils.isEmpty(entity.getId())){
            entity.setId(String.valueOf(batchUidService.getUid(getTableName(entity))));
        }
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        entity.setCreatorId(uapUserPo.getId());
        entity.setCreator(uapUserPo.getName());
        entity.setUpdaterId(uapUserPo.getId());
        entity.setUpdater(uapUserPo.getName());
    }

    /**
     * 填充修改字段
     */
    public void fulfillUpdateProperty(T entity){
        UapUserPo uapUserPo = USER_INFO.get() != null ? USER_INFO.get() : new UapUserPo();
        entity.setUpdateTime(new Date());
        entity.setUpdaterId(uapUserPo.getId());
        entity.setUpdater(uapUserPo.getName());
    }

    /**
     * 链式业务查询条件
     * @return 链式查询拼接
     */
    public LambdaQueryWrapper<T> lambdaQueryWrapper(){
        return new LambdaQueryWrapper<>();
    }

    /**
     * 获取表名
     */
    public String getTableName(T entity){
        Class<? extends BaseEntity> aClass = entity.getClass();
        TableName tableName = aClass.getDeclaredAnnotation(TableName.class);
        if (tableName == null){
            throw new MedicalBusinessException("未定义表名");
        }
        return tableName.value();
    }
}
