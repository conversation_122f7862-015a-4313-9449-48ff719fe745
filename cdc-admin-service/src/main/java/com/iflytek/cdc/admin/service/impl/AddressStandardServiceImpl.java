package com.iflytek.cdc.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.AddressStandard;
import com.iflytek.cdc.admin.mapper.AddressStandardMapper;
import com.iflytek.cdc.admin.service.AddressStandardService;
import com.iflytek.cdc.admin.util.RedisHelperUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Service
public class AddressStandardServiceImpl extends ServiceImpl<AddressStandardMapper, AddressStandard> implements AddressStandardService {

    @Autowired
    AddressStandardMapper addressStandardMapper;

    @Resource
    public RedisTemplate<String, Object> redisTemplate;


    /**
     * 存在就更新 不存在就新增
     *
     * @param addressStandard 标准地址表实体类
     */
    @Override
    public void saveOrUpdateAddressStandard(AddressStandard addressStandard) {
        addressStandardMapper.saveOrUpdateAddressStandard(addressStandard);
    }

    @Override
    public Set<String> getUnStdWords() {
        return RedisHelperUtil.scan(redisTemplate, Constants.REDIS_KEY + Constants.TYPE_STD_WORD + "*");
    }

}
