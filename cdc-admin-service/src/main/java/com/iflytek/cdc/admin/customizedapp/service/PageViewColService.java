package com.iflytek.cdc.admin.customizedapp.service;


import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPageViewCol;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface PageViewColService extends ICdcService<TbCdccsPageViewCol> {
    /**
     * 通过页面数据保存或修改
     */
    void saveByPage(TbCdccsPage page, List<TbCdccsPageViewCol> pageViewColList);

    /**
     * 通过pageId 查询
     */
    List<TbCdccsPageViewCol> listByPageId(String pageId);

    /**
     * 根据pageId 删除
     */
    void deleteByPageId(String pageId);
}
