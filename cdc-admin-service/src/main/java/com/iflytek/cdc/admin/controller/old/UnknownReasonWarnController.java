package com.iflytek.cdc.admin.controller.old;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.enums.UnknownReasonMonitorObjectEnum;
import com.iflytek.cdc.admin.service.UnknownReasonWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
@Api(value = "/", description = "不明原因预警规则维护", tags = "不明原因预警规则维护")
public class UnknownReasonWarnController {
    
    @Resource
    UnknownReasonWarnService unknownReasonWarnService;

    @ApiOperation("不明原因类维护-不明原因规则预警列表查询")
    @PostMapping("/{version}/pt/unknownReasonWarn/pageList")
    public PageInfo<UnknownReasonWarnDto> getUnknownReasonWarnPageList(@RequestBody UnknownReasonQueryDto dto) {
        return unknownReasonWarnService.getUnknownReasonWarnPageList(dto);
    }


    @ApiOperation("不明原因类维护-编辑不明原因预警")
    @PostMapping("/{version}/pt/unknownReasonWarn/update")
    public void updateUnknownReasonWarn(@RequestBody UnknownReasonWarnDto UnknownReasonWarnDto,String loginUserId) {
        unknownReasonWarnService.updateUnknownReasonWarn(UnknownReasonWarnDto,loginUserId);
    }

    @ApiOperation("不明原因类维护-修改症状预警状态")
    @PostMapping("/{version}/pt/unknownReasonWarn/updateStatus")
    public void updateUnknownReasonWarnStatus(@RequestBody UnknownReasonWarnDto UnknownReasonWarnDto,String loginUserId) {
        unknownReasonWarnService.updateUnknownReasonWarnStatus(UnknownReasonWarnDto,loginUserId);
    }

    @ApiOperation("不明原因类维护-获取不明原因预警")
    @GetMapping("/{version}/pt/unknownReasonWarn/getWarnById")
    public UnknownReasonWarnDto getWarnById(@RequestParam String warnId) {
        return unknownReasonWarnService.getWarnById(warnId);
    }

    @ApiOperation("不明原因类维护-获取监测对象列表")
    @GetMapping("/{version}/pt/unknownReasonWarn/getMonitorObjectList")
    public List<Map<String,Object>> getMonitorObjectList() {
        return UnknownReasonMonitorObjectEnum.getAllToList();
    }


    @ApiOperation("不明原因类维护-不明原因规则预警列表查询")
    @GetMapping("/{version}/pt/unknownReasonWarn/getAllList")
    public List<UnknownReasonWarnDto> unknownReasonWarnAllList(@RequestParam(required = false) String diseaseCode ) {
        return unknownReasonWarnService.getUnknownReasonWarnAllList(diseaseCode);
    }

    @ApiOperation("不明原因类维护-不明原因规则预警列表导出")
    @GetMapping("/{version}/pt/unknownReasonWarnRule/export")
    @LogExportAnnotation
    public void exportUnknownReasonWarnRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        unknownReasonWarnService.exportUnknownReasonWarnRule(response);
    }
}
