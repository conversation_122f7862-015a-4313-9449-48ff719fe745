package com.iflytek.cdc.admin.controller.brief;

import com.google.common.collect.Maps;
import com.iflytek.cdc.admin.enums.BriefReportTypeEnum;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.dto.brief.TemplateSettingDto;
import com.iflytek.cdc.admin.entity.brief.TemplateEntity;
import com.iflytek.cdc.admin.service.brief.BriefTemplateService;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.TemplateSettingVo;
import com.iflytek.cdc.admin.vo.brief.TemplateVo;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简报模板控制器
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Api(tags = "简报模板管理")
@RestController
@RequestMapping("/pt/{version}/brief/template")
@RequiredArgsConstructor
public class BriefReportTemplateController {

    private final BriefTemplateService briefTemplateService;

    /**
     * 列表
     */
    @PostMapping("/list")
    public ApiResult<PageInfo<TemplateVo>> list(@RequestBody TemplateSearchDto searchDto) {
        return briefTemplateService.queryPage(searchDto);
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/saveOrUpdate")
    @OperationLogAnnotation(operationName = "简报模板管理-保存")
    public ApiResult<?> save(@RequestParam String loginUserId, @RequestBody TemplateEntity template) {
        return briefTemplateService.saveOrUpdateTemplate(template, loginUserId);
    }

    /**
     * 模板配置
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/setting")
    @OperationLogAnnotation(operationName = "简报模板管理-模板配置")
    public ApiResult<?> setting(@RequestParam String loginUserId, @RequestBody TemplateSettingDto settingDto) {
        settingDto.setUpdatorId(loginUserId);
        return briefTemplateService.setting(settingDto);
    }

    /**
     * 获取模板配置详情
     */
    @GetMapping("/getSetting/{templateId}")
    public ApiResult<?> getSetting(@PathVariable("templateId") String id) {
        TemplateSettingVo settingVo = briefTemplateService.getSetting(id);
        return ApiResult.ok(settingVo);
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("/delete/{id}")
    @OperationLogAnnotation(operationName = "简报模板管理-删除")
    public ApiResult<?> delete(@RequestParam String loginUserId, @RequestParam String loginUserName,
                               @PathVariable("id") String id) {
        return briefTemplateService.deleteById(loginUserId, loginUserName, id);
    }

    @PostMapping("/exportTemplateList")
    @ApiOperation("简报模板管理-导出")
    public ResponseEntity<byte[]> exportTemplateList(@RequestBody TemplateSearchDto searchDto) {
        return briefTemplateService.exportTemplateList(searchDto);
    }


    @PostMapping("/getBriefReportType")
    @ApiOperation("报告类型")
    public Map<String, List<Map<String, String>>> getBriefReportType(){
        List<Map<String, String>> epidemicBriefReportType = BriefReportTypeEnum.getEpidemicBriefReportType();
        List<Map<String, String>> qualityBriefReportType = BriefReportTypeEnum.getQualityBriefReportType();
        Map<String, List<Map<String, String>>> result = Maps.newHashMap();
        result.put("epidemicAnalysisList",epidemicBriefReportType);
        result.put("qualityAnalysisList",qualityBriefReportType);
        return result;
    }
}
