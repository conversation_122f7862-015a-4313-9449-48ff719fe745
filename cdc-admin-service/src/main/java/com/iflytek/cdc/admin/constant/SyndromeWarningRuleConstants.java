package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.enums.SyndromeCaseScopeEnum;
import com.iflytek.cdc.admin.enums.SyndromeWarningMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

@ApiModel("症候群预警规则常量")
@Data
public class SyndromeWarningRuleConstants {

    private static final SyndromeWarningRuleConstants instance = new SyndromeWarningRuleConstants();

    @ApiModelProperty("预警方法")
    private Object warningMethod = SyndromeWarningMethodEnum.values();
    @ApiModelProperty("排除病例")
    private Object hasExcludedCase = HasExcludedCaseEnum.values();
    @ApiModelProperty("病例类型")
    private Object caseType = CaseTypeEnum.values();
    @ApiModelProperty("是否选择亚组")
    private Object choseChildType = ChoseChildTypeEnum.values();

    /**************************症候群******************************/
    @ApiModelProperty("症候群病例范围")
    private Object syndromeProcessScope = SyndromeCaseScopeEnum.values();

    public static SyndromeWarningRuleConstants getInstance(){
        return instance;
    }

    /**
     * 包含排除病例
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum HasExcludedCaseEnum{
        YES("0", "剔除排除病例"),
        NO("1", "包含排除病例"),
        ;
        private String value;
        private String label;

        HasExcludedCaseEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 病例类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum CaseTypeEnum{
        NON_SEVERE("0", "非危重症病例"),
        NO_DEATH_SEVERE("1", "危重症未死亡病例"),
        DEATH("2", "死亡病例"),
        ;
        private String value;
        private String label;

        CaseTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 选择亚组类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum ChoseChildTypeEnum{
        YES("0", "不区分亚组"),
        NO("1", "仅特定亚组"),
        ;
        private String value;
        private String label;

        ChoseChildTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 预警等级
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum RiskLevel{
        NOT_NEED_PROCESS("6","01", "无需处理"),
        NEED_PROCESS("7", "02", "需要处理"),
        ;
        private String id;
        private String value;
        private String label;

        RiskLevel(String id, String value, String label) {
            this.id = id;
            this.value = value;
            this.label = label;
        }
    }

}
