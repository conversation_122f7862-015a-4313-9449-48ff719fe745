package com.iflytek.cdc.admin.datamodel.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.model.dto.EntityErQueryDTO;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface EntityErModelService extends ICdcService<com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel> {

    TbCdcdmEntityErModel load(String id);

    /**
     * 查询所有er模型
     * @return er模型实例
     */
    List<TbCdcdmEntityErModel> list(EntityErQueryDTO queryDTO);

    /**
     * 新增数据
     *
     * @param input 实例对象
     * @return 实例对象
     */
    TbCdcdmEntityErModel create(TbCdcdmEntityErModel input);

    /**
     * 更新数据
     *
     * @param input 实例对象
     * @return 实例对象
     */
    TbCdcdmEntityErModel update(TbCdcdmEntityErModel input);

    /**
     * 分页查询
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageInfo<TbCdcdmEntityErModel> pageList(EntityErQueryDTO queryDTO);

    /**
     * 根据编码修改或更新
     */
    TbCdcdmEntityErModel saveByCode(TbCdcdmEntityErModel input);

}
