package com.iflytek.cdc.admin.service.province.impl;

import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicMonitorConfigMapper;
import com.iflytek.cdc.admin.model.mr.dto.EndemicMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EndemicDiseaseInfoVO;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicMonitorConfigMapper;
import com.iflytek.cdc.admin.service.EndemicProcessMonitorService;

import javax.annotation.Resource;
import java.util.*;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class EndemicProcessMonitorServiceImpl extends ServiceImpl<TbCdcmrEndemicMonitorConfigMapper, TbCdcmrEndemicMonitorConfig> implements EndemicProcessMonitorService {

    @Resource
    private TbCdcmrEndemicDiseaseInfoMapper diseaseInfoMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CommonUtils commonUtils;

    @Resource
    private TbCdcmrEndemicMonitorConfigMapper monitorConfigMapper;

    private static final String TB_CDCMR_EMERGING_DISEASE_INFO = "tb_cdcmr_endemic_disease_info";

    private static final String TB_CDCMR_EMERGING_MONITOR_CONFIG = "tb_cdcmr_endemic_monitor_config";

    @Value("${generateStrLength:8}")
    private int length;

    @Override
    public List<EndemicDiseaseInfoVO> getEndemicTreeInfo() {
        return diseaseInfoMapper.getEndemicTreeInfo();
    }

    @Override
    public String editSubEndemicInfo(TbCdcmrEndemicDiseaseInfo endemicDiseaseInfo) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_DISEASE_INFO));
        //非空为更新，为空则新增
        if(endemicDiseaseInfo.getId() == null){
            //新增id、code后端生成
            endemicDiseaseInfo.setId(id);
            //查目前疾病code
            List<String> diseaseCodeList = diseaseInfoMapper.getAllDiseaseCodeBy();
            endemicDiseaseInfo.setDiseaseCode(commonUtils.generateSyndromeCode(length, diseaseCodeList));
        }

        //状态默认开启，默认未删除
        endemicDiseaseInfo.setStatus(endemicDiseaseInfo.getStatus() == null ? Integer.valueOf(StatusEnum.STATUS_ON.getCode()) : endemicDiseaseInfo.getStatus());
        endemicDiseaseInfo.setDeleteFlag(endemicDiseaseInfo.getDeleteFlag() == null ? DeleteFlagEnum.NO.getCode() : endemicDiseaseInfo.getDeleteFlag());
        endemicDiseaseInfo.setCreateTime(new Date());
        endemicDiseaseInfo.setCreator(uapUserPo.getName());
        endemicDiseaseInfo.setCreatorId(uapUserPo.getId());
        endemicDiseaseInfo.setUpdateTime(new Date());
        endemicDiseaseInfo.setUpdater(uapUserPo.getName());
        endemicDiseaseInfo.setUpdaterId(uapUserPo.getId());
        diseaseInfoMapper.insertOrUpdate(Collections.singletonList(endemicDiseaseInfo));

        return id;
    }

    @Override
    public void deleteSubEndemicInfo(String id) {

        diseaseInfoMapper.updateDeleteFlagById(id);
    }

    @Override
    public List<TbCdcmrEndemicMonitorConfig> getEndemicInfoConfigs(EndemicMonitorConfigQueryDTO queryDTO) {

        return monitorConfigMapper.queryByDiseaseInfoId(queryDTO);
    }

    @Override
    public void editEndemicProcessDefinition(String diseaseInfoId, List<TbCdcmrEndemicMonitorConfig> endemicMonitorConfigs) {

        UapUserPo uapUserPo = USER_INFO.get();
        //软删除该疾病下的所有规则
        monitorConfigMapper.updateByEndemicInfoId(diseaseInfoId);

        String loginUserId = null;
        String loginUserName = null;
        if(uapUserPo != null){
            loginUserId = uapUserPo.getId();
            loginUserName = uapUserPo.getName();
        }
        //重新set id的值，并插入配置表
        for (TbCdcmrEndemicMonitorConfig monitorConfig : endemicMonitorConfigs) {

            monitorConfig.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_MONITOR_CONFIG)));
            //状态默认开启，默认未删除
            monitorConfig.setStatus(Constants.STATUS_ON);
            monitorConfig.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            monitorConfig.setCreateTime(new Date());
            monitorConfig.setCreator(loginUserName);
            monitorConfig.setCreatorId(loginUserId);
            monitorConfig.setUpdateTime(new Date());
            monitorConfig.setUpdater(loginUserName);
            monitorConfig.setUpdaterId(loginUserId);
            monitorConfig.setDiseaseInfoId(diseaseInfoId);
        }
        if(CollectionUtils.isNotEmpty(endemicMonitorConfigs)) {
            monitorConfigMapper.batchInsert(endemicMonitorConfigs);
        }
    }

    @Override
    public List<DiagnoseListVO> getDiagnoseCodeList() {

        return monitorConfigMapper.getDiagnoseCodeList();
    }

    @Override
    public List<String> getEndemicDiseaseCodeByName(String diseaseName) {

        String diseaseCode = diseaseInfoMapper.getEndemicDiseaseCodeByName(diseaseName);
        return StringUtils.isNotBlank(diseaseCode) ? Collections.singletonList(diseaseCode) : new ArrayList<>();
    }
}
