package com.iflytek.cdc.admin.datamodel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DataModelValueDTO {

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型版本主键ID
     */
    @ApiModelProperty(name = "主键ID")
    private String modelVersionId ;

    /**
     * 数据模型版本主键ID
     */
    @ApiModelProperty(name = "er模型ID")
    private String erModelId ;


    /**
     * 数据模型名称
     */
    @ApiModelProperty(name = "数据模型名称")
    private String modelName ;

    /**
     * 数据模型分类
     */
    @ApiModelProperty(name = "数据模型分类")
    private String modelType ;

    /**
     * 数据模型备注
     */
    @ApiModelProperty(name = "数据模型备注")
    private String dataModelNote ;

    /**
     * 数据模型版本备注
     */
    @ApiModelProperty(name = "数据模型版本备注")
    private String dataModelVersionNote ;

    /**
     * 数据模型标签
     */
    @ApiModelProperty(name = "数据模型标签")
    private String modelLabel ;

    /**
     * 版本
     */
    @ApiModelProperty(name = "版本")
    private String version ;

    /**
     * 状态
     */
    @ApiModelProperty(name = "状态")
    private String status ;

    /**
     * 是否启用 1-是，0-否
     */
    @ApiModelProperty(name = "是否启用 1-是，0-否")
    private Integer isEnable ;

    /**
     * 是否删除 1-已删除、0-未删除
     */
    @ApiModelProperty(name = "是否删除 1-已删除、0-未删除")
    private Integer isDeleted ;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime ;

    @ApiModelProperty(name = "创建开始时间")
    private Date createStartTime ;
    @ApiModelProperty(name = "创建结束时间")
    private Date createEndTime ;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    private Date updateTime ;

    @ApiModelProperty(name = "修改开始时间")
    private Date updateStartTime ;
    @ApiModelProperty(name = "修改结束时间")
    private Date updateEndTime ;

    @ApiModelProperty(name = "关键字 兼容原有xml")
    private String keyWord ;

    @ApiModelProperty("区别系统")
    private String systemType;

}
