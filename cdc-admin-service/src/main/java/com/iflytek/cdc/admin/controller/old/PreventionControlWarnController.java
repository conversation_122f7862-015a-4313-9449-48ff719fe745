package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.PreventionControlQueryDto;
import com.iflytek.cdc.admin.dto.PreventionControlWarnDto;
import com.iflytek.cdc.admin.service.PreventionControlWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@Api(tags = "联防联控标准维护服务",description = "联防联控标准维护服务")
@AllArgsConstructor
public class PreventionControlWarnController {

    @Resource
    PreventionControlWarnService preventionControlWarnService;

    @ApiOperation("不明原因类维护-不明原因规则预警列表查询")
    @PostMapping("/{version}/pt/preventionControlWarn/pageList")
    public PageInfo<PreventionControlWarnDto> getPreventionControlWarnPageList(@RequestBody PreventionControlQueryDto dto) {
        return preventionControlWarnService.getPreventionControlWarnPageList(dto);
    }


    @ApiOperation("不明原因类维护-编辑不明原因预警")
    @PostMapping("/{version}/pt/preventionControlWarn/update")
    public void updatePreventionControlWarn(@RequestBody PreventionControlWarnDto warnDto,String loginUserId) {
        preventionControlWarnService.updatePreventionControlWarn(warnDto,loginUserId);
    }

    @ApiOperation("不明原因类维护-修改症状预警状态")
    @PostMapping("/{version}/pt/preventionControlWarn/updateStatus")
    public void updatePreventionControlWarnStatus(@RequestBody PreventionControlWarnDto warnDto,String loginUserId) {
        preventionControlWarnService.updatePreventionControlWarnStatus(warnDto,loginUserId);
    }

    @ApiOperation("不明原因类维护-获取不明原因预警")
    @GetMapping("/{version}/pt/preventionControlWarn/getWarnById")
    public PreventionControlWarnDto getWarnById(@RequestParam String warnId) {
        return preventionControlWarnService.getWarnById(warnId);
    }


    @ApiOperation("不明原因类维护-不明原因规则预警列表查询")
    @GetMapping("/{version}/pt/preventionControlWarn/getAllList")
    public List<PreventionControlWarnDto> getPreventionControlWarnAllList(@RequestParam(required = false) String preventionControlCode ) {
        return preventionControlWarnService.getPreventionControlWarnAllList(preventionControlCode);
    }

    @ApiOperation("不明原因类维护-不明原因规则预警列表导出")
    @GetMapping("/{version}/pt/preventionControlWarnRule/export")
    @LogExportAnnotation
    public void exportPreventionControlWarnRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        preventionControlWarnService.exportPreventionControlWarnRule(response);
    }
}
