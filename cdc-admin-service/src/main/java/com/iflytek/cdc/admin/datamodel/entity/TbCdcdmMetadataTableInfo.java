package com.iflytek.cdc.admin.datamodel.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
@Data
@TableName("tb_cdcdm_metadata_table_info")
public class TbCdcdmMetadataTableInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表编码
     */
    @ApiModelProperty(value = "表编码")
    private String tableCode ;

    /**
     * 表schema
     */
    @ApiModelProperty(value = "表schema")
    private String schema ;

    /**
     * 表名称
     */
    @ApiModelProperty(value = "表名称")
    private String tableName ;

    /**
     * 表类型
     */
    @ApiModelProperty(value = "表类型")
    private String tableType ;

    /**
     * 表描述
     */
    @ApiModelProperty(value = "表描述")
    private String tableDesc ;

    /**
     * 表别名, 全局唯一
     */
    @ApiModelProperty(value = "表别名, 全局唯一")
    private String alias ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo ;

    /**
     * 过滤条件
     */
    @ApiModelProperty(value = "")
    private String filterCondition ;

    /**
     * 外键id
     */
    private String refId;

    /**
     * 实体表状态
     */
    private String tableStatus;

    @ApiModelProperty("是否自定义")
    private String customizedEnable;

    @TableField(exist = false)
    private List<TbCdcdmMetadataTableColumnInfo> columnInfoList;
}