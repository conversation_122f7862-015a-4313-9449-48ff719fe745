package com.iflytek.cdc.admin.outbound.common.config;

import com.iflytek.cdc.admin.outbound.util.OutBoundRestTemplateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Slf4j
@Configuration
public class OutBoundRestTemplateConfig {

    private final OutBoundRestTemplateCommonConfig outBoundRestCommonConfig;

    public OutBoundRestTemplateConfig(@Qualifier("outBoundRestTemplateCommonConfig") OutBoundRestTemplateCommonConfig outBoundRestCommonConfig) {
        this.outBoundRestCommonConfig = outBoundRestCommonConfig;
    }

    @Bean(name = "outBoundRestTemplateDTO")
    public OutBoundRestTemplateDTO outBoundRestTemplateDTO() {

        log.info("外部调用-RestTemplate配置 :" + outBoundRestCommonConfig.getOutInvoke().toString());

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            @Override
            protected boolean hasError(HttpStatus statusCode) {
                return super.hasError(statusCode);
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                //避免原始Exception被拦截
            }
        });

        //辅诊平台灰度增加label处理
        OutBoundRestTemplateDTO test = new OutBoundRestTemplateDTO();
        test.setRestTemplate(restTemplate);
        return test;
    }
}
