package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;

import java.util.List;

public interface WarningGradeConfigService {
    PageInfo<SyndromeGradeConfigVO> syndromeWarningGradeConfigPageList(SyndromeGradeConfigParam param);

    PageInfo<InfectedGradeConfigVO> infectedWarningGradeConfigPageList(InfectedGradeConfigParam param);

    PageInfo<SymptomGradeConfigVO> symptomWarningGradeConfigPageList(SymptomGradeConfigParam param);

    PageInfo<PoisonGradeConfigVO> poisonWarningGradeConfigPageList(PoisonGradeConfigParam param);


    PageInfo<UnknownReasonGradeConfigVO> unknownReasonWarningGradeConfigPageList(UnknownReasonGradeConfigParam param);

    PageInfo<PreventionControlGradeConfigVO> preventionControlWarningGradeConfigPageList(PreventionControlGradeConfigParam param);

    PageInfo<OutpatientGradeConfigVO> outpatientWarningGradeConfigPageList(OutpatientGradeConfigParam param);

    PageInfo<CustomizedGradeConfigVO> customizedWarningGradeConfigPageList(CustomizedGradeConfigParam param);

    void updateWarningGradeConfig(WarningGradeConfigVO configVO, String loginUserId);

    void updateWarningGradeConfigStatus(TbCdcmrWarningGradeConfig config, String loginUserId);


    List<WarningGradeConfigVO> getConfigByType(String configType);


}
