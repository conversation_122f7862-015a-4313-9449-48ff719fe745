package com.iflytek.cdc.admin.outbound.mapper;

import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.model.dto.AudioDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CaseAudioMapper {

    AudioRecord findById(@Param("id") String id);

    int updateAudioFinalTextById(@Param("updatedAudioFinalText") String updatedAudioFinalText, @Param("id") String id);

    int insertSelective(AudioRecord audioRecord);

    int deleteByIdAndBusinessId(@Param("id") String id, @Param("businessId") String businessId);

    int deleteByPathAndBusinessId(@Param("audioPath") String audioPath, @Param("businessId") String businessId);

    List<AudioDTO> findByBusinessId(@Param("businessId") String businessId);

    int updatefileName(@Param("id") String id, @Param("name") String name);

    AudioRecord getCallAudio(@Param("businessId")String businessId, @Param("audioAddress") String audioAddress);

    Integer findMaxIndex(@Param("businessId") String businessId);

    int checkIsExistCaseAudio(@Param("audioPath") String audioPath,@Param("businessId") String businessId);

    int updateAudioUrl(@Param("url")String url,@Param("businessId") String businessId);

    int updateByPathAndBusinessId(@Param("url")String url, @Param("businessId") String businessId);

    String getTempTextByPathAndCaseId(@Param("url")String url,@Param("businessId") String businessId);
}