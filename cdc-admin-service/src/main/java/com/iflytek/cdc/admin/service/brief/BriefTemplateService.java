package com.iflytek.cdc.admin.service.brief;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.dto.brief.TemplateSettingDto;
import com.iflytek.cdc.admin.entity.brief.TemplateEntity;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.TemplateSettingVo;
import com.iflytek.cdc.admin.vo.brief.TemplateVo;
import org.springframework.http.ResponseEntity;

/**
 * 简报模板管理-服务接口类
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
public interface BriefTemplateService extends IService<TemplateEntity> {

    ApiResult<PageInfo<TemplateVo>> queryPage(TemplateSearchDto searchDto);

    ApiResult<?> deleteById(String loginUserId, String loginUserName, String id);

    ApiResult<?> saveOrUpdateTemplate(TemplateEntity template, String loginUserId);

    ApiResult<?> setting(TemplateSettingDto settingDto);

    TemplateSettingVo getSetting(String id);

    ResponseEntity<byte[]> exportTemplateList(TemplateSearchDto searchDto);
}
