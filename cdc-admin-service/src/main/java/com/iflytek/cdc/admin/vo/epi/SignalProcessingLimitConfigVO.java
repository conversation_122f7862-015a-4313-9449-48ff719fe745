package com.iflytek.cdc.admin.vo.epi;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("风险等级信号处理时限")
public class SignalProcessingLimitConfigVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "核实时限默认值")
    private Integer checkTimeLimitDefault;

    @ApiModelProperty(value = "核实时限最大值")
    private Integer checkTimeLimitMax;

    @ApiModelProperty(value = "现场调查时限默认值")
    private Integer investTimeLimitDefault;

    @ApiModelProperty(value = "现场调查时限最大值")
    private Integer investTimeLimitMax;

    @ApiModelProperty(value = "发起研判时限默认值")
    private Integer judgeTimeLimitDefault;

    @ApiModelProperty(value = "发起研判时限最大值")
    private Integer judgeTimeLimitMax;

}
