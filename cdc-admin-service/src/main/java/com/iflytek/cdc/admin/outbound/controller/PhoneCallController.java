package com.iflytek.cdc.admin.outbound.controller;

import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.model.vo.ManualCallResultVo;
import com.iflytek.cdc.admin.outbound.model.vo.ManualCallSaveVo;
import com.iflytek.cdc.admin.outbound.service.AudioTextService;
import com.iflytek.cdc.admin.outbound.service.PhoneCallService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @description:
 * @author: shenghuang
 * @create: 2022-11-15 18:51
 **/
@RestController
@RequestMapping("/v1/pt/manual-call")
@Api(tags = "电话流调")
public class PhoneCallController {

    @Resource
    private PhoneCallService phoneCallService;

    @Resource
    private AudioTextService audioTextService;

    @GetMapping("/account")
    @ApiOperation("获取软段话配置并生成任务")
    public String getAccount(@RequestParam String name,@RequestParam String telephone,@RequestParam String relationId) {
        return phoneCallService.getAccount(name,telephone,relationId);
    }

    @PostMapping("/update/{recordId}")
    @ApiOperation("更新软电话记录的更新时间")
    public String updateTime( @PathVariable String recordId) {
        return phoneCallService.updateTime(recordId);
    }

    @PostMapping("/result/{recordId}/{relationId}")
    @ApiOperation("提交软电话记录的结果")
    public String updateResult( @PathVariable String recordId, @PathVariable String relationId, 
                               @RequestBody ManualCallResultVo vo) {
        return phoneCallService.updateResult(recordId,relationId,vo);
    }

    @PostMapping("/record/url")
    @ApiOperation("获取软电话音频地址")
    public String getRecordUrl( @RequestParam String audioName, @RequestParam String relationId) {
        return phoneCallService.getRecordUrl(audioName, relationId);
    }

    @PostMapping("/record")
    @ApiOperation("获取软电话音频记录")
    public AudioRecord getRecord(@RequestParam String relationId) {
        return phoneCallService.getRecord(relationId);
    }

    @PostMapping("/save")
    @ApiOperation("保存软电话音频临时记录")
    public void saveAudioTempText(  @RequestBody ManualCallSaveVo vo) {
         phoneCallService.saveAudio(vo.getTempText(), vo.getAudioName(), vo.getRelationId());
    }

    @GetMapping("/getHandShakeParams")
    @ApiOperation("获取握手参数")
    public String getHandShakeParams() {
        return audioTextService.getHandShakeUrlAndParams();
    }



}
