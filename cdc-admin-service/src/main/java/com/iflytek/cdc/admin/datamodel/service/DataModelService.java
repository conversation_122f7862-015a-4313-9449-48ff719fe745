package com.iflytek.cdc.admin.datamodel.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelFormQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.*;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormValue;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelValueDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface DataModelService {

    /**
     * 编辑数据模型
     * */
    Map<String, String> editDataModel(DataModelValueDTO dto, String loginUserName);

    /**
     * 查询数据模型列表
     * */
    PageInfo<DataModelListVO> getDataModelList(String status, String keyWord, String modelId, Integer pageIndex, Integer pageSize);

    PageInfo<DataModelListVO> getDataModelList(DataModelValueDTO dto, Integer pageIndex, Integer pageSize);

    /**
     * 查询数据模型列表 不分页
     * */
    List<DataModelListVO> allDataModelList(String status);

    /**
     * 发布数据模型 针对某个版本
     * 发布数据模型 指将该版本更新状态为已发布 同时赋予最新的版本号
     * */
    void publishDataModel(String modelId, String modelVersionId, String loginUserName);

    /**
     * 作废数据模型 针对某个版本
     * 作废数据模型 指将该版本更新状态为已失效 查询数据模型时 能看到作废的数据模型 标为已失效
     * 注 : 草稿版本作废后还是草稿
     * */
    void updateDataModel(String modelVersionId, String loginUserName);

    /**
     * 删除数据模型 针对某个版本
     * 删除数据模型 指将该版本标识为已删除 查询数据模型时 无法看到已删除的数据模型
     * */
    void deleteDataModel(String modelVersionId, String loginUserName);

    /**
     * 查询某个数据模型 配置的表单列表
     * */
    PageInfo<ModelFormListVO> getModelFormList(DataModelFormQueryDTO dto);

    /**
     * 数据模型 历史版本记录
     * */
    PageInfo<DataModelVersionHistoryVO> getDataModelVersionHistory(String modelId,
                                                                   Integer pageIndex,
                                                                   Integer pageSize);

    /**
     * 编辑表单
     * */
    String editModelForm(TbCdcdmDataModelForm tbCdcdmDataModelForm, String loginUserName);

    /**
     * 根据表单id 查询表单配置
     * */
    TbCdcdmDataModelForm getFormInfoByFormId(String modelFormId);

    /**
     * 表单填报 数据存储
     * */
    void saveModelFormData(TbCdcdmDataFormValue tbCdcdmDataFormValue, String loginUserName);

    /**
     * 根据数据模型id 查询该数据模型 已发布 最新的表单列表
     * */
    DataModelFormConfigVO getDataModelFormsByModelId(String modelId);

    /**
     * 查看 表单填报数据
     * */
    TbCdcdmDataFormValue getModelFormData(String id);

    /**
     * 编辑表单在模型中展示顺序
     * */
    void editFormSequence(List<TbCdcdmDataModelForm> tbCdcdmDataModelForms, String loginUserName);

    /**
     * 查询数据模型中表单配置的字段
     * */
    List<ModelFormFieldsVO> getModelFormFields();

    void uploadFile(MultipartFile file, String loginUserName);

    /**
     * 更新已发布数据模型的发布状态
     * 解决已发布模型 修改配置时 会产生新的版本影响研测操作
     * */
    void updatePublishDataModel(String modelId, String modelVersionId, String status);

    /**
     * 根据configInfo 查询表单
     * */
    List<TbCdcdmDataModelForm> getModelFormByConfigInfo(String configInfo);

}
