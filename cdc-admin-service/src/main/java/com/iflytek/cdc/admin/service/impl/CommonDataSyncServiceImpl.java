package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.dto.UpdateMdmDataDTO;
import com.iflytek.cdc.admin.entity.CommonMdmDictInfo;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.enums.CodeTableEnum;
import com.iflytek.cdc.admin.mapper.CommonDataSyncMapper;
import com.iflytek.cdc.admin.service.CommonDataSyncService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.*;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;


/**
 * <AUTHOR>
 * @date 2021/8/6 15:47
 **/
@Service
@Slf4j
public class CommonDataSyncServiceImpl implements CommonDataSyncService {

    private final MdmDataSyncService mdmDataSyncService;

    private final UapUserApi uapUserApi;

    private final BatchUidService batchUidService;

    private final CommonDataSyncMapper dMapper;


    public CommonDataSyncServiceImpl(MdmDataSyncService mdmDataSyncService, UapUserApi uapUserApi, BatchUidService batchUidService, CommonDataSyncMapper dMapper) {
        this.mdmDataSyncService = mdmDataSyncService;
        this.uapUserApi = uapUserApi;
        this.batchUidService = batchUidService;
        this.dMapper = dMapper;
    }

    @Override
    public <T extends CommonMdmDictInfo> void mdmDataSyncs(@RequestParam String loginUserId, @RequestParam String dataCode) {
        try {
            String tableName = CodeTableEnum.getTableName(dataCode);
            if (isCanUse(dataCode)) {
                log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
                throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
            }
            //获取登录人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            //先查询本地数据表所有的编码数据
            HashSet<T> localInfos = new HashSet<>(dMapper.queryLocalInfo(tableName));
            //获取mdm主数据平台数据
            List<TermCodedValueInfo> mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(dataCode,
                    MdmDataSyncConstants.SYNDROME_PAGENUMBER, MdmDataSyncConstants.SYNDROME_PAGESIZE);
            //转换为本地数据使用hashSet保存
            HashSet<T> mdmLocalCodes = new HashSet<>();
            for (TermCodedValueInfo mdm : mdmCodeValues) {
                T dictInfo = null;
                try {
                    dictInfo = (T)CommonMdmDictInfo.class.newInstance();
                    dictInfo.setId(String.valueOf(batchUidService.getUid(tableName)));
                    dictInfo.setDictCode(mdm.getCodedValue());
                    dictInfo.setDictName(mdm.getDescription());
                    dictInfo.setCreateUser(uapUser.getId());
                    dictInfo.setIsEnabled(MdmDataSyncConstants.YES_USE);
                    dictInfo.setUpdateUser(uapUser.getId());
                } catch (InstantiationException | IllegalAccessException e) {
                    e.printStackTrace();
                }
                mdmLocalCodes.add(dictInfo);
            }
            Iterator<T> localIter = localInfos.iterator();
            List<T> localExistData = new ArrayList<>();
            while (localIter.hasNext()) {
                //本地库存在而mdm不存在
                T local = localIter.next();
                if (!mdmLocalCodes.contains(local)) {
                    //数据放入本地数据中
                    localExistData.add(local);
                }
            }
            Iterator<T> mdmIter = mdmLocalCodes.iterator();
            List<T> mdmExistData = new ArrayList<>();
            List<T> allExistData = new ArrayList<>();
            //迭代mdm
            while (mdmIter.hasNext()) {
                T mdm = mdmIter.next();
                //如果mdm存在本地不存在则新增
                if (!localInfos.contains(mdm)) {
                    mdmExistData.add(mdm);
                } else {
                    //如果mdm和本地都存在
                    allExistData.add(mdm);
                }
            }
            //如果mdm存在而本地不存在，则直接插入数据库中
            if (CollUtil.isNotEmpty(mdmExistData)) {
                //如果数量太大，则分组插入
                if (mdmExistData.size() > MdmDataSyncConstants.MAX_INSERT_MDM_SIZE) {
                    List<List<T>> mdmExistDataGroup = Lists.partition(mdmExistData, MdmDataSyncConstants.SYNDROME_PAGESIZE);
                    //分批插入
                    for (List<T> data : mdmExistDataGroup) {
                        dMapper.insertLocalInfo(data, tableName);
                    }
                } else {
                    //一次性插入
                    dMapper.insertLocalInfo(mdmExistData, tableName);
                }
            }
            //本地库中存在而mdm不存在的数据，更新本地库中数据的状态
            UpdateMdmDataDTO ud = new UpdateMdmDataDTO();
            List<String> updateIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(localExistData)) {
                localExistData.forEach(localData -> updateIds.add(localData.getId()));
                ud.setUpdateUser(uapUser.getId());
                ud.setUseStatus(MdmDataSyncConstants.NO_USE);
                ud.setTableName(tableName);
                //数量过大
                if (updateIds.size() > MdmDataSyncConstants.MAX_INSERT_MDM_SIZE) {
                    //对id进行分组
                    List<List<String>> updateIdGroup = Lists.partition(updateIds, MdmDataSyncConstants.MDM_UPDATE_ID_SIZE);
                    //分批次插入
                    for (List<String> ids : updateIdGroup) {
                        ud.setIds(ids);
                        dMapper.updateMasterDataStatus(ud);
                    }
                } else {
                    //批量更新mdm已删除的数据状态
                    ud.setIds(updateIds);
                    dMapper.updateMasterDataStatus(ud);
                }
            }
            //如果本地库中存在且mdm库也存在的数据，如果编码名称有修改
            if (CollUtil.isNotEmpty(allExistData)) {
                allExistData.forEach(allData ->
                        dMapper.updateCodeName(allData.getDictCode(), allData.getDictName(), uapUser.getId(), tableName));
            }
        } catch (MedicalBusinessException r) {
            log.error("主数据同步异常：{}", r);
            throw new MedicalBusinessException(r.getMessage());
        } catch (Exception e) {
            log.error("主数据同步异常：{}", e);
            throw new MedicalBusinessException("数据同步失败");
        }
    }

    public boolean isCanUse(String originCode) {
        //查询字典目录
        MdmPageData<TermDictInfo, TermDictInfoFilter> mdmPageData = mdmDataSyncService.getMdmTermDictInfo(originCode);
        List<TermDictInfo> dictInfos = mdmPageData.getEntities();
        //判断为空
        if (CollUtil.isEmpty(dictInfos)) {
            return true;
        }
        //如果被删除
        if (MdmDataSyncConstants.MDM_YES_DELETE.equals(dictInfos.get(0).getDeleted().toString())) {
            return true;
        }
        //判断是否被禁用
        return dictInfos.get(0).getEnabled().toString().equals(MdmDataSyncConstants.NO_USE);
    }

    private TUapUser getUapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (userCache == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }

    @Override
    public <T extends CommonMdmDictInfo> boolean getDictStatus(String id, String dataCode) {
        String tableName = CodeTableEnum.getTableName(dataCode);
        //根据id查询该字典值域的详细
        T t = dMapper.queryDictInfoById(tableName, id);
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(dataCode);
        //判断字典目录是否存在
        if (isCanUse(dataCode)) {
            log.error("未查询到mdm该字典目录，或者该字典目录已被禁用:{}", di.getTargetCode());
            return false;
        }
        //查询字典值域
        String[] codeValue = {t.getDictCode()};
        //获取字典值域数据
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfo(di, MdmDataSyncConstants.SYNDROME_PAGENUMBER,
                MdmDataSyncConstants.SYNDROME_PAGESIZE, codeValue);
        if (CollUtil.isEmpty(mdmData.getEntities())) {
            return false;
        }
        return !StringUtils.isBlank(mdmData.getEntities().get(0).getId().toString());
    }
}
