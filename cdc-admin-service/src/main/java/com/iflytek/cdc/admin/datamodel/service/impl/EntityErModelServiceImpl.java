package com.iflytek.cdc.admin.datamodel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmEntityErModelMapper;
import com.iflytek.cdc.admin.datamodel.model.EntityRelation;
import com.iflytek.cdc.admin.customizedapp.model.dto.EntityErQueryDTO;
import com.iflytek.cdc.admin.datamodel.service.EntityErModelService;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntityErModelServiceImpl extends CdcServiceBaseImpl<TbCdcdmEntityErModelMapper, TbCdcdmEntityErModel> implements EntityErModelService {
    @Resource
    private TbCdcdmEntityErModelMapper tbCdccsEntityErModelMapper;

    @Resource
    private BatchUidService batchUidService;

    @Override
    public TbCdcdmEntityErModel load(String id) {
        TbCdcdmEntityErModel byId = getById(id);
        return convert(byId);
    }

    @Override
    public List<TbCdcdmEntityErModel> list(EntityErQueryDTO queryDTO) {
        LambdaQueryWrapper<TbCdcdmEntityErModel> lambdaQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDTO.getQueryKey())){
            lambdaQuery.like(TbCdcdmEntityErModel::getObjName, queryDTO.getQueryKey());
        }
        lambdaQuery.orderByDesc(TbCdcdmEntityErModel::getCreateTime);
        List<TbCdcdmEntityErModel> tbCdccsTbCdcdmEntityErModels = list(lambdaQuery);
        Gson gson = new Gson();
        return tbCdccsTbCdcdmEntityErModels.stream().map(t -> convert(t, gson)).collect(Collectors.toList());
    }
    @Override
    public void beforeCreate(TbCdcdmEntityErModel entity) {
        super.beforeCreate(entity);
        duplicate(entity, "编码重复", TbCdcdmEntityErModel::getObjCode);
        convertToJson(entity);
    }

    @Override
    public void beforeUpdate(TbCdcdmEntityErModel entity) {
        super.beforeUpdate(entity);
        duplicate(entity, "编码重复", TbCdcdmEntityErModel::getObjCode);
        convertToJson(entity);
    }

    @Override
    public PageInfo<TbCdcdmEntityErModel> pageList(EntityErQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        Gson gson = new Gson();
        PageInfo<TbCdcdmEntityErModel> tbCdcdmEntityErModelPageInfo = new PageInfo<>(tbCdccsEntityErModelMapper.pageList(queryDTO));
        tbCdcdmEntityErModelPageInfo.getList().forEach(d -> {
            convert(d, gson);
        });
        return tbCdcdmEntityErModelPageInfo;
    }

    @Override
    public TbCdcdmEntityErModel saveByCode(TbCdcdmEntityErModel input) {
        if (StringUtils.isNotEmpty(input.getObjCode())){
            TbCdcdmEntityErModel tbCdcdmEntityErModel = getBaseMapper().selectOne(lambdaQueryWrapper().eq(TbCdcdmEntityErModel::getObjCode, input.getObjCode()));
            if (tbCdcdmEntityErModel != null){
                input.setId(tbCdcdmEntityErModel.getId());
                return update(input);
            }else {
                return create(input);
            }
        }else {
           return create(input);
        }
    }


    /**
     * 转换设置er 模型
     */
    private TbCdcdmEntityErModel convertToJson(TbCdcdmEntityErModel model){
        Gson gson = new Gson();
        model.setRelationJson(gson.toJson(model.getEntityRelation()));
        return model;
    }
    /**
     * 转换设置er 模型
     */
    private TbCdcdmEntityErModel convert(TbCdcdmEntityErModel model){
        Gson gson = new Gson();
        model.setEntityRelation(gson.fromJson(model.getRelationJson(), EntityRelation.class));
        return model;
    }

    /**
     * 转换设置er 模型
     */
    private TbCdcdmEntityErModel convert(TbCdcdmEntityErModel model, Gson gson){
        model.setEntityRelation(gson.fromJson(model.getRelationJson(), EntityRelation.class));
        return model;
    }
}
