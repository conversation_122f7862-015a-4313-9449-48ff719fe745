package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.constant.SendSMSTypeEnum;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.enums.CodeTableEnum;
import com.iflytek.cdc.admin.evaluation.builder.QueryBuilders;
import com.iflytek.cdc.admin.mapper.MedicalWarnMapper;
import com.iflytek.cdc.admin.mapper.MedicalWarnRuleMapper;
import com.iflytek.cdc.admin.sdk.entity.QueryWarnRuleInfoFilter;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.MedicalWarnService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.cdc.admin.util.MyCollectionUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.util.StringUtils;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;


@Slf4j
@Service("medicalWarnService")
public class MedicalWarnServiceImpl extends AbstractSyncMdmDataService implements MedicalWarnService {
    @Resource
    private ParamConfigService paramConfigService;

    @Autowired
    private MedicalWarnMapper medicalWarnMapper;

    @Autowired
    private MedicalWarnRuleMapper medicalWarnRuleMapper;

    @Autowired
    private UapUserService uapUserService;

    public MedicalWarnServiceImpl(UapUserApi uapUserApi, MdmDataSyncService mdmDataSyncService, BatchUidService batchUidService) {
        super(uapUserApi, mdmDataSyncService, batchUidService);
    }





    @Override
    public ResponseResult medicalWarnRuleList(String wranId, String loginUserId) {
        if (StringUtils.isBlank(wranId)) {
            return new ResponseResult(Constants.ERROR_CODE, "参数异常");
        }
        // 校验warnId是否存在
        MedicalWarn medicalWarn = medicalWarnMapper.selectByPrimaryKey(wranId);
        ;
        if (medicalWarn == null || medicalWarn.getId() == null) {
            return new ResponseResult(Constants.ERROR_CODE, "当前传染病病种不存在");
        }
        // 查询当前病种下所有的规则
        List<MedicalWarnRule> ruleList = medicalWarnRuleMapper.queryByWranId(wranId, medicalWarn.getDiseaseCode());
        medicalWarn.setRuleList(ruleList);

        return new ResponseResult(medicalWarn);
    }

    @Override
    public ResponseResult medicalWarnPageList(MedicalWarnPageListDto medicalWarnPageListDto, String loginUserId) {
        // 获取uap信息
        UapUserExtend userInfo = uapUserService.getUserInfo(loginUserId);
        PageHelper.startPage(medicalWarnPageListDto.getPageIndex(), medicalWarnPageListDto.getPageSize());
        // 查询父级节点
        PageInfo<MedicalWarnPageListResponseDto> pageInfo = new PageInfo<>(medicalWarnMapper.pageList(medicalWarnPageListDto));
        //插叙子节点
        List<MedicalWarnPageListResponseDto> subList = medicalWarnMapper.subPageList(medicalWarnPageListDto);
        pageInfo.getList().forEach(e -> {
            List<MedicalWarnPageListResponseDto> newList = new ArrayList<>();
            subList.forEach(s -> {
                if (e.getDiseaseCode().equals(s.getDiseaseParentCode())) {
                    newList.add(s);
                }
            });
            e.setSubList(newList);
        });
        return new ResponseResult(pageInfo);
    }

    @Override
    @Transactional
    public ResponseResult medicalWarnModifyStatus(String id, String status) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(status)) {
            return new ResponseResult(Constants.ERROR_CODE, "参数异常");
        }
        medicalWarnMapper.medicalWarnModifyStatus(id, status);
        return new ResponseResult(Constants.SUCCESS_CODE, Constants.SUCCESS_MSG);
    }

    @Override
    public Boolean isCanModify(String id) {
//         判断是否可以修改状态
//        TODO 临时取消判断
        MedicalWarn medicalWarn = medicalWarnMapper.selectById(id);
        String diseaseCode = medicalWarn.getDiseaseCode();
        if (!isCanStart(diseaseCode)) {
            return false;
        }
        return true;
    }

    @Override
    public void medicalWarnExport(HttpServletResponse response, String loginUserId) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        // 调用uap获取用户信息
        UapUserExtend userInfo = uapUserService.getUserInfo(loginUserId);
        String areaCode = userInfo.getAreaCode();


        List<WarnRuleExportDataDto> dataList = medicalWarnMapper.queryChooseExportData(areaCode);

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 修改状态名称
        dataList.forEach(this::formatWarnRuleExportDataDto);

        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<MedicalWarn> queryAll(QueryWarnRuleInfoFilter dto) {
        String diseaseCode = dto.getDiseaseCode();
        // 判断当前病种code是否可用
        if (StringUtil.isNotEmpty(diseaseCode)) {
            if (!isCanStart(diseaseCode)) {
                throw new MedicalBusinessException("当前病种在mdm系统中不可用或不存在");
            }
        }
        // 去DB中查询
        List<MedicalWarnRule> ruleList = medicalWarnRuleMapper.selectAll();
        List<MedicalWarn> medicalWarn = medicalWarnMapper.selectAll();
        medicalWarn.forEach(e -> {
            List<MedicalWarnRule> addList = ruleList.stream().filter(r ->
                    r.getDiseaseCode().equals(e.getDiseaseCode())
            ).collect(Collectors.toList());
            e.setRuleList(addList);
        });
        return medicalWarn;
    }

    @Override
    public MedicalWarn queryById(String id, String loginUserId) {
        MedicalWarn warn = medicalWarnMapper.selectByPrimaryKey(id);
        warn.setRuleList(medicalWarnRuleMapper.queryByWranId(warn.getId(), warn.getDiseaseCode()));
        return warn;
    }

    @Override
    public ResponseResult editById(MedicalWarnEditDto editDto) {
        medicalWarnMapper.editMedicalWarn(editDto);
        return new ResponseResult(Constants.SUCCESS_CODE, Constants.SUCCESS_MSG);

    }


    private  void setDiseaseTypeCode(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getDiseaseTypeCode())) {
            warnRuleExportDataDto.setDiseaseTypeCode("甲类");
        } else if ("2".equals(warnRuleExportDataDto.getDiseaseTypeCode())) {
            warnRuleExportDataDto.setDiseaseTypeCode("乙类");
        } else if ("3".equals(warnRuleExportDataDto.getDiseaseTypeCode())) {
            warnRuleExportDataDto.setDiseaseTypeCode("丙类");
        } else if ("9".equals(warnRuleExportDataDto.getDiseaseTypeCode())) {
            warnRuleExportDataDto.setDiseaseTypeCode("其他传染病");
        } else {
            warnRuleExportDataDto.setDiseaseTypeCode("/");
        }
    }

    private  void setBusinessType(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("t".equals(warnRuleExportDataDto.getBusinessType())) {
            warnRuleExportDataDto.setBusinessType("一级");
        } else if ("f".equals(warnRuleExportDataDto.getBusinessType())) {
            warnRuleExportDataDto.setBusinessType("二级");
        } else {
            warnRuleExportDataDto.setBusinessType("/");
        }
    }

    private  void setSingleCase(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getIsSingleCase())) {
            warnRuleExportDataDto.setIsSingleCase("是");
        } else if ("0".equals(warnRuleExportDataDto.getIsSingleCase())) {
            warnRuleExportDataDto.setIsSingleCase("否");
        } else {
            warnRuleExportDataDto.setIsSingleCase("/");
        }
    }

    private void setEventGenerationNode(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getEventGenerationNode())) {
            warnRuleExportDataDto.setEventGenerationNode("报卡弹报");
        } else if ("2".equals(warnRuleExportDataDto.getEventGenerationNode())) {
            warnRuleExportDataDto.setEventGenerationNode("医生提交");
        } else if ("3".equals(warnRuleExportDataDto.getEventGenerationNode())) {
            warnRuleExportDataDto.setEventGenerationNode("防保科审核");
        } else if ("4".equals(warnRuleExportDataDto.getEventGenerationNode())) {
            warnRuleExportDataDto.setEventGenerationNode("上报大疫情网");
        } else {
            warnRuleExportDataDto.setEventGenerationNode("/");
        }
    }

    private void setSilenceDuration(WarnRuleExportDataDto warnRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(warnRuleExportDataDto.getSilenceDuration())) {
            warnRuleExportDataDto.setSilenceDuration(warnRuleExportDataDto.getSilenceDuration() + "分钟");
        } else {
            warnRuleExportDataDto.setSilenceDuration("/");
        }
    }

    private void setAiRemovedDuration(WarnRuleExportDataDto warnRuleExportDataDto) {
        if (warnRuleExportDataDto.getAiRemovedDuration() != null) {
            warnRuleExportDataDto.setAiRemovedDuration(warnRuleExportDataDto.getAiRemovedDuration() + "天");

        } else {
            warnRuleExportDataDto.setAiRemovedDuration("/");
        }
    }

    private void setMonitorObject(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getMonitorObject())) {
            warnRuleExportDataDto.setMonitorObject("等级医院");
        } else if ("2".equals(warnRuleExportDataDto.getMonitorObject())) {
            warnRuleExportDataDto.setMonitorObject("基层医疗");
        } else if ("3".equals(warnRuleExportDataDto.getMonitorObject())) {
            warnRuleExportDataDto.setMonitorObject("学校/单位");
        } else if ("4".equals(warnRuleExportDataDto.getMonitorObject())) {
            warnRuleExportDataDto.setMonitorObject("街道区域");
        } else {
            warnRuleExportDataDto.setMonitorObject("/");
        }
    }

    private void setTimeScope(WarnRuleExportDataDto warnRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(warnRuleExportDataDto.getTimeScope())) {
            if ("1".equals(warnRuleExportDataDto.getTimeScopeUnit())) {
                warnRuleExportDataDto.setTimeScope("≤" + warnRuleExportDataDto.getTimeScope() + "天");
            } else if ("2".equals(warnRuleExportDataDto.getTimeScopeUnit())) {
                warnRuleExportDataDto.setTimeScope("≤" + warnRuleExportDataDto.getTimeScope() + "周");
            } else if ("3".equals(warnRuleExportDataDto.getTimeScopeUnit())) {
                warnRuleExportDataDto.setTimeScope("≤" + warnRuleExportDataDto.getTimeScope() + "月");
            }
        } else {
            warnRuleExportDataDto.setTimeScope("/");
        }
    }

    private void setCount(WarnRuleExportDataDto warnRuleExportDataDto) {
        if (warnRuleExportDataDto.getMedicalCount() != null){
            warnRuleExportDataDto.setCount(String.format("≥%s", warnRuleExportDataDto.getMedicalCount()));
        }else {
            warnRuleExportDataDto.setCount("/");
        }

    }

    private void setMedicalAttribute(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("疑似病例");
        } else if ("2".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("临床诊断病历");
        } else if ("3".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("确诊病例");
        } else if ("4".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("病原携带者");
        } else if ("5".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("阳性检测");
        } else if ("99".equals(warnRuleExportDataDto.getMedicalAttribute())) {
            warnRuleExportDataDto.setMedicalAttribute("全部");
        } else {
            warnRuleExportDataDto.setMedicalAttribute("/");
        }
    }

    private void setWarnType(WarnRuleExportDataDto warnRuleExportDataDto) {
        if ("1".equals(warnRuleExportDataDto.getWarnType())) {
            warnRuleExportDataDto.setWarnType("预警信号");
        } else if ("2".equals(warnRuleExportDataDto.getWarnType())) {
            warnRuleExportDataDto.setWarnType("上报提示");
        } else {
            warnRuleExportDataDto.setWarnType("/");
        }
    }

    /**
     * 设置判断依据
     */
    private  void setType(WarnRuleExportDataDto warnRuleExportDataDto) {
        warnRuleExportDataDto.setType(Constants.WarnRule.getGenerateType(warnRuleExportDataDto.getType()));
    }

    @NotNull
    private Boolean isCanStart(String diseaseCode) {
        String dataCode = CodeTableEnum.INFECT_DIS.getCode();
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(dataCode);
        //判断字典目录是否存在
        if (isCanUse(dataCode)) {
            log.error("未查询到mdm该字典目录，或者该字典目录已被禁用:{}", di.getTargetCode());
            return false;
        }
        //查询字典值域
        String[] codeValue = {diseaseCode};
        //获取字典值域数据
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfo(di, MdmDataSyncConstants.SYNDROME_PAGENUMBER,
                MdmDataSyncConstants.SYNDROME_PAGESIZE, codeValue);
        if (CollUtil.isEmpty(mdmData.getEntities())) {
            return false;
        }
        return !org.apache.commons.lang3.StringUtils.isBlank(mdmData.getEntities().get(0).getId().toString());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult saveOrUpdateMedicalWarnRule(MedicalWarnRuleSaveOrUpdateDto dto, String loginUserId) {
        if (dto == null) {
            return new ResponseResult("参数不能为空");
        }

        //获取登录人员信息
        UapUserExtend userInfo = uapUserService.getUserInfo(loginUserId);

        dto.setLoginUserId(loginUserId);
        // 先更新tb_cdcmr_medical_warn表
        medicalWarnMapper.modify(dto);

        // 逻辑删除原病种下的所有规则
        medicalWarnRuleMapper.deleteRules(dto.getId(), dto.getDiseaseCode());

        // 新增记录
        List<MedicalWarnRule> ruleList = dto.getRuleList();
        if (MyCollectionUtil.isNotNullAndSizeMoreThanZero(ruleList)) {
            // 插入记录
            ruleList.forEach(medicalWarnRule -> {
                Integer orginalTimeScope = medicalWarnRule.getTimeScope();
                medicalWarnRule.setRuleJson(null);
                medicalWarnRule.setDiseaseCode(dto.getDiseaseCode());
                medicalWarnRule.setDiseaseName(dto.getDiseaseName());
                medicalWarnRule.setRuleId(dto.getId());
                String id = String.valueOf(batchUidService.getUid(TableName.MEDICAL_WARN_RULE));
                medicalWarnRule.setId(id);
                medicalWarnRule.setCreateDatetime(new Date());
                medicalWarnRule.setCreator(userInfo.getId());
                medicalWarnRule.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
                medicalWarnRule.setRuleJson(this.generatorJson(medicalWarnRule));
                medicalWarnRule.setTimeScope(orginalTimeScope);
                medicalWarnRuleMapper.insert(medicalWarnRule);
            });
        }
        return new ResponseResult(Constants.SUCCESS_CODE, Constants.SUCCESS_MSG);
    }

    @Override
    @Transactional
    public void addMedicalWarn(MedicalWarn medicalWarn, String loginUserId) {
        if (medicalWarn == null){
            throw new MedicalBusinessException("参数不能为空！");
        }
        medicalWarn.setId(String.valueOf(batchUidService.getUid(TableName.MEDICAL_WARN)));
        medicalWarn.setCreateDatetime(new Date());
        medicalWarn.setCreator(loginUserId);
        medicalWarn.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
        medicalWarnMapper.insert(medicalWarn);
    }

    public String generatorJson(MedicalWarnRule medicalWarnRule) {
        RuleJsonDto ruleJsonDto = new RuleJsonDto();
        // 处理时间：任何时间单位过来都转换成以天为单位
        Integer timeScope = medicalWarnRule.getTimeScope();
        String timeUnit = medicalWarnRule.getTimeUnit();
        switch (timeUnit) {
            case Constants.COMMON_STRNUM_TWO:
                medicalWarnRule.setTimeScope(7 * timeScope);
                break;
            case Constants.COMMON_STRNUM_THREE:
                medicalWarnRule.setTimeScope(30 * timeScope);
                break;
            default:
                break;
        }
        BeanUtils.copyProperties(medicalWarnRule, ruleJsonDto);
        ruleJsonDto.setTimeUnit(Constants.TIME_UNIT_DAY);
        JSONObject threeJson = JSONObject.parseObject(JSONObject.toJSONString(ruleJsonDto));

        JSONObject twoJson = new JSONObject();
        twoJson.put(medicalWarnRule.getId(), threeJson);
        String rangeJson = "";
        switch (medicalWarnRule.getMedicalOperate()) {
            // 小于等于     1=小于等于    2=小于    3=大于等于    4=大于   5=等于
            case Constants.COMMON_STRNUM_ONE:
                rangeJson = QueryBuilders.rangeQuery(medicalWarnRule.getId()).to(medicalWarnRule.getMedicalCount(), true).toJson().toString();
                break;
            // 小于
            case Constants.COMMON_STRNUM_TWO:
                rangeJson = QueryBuilders.rangeQuery(medicalWarnRule.getId()).to(medicalWarnRule.getMedicalCount(), false).toJson().toString();
                break;
            // 大于等于
            case Constants.COMMON_STRNUM_THREE:
                rangeJson = QueryBuilders.rangeQuery(medicalWarnRule.getId()).from(medicalWarnRule.getMedicalCount(), true).toJson().toString();
                break;
            // 大于
            case Constants.COMMON_STRNUM_FOUR:
                rangeJson = QueryBuilders.rangeQuery(medicalWarnRule.getId()).from(medicalWarnRule.getMedicalCount(), false).toJson().toString();
                break;
            // 等于
            case Constants.COMMON_STRNUM_FIVE:
                JSONObject jsonObjectTime = new JSONObject();
                jsonObjectTime.put("eq", medicalWarnRule.getMedicalCount());
                JSONObject jsonObjectId = new JSONObject();
                jsonObjectId.put(medicalWarnRule.getId(), jsonObjectTime);
                JSONObject jsonObjectRange = new JSONObject();
                jsonObjectRange.put("range", jsonObjectId);
                rangeJson = jsonObjectRange.toJSONString();
                break;
            default:
                break;
        }

        JSONObject res = new JSONObject();
        res.put("data", twoJson);
        res.put("rule", JSONObject.parseObject(rangeJson));

        return JSONObject.toJSONString(res);
    }

    @Override
    @Transactional
    public void addByInfectiousDisease(InfectiousDiseases infectiousDiseases) {
        MedicalWarn medicalWarn = new MedicalWarn();
        medicalWarn.setDiseaseCode(infectiousDiseases.getDiseasesCode());
        medicalWarn.setDiseaseName(infectiousDiseases.getDiseasesName());
        medicalWarn.setRuleStatus(Constants.COMMON_STRNUM_ZERO);
        addMedicalWarn(medicalWarn, infectiousDiseases.getCreateUser());
    }

    public List<TermCodedValueInfo> getMdmDataSync(String code) {
        //判断字典目录是否存在
        if (isCanUse(code)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        List<TermCodedValueInfo> mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(code,
                MdmDataSyncConstants.SYNDROME_PAGENUMBER, MdmDataSyncConstants.SYNDROME_PAGESIZE);
        return mdmCodeValues;
    }


    @Override
    public ResponseResult syncMdmData(String code, String loginUserId) {
        //todo 等待mdm修改字典目录接口，获取字典目录状态，如果被删除或被禁用，则直接将所有本地数据状态修改为禁用。
        //        //先查询本地数据表所有的编码数据
        code = MdmDataSyncConstants.getMap().get(code);
        if (isCanUse(code)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        executeSyncMdmData(code, loginUserId);
        //同步成功，返回true，表示同步
        return new ResponseResult(Constants.SUCCESS_CODE, "同步成功");
    }


    @Override
    @Transactional
    public ResponseResult singleCaseSwitch(MedicalWarnEditDto editDto, String loginUserId) {
        if (editDto.getIsSingleCase() == 1) {
            List<MedicalWarnRule> rules = medicalWarnRuleMapper.queryByWranId(editDto.getId(), editDto.getDiseaseCode());
            List<String> ids = rules.stream().map(MedicalWarnRule::getId).collect(Collectors.toList());
            if (!ids.isEmpty()) {
                medicalWarnRuleMapper.updateSingleCaseByIds(ids);
            }
        }

        medicalWarnMapper.editMedicalWarn(editDto);
        return new ResponseResult(Constants.SUCCESS_CODE, "修改成功");
    }

    @Override
    public List<SendSMSTypeDto> getTypeList(Integer type) {
        List<SendSMSTypeDto> result = new ArrayList<>();

        SendSMSTypeEnum[] values = SendSMSTypeEnum.values();
        for (SendSMSTypeEnum typeEnum : values) {
            SendSMSTypeDto sendSMSTypeDto = new SendSMSTypeDto();

            sendSMSTypeDto.setCode(typeEnum.getCode());
            sendSMSTypeDto.setDesc(typeEnum.getDesc());

            String code = sendSMSTypeDto.getCode();
            if (Integer.parseInt(code) < 0) {
                if (1 == type) {
                    result.add(sendSMSTypeDto);
                }
            } else {
                if (2 == type) {
                    result.add(sendSMSTypeDto);
                }
            }
        }

        return result;
    }

    public void executeSyncMdmData(String code, String loginUserId) {

        //获取本地数据
        List<MedicalWarn> medicalWarnRules = queryInfecInfo(new SearchInfecInfoDTO());
        //获取mdm主数据平台数据
        long startTime = System.currentTimeMillis();
        List<TermCodedValueInfo> mdmCodeValues = getMdmDataSync(code);
        if (CollectionUtils.isEmpty(mdmCodeValues)) {
            log.warn("同步MDM传染病病种数据为空");
            return;
        }
        long endTime = System.currentTimeMillis();
        log.warn("同步病种数据运行时间： " + (endTime - startTime) + "ms");
        insertSyncMdmData(loginUserId, medicalWarnRules, mdmCodeValues);
        updateSyncMdmData(loginUserId, medicalWarnRules, mdmCodeValues);
        updateCodeNme(loginUserId, medicalWarnRules, mdmCodeValues);
    }


    public List<MedicalWarn> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        List<MedicalWarn> medicalWarns = medicalWarnMapper.queryInfecInfo(searchInfecInfoDTO);
        return medicalWarns;
    }

    public void updateSyncMdmData(String loginUserId, List<MedicalWarn> medicalWarnRule, List<TermCodedValueInfo> mdmCodeValues) {
        //获取登录人员信息
        TUapUser uapUser = getUapUser(loginUserId);
        //取出本地库中存在而mdm不存在的数据
        List<MedicalWarn> localExistData = medicalWarnRule.stream().filter(s -> mdmCodeValues.stream().map(TermCodedValueInfo::getCodedValue).
                noneMatch(code -> Objects.equals(s.getDiseaseCode(), code))).collect(Collectors.toList());
        //本地库中存在而mdm不存在的数据
        UpdateInfecDTO ud = new UpdateInfecDTO();
        List<String> updateIds = new ArrayList<>();
        localExistData.forEach(localData -> {
            updateIds.add(localData.getId());
        });
        ud.setUpdateUser(uapUser.getId());
        ud.setIsEnable(MdmDataSyncConstants.NO_USE);
        ud.setIds(updateIds);
        //批量更新mdm已删除的数据状态
        if (updateIds.size() > 0) {
            medicalWarnMapper.updateInfecInfo(ud);
        }
    }


    public void insertSyncMdmData(String loginUserId, List<MedicalWarn> medicalWarnRule, List<TermCodedValueInfo> mdmCodeValues) {
        //获取登录人员信息
        TUapUser uapUser = getUapUser(loginUserId);
        //取出mdm库中存在而本地库中不存在的数据
        List<TermCodedValueInfo> mdmExistData = mdmCodeValues.stream().filter(mdm -> medicalWarnRule.stream().map(MedicalWarn::getDiseaseCode).
                noneMatch(code -> Objects.equals(mdm.getCodedValue(), code))).collect(Collectors.toList());
        //如果mdm库中存在而本地库中不存在的数据，则直接插入到本地库中
        List<MedicalWarn> medicalWarnRules = new ArrayList<MedicalWarn>();
        //格式化需要插入数据库中的数据
        mdmExistData.forEach(mdmData -> {
            MedicalWarn si = new MedicalWarn();
            si.setId(String.valueOf(batchUidService.getUid(TableName.MEDICAL_WARN)));
            si.setDiseaseCode(mdmData.getCodedValue());
            si.setDiseaseName(mdmData.getDescription());
            si.setCreator(uapUser.getId());
            si.setDeleteFlag(MdmDataSyncConstants.MDM_NO_DELETE);
            si.setRuleStatus(MdmDataSyncConstants.YES_USE);
            medicalWarnRules.add(si);
        });
        if (medicalWarnRules.size() > 0) {
            medicalWarnMapper.insertMedicalWarns(medicalWarnRules);
        }
    }

    public void updateCodeNme(String loginUserId, List<MedicalWarn> medicalWarnRules, List<TermCodedValueInfo> mdmCodeValues) {
        TUapUser uapUser = getUapUser(loginUserId);
        //取出本地库中存在而mdm也存在的数据
        List<TermCodedValueInfo> allExistData = mdmCodeValues.stream().filter(mdm -> medicalWarnRules.stream().map(MedicalWarn::getDiseaseCode).anyMatch(
                code -> Objects.equals(mdm.getCodedValue(), code))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(allExistData)) {
            allExistData.forEach(allData ->
                    medicalWarnMapper.updateCodeName(allData.getCodedValue(), allData.getDescription(), uapUser.getId()));
        }
    }

    /**
     * 格式导出的数据
     */
    private void formatWarnRuleExportDataDto(WarnRuleExportDataDto dto){
        setDiseaseTypeCode(dto);
        setBusinessType(dto);
        setCount(dto);
        setAiRemovedDuration(dto);
        setMonitorObject(dto);
        setEventGenerationNode(dto);
        setMedicalAttribute(dto);
        setSilenceDuration(dto);
        setSingleCase(dto);
        setTimeScope(dto);
        setWarnType(dto);
        setType(dto);
    }





}
