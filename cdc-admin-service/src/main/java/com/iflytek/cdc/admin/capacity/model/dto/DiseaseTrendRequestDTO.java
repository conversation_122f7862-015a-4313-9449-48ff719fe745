package com.iflytek.cdc.admin.capacity.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("疾病趋势预测请求")
public class DiseaseTrendRequestDTO {
    @ApiModelProperty(value = "历史数据")
    private List<Integer> historyData;//历史数据
    @ApiModelProperty(value = "预测的步数")
    private Integer predictStep;//预测的步数
    @ApiModelProperty(value = "传染病类型")
    private String disease;//传染病类型，如果为空则采用通用模型
    
    @ApiModelProperty(value = "0到3岁人数")
    private Integer ageGroup0To3Num;
    
    @ApiModelProperty(value = "4到6岁人数")
    private Integer ageGroup4To6Num;
    
    @ApiModelProperty(value = "7到18岁人数")
    private Integer ageGroup7To18Num;
    
    @ApiModelProperty(value = "19到60岁人数")
    private Integer ageGroup19To60Num;

    @ApiModelProperty(value = "年龄段60以上")
    private Integer ageGroupApprove60Num;

    @ApiModelProperty("男性数量")
    private Integer maleNum;

    @ApiModelProperty("女性数量")
    private Integer femaleNum;

    @ApiModelProperty(value =  "历史数据的发病月份")
    @NotNull
    private Integer historyDataMonth;//历史数据的发病月份，取占的多数就行，比如7月2天且8月5天，则填8月
}
