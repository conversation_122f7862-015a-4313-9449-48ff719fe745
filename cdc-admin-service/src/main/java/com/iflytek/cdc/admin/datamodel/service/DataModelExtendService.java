package com.iflytek.cdc.admin.datamodel.service;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelExtend;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.service.ICdcService;

/**
 * 数据模型扩展
 */
public interface DataModelExtendService extends ICdcService<TbCdcdmDataModelExtend> {

    /**
     * 添加模型扩展
     */
    void saveDataModelExtend(String modelId,
                             String modelVersionId,
                             String entityErModelId);

    /**
     * 根据模型ID加载实体模型。
     * @return 返回根据模型ID加载的实体模型。
     */
    String loadErModelIdByModelId(String modelId);
    
    /**
     * 根据模型ID加载实体模型。
     * @return 返回根据模型ID加载的实体模型。
     */
    TbCdcdmEntityErModel loadByModelId(String modelId);
}
