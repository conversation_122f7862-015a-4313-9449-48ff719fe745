package com.iflytek.cdc.admin.customizedapp.service;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.enums.VersionTypeEnum;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface VersionService extends ICdcService<TbCdccsVersion> {

    /**
     * 版本创建
     * @param businessId 业务id
     * @param businessKey 业务标识
     * @param businessType 业务类型
     */
    void create( String businessId, String businessKey, String versionName, VersionTypeEnum businessType);

    /**
     * 获取发布的版本
     * @param businessKey 业务标识
     * @param businessType 业务类型
     * @return 版本
     */
    TbCdccsVersion loadPublished(String businessKey, VersionTypeEnum businessType);

    /**
     * 获取发布的版本
     * @param businessKey 业务标识
     * @param businessType 业务类型
     * @return 版本的业务数据id
     */
    String loadPublishedBusinessId(String businessKey, VersionTypeEnum businessType);

    /**
     * 版本发布
     * @param businessId 业务数据id
     * @param businessType 业务类型
     */
    void published(String businessId, VersionTypeEnum businessType);

    /**
     * 获取业务数据的版本
     * @param businessId 业务数据id
     * @param businessType 业务类型
     * @return 版本
     */
    TbCdccsVersion loadByBusinessId(String businessId, VersionTypeEnum businessType);

    /**
     * 根据业务数据key 查询
     * @param businessKey 业务数据标识
     * @param businessType 业务类型
     * @return 版本集合
     */
    List<TbCdccsVersion> listByBusinessKey(String businessKey, VersionTypeEnum businessType);
}
