package com.iflytek.cdc.admin.datamodel.mapper;

import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataAttr;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataAttrValue;
import com.iflytek.cdc.admin.model.dm.vo.DataDictListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @deprecated 使用 TbCdcdmDataDictMapper 替代
 */
@Mapper
public interface ValueDomainManagementMapper {

    List<DataDictListVO> getValueDomainList(@Param("id") String id,
                                            @Param("name") String name,
                                            @Param("attrCount") Integer attrCount);

    void editValueDomain(TbCdcdmDataAttr tbCdcdmDataAttr);

    List<TbCdcdmDataAttrValue> getValueDomainDetailList(@Param("dataAttrId") String dataAttrId);

    List<TbCdcdmDataAttrValue> getDetailListByName(@Param("id") String id,
                                                   @Param("name") String name);

    void batchInsertIntoValue(List<TbCdcdmDataAttrValue> values);

    void updateValueDetail(String id, String loginUserName);

    void updateDataAttr(@Param("attrId") String attrId);
}
