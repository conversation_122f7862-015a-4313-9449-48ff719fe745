package com.iflytek.cdc.admin.controller.old;


import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionParam;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionVO;
import com.iflytek.cdc.admin.service.ClientUpgradeUrlService;
import com.iflytek.cdc.admin.service.ClientUpgradeVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;



/**
 * @ClassName ClientUpgradeController
 * @Description 客户端升级服务地址和版本配置管理
 * <AUTHOR>
 * @Date 2021/6/30 14:18
 * @Version 1.0
 */

@Api(tags = "客户端版本升级管理")
@RestController
@AllArgsConstructor
public class ClientUpgradeController {

    @Resource
    private ClientUpgradeUrlService clientUpgradeUrlService;

    @Resource
    private ClientUpgradeVersionService clientUpgradeVersionService;


    /**
     * 机构升级服务配置主界面不同机构的服务地址查询接口
     * @param clientUpgradeParamDto
     * @param loginUserId
     * @return
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级服务配置-查询")
    @PostMapping("/{version}/pt/client/upgrade/queryClientUpgradeUrl")
    public PageData<ClientUpgradeUrlDto> queryClientUpgradeUrlDto(@RequestBody @Valid ClientUpgradeParamDto clientUpgradeParamDto,@RequestParam String loginUserId) {
        return clientUpgradeUrlService.queryClientUpgradeUrlDto(clientUpgradeParamDto,loginUserId);
    }


    /**
     * 机构升级服务配置主界面
     *
     * @param clientUpgradeUrlDto
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级服务配置-新增")
    @PostMapping("/{version}/pt/client/upgrade/addClientUpgradeUrl")
    public void addClientUpgradeUrlDto(@RequestBody ClientUpgradeUrlDto clientUpgradeUrlDto,@RequestParam String loginUserId) {
        clientUpgradeUrlService.addClientUpgradeUrlDto(clientUpgradeUrlDto,loginUserId);
    }


    /**
     * 机构升级服务配置主界面修改
     *
     * @param clientUpgradeUrlDto
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级服务配置-修改")
    @PostMapping("/{version}/pt/client/upgrade/updateClientUpgradeUrl")
    public void updateClientUpgradeUrlDto(@RequestBody ClientUpgradeUrlDto clientUpgradeUrlDto,@RequestParam String loginUserId) {
        clientUpgradeUrlService.updateClientUpgradeUrlDto(clientUpgradeUrlDto,loginUserId);
    }

    /**
     * 机构版本版本维度查询
     *
     * * @return
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-维度查询")
    @PostMapping("/{version}/pt/client/upgrade/queryClientUpgradeVersion")
    public PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDto(@RequestBody @Valid ClientUpgradeParamDto clientUpgradeParamDto,@RequestParam String loginUserId) {
        return clientUpgradeVersionService.queryClientUpgradeVersionDto(clientUpgradeParamDto,loginUserId);
    }


    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-历史版本")
    @PostMapping("/{version}/pt/client/upgrade/queryClientUpgradeVersionHis")
    public PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDtoHis(@RequestBody @Valid ClientUpgradeParamDto clientUpgradeParamDto,@RequestParam String loginUserId) {
        return clientUpgradeVersionService.queryClientUpgradeVersionDtoHis(clientUpgradeParamDto,loginUserId);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-根据版本查询机构")
    @PostMapping("/{version}/pt/client/upgrade/queryClientUpgradeByVersionCode")
    public List<ClientUpgradeVersionDto> queryClientUpgradeByVersionCode(@RequestBody @Valid ClientUpgradeParamDto clientUpgradeParamDto,@RequestParam String loginUserId) {
        return clientUpgradeVersionService.queryClientUpgradeByVersionCode(clientUpgradeParamDto,loginUserId);
    }


    /**
     * 机构版本配置主界面
     *
     * @param clientUpgradeVersionDto
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-新增")
    @PostMapping("/{version}/pt/client/upgrade/addClientUpgradeVersion")
    public void addClientUpgradeVersionDto(@RequestBody ClientUpgradeVersionDto clientUpgradeVersionDto,@RequestParam String loginUserId) {
        clientUpgradeVersionService.addClientUpgradeVersionDto(clientUpgradeVersionDto,loginUserId);
    }

    /**
     * 机构版本修改主界面
     *
     * @param clientUpgradeVersionDto
     */
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-修改")
    @PostMapping("/{version}/pt/client/upgrade/updateClientUpgradeVersion")
    public void updateClientUpgradeVersionDto(@RequestBody ClientUpgradeVersionDto clientUpgradeVersionDto,@RequestParam String loginUserId) {
        clientUpgradeVersionService.updateClientUpgradeVersionDto(clientUpgradeVersionDto,loginUserId);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-删除")
    @PostMapping("/{version}/pt/client/upgrade/deleteClientUpgradeVersion")
    public void deleteClientUpgradeVersionDto(@RequestBody ClientUpgradeVersionDto clientUpgradeVersionDto,@RequestParam String loginUserId) {
        clientUpgradeVersionService.deleteClientUpgradeVersionDto(clientUpgradeVersionDto,loginUserId);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("机构升级版本配置-根据版本查询多个机构合集")
    @PostMapping("/{version}/pt/client/upgrade/queryOrgClientUpgradeById")
    public ClientUpgradeVersionDto queryOrgClientUpgradeById(@RequestBody @Valid ClientUpgradeParamDto clientUpgradeParamDto,@RequestParam String loginUserId) {
        return clientUpgradeVersionService.queryOrgClientUpgradeById(clientUpgradeParamDto,loginUserId);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation(value = "查询客户端升级版本")
    @PostMapping("/{version}/pt/client/upgrade/queryClientUpgradeVersionInfo")
    public List<PackageVersionVO> queryClientUpgradeVersionInfo(@PathVariable String version, @RequestBody ClientUpgradeFilter clientUpgradeFilter) {
        return clientUpgradeVersionService.queryClientUpgradeVersion(clientUpgradeFilter);
    }
}
