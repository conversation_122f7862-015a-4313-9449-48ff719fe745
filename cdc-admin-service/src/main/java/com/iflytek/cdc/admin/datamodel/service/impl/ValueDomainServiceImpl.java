package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.ApiService;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.datamodel.service.ValueDomainService;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.enums.DataDictTypeEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcdmDataDictMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcdmDataDictValueMapper;
import com.iflytek.cdc.admin.model.dm.dto.*;
import com.iflytek.cdc.admin.model.dm.vo.DataDictListVO;
import com.iflytek.cdc.admin.model.dm.vo.DataDictObjectVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

import com.alibaba.excel.EasyExcel;
import com.iflytek.cdc.admin.datamodel.listener.ValueDomainImportListener;
import com.iflytek.cdc.admin.datamodel.model.dto.ValueDomainImportDTO;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.ByteArrayOutputStream;

@Service
@Slf4j
public class ValueDomainServiceImpl implements ValueDomainService {

    public static final String TB_CDCDM_DATA_DICT = "tb_cdcdm_data_dict";

    public static final String TB_CDCDM_DATA_DICT_VALUE = "tb_cdcdm_data_dict_value";

    @Resource
    private TbCdcdmDataDictMapper tbCdcdmDataDictMapper;

    @Resource
    private TbCdcdmDataDictValueMapper tbCdcdmDataDictValueMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private ApiService apiService;

    @Resource
    private CommonUtils commonUtils;

    private void checkDuplicateValueDomainName(String name, String existedId) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        if (tbCdcdmDataDictMapper.countByNameNorId(name, existedId) > 0) {
            throw new MedicalBusinessException("名称重复，请重新定义值域");
        }
    }

    @Override
    public PageInfo<DataDictListVO> getValueDomainList(String id, String name, Integer attrCount, Integer pageIndex, Integer pageSize) {

        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(tbCdcdmDataDictMapper.getDataDictList(id, name, attrCount));
    }

    @Override
    public TbCdcdmDataDict getValueDomainDictById(String id) {
        return tbCdcdmDataDictMapper.selectById(id   );
    }

    @Override
    public String editValueDomain(TbCdcdmDataDict tbCdcdmDataDict, UapUserPo uapUserPo) {
        this.checkDuplicateValueDomainName(tbCdcdmDataDict.getName(), tbCdcdmDataDict.getId());
        if(tbCdcdmDataDict.getId() == null) {
            tbCdcdmDataDict.setId(String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_DICT)));
        }
        //code不填时，默认与name一致
        tbCdcdmDataDict.setCode(Optional.ofNullable(tbCdcdmDataDict.getCode()).orElse(tbCdcdmDataDict.getName()));
        tbCdcdmDataDict.setStatus(Constants.STATUS_ON);
        tbCdcdmDataDict.setDeleteFlag(Constants.NO_DELETE);
        tbCdcdmDataDict.setCreateTime(new Date());
        tbCdcdmDataDict.setUpdateTime(new Date());
        if(uapUserPo != null) {
            tbCdcdmDataDict.setCreatorId(uapUserPo.getId());
            tbCdcdmDataDict.setUpdaterId(uapUserPo.getId());
            tbCdcdmDataDict.setCreator(uapUserPo.getName());
            tbCdcdmDataDict.setUpdater(uapUserPo.getName());
        }
        tbCdcdmDataDictMapper.editValueDomain(tbCdcdmDataDict);
        return tbCdcdmDataDict.getId();
    }

    @Override
    public PageInfo<TbCdcdmDataDictValue> getValueDomainDetailList(String dataDictId,
                                                                   String type,
                                                                   Integer pageIndex,
                                                                   Integer pageSize) {

        if(DataDictTypeEnum.DOMAIN_VALUE.getValue().equals(type)) {

            PageHelper.startPage(pageIndex, pageSize);
            return new PageInfo<>(tbCdcdmDataDictValueMapper.getValueDomainDetailList(dataDictId));
        }
        if(DataDictTypeEnum.URL.getValue().equals(type)) {

            List<TbCdcdmDataDictValue> dataDictValues = getDataDictValuePageInfo(dataDictId);

            return commonUtils.pageList(dataDictValues, pageIndex, pageSize);        }
        return new PageInfo<>(new ArrayList<>());
    }

    private List<TbCdcdmDataDictValue> getDataDictValuePageInfo(String dataDictId) {
        List<TbCdcdmDataDictValue> dictValues = tbCdcdmDataDictValueMapper.getValueDomainDetailList(dataDictId);
        if (CollectionUtil.isNotEmpty(dictValues)) {
            String url = dictValues.get(0).getName();
            //查询
            DataDictUrlQueryDTO dataDictUrlQueryDTO = new DataDictUrlQueryDTO();
            List<TreeNode> root = apiService.doPostList(url, dataDictUrlQueryDTO, TreeNode.class);
            List<TbCdcdmDataDictValue> dataDictValues = new ArrayList<>();
            convertToDictValueList(root, dataDictValues);
            return dataDictValues;
        }
        return new ArrayList<>();
    }

    @Override
    public List<TbCdcdmDataDictValue> getDataDictValueList(String dataDictId) {
        TbCdcdmDataDict domainDictById = this.getValueDomainDictById(dataDictId);
        if (domainDictById != null) {
            String type = domainDictById.getType();
            if(DataDictTypeEnum.DOMAIN_VALUE.getValue().equals(type)) {
                return tbCdcdmDataDictValueMapper.getValueDomainDetailList(dataDictId);
            }
            if(DataDictTypeEnum.URL.getValue().equals(type)) {

            }
        }
        return new ArrayList<>();
    }

    @Override
    public void convertToDictValueList(List<TreeNode> root, List<TbCdcdmDataDictValue> dictValues){

        for (TreeNode treeNode : root) {
            TbCdcdmDataDictValue value = new TbCdcdmDataDictValue();
            value.setId(treeNode.getId());
            value.setCode(treeNode.getValue());
            value.setName(treeNode.getLabel());
            value.setParentId(treeNode.getParentId());
            dictValues.add(value);
            if(treeNode.getChildren() != null){
                convertToDictValueList(treeNode.getChildren(), dictValues);
            }
        }
    }

    @Override
    public List<TreeNode> getValueByKey(String id, String code, String name) {

        List<TbCdcdmDataDictValue> dataDictValues = tbCdcdmDataDictValueMapper.getValueByKey(id, code, name);
        if(CollectionUtil.isNotEmpty(dataDictValues)) {

            return TreeNode.buildTreeByNodeList(dataDictValues,
                                                TbCdcdmDataDictValue::getId,
                                                TbCdcdmDataDictValue::getParentId,
                                                TbCdcdmDataDictValue::getName,
                                                TbCdcdmDataDictValue::getCode);
        }
        return new ArrayList<>();
    }

    @Override
    public DataDictObjectVO getDictValueBy(DictValueQueryDTO dto) {

        DataDictObjectVO objectVO = new DataDictObjectVO();
        List<TbCdcdmDataDictValue> dataDictValues = tbCdcdmDataDictValueMapper.getDataDetailBy(dto);
        //存在值域则判断
        if(CollectionUtil.isNotEmpty(dataDictValues)) {
            //判断查询值域种类
            String type = dataDictValues.get(0).getType();
            objectVO.setType(type);
            //普通值域（key-value格式）
            if(DataDictTypeEnum.DOMAIN_VALUE.getValue().equals(type)) {
                List<TreeNode> root = TreeNode.buildTreeByNodeList(dataDictValues,
                                                                   TbCdcdmDataDictValue::getId,
                                                                   TbCdcdmDataDictValue::getParentId,
                                                                   TbCdcdmDataDictValue::getName,
                                                                   TbCdcdmDataDictValue::getCode);
                TreeNode.setChildrenToNull(root);
                return this.returnByTreeDepth(objectVO, root, dto.getPageIndex(), dto.getPageSize());
            }
            //链接形式
            if(DataDictTypeEnum.URL.getValue().equals(type)) {
                String url = dataDictValues.get(0).getName();
                //查询
                DataDictUrlQueryDTO dataDictUrlQueryDTO = DataDictUrlQueryDTO.builder().name(dto.getSearchWord()).build();
                List<TreeNode> root = apiService.doPostList(url, dataDictUrlQueryDTO, TreeNode.class);
                TreeNode.setChildrenToNull(root);
                return this.returnByTreeDepth(objectVO, root, dto.getPageIndex(), dto.getPageSize());
            }
        }
        return objectVO;
    }

    /**
     * 根据森林最大深度判断是否分页处理
     * */
    private DataDictObjectVO returnByTreeDepth(DataDictObjectVO objectVO,
                                               List<TreeNode> root,
                                               Integer pageIndex,
                                               Integer pageSize){

        int depth = TreeNode.getForestMaxDepth(root);
        //主要处理机构前端加载问题
        if (depth <= 1){
            //手动分页
            PageInfo<TreeNode> pageInfo = commonUtils.pageList(root, pageIndex, pageSize);
            objectVO.setTreeNodePageInfo(pageInfo);
            objectVO.setIsPage(Boolean.TRUE);
            objectVO.setIsLazyLoad(Boolean.FALSE);
            return objectVO;
        }
        objectVO.setTreeNodeList(root);
        objectVO.setIsPage(Boolean.FALSE);
        objectVO.setIsLazyLoad(Boolean.TRUE);
        return objectVO;
    }

    @Override
    @Transactional
    public void addValueDomainDetail(List<TbCdcdmDataDictValue> dictValues) {

        if(CollectionUtil.isNotEmpty(dictValues)) {
            UapUserPo uapUserPo = USER_INFO.get();
            for (int i = 0; i < dictValues.size(); i++) {
                TbCdcdmDataDictValue e = dictValues.get(i);
                if(StringUtils.isBlank(e.getId())) {
                    String id = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_DICT_VALUE));
                    e.setId(id);
                }
                e.setStatus(Constants.STATUS_ON);
                e.setDeleteFlag(Constants.DELETE_FLAG_STR_0);
                e.setCreateTime(new Date());
                e.setUpdateTime(new Date());
                e.setSortOrder(dictValues.size() - i);
                if(uapUserPo != null) {
                    e.setCreatorId(uapUserPo.getId());
                    e.setCreator(uapUserPo.getName());
                    e.setUpdaterId(uapUserPo.getId());
                    e.setUpdater(uapUserPo.getName());
                }
            }
            tbCdcdmDataDictValueMapper.updateSortOrderByAdd(dictValues.get(0).getDataDictId(),dictValues.size());
            tbCdcdmDataDictValueMapper.batchInsertIntoValue(dictValues);
            //更新该值域的值个数
            String dictId = dictValues.get(0).getDataDictId();
            tbCdcdmDataDictMapper.updateDataDictCount(dictId);
        }
    }

    @Override
    public void updateValueDomainDetail(List<TbCdcdmDataDictValue> dictValues) {
        if (dictValues == null || dictValues.isEmpty()){
            return;
        }
        UapUserPo uapUserPo = USER_INFO.get();
        dictValues.forEach(item -> {
            item.setUpdateTime(new Date());
            item.setUpdaterId(uapUserPo.getId());
            item.setUpdater(uapUserPo.getName());
        });
        tbCdcdmDataDictValueMapper.updateValueDomainDetail(dictValues);
        // 获取最后一个元素
        TbCdcdmDataDictValue tbCdcdmDataDictValue = dictValues.get(dictValues.size() - 1);
        String id = tbCdcdmDataDictValue.getId();
        LambdaQueryWrapper<TbCdcdmDataDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcdmDataDictValue::getId,id).eq(TbCdcdmDataDictValue::getDeleteFlag,"0");

        tbCdcdmDataDictValue = tbCdcdmDataDictValueMapper.selectOne(queryWrapper);
        tbCdcdmDataDictValueMapper.updateSortOrderByUpdate(tbCdcdmDataDictValue.getDataDictId(),tbCdcdmDataDictValue.getSortOrder());
        tbCdcdmDataDictValueMapper.updateSortOrderByUpdateOne(tbCdcdmDataDictValue.getId());

    }

    @Override
    @Transactional
    public void deleteValueDomainDetail(String id, String attrId) {
        LambdaQueryWrapper<TbCdcdmDataDictValue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdcdmDataDictValue::getId,id).eq(TbCdcdmDataDictValue::getDeleteFlag,"0");

        TbCdcdmDataDictValue tbCdcdmDataDictValue = tbCdcdmDataDictValueMapper.selectOne(queryWrapper);
        if (tbCdcdmDataDictValue == null){
            return;
        }
        UapUserPo uapUserPo = USER_INFO.get();
        tbCdcdmDataDictValueMapper.updateValueDetail(id, uapUserPo.getName(), uapUserPo.getId());
        tbCdcdmDataDictValueMapper.updateSortOrderByDelete(tbCdcdmDataDictValue.getDataDictId(),tbCdcdmDataDictValue.getSortOrder());
        //更新该值域的值个数
        tbCdcdmDataDictMapper.updateDataDictCount(attrId);
    }

    @Override
    public List<TbCdcdmDataDictValue> listNameByCode(DataDictNameQueryDTO dto) {

        if(StringUtils.isBlank(dto.getId()) || CollectionUtil.isEmpty(dto.getCodeList())) {
            return new ArrayList<>();
        }
        DictValueQueryDTO queryDTO = DictValueQueryDTO.builder().id(dto.getId()).build();
        List<TbCdcdmDataDictValue> dataDictValues = tbCdcdmDataDictValueMapper.getDataDetailBy(queryDTO);
        //存在值域则判断
        if(CollectionUtil.isNotEmpty(dataDictValues)) {
            //判断查询值域种类
            String type = dataDictValues.get(0).getType();
            //普通值域（key-value格式）
            if(DataDictTypeEnum.DOMAIN_VALUE.getValue().equals(type)) {
                dataDictValues = dataDictValues.stream()
                                               .filter(e -> dto.getCodeList().contains(e.getCode()))
                                               .collect(Collectors.toList());
                return dataDictValues;
            }
            //链接形式
            if(DataDictTypeEnum.URL.getValue().equals(type)) {
                String url = dataDictValues.get(0).getName();
                //查询
                DataDictUrlQueryDTO dataDictUrlQueryDTO = DataDictUrlQueryDTO.builder().build();
                List<TreeNode> root = apiService.doPostList(url, dataDictUrlQueryDTO, TreeNode.class);
                TreeNode.setChildrenToNull(root);
                //存储url值域中与查询值所匹配的结果
                List<TbCdcdmDataDictValue> res = new ArrayList<>();
                //存储所有匹配的节点
                List<TreeNode> allMatchNode = getAllForestMatchNode(root, dto.getCodeList());

                allMatchNode.forEach(e -> {
                    TbCdcdmDataDictValue value = TbCdcdmDataDictValue.builder()
                                                                     .name(e.getLabel())
                                                                     .code(e.getValue())
                                                                     .type(type)
                                                                     .build();
                    res.add(value);
                });
                return res;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<TbCdcdmDataDictValue> listByDataDictIds(List<String> dataDictIds) {
        DictValueQueryDTO queryDTO = DictValueQueryDTO.builder().ids(dataDictIds).build();
        return tbCdcdmDataDictValueMapper.getDataDetailBy(queryDTO);
    }

    /**
     * 森林节点遍历
     * */
    private List<TreeNode> getAllForestMatchNode(List<TreeNode> root, List<String> valueList) {

        List<TreeNode> res = new ArrayList<>();
        root.forEach(e -> {
            getMatchNode(res, e, valueList);
        });
        return res;
    }

    /**
     * 匹配树节点
     * */
    private void getMatchNode(List<TreeNode> res, TreeNode node, List<String> valueList) {

        if(node == null){
            return;
        }

        if(valueList.contains(node.getValue())){
            res.add(node);
        }
        if(node.getChildren() != null) {
            for (TreeNode child : node.getChildren()) {
                getMatchNode(res, child, valueList);
            }
        }
    }

    @Override
    @Transactional
    public void addValueDomainBy(AddNewDataDictDTO dto) {

        UapUserPo userPo = new UapUserPo();
        userPo.setId("100001");
        userPo.setName("admin");

        //单层 非url值域使用
        TbCdcdmDataDict dataDict = TbCdcdmDataDict.builder().name(dto.getName()).attrCount(1).type("1").build();
        String dataDictId = editValueDomain(dataDict, userPo);

        List<String> detailList = Arrays.asList(dto.getValueDetail().trim().split("、"));
        List<TbCdcdmDataDictValue> dictValues = new ArrayList<>();
        detailList.forEach(e -> {
            TbCdcdmDataDictValue value = TbCdcdmDataDictValue.builder().dataDictId(dataDictId).name(e).code(e).build();
            value.setCreator("admin");
            value.setCreatorId("100001");
            value.setUpdater("admin");
            value.setUpdaterId("100001");
            dictValues.add(value);
        });
        addValueDomainDetail(dictValues);
    }

    @Override
    @Transactional
    public void addMulValueDomainBy(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new MedicalBusinessException("上传文件不能为空");
        }

        try {
            // 获取当前用户信息
            UapUserPo uapUserPo = USER_INFO.get();
            if (uapUserPo == null) {
                uapUserPo = new UapUserPo();
                uapUserPo.setId("100001");
                uapUserPo.setName("admin");
            }

            // 读取 Excel 文件的所有 sheet 页
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            int sheetCount = workbook.getNumberOfSheets();

            for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();
                
                // 跳过空 sheet
                if (sheet.getLastRowNum() <= 0) {
                    continue;
                }

                // 创建值域字典
                TbCdcdmDataDict dataDict = createDataDict(sheetName, uapUserPo);
                String dataDictId = editValueDomain(dataDict, uapUserPo);

                // 使用 EasyExcel 读取当前 sheet 的数据
                ValueDomainImportListener listener = new ValueDomainImportListener(dataDictId, sheetName);
                
                EasyExcel.read(file.getInputStream(), ValueDomainImportDTO.class, listener)
                        .sheet(sheetIndex)
                        .doRead();

                // 获取导入的数据
                List<TbCdcdmDataDictValue> dataDictValues = listener.getDataDictValues();
                
                if (!dataDictValues.isEmpty()) {
                    // 更新层级数
                    dataDict.setAttrCount(listener.getMaxLevel());
                    tbCdcdmDataDictMapper.editValueDomain(dataDict);
                    
                    // 保存值域字典值并更新父级ID
                    addValueDomainDetailWithParentMapping(dataDictValues);
                    
                    log.info("成功导入值域 [{}]，共 {} 条数据，最大层级数：{}", 
                            sheetName, dataDictValues.size(), listener.getMaxLevel());
                } else {
                    log.warn("Sheet [{}] 没有有效数据", sheetName);
                }
            }

            workbook.close();
            
        } catch (Exception e) {
            log.error("导入值域失败", e);
            throw new MedicalBusinessException("导入值域失败：" + e.getMessage());
        }
    }

    /**
     * 添加值域详情并更新父级ID映射
     */
    private void addValueDomainDetailWithParentMapping(List<TbCdcdmDataDictValue> dictValues) {
        if (CollectionUtil.isEmpty(dictValues)) {
            return;
        }

        UapUserPo uapUserPo = USER_INFO.get();
        
        // 第一步：保存所有数据，获取真实ID
        for (int i = 0; i < dictValues.size(); i++) {
            TbCdcdmDataDictValue e = dictValues.get(i);
            if (StringUtils.isBlank(e.getId())) {
                String id = String.valueOf(batchUidService.getUid(TB_CDCDM_DATA_DICT_VALUE));
                e.setId(id);
            }
            e.setStatus(Constants.STATUS_ON);
            e.setDeleteFlag(Constants.DELETE_FLAG_STR_0);
            e.setCreateTime(new Date());
            e.setUpdateTime(new Date());
            e.setSortOrder(dictValues.size() - i);
            if (uapUserPo != null) {
                e.setCreatorId(uapUserPo.getId());
                e.setCreator(uapUserPo.getName());
                e.setUpdaterId(uapUserPo.getId());
                e.setUpdater(uapUserPo.getName());
            }
        }

        // 创建 code 到 id 的映射
        Map<String, String> codeToIdMap = new HashMap<>();
        for (TbCdcdmDataDictValue value : dictValues) {
            codeToIdMap.put(value.getCode(), value.getId());
        }

        // 第二步：更新父级ID
        for (TbCdcdmDataDictValue value : dictValues) {
            if (StringUtils.isNotBlank(value.getParentId())) {
                // 将临时的 code 替换为真实的 id
                String realParentId = codeToIdMap.get(value.getParentId());
                if (realParentId != null) {
                    value.setParentId(realParentId);
                }
            }
        }

        // 第三步：批量插入数据
        tbCdcdmDataDictValueMapper.updateSortOrderByAdd(dictValues.get(0).getDataDictId(), dictValues.size());
        tbCdcdmDataDictValueMapper.batchInsertIntoValue(dictValues);
        
        // 更新该值域的值个数
        String dictId = dictValues.get(0).getDataDictId();
        tbCdcdmDataDictMapper.updateDataDictCount(dictId);
    }

    /**
     * 创建值域字典
     */
    private TbCdcdmDataDict createDataDict(String name, UapUserPo uapUserPo) {
        TbCdcdmDataDict dataDict = new TbCdcdmDataDict();
        dataDict.setName(name);
        dataDict.setCode(name); // 默认使用名称作为编码
        dataDict.setType(DataDictTypeEnum.DOMAIN_VALUE.getValue()); // 设置为值域类型
        dataDict.setAttrCount(1); // 初始层级数为1，后续会根据实际数据更新
        dataDict.setStatus(Constants.STATUS_ON);
        dataDict.setDeleteFlag(Constants.NO_DELETE);
        dataDict.setCreateTime(new Date());
        dataDict.setUpdateTime(new Date());
        
        if (uapUserPo != null) {
            dataDict.setCreatorId(uapUserPo.getId());
            dataDict.setUpdaterId(uapUserPo.getId());
            dataDict.setCreator(uapUserPo.getName());
            dataDict.setUpdater(uapUserPo.getName());
        }
        
        return dataDict;
    }

    /**
     * 生成值域导入模板
     * @return Excel 文件的字节数组
     */
    public byte[] generateImportTemplate() {
        try {
            // 创建示例数据
            List<ValueDomainImportDTO> templateData = new ArrayList<>();
            
            // 示例1：3层级结构
            ValueDomainImportDTO example1 = new ValueDomainImportDTO();
            example1.setLevel1("内科");
            example1.setLevel2("呼吸内科");
            example1.setLevel3("肺炎");
            templateData.add(example1);
            
            ValueDomainImportDTO example2 = new ValueDomainImportDTO();
            example2.setLevel1("内科");
            example2.setLevel2("呼吸内科");
            example2.setLevel3("支气管炎");
            templateData.add(example2);
            
            ValueDomainImportDTO example3 = new ValueDomainImportDTO();
            example3.setLevel1("内科");
            example3.setLevel2("心血管内科");
            example3.setLevel3("高血压");
            templateData.add(example3);
            
            // 示例2：2层级结构
            ValueDomainImportDTO example4 = new ValueDomainImportDTO();
            example4.setLevel1("性别");
            example4.setLevel2("男");
            templateData.add(example4);
            
            ValueDomainImportDTO example5 = new ValueDomainImportDTO();
            example5.setLevel1("性别");
            example5.setLevel2("女");
            templateData.add(example5);
            
            // 示例3：单层级结构
            ValueDomainImportDTO example6 = new ValueDomainImportDTO();
            example6.setLevel1("是");
            templateData.add(example6);
            
            ValueDomainImportDTO example7 = new ValueDomainImportDTO();
            example7.setLevel1("否");
            templateData.add(example7);

            // 使用 EasyExcel 生成模板
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, ValueDomainImportDTO.class)
                    .sheet("值域模板")
                    .doWrite(templateData);
            
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            log.error("生成导入模板失败", e);
            throw new MedicalBusinessException("生成导入模板失败：" + e.getMessage());
        }
    }

    @Override
    public void saveDragSort(DragSortDataDTO dto) {
        if (dto == null){
            return;
        }
        String dataDictId = dto.getDataDictId();
        List<String> dictValueIdList = dto.getDictValueIdList();
        Integer pageIndex = dto.getPageIndex();
        Integer pageSize = dto.getPageSize();
        // 判断起始页码 获取起始的sortOrder
        if (pageIndex < 1 ){
            pageIndex = 1;
        }
        int startSortOrder = (pageIndex - 1) * pageSize + 1;
        // 构造排序对象
        List<TbCdcdmDataDictValue> tbCdcdmDataDictValueList = Lists.newArrayList();
        for (int i = 0; i < dictValueIdList.size(); i++) {
            TbCdcdmDataDictValue value = new TbCdcdmDataDictValue();
            value.setId(dictValueIdList.get(i));
            value.setSortOrder(startSortOrder + i);
            tbCdcdmDataDictValueList.add(value);
        }
        // 当前页重排
        tbCdcdmDataDictValueMapper.saveDragSort(dataDictId,tbCdcdmDataDictValueList);
    }

    @Override
    public List<TbCdcdmDataDictValue> getValueBy(String id, String code, String name, String type) {
        List<TbCdcdmDataDictValue> dictValues = tbCdcdmDataDictValueMapper.getValueByKey(id, code, name);

        if (DataDictTypeEnum.DOMAIN_VALUE.getValue().equals(type)) {
            return dictValues;
        }
        if (DataDictTypeEnum.URL.getValue().equals(type)) {
            if (CollectionUtil.isNotEmpty(dictValues)) {
                String url = dictValues.get(0).getName();
                //查询
                DataDictUrlQueryDTO dataDictUrlQueryDTO = new DataDictUrlQueryDTO();
                List<TreeNode> root = apiService.doPostList(url, dataDictUrlQueryDTO, TreeNode.class);
                List<TbCdcdmDataDictValue> dataDictValues = new ArrayList<>();
                convertToDictValueList(root, dataDictValues);
                return dataDictValues;
            }
        }
        return new ArrayList<>();
    }
}
