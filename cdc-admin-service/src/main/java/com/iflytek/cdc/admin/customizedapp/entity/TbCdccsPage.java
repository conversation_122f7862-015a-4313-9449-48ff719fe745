package com.iflytek.cdc.admin.customizedapp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.beans.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * 页面;
 * <AUTHOR> fengwang35
 * @date : 2024-9-9
 */
@ApiModel(value = "页面")
@TableName("tb_cdccs_page")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsPage extends BaseEntity implements Serializable{
    /** 应用id */
    @ApiModelProperty(value = "应用id")
    private String appId ;
    /** 应用编码 */
    @ApiModelProperty(value = "应用编码")
    private String appName ;
    /** 页面名称 */
    @ApiModelProperty(value = "页面名称")
    private String pageName ;
    /** 页面编码 */
    @ApiModelProperty(value = "页面编码")
    private String pageCode ;
    /** 是否流程 */
    @ApiModelProperty(value = "是否流程")
    private String isProcess ;
    /** 流程模板id */
    @ApiModelProperty(value = "流程模板id")
    private String processId ;
    /** 扩展属性JSON */
    @ApiModelProperty(value = "扩展属性JSON")
    private String extendJson ;
    /** 入参配置 */
    @ApiModelProperty(value = "入参配置")
    private String inParameter ;
    /** 数据模型 */
    @ApiModelProperty(value = "数据模型")
    private String dataModelId ;

    @TableField(exist = false)
    @ApiModelProperty("列配置")
    private List<TbCdccsPageViewCol> pageViewColList;

    @TableField(exist = false)
    @ApiModelProperty("按钮配置")
    private List<TbCdccsPageButtonSetting> buttonSettingList;

    @TableField(exist = false)
    @ApiModelProperty("任务配置")
    private List<TbCdccsTaskSetting> taskSettingList;

    @Data
    class InParameter{


        private String name;

        private String desc;

        private String tableKey;

        private String tableColumn;

        private String defaultValue;
    }
}