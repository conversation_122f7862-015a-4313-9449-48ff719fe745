package com.iflytek.cdc.admin.datamodel.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcdm_metadata_table_sql_log")
@ApiModel("模型转换为实体表的sql")
public class TbCdcdmMetadataTableSqlLog extends BaseEntity {
    private String status;
    private String sql;
    private String tableId;
    private String remark;
}
