package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.dto.outcall.BusinessPersonResult;
import com.iflytek.cdc.admin.entity.UapAuth;
import com.iflytek.cdc.admin.entity.UapAuthNode;
import com.iflytek.cdc.admin.entity.UapUserExtend;
import com.iflytek.cdc.admin.entity.UapUserPo;
import com.iflytek.cdc.admin.mapper.TbCdcmrUserDataAuthMapper;
import com.iflytek.cdc.admin.mapper.UserConfigMapper;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgUser;
import com.iflytek.cdc.admin.common.vo.uap.UapUserSearchQueryDTO;
import com.iflytek.cdc.admin.util.NationUtil;
import com.iflytek.cdc.admin.util.StringEmptyUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.uap.ext.pojo.UapMdmEmpUserDto;
import com.iflytek.zhyl.uap.usercenter.entity.TUapApp;
import com.iflytek.zhyl.uap.usercenter.entity.TUapOrganization;
import com.iflytek.zhyl.uap.usercenter.entity.TUapRole;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.pojo.UapMdmDataOrgDetailDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgDto;
import com.iflytek.zhyl.uap.usercenter.service.UapAppApi;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import com.iflytek.zhyl.uap.usercenter.service.UapRoleApi;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/12 10:33
 **/
@Service
@Slf4j
@Deprecated
public class UapUserServiceImpl implements UapUserService {

    @Resource
    private UserConfigMapper userConfigMapper;

    private final UapUserApi uapUserApi;

    private final UapOrgApi uapOrgApi;

    private final UapRoleApi uapRoleApi;

    private final UapAppApi uapAppApi;

    private final RestTemplate restTemplate;

    @Resource
    private TbCdcmrUserDataAuthMapper tbCdcmrUserDataAuthMapper;
    @Resource
    private UapOrgService uapOrgService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Value("${usercenter.admin.loginName:admin}")
    private String loginUserName;

    @Value("${app.build.id}")
    private String buildId;

    public UapUserServiceImpl(UapUserApi uapUserApi, UapOrgApi uapOrgApi, UapRoleApi uapRoleApi, UapAppApi uapAppApi, RestTemplate restTemplate) {
        this.uapUserApi = uapUserApi;
        this.uapOrgApi = uapOrgApi;
        this.uapRoleApi = uapRoleApi;
        this.uapAppApi = uapAppApi;
        this.restTemplate = restTemplate;
    }

    @Value("${app-code}")
    public String appCode;

    @Value("${sys-role-remake}")
    public String sysRoleRemake;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;

    @Override
    @Deprecated
    public UapUserExtend getUserInfo(String loginUserId) {
        UapUserExtend uapUser = new UapUserExtend();
        try {
            TUapUser tUapUser = uapUserApi.getUserDetail(loginUserId).getUapUser();
            //解决疾控中心 - 部门下的成员无法查看到地图轮廓问题
            TUapUser tUapUser1 = uapUserApi.findPwdUserByLoginName(tUapUser.getLoginName());
            BeanUtil.copyProperties(tUapUser1, uapUser);
            String orgId;
            if (userCenterVersion.equals("5.0.0")) {
                String orgUrl = "http://uap-service-ext-service/v1/pv/TUapOrganization/byDepartmentId?departmentId=" + uapUser.getOrgId();
                orgId = Objects.requireNonNull(restTemplate.getForObject(orgUrl, TUapOrganization.class)).getId();
            } else {
                String orgUrl = "http://uap-service-ext-service/v1/pt/TUapOrganization/" + uapUser.getOrgId();
                orgId = Objects.requireNonNull(restTemplate.getForObject(orgUrl, JSONObject.class)).getObject("uapOrganization", TUapOrganization.class).getId();
            }
            //根据人员所在机构id获取机构详细信息
            UapOrgDto uapOrgDto = uapOrgApi.getExt(orgId);


            if (uapOrgDto == null) {
                log.error("获取登录用户信息查询机构信息异常：{}", loginUserId);
                throw new MedicalBusinessException("获取机构信息异常");
            }
            TUapOrganization uapOrg = uapOrgDto.getUapOrganization();
            uapUser.setProvinceCode(uapOrg.getProvinceCode());
            uapUser.setProvinceName(uapOrg.getProvince());
            uapUser.setCityCode(uapOrg.getCityCode());
            uapUser.setCityName(uapOrg.getCity());
            uapUser.setDistrictCode(uapOrg.getDistrictCode());
            uapUser.setDistrictName(uapOrg.getDistrict());
            //设置机构编号
            uapUser.setOrgCode(uapOrg.getCode());
            //设置区域编号
            uapUser.setAreaCode(StringEmptyUtil.notEmpty(uapOrg.getDistrictCode(), uapOrg.getCityCode(), uapOrg.getProvinceCode()));
            //设置区域名
            uapUser.setAreaName(StringEmptyUtil.notEmpty(uapOrg.getDistrict(), uapOrg.getCity(), uapOrg.getProvince()));
            //判断区域等级
            uapUser.setAreaLevel(NationUtil.getAreaLevel(uapOrg.getProvinceCode(), uapOrg.getCityCode(), uapOrg.getDistrictCode()));
            //设置角色信息
//            getRoleInfo(loginUserId, uapUser);
            //填写buildId
            uapUser.setBuildId(buildId);
            Integer isWaterMarkOpen = userConfigMapper.getWateMarkSwitch(loginUserId, Constants.WATERMARK_TYPE);
            //水印开关默认开启
            uapUser.setIsWaterMarkOpen(Optional.ofNullable(isWaterMarkOpen).orElse(1));
        } catch (Exception e) {
            log.error("通过uap获取用户信息异常:{}", e, loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return uapUser;
    }



    public UapUserExtend getRoleInfo(String loginUserId, UapUserExtend uapUser) {
        try {
            //根据appCode 获取appId
            UapAppApi.PageRequestUapApp.UapAppFilter uapAppFilter = new UapAppApi.PageRequestUapApp.UapAppFilter();
            uapAppFilter.setCode(appCode);
            UapAppApi.PageRequestUapApp pageRequestUapApp = new UapAppApi.PageRequestUapApp();
            pageRequestUapApp.setFilter(uapAppFilter);
            TUapApp uapApp = uapAppApi.searchExt(pageRequestUapApp, loginUserId).getEntities().get(0);
            if (uapApp == null) {
                log.error("通过uap查询应用服务异常{}", appCode);
                throw new MedicalBusinessException("获取应用服务信息异常！");
            }
            //获取角色信息
            UapRoleApi.PageRequestUapRole requestUapRole = new UapRoleApi.PageRequestUapRole();
            UapRoleApi.PageRequestUapRole.Filter filter = new UapRoleApi.PageRequestUapRole.Filter();
            filter.setUserId(loginUserId);
            //设置应用服务id
            filter.setAppId(uapApp.getId());
            requestUapRole.setFilter(filter);
            List<TUapRole> uapRoles = uapRoleApi.search(requestUapRole, loginUserId).getEntities();
            //判断是否有系统管理员角色
            if (CollUtil.isNotEmpty(uapRoles)) {
                uapUser.setIsSysManager(uapRoles.stream().filter(e -> e.getRemark().equals(sysRoleRemake)).findAny().isPresent());
                uapUser.setUapRole(uapRoles);
            }
            return uapUser;
        } catch (Exception e) {
            log.error("获取登录用户角色信息异常：{}", e);
            throw new MedicalBusinessException("获取用户信息异常");
        }
    }

    @Override
    public List<UapAuthNode> getBigScreenMenu() {
        String url = "http://uap-service-ext-service/v1/pt/TUapAuthority/user/tree?appCode=cdc-big-screen&loginUserId=" + getUapUserIdByName(loginUserName);
        UapAuthNode result = restTemplate.getForObject(url, UapAuthNode.class);
        String authUrl = "http://uap-service-ext-service/v1/pt/TUapAuthority/auth/search?appCode=cdc-big-screen&loginUserId=" + getUapUserIdByName(loginUserName);
        JSONArray authArray = restTemplate.getForObject(authUrl, JSONArray.class);
        List<UapAuth> authList = authArray.toJavaList(UapAuth.class);
        setAuth(result.getNodes(), authList);
        return result.getNodes();
    }

    private void setAuth(List<UapAuthNode> authNodes, List<UapAuth> authList) {
        for (UapAuthNode authNode : authNodes) {
            for (UapAuth auth : authList) {
                if (auth.getAuthId().equals(authNode.getId())) {
                    authNode.setChecked(true);
                    authNode.setAuthType(auth.getAuthType());
                }
            }
            if (authNode.getNodes() != null && !authNode.getNodes().isEmpty()) {
                setAuth(authNode.getNodes(), authList);
            }
        }
    }

    public String getUapUserIdByName(String loginName) {
        String url = "http://uap-service-ext-service/v1/pv/TUapUser/withPassword/" + loginName;
        UapUserPo userPo = restTemplate.getForObject(url, UapUserPo.class);
        if (Objects.nonNull(userPo)) {
            return userPo.getId();
        } else {
            return "";
        }
    }

    @Override
    public BusinessPersonResult getUapUserList(String loginUserId, String loginOrgId, BusinessPersonQueryVo queryVo) {
        BusinessPersonResult result = new BusinessPersonResult();
        String url = "http://uap-service-ext-service/v1/pt/mdmEmp/search?loginUserId=" + loginUserId;
        Map<String, Object> filter = new HashMap<>();
        filter.put("fetchUserExtand", 1);
        filter.put("fetchChildOrgUser", 1);
        filter.put("fetchNoOrgUser", 0);
        filter.put("keyword", queryVo.getKeyword());
        filter.put("phone", queryVo.getTelephone());
        filter.put("orgId", queryVo.getOrgId());
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("filter", filter);
        requestMap.put("pageSize", queryVo.getPageSize());
        requestMap.put("pageIndex", queryVo.getPageIndex());
        JSONObject requestResult = restTemplate.postForObject(url, requestMap, JSONObject.class);
        log.info("查询到用户列表");
        int total = requestResult.getJSONObject("next").getIntValue("total");
        List<UapMdmEmpUserDto> userDto = requestResult.getJSONArray("entities").toJavaList(UapMdmEmpUserDto.class);
        log.info("开始查询机构");
        List<BusinessPersonVO> businessPersonVOList = userDto.stream().map(BusinessPersonVO::fromEntity).map(this::getDept).collect(Collectors.toList());
        log.info("机构查询结束");
        List<String> businessIds = businessPersonVOList.stream().map(BusinessPersonVO::getId).collect(Collectors.toList());
        if (!businessIds.isEmpty()) {
            List<TbCdcmrUserDataAuth> personDataAuths = tbCdcmrUserDataAuthMapper.findByLoginUserIds(businessIds);
            for (BusinessPersonVO businessPerson : businessPersonVOList) {
                List<SymptomNameVO> symptomNameVOList = new ArrayList<>();
                List<InfectedNameVO> infectedNameVOList = new ArrayList<>();
                List<SympNameVO> sympNameVOList = new ArrayList<>();
                List<PoisonNameVO> poisonNameVOList = new ArrayList<>();
                List<OutpatientNameVO> outpatientNameVOList = new ArrayList<>();
                List<UnknownReasonNameVO> unknownReasonNameVOList = new ArrayList<>();
                List<PreventionControlNameVO> preventionControlNameVOList = new ArrayList<>();

                List<CustomizedNameVO> customizedNameVOList = new ArrayList<>();
                for (TbCdcmrUserDataAuth personDataAuth : personDataAuths) {
                    if (personDataAuth.getLoginUserId().equals(businessPerson.getId())) {
                        if (Constants.DATA_AUTH_SYNDROME == personDataAuth.getDataType()) {
                            SymptomNameVO symptomNameVO = new SymptomNameVO();
                            symptomNameVO.setSymptomName(personDataAuth.getAuthName());
                            symptomNameVO.setSymptomCode(personDataAuth.getAuthId());
                            symptomNameVOList.add(symptomNameVO);
                        } else if (Constants.DATA_AUTH_INFECTIOUS_DISEASE == personDataAuth.getDataType()) {
                            InfectedNameVO infectedNameVO = new InfectedNameVO();
                            infectedNameVO.setInfectedName(personDataAuth.getAuthName());
                            infectedNameVO.setInfectedCode(personDataAuth.getAuthId());
                            infectedNameVOList.add(infectedNameVO);
                        } else if (Constants.DATA_AUTH_SCHOOL_SYMPTOM == personDataAuth.getDataType()) {
                            SympNameVO sympNameVO = new SympNameVO();
                            sympNameVO.setSchoolSymptomCode(personDataAuth.getAuthId());
                            sympNameVO.setSchoolSymptomName(personDataAuth.getAuthName());
                            sympNameVOList.add(sympNameVO);
                        } else if (Constants.DATA_AUTH_POISON == personDataAuth.getDataType()) {
                            PoisonNameVO poisonNameVO = new PoisonNameVO();
                            poisonNameVO.setPoisonName(personDataAuth.getAuthName());
                            poisonNameVO.setPoisonCode(personDataAuth.getAuthId());
                            poisonNameVOList.add(poisonNameVO);
                        }else if (Constants.DATA_AUTH_OUTPATIENT == personDataAuth.getDataType()) {
                            OutpatientNameVO outpatientNameVO = new OutpatientNameVO();
                            outpatientNameVO.setOutpatientTypeName(personDataAuth.getAuthName());
                            outpatientNameVO.setOutpatientTypeCode(personDataAuth.getAuthId());
                            outpatientNameVOList.add(outpatientNameVO);
                        }else if (Constants.DATA_AUTH_UNKNOWN_REASON == personDataAuth.getDataType()) {
                            UnknownReasonNameVO unknownReasonNameVO = new UnknownReasonNameVO();
                            unknownReasonNameVO.setDiseaseName(personDataAuth.getAuthName());
                            unknownReasonNameVO.setDiseaseCode(personDataAuth.getAuthId());
                            unknownReasonNameVOList.add(unknownReasonNameVO);
                        }else if (Constants.DATA_AUTH_PREVENTION_CONTROL == personDataAuth.getDataType()) {
                            PreventionControlNameVO preventionControlNameVO = new PreventionControlNameVO();
                            preventionControlNameVO.setPreventionControlName(personDataAuth.getAuthName());
                            preventionControlNameVO.setPreventionControlCode(personDataAuth.getAuthId());
                            preventionControlNameVOList.add(preventionControlNameVO);
                        } else if (Constants.DATA_AUTH_CUSTOMIZED == personDataAuth.getDataType()) {
                            CustomizedNameVO customizedNameVO = new CustomizedNameVO();
                            customizedNameVO.setCustomizedName(personDataAuth.getAuthName());
                            customizedNameVO.setCustomizedCode(personDataAuth.getAuthId());
                            customizedNameVOList.add(customizedNameVO);
                        }
                    }
                }
                businessPerson.setSymptomList(symptomNameVOList);
                businessPerson.setInfectedList(infectedNameVOList);
                businessPerson.setSchoolSymptomList(sympNameVOList);
                businessPerson.setPoisonList(poisonNameVOList);
                businessPerson.setOutpatientList(outpatientNameVOList);
                businessPerson.setUnknownReasonList(unknownReasonNameVOList);
                businessPerson.setPreventionControlList(preventionControlNameVOList);
                businessPerson.setCustomizedList(customizedNameVOList);
            }
        }
        result.setResult(businessPersonVOList);
        result.setTotal(total);
        result.setPageIndex(queryVo.getPageIndex());
        result.setPageSize(queryVo.getPageSize());
        return result;
    }

    @Override
    public PageInfo<UapOrgUser> searchUapUserByOrgId(UapUserSearchQueryDTO queryDTO, String loginUserId) {
        PageInfo<UapOrgUser> pageInfo = uapServiceApi.searchUapUserByOrgId(queryDTO, loginUserId);
        UapMdmDataOrgDetailDto byOrgId = uapOrgService.getByOrgId(queryDTO.getOrgId());
        pageInfo.getList().forEach(c -> {
            c.setProvinceCode(byOrgId.getProvinceCode());
            c.setProvinceName(byOrgId.getProvince());
            c.setCityCode(byOrgId.getCityCode());
            c.setCityName(byOrgId.getCity());
            c.setDistrictCode(byOrgId.getDistrictCode());
            c.setDistrictName(byOrgId.getDistrict());
        });
        return pageInfo;
    }

    @Override
    public List<UapOrgUser> listByOrgId(String orgId) {
        UapMdmDataOrgDetailDto byOrgId = uapOrgService.getByOrgId(orgId);
        List<UapMdmEmpUserDto> uapMdmEmpUserDtos = uapServiceApi.listByOrgId(orgId);
        
        return uapMdmEmpUserDtos.stream().map(u ->{
            com.iflytek.cdc.admin.common.vo.uap.UapUserPo userPo = uapServiceApi.getUser(u.getId());
            UapOrgUser orgUser = new UapOrgUser();
            orgUser.setId(u.getId());
            orgUser.setName(u.getName());
            orgUser.setLoginName(u.getLoginName());
            orgUser.setPhone(userPo.getPhone());
            orgUser.setOrgId(byOrgId.getOrgId());
            orgUser.setOrgName(byOrgId.getOrgName());
            orgUser.setProvinceCode(byOrgId.getProvinceCode());
            orgUser.setProvinceName(byOrgId.getProvince());
            orgUser.setCityCode(byOrgId.getCityCode());
            orgUser.setCityName(byOrgId.getCity());
            orgUser.setDistrictCode(byOrgId.getDistrictCode());
            orgUser.setDistrictName(byOrgId.getDistrict());
            return orgUser;
        }).collect(Collectors.toList());
    }

    BusinessPersonVO getDept(BusinessPersonVO businessPersonVO) {
        log.info("开始查询uap机构接口:");
        if (!StringUtils.isBlank(businessPersonVO.getStatDimId())) {
            UapMdmDataOrgDetailDto orgDto = uapOrgService.getByOrgId(businessPersonVO.getStatDimId());

            log.info(orgDto.getOrgName());
            if ("C1".equals(orgDto.getNodeType())) {
                String orgUrl = "http://uap-service-ext-service/v1/pv/TUapOrganization/byDepartmentId?departmentId=" + businessPersonVO.getStatDimId();
                TUapOrganization uapOrganization = restTemplate.getForObject(orgUrl, TUapOrganization.class);
                businessPersonVO.setDeptName(orgDto.getOrgName());
                businessPersonVO.setProvinceCode(uapOrganization.getProvinceCode());
                businessPersonVO.setProvinceName(uapOrganization.getProvince());
                businessPersonVO.setCityCode(uapOrganization.getCityCode());
                businessPersonVO.setCityName(uapOrganization.getCity());
                businessPersonVO.setDistrictCode(uapOrganization.getDistrictCode());
                businessPersonVO.setDistrictName(uapOrganization.getDistrict());
                businessPersonVO.setStatDimId(uapOrganization.getId());
                businessPersonVO.setStatDimName(uapOrganization.getName());
            } else {
                businessPersonVO.setProvinceCode(orgDto.getProvinceCode());
                businessPersonVO.setProvinceName(orgDto.getProvince());
                businessPersonVO.setCityCode(orgDto.getCityCode());
                businessPersonVO.setCityName(orgDto.getCity());
                businessPersonVO.setDistrictCode(orgDto.getDistrictCode());
                businessPersonVO.setDistrictName(orgDto.getDistrict());
                businessPersonVO.setStatDimId(orgDto.getOrgId());
                businessPersonVO.setStatDimName(orgDto.getOrgName());
            }
        }

        return businessPersonVO;
    }

}
