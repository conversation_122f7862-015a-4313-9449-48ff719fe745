package com.iflytek.cdc.admin.outbound.service;

import com.google.gson.Gson;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.outbound.constant.OutboundInvestigationConstant;
import com.iflytek.cdc.admin.outbound.entity.OutboundPerson;
import com.iflytek.cdc.admin.outbound.entity.OutboundRecord;
import com.iflytek.cdc.admin.outbound.entity.OutboundTemplate;
import com.iflytek.cdc.admin.outbound.entity.SignalSmsRecord;
import com.iflytek.cdc.admin.outbound.model.dto.WarningSignalSmsRecordDTO;
import com.iflytek.cdc.admin.outbound.model.dto.input.CallTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.PlanTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.SmsTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Dweller;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Var;
import com.iflytek.cdc.admin.outbound.model.dto.output.CreateCallRs;
import com.iflytek.cdc.admin.outbound.util.OutboundTemplateUtils;
import com.iflytek.cdc.admin.outbound.util.Response;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

import cn.hutool.core.collection.CollUtil;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class OutboundService {

    private static final Gson gson = new Gson();
    @Resource
    private CreateOutboundService createOutboundService;

    @Resource
    private OutboundRecordService outboundRecordService;
    
    @Resource
    private OutboundPersonService outboundPersonService;

    @Resource
    private SignalSmsRecordService signalSmsRecordService;

    @Resource
    private OutboundTemplateService outboundTemplateService;

    @Resource
    private BatchCommonService batchCommonService;

    @Value("${outcall.smsId:12107}")
    private String smsId;
    @Value("${fpva.outCall.appName:预警平台}")
    private String appName;
    @Value("${outcall.signalSmsTemplateId:10}")
    private String signalSmsTemplateId;

    public OutboundRecord sendPlan(PlanTask planTask) {
        OutboundRecord outboundRecord = processTask(planTask.getPlanId(), planTask);
        savePerson(outboundRecord, planTask.getDwellers());
        return outboundRecord;
    }

    public OutboundRecord sendCall(CallTask callTask) {
        OutboundRecord outboundRecord = processTask(String.valueOf(callTask.getSpeechId()), callTask);
        savePerson(outboundRecord, callTask.getDwellers());
        return outboundRecord;
    }

    public OutboundRecord sendSms(SmsTask smsTask) {
        OutboundRecord outboundRecord = processTask(String.valueOf(smsTask.getSmsId()), smsTask);
        savePerson(outboundRecord, smsTask.getDwellers());
        return outboundRecord;
    }

    private <T> OutboundRecord processTask(String speechOrSmsId, T taskDetail) {
        OutboundRecord outboundRecord = new OutboundRecord();
        outboundRecord.setSpeechOrSmsId(speechOrSmsId);
        outboundRecord.setRequestContent(gson.toJson(taskDetail));
        outboundRecord.setType(OutboundRecord.Type.PHONE.getCode());
        try {
            Response<CreateCallRs> createCallRsResponse = executeServiceMethod(taskDetail);
            outboundRecord.setResponseContent(gson.toJson(createCallRsResponse));
            outboundRecord.setStatus(OutboundRecord.CallStatus.CALLING.getCode());
            if (createCallRsResponse.isSuccess()) {
                outboundRecord.setBatchNo(createCallRsResponse.getData().getBatch().get(0));
            }else {
                outboundRecord.setStatus(OutboundRecord.CallStatus.FAILED.getCode());
            }
        } catch (Exception e) {
            outboundRecord.setStatus(OutboundRecord.CallStatus.FAILED.getCode());
            outboundRecord.setResponseContent(e.getMessage());
        }
        outboundRecordService.create(outboundRecord);
        return outboundRecord;
    }
    
    private void savePerson(OutboundRecord record, List<Dweller> users){
        List<OutboundPerson> personList = new ArrayList<>();
        users.forEach(u -> {
            OutboundPerson person = new OutboundPerson();
            BeanUtils.copyProperties(u, person);
            person.setName(u.getDwellerName());
            person.setBatchNo(record.getBatchNo());
            if (u.getExtProperty() != null){
                person.setExtProperty(gson.toJson(u.getExtProperty()));
            }
            person.setOutboundRecordId(record.getId());
            personList.add(person);
        });
        outboundPersonService.saveBatch(personList);
    }

    private <T> Response<CreateCallRs> executeServiceMethod(T taskDetail){
        if (taskDetail instanceof PlanTask) {
            return createOutboundService.execPlanTask((PlanTask) taskDetail);
        } else if (taskDetail instanceof CallTask) {
            return createOutboundService.execCallTask((CallTask) taskDetail);
        } else if (taskDetail instanceof SmsTask) {
            return createOutboundService.execSmsTask((SmsTask) taskDetail);
        } else {
            throw new MedicalBusinessException("Unknown task type");
        }
    }

    /**
     * 添加预警信号短信记录
     * @param dtoList 预警信号短信记录DTO
     * @return 添加的记录ID
     */
    public Integer addSignalSmsRecord(List<WarningSignalSmsRecordDTO> dtoList) {

        OutboundTemplate template = outboundTemplateService.loadCascade(signalSmsTemplateId);
        if (template == null) {
            log.error("预警信号短信模板不存在");
            return 0;
        }
        smsId = template.getSpeechId();
        List<SignalSmsRecord> signalSmsRecords = new ArrayList<>();
        for (WarningSignalSmsRecordDTO dto : dtoList) {

            SmsTask smsTask = new SmsTask();
            smsTask.setDwellers(buildDwellerList(dto));
            smsTask.setType(Integer.valueOf(OutboundInvestigationConstant.ExecutionTimeType.NOW.getCode()));
            smsTask.setSmsId(smsId);
            List<Var> smsVars = new ArrayList<>();
            String content = buildSignalSmsContent(dto,template.getTemplateContent());
            smsVars.add(Var.builder().remarks("content").content(content).build());
            smsVars.add(Var.builder().remarks("center").content(appName).build());
            smsTask.setVars(smsVars);
            String requestContent = gson.toJson(smsTask);
            String responseContent = "";
            String batchId = "";
            String callStatus = OutboundRecord.CallStatus.FAILED.getCode();
            try {
                Response<CreateCallRs> response = createOutboundService.execSmsTask(smsTask);
                responseContent = gson.toJson(response);
    
                if (response.isSuccess()) {
                    batchId = response.getData().getBatch().get(0);
                    callStatus = OutboundRecord.CallStatus.CALLED.getCode();

                }
            } catch (Exception e) {
                responseContent = e.getMessage();
                log.error("发送预警信号短信失败", e);
            }
            SignalSmsRecord signalSmsRecord = new SignalSmsRecord();
            signalSmsRecord.setId(batchCommonService.uuid(SignalSmsRecord.TB_NAME));
            signalSmsRecord.setSignalId(dto.getSignalId());
            signalSmsRecord.setWarningType(dto.getWarningType());
            signalSmsRecord.setPhone(dto.getPhone());
            signalSmsRecord.setUserId(dto.getUserId());
            signalSmsRecord.setUserName(dto.getUserName());
            signalSmsRecord.setRequestContent(requestContent);
            signalSmsRecord.setResponseContent(responseContent);
            signalSmsRecord.setStatus(callStatus);
            signalSmsRecord.setBatchId(batchId);
            signalSmsRecord.setCreateTime(new Date());
            signalSmsRecord.setSpeechOrSmsId(smsId);
            signalSmsRecords.add(signalSmsRecord);

        }
        if (CollUtil.isNotEmpty(signalSmsRecords)) {
            signalSmsRecordService.saveBatch(signalSmsRecords);
        }

        return 1;
    }

    private List<Dweller> buildDwellerList(WarningSignalSmsRecordDTO dto) {
        List<Dweller> dwellers = new ArrayList<>();
        Dweller dweller = Dweller.builder()
                .dwellerName(dto.getUserName())
                .telephone(dto.getPhone())
                .relationId(dto.getUserId())
                .build();
        dwellers.add(dweller);
        return dwellers;
    }

    private String buildSignalSmsContent(WarningSignalSmsRecordDTO dto, String templateContent) {
        Map<String, String> params = new HashMap<>();
        params.put("signalId", dto.getSignalId());
        params.put("statDimName", dto.getStatDimName());
        params.put("warningReason", dto.getWarningReason());
        return OutboundTemplateUtils.replaceTemplateVars(templateContent, params);
    }



}
