package com.iflytek.cdc.admin.outbound.constant.enums;

/**
 * 是、否-枚举
 * <AUTHOR>
 * @date 2020/11/6 11:32
 */
public enum TrueOrFalseEnum {
    /**
     * 是
     */
    YES("1","是", "有"),
    /**
     * 否
     */
    NO("2","否", "没有");

    private String code;

    private String desc;

    private String desc1;

    TrueOrFalseEnum(String code, String desc, String desc1) {
        this.code = code;
        this.desc = desc;
        this.desc1 = desc1;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDesc1() {
        return desc1;
    }
}
