package com.iflytek.cdc.admin.outbound.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarningSignalSmsRecordDTO {

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id")
    private String userId;

    @ApiModelProperty("用户name")
    @NotNull(message = "用户name")
    private String userName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("信号id")
    @NotNull(message = "信号id不能为空")
    private String signalId;

    @ApiModelProperty(value = "预警信号类型")
    @NotNull(message = "预警信号类型不能为空")
    private String warningType;
    /**
     * 信号发生地
     */
    @ApiModelProperty(value = "信号发生地")
    private String statDimName ;

    /**
     * 信号所在市
     */
    @ApiModelProperty(value = "信号所在市")
    private String cityName ;

    /**
     * 信号所造县区
     */
    @ApiModelProperty(value = "信号所造县区")
    private String districtName ;

    /**
     * 信号所在乡镇街道
     */
    @ApiModelProperty(value = "信号所在乡镇街道")
    private String streetName ;
    /**
     * 预警原因
     */
    @ApiModelProperty(value = "预警原因")
    private String warningReason ;

}
