package com.iflytek.cdc.admin.customizedapp.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.List;

/**
 * 任务配置表;
 * <AUTHOR> fengwang35
 * @date : 2024-10-17
 */
@ApiModel(value = "任务配置表")
@TableName("tb_cdccs_task_setting")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsTaskSetting extends BaseEntity implements Serializable{
    public static final String TABLE_NAME = "tb_cdccs_task_setting";
    /** 任务名称 */
    @ApiModelProperty(value = "任务名称")
    private String taskName ;
    /** 任务类型 */
    @ApiModelProperty(value = "任务类型")
    private String taskType ;
    /** 原始数据的数据模型Id **/
    @ApiModelProperty(value = "原始的数据模型Id")
    private String originalDataModelId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private String appId;

    @TableField(exist = false)
    private List<Filter> filters;

    /**
     * 数据过滤条件
     */
    @ApiModelProperty("数据过滤JSON字段")
    private String filterJson;

    @ApiModelProperty("任务提醒配置详情")
    @TableField(exist = false)
    private List<TbCdccsReminderTaskSettingDetail> reminderTaskSettingDetails;


    @Data
    public static class Filter{
        private String keyLabel;
        private String tableKey;
        private String tableColumn;
        private String valueLabel;
        private Object value;
        private String remark;
        private String dataModelField;
    }

}