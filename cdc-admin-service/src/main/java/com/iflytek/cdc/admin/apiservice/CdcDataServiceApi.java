package com.iflytek.cdc.admin.apiservice;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.iflytek.cdc.admin.dto.DictParams;
import com.iflytek.cdc.admin.common.util.ApiTool;
import com.iflytek.cdc.admin.dto.ExportTaskDTO;
import com.iflytek.cdc.admin.dto.brief.IndicatorSearchDto;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;
import com.iflytek.cdc.admin.vo.DictValueVO;
import com.iflytek.cdc.admin.vo.brief.IndicatorVo;
import com.iflytek.cdc.admin.vo.region.DimRegionNation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class CdcDataServiceApi {
    @Value("${cdc-data-service:http://cdc-data-service}")
    private String apiUrl;

    @Resource
    private ApiTool apiTool;

    public List<String> getTagsByDataSource( List<String> dataSources) {
        String url = String.format("%s/pt/v1/dim/symptom/getTagsByDataSource", apiUrl);
        String response = apiTool.doPost(url, dataSources, String.class);
        return new Gson() .fromJson(response, new TypeToken<List<String>>() {}.getType());
    }

    public List<String> getTagValuesBy(String tag, List<String> dataSources) {

        String url = String.format("%s/pt/v1/dim/symptom/getTagValuesByTag?tag=%s", apiUrl, tag);
        String response = apiTool.doPost(url, dataSources, String.class);
        return new Gson() .fromJson(response, new TypeToken<List<String>>() {}.getType());
    }

    public PageInfo<DictValueVO> searchDictValueBy(DictParams dictParams) {
        String url = String.format("%s/pt/v1/dim/searchDictValueBy", apiUrl);
        
        String response = apiTool.doPost(url, dictParams, String.class);
        return new Gson() .fromJson(response, new TypeToken<PageInfo<DictValueVO>>() {}.getType());
    }

    public List<IndicatorVo> getIndicatorValuesBy(List<IndicatorSearchDto> searchDtos, String businessType) {
        String url = String.format("%s/v1/pt/indicator/query/%s", apiUrl, businessType);
        String response = apiTool.doPost(url, searchDtos, String.class);
        return new Gson().fromJson(response, new TypeToken<List<IndicatorVo>>() {}.getType());
    }

    public TbCdcmrExportTask addAndRunDetailExportTask(ExportTaskDTO task) {
        String url = String.format("%s/pt/v1/dataExport/detail/task", apiUrl);
        String response = apiTool.doPost(url, task, String.class);
        return new Gson().fromJson(response, TbCdcmrExportTask.class);
    }

    /**
     * 从数仓维表中获取区划信息
     * 
     * @param code 区划编码
     * @return 区划信息
     */
    public DimRegionNation getRegionByCode(String code) {
        String url = String.format("%s/pt/v1/getRegionByCode?code=%s", apiUrl, code);
        return apiTool.doGet(url, DimRegionNation.class);
    }

    public void editIllnessRecord(String loginUserId, JSONObject jsonObject) {
        String url = String.format("%s/pt/v1/edr/editIllnessRecord?loginUserId=%s", apiUrl, loginUserId);
        apiTool.doPost(url, jsonObject, Object.class);
    }
}
