package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.CustomizedWarnVO;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO;
import com.iflytek.cdc.admin.dto.CustomizedWarningTaskQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningTaskVO;
import com.iflytek.cdc.admin.entity.CascadeVO;

import java.util.List;

public interface CustomizedWarningTaskService {
    void addCustomizedWarningTask(CustomizedWarningTaskVO taskVO, String loginUserId, String loginUserName);

    void updateCustomizedWarningTask(CustomizedWarningTaskVO taskVO, String loginUserId, String loginUserName);


    void deleteCustomizedWarningTask(String id, String loginUserId, String loginUserName);


    PageInfo<CustomizedWarningTaskVO> getCustomizedWarningTaskList(CustomizedWarningTaskQueryDTO queryDTO);

    CustomizedWarningTaskVO getCustomizedWarningTaskById(String id);

    List<CustomizedWarningTaskVO> getAllEnabledWarningTask();

    void updateCustomizedWarningTaskStatus(Integer status, String id, String loginUserId, String loginUserName);

    Boolean checkCustomizedWarnByName(String name);

    List<CascadeVO> getCustomizedNameList();

    List<CascadeVO> getCustomizedNameList(String loginUserId);

    CustomizedWarnVO getWarningRuleByTaskId(String taskId);

    String getWarningRuleIdByTaskId(String taskId);
}
