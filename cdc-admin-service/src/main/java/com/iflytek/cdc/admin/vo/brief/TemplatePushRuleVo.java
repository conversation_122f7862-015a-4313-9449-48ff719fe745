package com.iflytek.cdc.admin.vo.brief;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_template_rule")
public class TemplatePushRuleVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    @TableId
    private String id;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 区名称
     */
    private String districtName;
    /**
     * 权限者ID
     */
    private String authUserId;
    /**
     * 权限者
     */
    private String authUserName;
    /**
     * 权限时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date authTime;
    /**
     * 统计周期
     */
    private String statisticsCycle;
    /**
     * 标题
     */
    private String title;


    private String briefReportType;
}
