package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.outbound.entity.SignalSmsRecord;
import com.iflytek.cdc.admin.outbound.mapper.SignalSmsRecordMapper;
import com.iflytek.cdc.admin.outbound.service.SignalSmsRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 信号短信记录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SignalSmsRecordServiceImpl extends ServiceImpl<SignalSmsRecordMapper, SignalSmsRecord> implements SignalSmsRecordService {

 

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSignalSmsRecord(SignalSmsRecord signalSmsRecord) {
        try {
            return this.save(signalSmsRecord);
        } catch (Exception e) {
            log.error("保存信号短信记录失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchSignalSmsRecord(List<SignalSmsRecord> signalSmsRecords) {
        try {
            return this.saveBatch(signalSmsRecords);
        } catch (Exception e) {
            log.error("批量保存信号短信记录失败", e);
            throw e;
        }
    }


} 