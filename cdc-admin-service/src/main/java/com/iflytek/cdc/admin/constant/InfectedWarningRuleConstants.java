package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.enums.EventLevelEnum;
import com.iflytek.cdc.admin.enums.EventReportTypeEnum;
import com.iflytek.cdc.admin.enums.InfectedWarningMethodEnum;
import com.iflytek.cdc.admin.enums.WarningPriorityEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 * @date :2024/5/23 15:00
 * @description:InfectedWarningRuleConstants
 */
@Data
public class InfectedWarningRuleConstants {
    @ApiModelProperty(value = "传染病分类类型")
    private Object infectedClassType = InfectedWarningRuleConstants.InfectedClassTypeEnum.values();

    @ApiModelProperty(value = "传染病病种类型")
    private Object infectType = InfectedWarningRuleConstants.InfectedTypeEnum.values();

    @ApiModelProperty(value = "传染病预警方法")
    private Object warningMethod = InfectedWarningRuleConstants.WarningMethodEnum.values();

    @ApiModelProperty(value = "预警周期")
    private Object warningPeriod = InfectedWarningRuleConstants.WarningPeriodEnum.values();

    @ApiModelProperty(value = "指标")
    private Object warningIndicator = WarningRuleConstants.WarningIndicatorEnum.values();

    @ApiModelProperty(value = "病例范围")
    private Object caseScope = InfectedWarningRuleConstants.CaseScopeEnum.values();

    @ApiModelProperty(value = "人群范围")
    private Object populationScope = InfectedWarningRuleConstants.PopulationScopeEnum.values();

    @ApiModelProperty(value = "空间维度类型")
    private Object areaType = WarningRuleConstants.AreaTypeEnum.values();

    @ApiModelProperty(value = "空间维度层级")
    private Object areaLevel = WarningRuleConstants.AreaLevelEnum.values();

    @ApiModelProperty(value = "地址维度类型")
    private Object addressType = WarningRuleConstants.AddressTypeEnum.values();

    @ApiModelProperty(value = "基线数据计算方式")
    private Object baselineData = InfectedWarningRuleConstants.BaselineDataEnum.values();

    /**************************************预警规则改版**********************************************/
    @ApiModelProperty(value = "传染病预警方法")
    private Object infectedWarningMethod = InfectedWarningMethodEnum.values();

    @ApiModelProperty(value = "预警规则优先级")
    private Object warningRulePriority = WarningPriorityEnum.values();

    @ApiModelProperty(value = "事件分级")
    private Object eventLevel = EventLevelEnum.values();

    @ApiModelProperty(value = "事件报告类型")
    private Object eventReportType = EventReportTypeEnum.values();

    @ApiModelProperty(value = "地址类型与空间层级映射")
    private Map<String, List<WarningRuleConstants.AreaLevelEnum>> addressToAreaLevel = buildAddressToAreaLevelMap();

    private Map<String, List<WarningRuleConstants.AreaLevelEnum>> buildAddressToAreaLevelMap() {
        Map<String, List<WarningRuleConstants.AreaLevelEnum>> map = new LinkedHashMap<>();

        List<WarningRuleConstants.AreaLevelEnum> fullLevels = Arrays.asList(
                WarningRuleConstants.AreaLevelEnum.PROVINCE,
                WarningRuleConstants.AreaLevelEnum.CITY,
                WarningRuleConstants.AreaLevelEnum.DISTRICT,
                WarningRuleConstants.AreaLevelEnum.STREET,
                WarningRuleConstants.AreaLevelEnum.VILLAGE,
                WarningRuleConstants.AreaLevelEnum.TEAM,
                WarningRuleConstants.AreaLevelEnum.KEY_PLACE
        );

        map.put(WarningRuleConstants.AddressTypeEnum.CASE_ADDRESS.name(), fullLevels);
        map.put(WarningRuleConstants.AddressTypeEnum.LIVING_ADDRESS.name(), fullLevels);

        map.put(WarningRuleConstants.AddressTypeEnum.SCHOOL.name(), Collections.singletonList(
                WarningRuleConstants.AreaLevelEnum.SCHOOL));
        map.put(WarningRuleConstants.AddressTypeEnum.COMPANY.name(), Collections.singletonList(
                WarningRuleConstants.AreaLevelEnum.COMPANY));
        map.put(WarningRuleConstants.AddressTypeEnum.ORGANIZATION.name(), Collections.singletonList(
                WarningRuleConstants.AreaLevelEnum.ORGANIZATION));
        return map;
    }
    /**
     * 传染病分类类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum InfectedClassTypeEnum{
        STATUTORY("01", "法定传染病"),
        OTHER("09", "其他传染病"),
        ;
        private String value;
        private String label;

        InfectedClassTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 传染病病种类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum InfectedTypeEnum{
        甲类("1", "甲类"),
        乙类("2", "乙类"),
        丙类("3", "丙类"),
        其他传染病("4", "其他传染病"),
        其他疾病("5", "其他疾病"),
        ;
        private String value;
        private String label;

        InfectedTypeEnum(String value, String desc) {
            this.value = value;
            this.label = desc;
        }
    }

    /**
     * 传染病预警方法
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum WarningMethodEnum{
        SCAM("SCAM", "单病例预警法"),
        EAT("EAT", "聚集性疫情法"),
        EWMA("EWMA", "指数加权移动平均法"),
        CUSUM("CUSUM", "累积和控制图法"),
        MPM("MPM", "移动百分位数法"),
        MA("MA", "移动平均数法"),
        ;
        private String value;
        private String label;

        WarningMethodEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 预警周期
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum WarningPeriodEnum{
        REAL_TIME("REAL_TIME", "实时探测"),
        T_PLUS_1("T_PLUS_1", "每日运算1次"),
        ;
        private String value;
        private String label;

        WarningPeriodEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 病例类型(复用) 病例范围
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum CaseScopeEnum {
        疑似病例("1", "疑似病例"),
        临床诊断病例("2", "临床诊断病例"),
        确诊病例("3", "确诊病例"),
        病原携带者("4", "无症状感染者/带菌者/隐性感染/携带者"),
        ;
        private String value;
        private String label;

        CaseScopeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }
    /**
     * 人群范围
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum PopulationScopeEnum {
        ALL("ALL", "全部人群"),
        SPEC("SPEC", "特定人群"),
        ;
        private String value;
        private String label;

        PopulationScopeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 基线数据计算方式
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum BaselineDataEnum{

        PREV("PREV", "上一期"),
        HISTORY("HISTORY", "历史同期"),
        ;
        private String value;
        private String label;

        BaselineDataEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

}
