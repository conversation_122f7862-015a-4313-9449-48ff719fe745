package com.iflytek.cdc.admin.vo.brief;

import java.io.Serializable;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.brief.BriefIndicatorsEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 简报信息vo
 * 
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_brief_info")
public class BriefInfoVo implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	/**
	 * 简报标题
	 */
	private String title;

	/**
	 * 内容
	 */
	private String content;

	/**
	 * 附件标题
	 */
	private String attachmentTitle;

	/**
	 * 附件维度指标集合
	 */
	@ApiModelProperty("附件维度指标集合")
	private List<BriefIndicatorsEntity> attachmentIndicators;

	private String templateId;

}
