package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom;
import com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo;
import com.iflytek.cdc.admin.service.InfectiousDiseasesApiService;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * @ClassName InfecDisController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 16:02
 * @Version 1.0
 */

@RestController
@Api(tags = "传染病标准维护服务")
@AllArgsConstructor
public class InfecDisController {

    @Resource
    private SyncMdmDataService infecDisService;

    @Resource
    private InfectiousDiseasesApiService infecDisApiService;

    @ApiOperation("传染病标准维护服务-新增")
    @PostMapping("/{version}/pt/infectious/diseases/add")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void addDiseases(@RequestBody InfectiousDiseasesDto infectiousDiseasesDto, @RequestParam String loginUserId) {
        infecDisService.addInfectiousDiseases(loginUserId, infectiousDiseasesDto);
    }

    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    @ApiOperation("传染病标准维护服务-查询")
    @PostMapping("/{version}/pt/infectious/diseases/queryInfecDis")
    public PageData<InfectiousDiseases> queryInfecDis(@RequestBody SearchInfecInfoDTO sd, @RequestParam String loginUserId) {
        return infecDisService.queryInfecPageInfo(sd);
    }

    @PostMapping("/{version}/pt/infectious/diseases/updateInfecDis")
    @ApiOperation("传染病标准维护服务-修改")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateInfectiousDiseasesInfo( @RequestBody InfectiousDiseases infecDis, @RequestParam String loginUserId){
        infecDisService.updateInfecInfo(loginUserId,infecDis);
    }


    @PostMapping("/{version}/pt/infectious/diseases/mdmDataSync")
    @ApiOperation("传染病标准维护服务-同步")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void  mdmDataSync( @RequestParam String loginUserId){
        infecDisService.syncMdmData(null,loginUserId);
    }

    @GetMapping("/{version}/pt/infectious/getDicMdmData")
    @ApiOperation("传染病标准维护服务-获取字典值")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<TermCodedValueInfo> getDicMdmData(@RequestParam String code){
        return infecDisService.getMdmDataSync(code);
    }

    @GetMapping("/{version}/pt/infectious/getInfecDisStatus")
    @ApiOperation("传染病标准维护服务-启用前判断是否可以启用")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  boolean  getInfecDisStatus(@PathVariable String version,@RequestParam String id){
        return infecDisService.judgeIsUse(id);
    }

    @GetMapping("/{version}/pt/infectious/queryInfecDisTree")
    @ApiOperation("传染病病种树形结构")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  List<InfecDisTreeInfo>  queryInfecDisTree(){
        return infecDisApiService.queryInfecDisTree();
    }
}
