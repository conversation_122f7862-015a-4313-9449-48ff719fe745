package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import lombok.Data;

import java.util.List;

/**
 * 时间序列分析请求参数
 */
@Data
public class ArimaRequest {

    /**
     * 历史数据
     */
    private HistoryData historyData;

    /**
     * 预测的步长
     */
    private Integer step;


    /**
     * 历史数据
     */
    @Data
    public static class HistoryData{

        /**
         * 历史数据日期
         */
        private List<String> timestamp;

        /**
         * 预测的数量
         */
        private List<Integer> quantity;
    }
}
