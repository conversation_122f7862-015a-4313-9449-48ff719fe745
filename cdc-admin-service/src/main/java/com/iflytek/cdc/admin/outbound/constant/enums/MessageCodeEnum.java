package com.iflytek.cdc.admin.outbound.constant.enums;

/**
 * <AUTHOR>
 */

public enum MessageCodeEnum {

    CODE_0("0","成功","记录id"),
    CODE_10001("10001","参数有误","仔细检查文档说明"),
    CODE_10002("10002","验证已失效","时间戳超出5分钟已失效，重新使用最新时间并生成新的sign"),
    CODE_10003("10003","机构缺失厂商来源配置，请联系管理员","联系管理员配置对应的secretKey"),
    CODE_10004("10004","Sign错误","仔细检查文档sign的生成说明"),
    CODE_20001("20001","居民被全部过滤","仔细检查dwellers参数说明"),
    CODE_20002("20002","planId对应的业务方案为空","联系管理员确认planId是否一致"),
    CODE_20003("20003","planId对应的业务方案有误","联系管理员确认planId对应的业务方案是否正常"),
    CODE_20004("20004","speechId对应的业务话术为空","联系管理员确认speechId是否一致"),
    CODE_20005("20005","smsId对应的业务短信为空","联系管理员确认smsId是否一致"),
    CODE_20006("20006","提示：当前外呼时间挺晚了，可能影响居民休息，建议您明天上午八点后再呼！","任务时间+重播间隔不能进入晚9早6的限制内"),
    CODE_20007("20007","执行时间不能小于当前时间","检查设置的指定时间参数，指定时间只能设置未来任务"),
    CODE_20008("20008","自我介绍变量长度超额","联系管理员检查自我介绍配置"),
    CODE_20009("20009","敏感词限制","检查变量内容，去除敏感词"),
    CODE_20010("20010","该话术缺失对应平台配置","联系管理员检查话术配置"),
    CODE_20011("20011","单位服务量超额","已达到设置的任务量上限，联系管理员调整配置"),
    CODE_20012("20012","单位服务量超期","已超过设置的任务，联系管理员调整配置"),
    CODE_20013("20013","话术变量不匹配","联系管理员或通过接口查询获取最新变量参数"),
    CODE_20014("20014","话术变量长度超额","仔细检查变量对应的长度限制"),
    CODE_20015("20015","短信变量不匹配","联系管理员获取最新变量参数"),
    CODE_20016("20016","短信长度超额","缩减短信内容或联系管理员调整短信长度配置"),
    CODE_30001("30001","数据不存在","联系管理员查看参数数据"),
    CODE_99999("99999","系统异常","联系管理员查询失败原因"),

    CUSTOM_C00000("C00000","成功","执行外呼成功！"),
    CUSTOM_C00001("C00001","成功","发送短信成功！"),
    CUSTOM_C00002("C20003","成功","获取外呼结果数据成功！"),
    CUSTOM_C20001("C20001","发送短信失败","发送短信失败，请联系管理员！"),
    CUSTOM_C20002("C20002","执行外呼失败","执行外呼失败，请联系管理员！"),
    CUSTOM_C20003("C20003","获取外呼结果数据失败","获取外呼结果数据失败！");


    private String code;
    private String desc;
    private String proposal;

    MessageCodeEnum(String code, String desc, String proposal){
        this.code = code;
        this.desc = desc;
        this.proposal = proposal;
    }

    public static boolean isSuccess(String code){
        return CODE_0.code.equals(code);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getProposal() {
        return proposal;
    }
}
