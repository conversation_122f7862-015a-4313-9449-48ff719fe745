package com.iflytek.cdc.admin.customizedapp.service;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsApp;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppQueryDTO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface AppService extends ICdcService<TbCdccsApp> {

    /**
     *
     * @param code 编码
     * @return 应用实例对象
     */
    TbCdccsApp loadByCode(String code);

    /**
     * 查询所有应用
     * @return 应用实例对象集合
     */
    List<TbCdccsApp> listAll(AppQueryDTO queryDTO);

    /**
     * 根据分类id查询
     * @param categoryId 分类id
     * @return 应用集合
     */
    List<TbCdccsApp> listByCategoryId(String categoryId, String appName);

    /**
     * 发布菜单
     */
    void published(String groupId, String loginUserId);

    /**
     * 根据应用分类删除
     */
    void deleteByCategoryId(String categoryId);


}
