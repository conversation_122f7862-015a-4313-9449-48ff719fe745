package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.service.CommonDataSyncService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.*;



/**
 * <AUTHOR>
 * @date 2021/8/5 17:17
 **/
@RestController
@EnableAsync
public class CommonDataSyncController {

    private final CommonDataSyncService cService;


    public CommonDataSyncController(CommonDataSyncService cService) {
        this.cService = cService;
    }

    @PostMapping("/{version}/pt/common/MdmDataSyncs")
    @ApiOperation("mdm数据同步")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void mdmDataSyncs(@PathVariable String version, @RequestParam String loginUserId, @RequestParam String dataCode) {
        cService.mdmDataSyncs(loginUserId, dataCode);
    }

    @GetMapping("/{version}/pt/common/getDictStatus")
    @ApiOperation("启用前判断是否可以启用")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public boolean getDictStatus(@PathVariable String version, @RequestParam String id, @RequestParam String dataCode) {
        return cService.getDictStatus(id, dataCode);
    }


}
