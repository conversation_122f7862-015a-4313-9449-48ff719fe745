package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.RiskReportPushDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordQueryDto;

public interface RiskReportPushService {
    Boolean pushReport(String loginUserId, RiskReportPushDto dto);

    PageInfo<RiskReportPushRecordDto> queryList(String loginUserId,RiskReportPushRecordQueryDto dto);

}
