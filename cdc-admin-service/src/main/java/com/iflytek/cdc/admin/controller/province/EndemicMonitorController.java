package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig;
import com.iflytek.cdc.admin.model.mr.dto.EndemicMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EndemicDiseaseInfoVO;
import com.iflytek.cdc.admin.service.EndemicProcessMonitorService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地方病 圈定病例范围
 * */
@Api(tags = "省统筹 - 监测地方病 监测病例定义")
@RequestMapping("/pt/{version}/endemicMonitor")
@RestController
public class EndemicMonitorController {

    @Resource
    private EndemicProcessMonitorService endemicProcessMonitorService;

    @PostMapping("/getEndemicTreeInfo")
    @ApiOperation("查询地方病结构树")
    public List<EndemicDiseaseInfoVO> getEndemicTreeInfo(){

        return endemicProcessMonitorService.getEndemicTreeInfo();
    }

    @PostMapping("/editSubEndemicInfo")
    @ApiOperation("编辑子类地方病传染病信息")
    public String editSubEndemicInfo(@RequestBody TbCdcmrEndemicDiseaseInfo EndemicDiseaseInfo){

        return endemicProcessMonitorService.editSubEndemicInfo(EndemicDiseaseInfo);
    }

    @GetMapping("/deleteSubEndemicInfo")
    @ApiOperation("删除子类地方病传染病信息")
    public void deleteSubEndemicInfo(@RequestParam String id){

        endemicProcessMonitorService.deleteSubEndemicInfo(id);
    }

    @PostMapping("/getEndemicInfoConfigs")
    @ApiOperation("查询该地方病下监测病例定义配置")
    public List<TbCdcmrEndemicMonitorConfig> getEndemicInfoConfigs(@RequestBody EndemicMonitorConfigQueryDTO queryDTO){
        return endemicProcessMonitorService.getEndemicInfoConfigs(queryDTO);
    }

    @PostMapping("/editEndemicProcessDefinition")
    @ApiOperation("编辑地方病传染病病例定义配置")
    @OperationLogAnnotation(operationName = "编辑地方病病例定义配置")
    public void editEndemicProcessDefinition(@RequestParam String diseaseInfoId,
                                              @RequestBody List<TbCdcmrEndemicMonitorConfig> EndemicMonitorConfigs){

        endemicProcessMonitorService.editEndemicProcessDefinition(diseaseInfoId, EndemicMonitorConfigs);
    }

    @GetMapping("/getEndemicDiseaseCodeByName")
    @ApiOperation("根据地方病传染病名称获取code以及其子类code")
    public List<String> getEndemicDiseaseCodeByName(@RequestParam String diseaseName){

        return endemicProcessMonitorService.getEndemicDiseaseCodeByName(diseaseName);
    }

}
