package com.iflytek.cdc.admin.customizedapp.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@ApiModel(value = "任务表")
@TableName("tb_cdccs_data_source_config")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsDataSourceConfig extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "编码")
    private String code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "路径")
    private String url;
    @ApiModelProperty(value = "用户名")
    private String username;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty("驱动类名")
    private String driverClassName;
    @ApiModelProperty("扩展参数")
    private String extendJson;
    @ApiModelProperty("数据库类型")
    private String dbType;
}
