package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.SyndromeWarningRuleConstants;
import com.iflytek.cdc.admin.dto.SyndromeWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.SyndromeDiseaseWarningRuleService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/syndromeDiseaseWarning")
@Api(tags = "预警规则维护-症候群")
public class SyndromeDiseaseWarningRuleController {

    @Resource
    private SyndromeDiseaseWarningRuleService syndromeDiseaseWarningRuleService;

    @PostMapping("/savaOrUpdateRule")
    @ApiOperation("编辑症候群规则")
    @OperationLogAnnotation(operationName = "编辑症候群规则")
    public void savaOrUpdateRule(@RequestBody TbCdcmrSyndromeDiseaseWarningRule rule){

        syndromeDiseaseWarningRuleService.savaOrUpdateRule(rule);
    }

    @GetMapping("/getRuleDetail")
    @ApiOperation("法定传染-预警规则详情")
    public PageInfo<TbCdcmrSyndromeDiseaseWarningRule> getRuleDetail(@RequestParam String diseaseInfoId,
                                                                     @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                     @RequestParam(defaultValue = "20") Integer pageSize,
                                                                     @RequestParam(required = false) String riskLevel,
                                                                     @RequestParam(required = false) String warningMethod,
                                                                     @RequestParam(required = false) String followStatus){

        return syndromeDiseaseWarningRuleService.getRuleDetail(diseaseInfoId, pageIndex, pageSize, riskLevel, warningMethod, followStatus);
    }

    @PostMapping("/getSyndromeTree")
    @ApiOperation("获取传染病结构树")
    public List<TreeNode> getSyndromeTree(@RequestBody SyndromeWaringRuleQueryDTO dto){

        return syndromeDiseaseWarningRuleService.getSyndromeTree(dto);
    }

    @GetMapping("/getConstants")
    @ApiOperation("常量获取")
    public Object getConstants(){

        return SyndromeWarningRuleConstants.getInstance();
    }


}
