package com.iflytek.cdc.admin.datamodel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DataModelFormQueryDTO {

    @ApiModelProperty(value = "数据模型id")
    @NotNull
    private String modelId;

    @ApiModelProperty(value = "数据模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "页码")
    private Integer pageIndex = 1;

    @ApiModelProperty(value = "页大小")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "是否重复")
    private Integer isRepeat;

    @ApiModelProperty(value = "表单名称")
    private String formName;
}
