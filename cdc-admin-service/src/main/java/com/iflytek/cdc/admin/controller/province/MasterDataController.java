package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.constant.InfectedDataConstants;
import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.AllMasterDataConfigInfo;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.MasterDataCommonService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/pt/{version}/masterData")
public class MasterDataController {

    @Resource
    private MasterDataCommonService commonService;

    @PostMapping("/getMasterData")
    @ApiOperation("获取主数据树形结构")
    public List<TreeNode> getMasterData(@RequestBody MasterDataQueryDTO dto){

        return commonService.getMasterData(dto);
    }

    @PostMapping("/getMasterDataByRootId")
    @ApiOperation("获取某个主数据的树形结构")
    public TreeNode getMasterDataByRootId(@RequestParam String masterDataType, @RequestParam String rootId){
        return commonService.getMasterDataByRootId(masterDataType, rootId);
    }

    @PostMapping("/getMasterDataConfigInfo")
    @ApiOperation("获取主数据基础信息以及其配置信息")
    public <T extends CommonMasterData> T getMasterDataConfigInfo(@RequestBody MasterDataQueryDTO dto){

        return commonService.getMasterDataConfigInfo(dto);
    }

    @PostMapping("/addMasterData")
    @ApiOperation("新增主数据")
    @OperationLogAnnotation(operationName = "新增主数据")
    public String addMasterData(@RequestBody MasterDataRecordDTO dto){

        return commonService.addMasterData(dto);
    }

    @PostMapping("/editMasterDataBaseInfo")
    @ApiOperation("编辑主数据信息")
    @OperationLogAnnotation(operationName = "编辑主数据信息")
    public void editMasterDataBaseInfo(@RequestBody MasterDataEditRecordDTO dto){

        commonService.editMasterDataBaseInfo(dto);
    }

    @PostMapping("/deleteMasterDataBaseInfo")
    @ApiOperation("删除主数据信息")
    @OperationLogAnnotation(operationName = "删除主数据信息")
    public void deleteMasterDataBaseInfo(@RequestBody MasterDataDeleteDTO dto){

        commonService.deleteMasterDataBaseInfo(dto);
    }

    @PostMapping("/syncMasterData")
    @ApiOperation("手动同步主数据")
    @OperationLogAnnotation(operationName = "手动同步主数据")
    public void syncMasterData(@RequestParam String masterDataType){

        commonService.syncMasterData(masterDataType);
    }

    @PostMapping("/uploadFile")
    @ApiOperation("上传主数据excel - 用于初始化数据")
    @OperationLogAnnotation(operationName = "上传主数据excel - 用于初始化数据")
    public void uploadFile(@RequestParam String masterDataType,
                           @RequestParam MultipartFile file){

        commonService.uploadFile(masterDataType, file);
    }

    @GetMapping("/getDiseaseInfoByCode")
    @ApiOperation("通过code获取疾病信息")
    public CommonMasterData getDiseaseInfoByCode(@RequestParam String masterDataType,
                                                 @RequestParam String masterDataCode){

        return commonService.getDiseaseInfoByCode(masterDataType, masterDataCode);
    }

    @PostMapping("/getConstants")
    @ApiOperation("获取静态变量")
    public InfectedDataConstants getConstants(){

        return new InfectedDataConstants();
    }

    @PostMapping("/getMasterDataConfigNote")
    @ApiOperation("查询主数据字段注释信息 - 前端联调使用")
    public AllMasterDataConfigInfo getMasterDataConfigNote(){

        return new AllMasterDataConfigInfo();
    }

}
