package com.iflytek.cdc.admin.datamodel.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 值域导入 DTO
 * 支持动态层级结构，每个 sheet 页对应一个值域字典
 */
@Data
public class ValueDomainImportDTO {

    @ExcelProperty("层级1")
    private String level1;

    @ExcelProperty("层级2")
    private String level2;

    @ExcelProperty("层级3")
    private String level3;

    @ExcelProperty("层级4")
    private String level4;

    @ExcelProperty("层级5")
    private String level5;

    @ExcelProperty("层级6")
    private String level6;

    @ExcelProperty("层级7")
    private String level7;

    @ExcelProperty("层级8")
    private String level8;

    @ExcelProperty("层级9")
    private String level9;

    @ExcelProperty("层级10")
    private String level10;

    /**
     * 获取指定层级的值
     * @param level 层级数（从1开始）
     * @return 对应层级的值
     */
    public String getLevelValue(int level) {
        switch (level) {
            case 1: return level1;
            case 2: return level2;
            case 3: return level3;
            case 4: return level4;
            case 5: return level5;
            case 6: return level6;
            case 7: return level7;
            case 8: return level8;
            case 9: return level9;
            case 10: return level10;
            default: return null;
        }
    }

    /**
     * 设置指定层级的值
     * @param level 层级数（从1开始）
     * @param value 值
     */
    public void setLevelValue(int level, String value) {
        switch (level) {
            case 1: this.level1 = value; break;
            case 2: this.level2 = value; break;
            case 3: this.level3 = value; break;
            case 4: this.level4 = value; break;
            case 5: this.level5 = value; break;
            case 6: this.level6 = value; break;
            case 7: this.level7 = value; break;
            case 8: this.level8 = value; break;
            case 9: this.level9 = value; break;
            case 10: this.level10 = value; break;
        }
    }

    /**
     * 获取最大有效层级数
     * @return 最大层级数
     */
    public int getMaxLevel() {
        for (int i = 10; i >= 1; i--) {
            if (getLevelValue(i) != null && !getLevelValue(i).trim().isEmpty()) {
                return i;
            }
        }
        return 0;
    }

    /**
     * 检查是否为空行
     * @return 是否为空行
     */
    public boolean isEmptyRow() {
        return getMaxLevel() == 0;
    }
} 