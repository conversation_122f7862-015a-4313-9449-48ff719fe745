package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.outcall.OutCallResponse;
import com.iflytek.cdc.admin.dto.outcall.SSOutCallContentDto;
import com.iflytek.cdc.admin.service.impl.OutCallService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URLEncoder;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/20
 */
@RestController
@Api(tags = "外呼接口")
@RefreshScope
public class OutCallController {

    @Resource
    private OutCallService outCallService;

    @Value("${out.call.url.prefix}")
    private String outCallUrlPrefix;
    @Value("${out.call.channel}")
    private String channel;
    @Value("${out.call.zhylSecret}")
    private String secret;

    @PostMapping("/{version}/pb/outCall/ssOutCallSms")
    @ApiOperation("短信外呼")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public OutCallResponse ssOutCallSms(@RequestBody SSOutCallContentDto dto, @PathVariable String version) {
        return outCallService.ssOutCallSms(dto);
    }


    /**
     *
     * @param loginName
     * @param rediectUrl 后管 /ifm/manager/  外呼 /fu/fpva/
     * @param version
     * @return
     */
    @GetMapping("/{version}/pb/outCall/jumpOutCallPage")
    @ApiOperation("跳转外呼")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public String jumpOutCallPage(@RequestParam String loginName, @RequestParam String rediectUrl, @PathVariable String version) {
        return getLoginUrl(outCallUrlPrefix, loginName, channel, secret, rediectUrl);
    }

    private String getLoginUrl(String url, String loginName, String channel, String secret, String rediectUrl){
        String baseUrl  = Base64Utils.encodeToString((url+rediectUrl).getBytes());
        System.out.println(baseUrl);
        String  sign  = DigestUtils.md5DigestAsHex((channel+url+"/gateway/redirect/"+baseUrl+loginName+secret).getBytes());
        System.out.println(sign);
        return url + "/common-api/v1/pb/authProxy/cookie/redirect?channel=" + channel + "&userid=" + loginName + "&sign=" + sign
                + "&url=" + URLEncoder.encode(url+"/gateway/redirect/") + baseUrl;
    }

}
