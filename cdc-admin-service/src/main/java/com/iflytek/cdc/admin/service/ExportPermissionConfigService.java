package com.iflytek.cdc.admin.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.ApprovalPermissionConfigDTO;
import com.iflytek.cdc.admin.dto.ExportConfigDTO;
import com.iflytek.cdc.admin.dto.ExportConfigQueryDTO;
import com.iflytek.cdc.admin.dto.ExportPermissionConfigDTO;

public interface ExportPermissionConfigService {

    //查询导出审核配置
    PageInfo<ExportConfigDTO> queryExportConfigs(ExportConfigQueryDTO queryDTO,String loginUserId);

    //更新审核权限
    boolean updateApprovalRequired(String loginUserId,String id, boolean approvalRequired);

    //查询导出权限配置
    ExportPermissionConfigDTO getExportPermissionConfig(String idOrCode);

    //查询审核权限配置
    ApprovalPermissionConfigDTO getApprovalPermissionConfig(String id);

    //更新导出权限配置
    boolean updateExportPermissionConfig(String id, ExportPermissionConfigDTO exportPermissionConfig, String loginUserId);

    //更新审核权限配置
    boolean updateApprovalPermissionConfig(String id, ApprovalPermissionConfigDTO approvalPermissionConfig, String loginUserId);

    //删除导出审核权限配置
    boolean deleteExportPermissionConfig(String id);
}
