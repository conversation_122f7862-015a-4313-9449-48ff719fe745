package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo;

import java.util.List;


/**
 * @ClassName InfectiousDiseasesService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 16:04
 * @Version 1.0
 */
public interface InfectiousDiseasesApiService {

    List<InfecDisTreeInfo> queryInfecDisTree();

    List<CascadeVO> getInfectedNameList();

    List<CascadeVO> getInfectedNameList(String loginUserId);

    List<CascadeVO> getInfectedNameListWithoutChildren();


}
