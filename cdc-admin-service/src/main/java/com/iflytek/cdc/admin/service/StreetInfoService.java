package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.model.dm.vo.AreaInfoVO;
import com.iflytek.cdc.admin.common.vo.AreaMappingVO;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;

import java.util.List;

public interface StreetInfoService {

    /**
     * 获取区域
     */
    List<AreaMappingVO> listByAreaCodes(Integer areaLevel, List<String> codes);

    /**
     * 获取当前登录用户所在的区域
     */
    LoginUserAreaVO loadCurrUserArea(String loginUserName);

    /**
     * 获取区域信息
     */
    AreaInfoVO getAreaInfo();
}
