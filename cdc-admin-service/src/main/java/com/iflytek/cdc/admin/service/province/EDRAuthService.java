package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessRecordAuthority;
import com.iflytek.cdc.admin.model.mr.dto.EDRAuthQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;

import java.util.List;
import java.util.Map;

public interface EDRAuthService {

    /**
     * 查询权限信息
     * */
    PageInfo<EDRAuthInfoVO> listAuthBy(EDRAuthQueryDTO dto);

    /**
     * 编辑edr权限
     * */
    void editEDRAuth(TbCdcmrIllnessRecordAuthority recordAuthority);

    /**
     * 查询该用户权限
     * */
    Map<String, List<TbCdcmrIllnessRecordAuthority>> getUserAuthInfo(String userId);

    EDRAuthResultVO validViewAuth(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord);

    void addAuthApplyRecord(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord);

    void changeAuthStatus(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord);

    PageInfo<TbCdcmrIllnessFilePermissionRecord>  getAuthApplyRecordList(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord);

    TbCdcmrIllnessFilePermissionRecord getPermissionRecordInfoById(String id);
}
