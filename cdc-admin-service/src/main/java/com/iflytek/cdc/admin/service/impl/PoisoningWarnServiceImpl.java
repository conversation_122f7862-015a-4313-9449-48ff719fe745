package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.enums.BusinessTypeEnum;
import com.iflytek.cdc.admin.enums.PoisoningMonitorObjectEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningTypeMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrPoisoningWarnRuleMapper;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.DataSourceConfigService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.PoisoningWarnService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PoisoningWarnServiceImpl implements PoisoningWarnService {


    @Resource
    TbCdcmrPoisoningMapper tbCdcmrPoisoningMapper;
    @Resource
    TbCdcmrPoisoningWarnMapper tbCdcmrPoisoningWarnMapper;
    @Resource
    TbCdcmrPoisoningWarnRuleMapper tbCdcmrPoisoningWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;
    @Resource
    TbCdcmrPoisoningTypeMapper tbCdcmrPoisoningTypeMapper;

    @Resource
    DataAuthService dataAuthService;

    @Resource
    private ParamConfigService paramConfigService;

    @Resource
    DataSourceConfigService dataSourceConfigService;
    @Override
    public PageInfo<TbCdcmrPoisoning> getPoisoningPageList(PoisoningQueryDTO poisoningQueryDTO) {
        PageHelper.startPage(poisoningQueryDTO.getPageIndex(), poisoningQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrPoisoningMapper.getList(poisoningQueryDTO));
    }

    @Override
    public void addPoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId) {
        if (tbCdcmrPoisoningMapper.getByPoisoningCode(tbCdcmrPoisoning.getPoisoningCode()) != null) {
            throw new MedicalBusinessException("已存在相同中毒编码!");
        }
        if (StringUtils.isNotEmpty(tbCdcmrPoisoning.getParentCode())){
            TbCdcmrPoisoning poisoning = tbCdcmrPoisoningMapper.getByPoisoningCode(tbCdcmrPoisoning.getParentCode());
            if (poisoning == null){
                throw new MedicalBusinessException("父类编码不存在");
            }
            tbCdcmrPoisoning.setParentCode(poisoning.getPoisoningCode());
            tbCdcmrPoisoning.setParentName(poisoning.getPoisoningName());
        }
        tbCdcmrPoisoning.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_poisoning")));
        tbCdcmrPoisoning.setUpdater(loginUserId);
        tbCdcmrPoisoning.setUpdateTime(new Date());
        tbCdcmrPoisoning.setIsDeleted(0);
        tbCdcmrPoisoningMapper.insert(tbCdcmrPoisoning);
        addPoisoningWarnByPoisoning(tbCdcmrPoisoning, loginUserId);
    }

    @Override
    public void deletePoisoning(String id) {
        TbCdcmrPoisoning poisoning = tbCdcmrPoisoningMapper.selectByPrimaryKey(id);
        TbCdcmrPoisoningWarn poisoningWarn = tbCdcmrPoisoningWarnMapper.getByPoisoningCode(poisoning.getPoisoningCode());
        if (!tbCdcmrPoisoningWarnRuleMapper.getRuleListByWarnId(poisoningWarn.getId()).isEmpty()) {
            throw new MedicalBusinessException("还有在使用的规则，请先删除规则后再进行删除！");
        }
        tbCdcmrPoisoningWarnMapper.deleteByPrimaryKey(poisoning.getId());
        tbCdcmrPoisoningMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void updatePoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId) {
        TbCdcmrPoisoning existPoisoning = tbCdcmrPoisoningMapper.getByPoisoningCode(tbCdcmrPoisoning.getPoisoningCode());

        if (existPoisoning != null && !Objects.equals(existPoisoning.getId(), tbCdcmrPoisoning.getId())) {
            throw new MedicalBusinessException("已存在相同中毒编码!");
        }
        TbCdcmrPoisoning updatePoisoning = new TbCdcmrPoisoning();
        updatePoisoning.setId(tbCdcmrPoisoning.getId());
        updatePoisoning.setUpdater(loginUserId);
        updatePoisoning.setUpdateTime(new Date());
        updatePoisoning.setPoisoningName(tbCdcmrPoisoning.getPoisoningName());
        updatePoisoning.setRemark(tbCdcmrPoisoning.getRemark());
        updatePoisoning.setStatus(tbCdcmrPoisoning.getStatus());
        updatePoisoning.setPoisoningCode(tbCdcmrPoisoning.getPoisoningCode());
        updatePoisoning.setPoisoningTypeName(tbCdcmrPoisoning.getPoisoningTypeName());
        updatePoisoning.setPoisoningTypeCode(tbCdcmrPoisoning.getPoisoningTypeCode());
        if (StringUtils.isNotEmpty(tbCdcmrPoisoning.getParentCode())){
            TbCdcmrPoisoning poisoning = tbCdcmrPoisoningMapper.getByPoisoningCode(tbCdcmrPoisoning.getParentCode());
            if (poisoning == null){
                updatePoisoning.setParentCode(null);
                updatePoisoning.setParentName(null);
            }else {
                updatePoisoning.setParentCode(poisoning.getPoisoningCode());
                updatePoisoning.setParentName(poisoning.getPoisoningName());
            }

        }
        savePoisoningWarnByPoisoning(updatePoisoning, loginUserId);
        tbCdcmrPoisoningMapper.updateByPrimaryKeySelective(updatePoisoning);
    }

    @Override
    public PageInfo<PoisoningWarnDto> getPoisoningWarnPageList(PoisoningQueryDTO poisoningQueryDTO) {
        PageHelper.startPage(poisoningQueryDTO.getPageIndex(), poisoningQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrPoisoningWarnMapper.getList(poisoningQueryDTO));
    }

    @Override
    public void updatePoisoningWarn(PoisoningWarnDto poisoningWarnDto, String loginUserId) {
        TbCdcmrPoisoningWarn tbCdcmrPoisoningWarn = new TbCdcmrPoisoningWarn();
        tbCdcmrPoisoningWarn.setId(poisoningWarnDto.getId());
        tbCdcmrPoisoningWarn.setStatus(poisoningWarnDto.getStatus());
        tbCdcmrPoisoningWarn.setPoisoningCode(poisoningWarnDto.getPoisoningCode());
        tbCdcmrPoisoningWarn.setPoisoningName(poisoningWarnDto.getPoisoningName());
        tbCdcmrPoisoningWarn.setRemark(poisoningWarnDto.getRemark());
        tbCdcmrPoisoningWarn.setUpdater(loginUserId);
        tbCdcmrPoisoningWarn.setIsDeleted(0);
        tbCdcmrPoisoningWarn.setUpdateTime(new Date());
        tbCdcmrPoisoningWarn.setMaxLifeCycle(poisoningWarnDto.getMaxLifeCycle());
        tbCdcmrPoisoningWarn.setSmsSendTypeCode(poisoningWarnDto.getSmsSendTypeCode());
        tbCdcmrPoisoningWarn.setSmsSendTypeDesc(poisoningWarnDto.getSmsSendTypeDesc());
        tbCdcmrPoisoningWarn.setEventGenerationNode(poisoningWarnDto.getEventGenerationNode());
        tbCdcmrPoisoningWarnMapper.updateByPrimaryKeySelective(tbCdcmrPoisoningWarn);
        updateRules(tbCdcmrPoisoningWarn.getId(), poisoningWarnDto.getRuleList(), loginUserId);
    }

    @Override
    public PoisoningWarnDto getWarnById(String warnId) {
        PoisoningWarnDto dto = new PoisoningWarnDto();
        TbCdcmrPoisoningWarn poisoningWarn = tbCdcmrPoisoningWarnMapper.selectByPrimaryKey(warnId);
        TbCdcmrPoisoning poisoning = tbCdcmrPoisoningMapper.getByPoisoningCode(poisoningWarn.getPoisoningCode());
        TbCdcmrDataSourceConfig dataSourceConfig = dataSourceConfigService.getByBusinessTypeAndSignalType(BusinessTypeEnum.POISONING.getCode(), poisoning.getPoisoningTypeCode());
        dto.setDataSource(Constants.DATA_SOURCE_CONFIG_RPT);
        if (dataSourceConfig != null){
            //数据来源:1.报卡2.病历
            dto.setDataSource(dataSourceConfig.getDataSource());
        }
        dto.setEventGenerationNode(poisoningWarn.getEventGenerationNode());
        dto.setId(poisoningWarn.getId());
        dto.setStatus(poisoningWarn.getStatus());
        dto.setRemark(poisoningWarn.getRemark());
        dto.setPoisoningCode(poisoningWarn.getPoisoningCode());
        dto.setPoisoningName(poisoningWarn.getPoisoningName());
        dto.setMaxLifeCycle(poisoningWarn.getMaxLifeCycle());
        dto.setSmsSendTypeCode(poisoningWarn.getSmsSendTypeCode());
        dto.setSmsSendTypeDesc(poisoningWarn.getSmsSendTypeDesc());
        dto.setRuleList(tbCdcmrPoisoningWarnRuleMapper.getRuleListByWarnId(warnId));
        return dto;
    }

    @Override
    public void updatePoisoningWarnStatus(PoisoningWarnDto poisoningWarnDto, String loginUserId) {
        TbCdcmrPoisoningWarn tbCdcmrPoisoningWarn = new TbCdcmrPoisoningWarn();
        tbCdcmrPoisoningWarn.setStatus(poisoningWarnDto.getStatus());
        tbCdcmrPoisoningWarn.setId(poisoningWarnDto.getId());
        tbCdcmrPoisoningWarnMapper.updateStatusByPrimaryKey(tbCdcmrPoisoningWarn);
    }

    @Override
    public List<PoisoningWarnDto> getPoisoningWarnAllList(String poisoningCode) {
        List<PoisoningWarnDto> warnList = tbCdcmrPoisoningWarnMapper.getAllList(poisoningCode);
        List<TbCdcmrPoisoningWarnRule> ruleList = tbCdcmrPoisoningWarnRuleMapper.getAllRuleList();
        warnList.forEach(poisoningWarnDto -> poisoningWarnDto.setRuleList(ruleList.stream().filter(tbCdcmrPoisoningWarnRule -> tbCdcmrPoisoningWarnRule.getWarnId().equals(poisoningWarnDto.getId())).collect(Collectors.toList())));
        return warnList;
    }

    @Override
    public void exportPoisoningWarnRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.POISONING_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<PoisoningWarnRuleExportDataDto> dataList = tbCdcmrPoisoningWarnRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 修改状态名称
        dataList.forEach(this::formatWarnRuleExportDataDto);
        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.POISONING_WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<CascadeVO> getPoisoningType() {
        List<TbCdcmrPoisoningType> result = tbCdcmrPoisoningTypeMapper.getList();
        List<CascadeVO> typeList = new ArrayList<>();
        result.forEach(e->{
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setLabel(e.getPoisoningTypeName());
            cascadeVO.setValue(e.getPoisoningTypeCode());
            typeList.add(cascadeVO);
        });
        return typeList;
    }

    @Override
    public List<CascadeVO> getPoisonNameList() {
//        List<TbCdcmrPoisoning> poisonInfoList = tbCdcmrPoisoningMapper.findAll();
        List<TbCdcmrPoisoning> poisonInfoList = tbCdcmrPoisoningMapper.findAllParent();
        return buildTree(poisonInfoList);
    }

    @Override
    public List<CascadeVO> getPoisonNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_POISON);

//        List<CascadeVO> poisons = getPoisonNameList();
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getPoisonDataAuthByLoginUserId(loginUserId);
//        Set<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toSet());
//        return CascadeVO.filterCascadeVOByCode(poisons, collect);
    }

    public void addPoisoningWarnByPoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId) {
        TbCdcmrPoisoningWarn tbCdcmrPoisoningWarn = new TbCdcmrPoisoningWarn();
        tbCdcmrPoisoningWarn.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_poisoning_warn")));
        tbCdcmrPoisoningWarn.setStatus(0);
        tbCdcmrPoisoningWarn.setPoisoningCode(tbCdcmrPoisoning.getPoisoningCode());
        tbCdcmrPoisoningWarn.setPoisoningName(tbCdcmrPoisoning.getPoisoningName());
        tbCdcmrPoisoningWarn.setPoisoningTypeCode(tbCdcmrPoisoning.getPoisoningTypeCode());
        tbCdcmrPoisoningWarn.setPoisoningTypeName(tbCdcmrPoisoning.getPoisoningTypeName());
        tbCdcmrPoisoningWarn.setRemark(null);
        tbCdcmrPoisoningWarn.setUpdater(loginUserId);
        tbCdcmrPoisoningWarn.setIsDeleted(0);
        tbCdcmrPoisoningWarn.setUpdateTime(new Date());
        tbCdcmrPoisoningWarn.setMaxLifeCycle(null);
        tbCdcmrPoisoningWarnMapper.insert(tbCdcmrPoisoningWarn);
    }

    public void savePoisoningWarnByPoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId) {
        TbCdcmrPoisoningWarn tbCdcmrPoisoningWarn = tbCdcmrPoisoningWarnMapper.getByPoisoningCode(tbCdcmrPoisoning.getPoisoningCode());
        if (tbCdcmrPoisoningWarn == null){
            addPoisoningWarnByPoisoning(tbCdcmrPoisoning, loginUserId);
        }else {
            tbCdcmrPoisoningWarn.setPoisoningCode(tbCdcmrPoisoning.getPoisoningCode());
            tbCdcmrPoisoningWarn.setPoisoningName(tbCdcmrPoisoning.getPoisoningName());
            tbCdcmrPoisoningWarn.setPoisoningTypeCode(tbCdcmrPoisoning.getPoisoningTypeCode());
            tbCdcmrPoisoningWarn.setPoisoningTypeName(tbCdcmrPoisoning.getPoisoningTypeName());
            tbCdcmrPoisoningWarn.setUpdater(loginUserId);
            tbCdcmrPoisoningWarn.setUpdateTime(new Date());
            tbCdcmrPoisoningWarnMapper.updateByPrimaryKeySelective(tbCdcmrPoisoningWarn);
        }
    }


    private void setMaxLifeCycle(PoisoningWarnRuleExportDataDto poisoningWarnRuleExportDataDto) {
        if (poisoningWarnRuleExportDataDto.getMaxLifeCycle() != null) {
            poisoningWarnRuleExportDataDto.setMaxLifeCycle(poisoningWarnRuleExportDataDto.getMaxLifeCycle() + "天");
        } else {
            poisoningWarnRuleExportDataDto.setMaxLifeCycle("/");
        }
    }

    private void setCount(PoisoningWarnRuleExportDataDto poisoningWarnRuleExportDataDto) {
        poisoningWarnRuleExportDataDto.setCount(convertValue("≥%s", poisoningWarnRuleExportDataDto.getMedicalCount()));
    }

    private  void setType(PoisoningWarnRuleExportDataDto poisoningWarnRuleExportDataDto) {
        String medicalOrDeath = poisoningWarnRuleExportDataDto.getType();
        poisoningWarnRuleExportDataDto.setType(Constants.WarnRule.getGenerateType(medicalOrDeath));
    }
    private void setMonitorObject(PoisoningWarnRuleExportDataDto poisoningWarnRuleExportDataDto) {
        if (PoisoningMonitorObjectEnum.HOSPITAL.getCode().equals(poisoningWarnRuleExportDataDto.getMonitorObject())) {
            poisoningWarnRuleExportDataDto.setMonitorObject(PoisoningMonitorObjectEnum.HOSPITAL.getName());
        } else if (PoisoningMonitorObjectEnum.PRIMARY.getCode().equals(poisoningWarnRuleExportDataDto.getMonitorObject())) {
            poisoningWarnRuleExportDataDto.setMonitorObject(PoisoningMonitorObjectEnum.PRIMARY.getName());
        } else if (PoisoningMonitorObjectEnum.ORG.getCode().equals(poisoningWarnRuleExportDataDto.getMonitorObject())) {
            poisoningWarnRuleExportDataDto.setMonitorObject(PoisoningMonitorObjectEnum.ORG.getName());
        } else if (PoisoningMonitorObjectEnum.STREET.getCode().equals(poisoningWarnRuleExportDataDto.getMonitorObject())) {
            poisoningWarnRuleExportDataDto.setMonitorObject(PoisoningMonitorObjectEnum.STREET.getName());
        } else {
            poisoningWarnRuleExportDataDto.setMonitorObject("/");
        }
    }


    private void setTimeScope(PoisoningWarnRuleExportDataDto poisoningWarnRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(poisoningWarnRuleExportDataDto.getTimeRange())) {
            if ("1".equals(poisoningWarnRuleExportDataDto.getTimeRangeUnit())) {
                poisoningWarnRuleExportDataDto.setTimeRange("≤" + poisoningWarnRuleExportDataDto.getTimeRange() + "天");
            } else if ("2".equals(poisoningWarnRuleExportDataDto.getTimeRangeUnit())) {
                poisoningWarnRuleExportDataDto.setTimeRange("≤" + poisoningWarnRuleExportDataDto.getTimeRange() + "周");
            } else if ("3".equals(poisoningWarnRuleExportDataDto.getTimeRangeUnit())) {
                poisoningWarnRuleExportDataDto.setTimeRange("≤" + poisoningWarnRuleExportDataDto.getTimeRange() + "月");
            }
        } else {
            poisoningWarnRuleExportDataDto.setTimeRange("/");
        }
    }

    /**
     * 格式数据
     */
    private String convertValue(String format, Object value){
        if (value == null || StringUtils.isEmpty((String)value)){
            return "/";
        }
        return String.format(format, value);
    }

    private void updateRules(String warnId, List<TbCdcmrPoisoningWarnRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_poisoning_warn_rule")));
            }
            rule.setWarnId(warnId);
            rule.setStatus(1);
            rule.setIsDeleted(0);
            rule.setUpdater(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrPoisoningWarnRule::getId).collect(Collectors.toList());
        tbCdcmrPoisoningWarnRuleMapper.deleteOtherByIds(idList, warnId);
        tbCdcmrPoisoningWarnRuleMapper.upsertRules(ruleList);
    }

    /**
     * 格式导出的数据
     */
    private void formatWarnRuleExportDataDto(PoisoningWarnRuleExportDataDto dto){
        setMonitorObject(dto);
        setMaxLifeCycle(dto);
        setTimeScope(dto);
        setCount(dto);
        setType(dto);
    }


    /**
     * 构造树
     */
    public List<CascadeVO> buildTree(List<TbCdcmrPoisoning> poisonInfoList){
        Map<String,TbCdcmrPoisoning> poisonInfoMap = poisonInfoList.stream().collect(Collectors.toMap(TbCdcmrPoisoning::getPoisoningCode, t -> t));
        Map<String, List<TbCdcmrPoisoning>> poisonParentMap = poisonInfoList.stream().filter(p -> !p.getPoisoningCode().equals(p.getParentCode())).collect(Collectors.groupingBy(TbCdcmrPoisoning::getParentCode));
        List<TbCdcmrPoisoning> roots = new ArrayList<>();
        for (TbCdcmrPoisoning tbCdcewPoisonInfo : poisonInfoList){
            if (org.apache.commons.lang.StringUtils.isEmpty(tbCdcewPoisonInfo.getParentCode())
                    || tbCdcewPoisonInfo.getPoisoningCode().equals(tbCdcewPoisonInfo.getParentCode())
                    || !poisonInfoMap.containsKey(tbCdcewPoisonInfo.getParentCode())){
                roots.add(tbCdcewPoisonInfo);
            }
        }
        return getChildren(roots, poisonParentMap);
    }

    /**
     * 获取子树
     */
    private List<CascadeVO> getChildren(List<TbCdcmrPoisoning> poisonInfoList,
                                        Map<String, List<TbCdcmrPoisoning>> poisonParentMap){
        List<CascadeVO> cascadeVOS = new ArrayList<>();
        for (TbCdcmrPoisoning poisonInfo : poisonInfoList){
            CascadeVO cascadeVO = CascadeVO.fromPoison(poisonInfo);
            if (poisonParentMap.get(poisonInfo.getPoisoningCode()) != null){
                cascadeVO.setChildren(getChildren(poisonParentMap.get(poisonInfo.getPoisoningCode()),  poisonParentMap));
            }
            cascadeVOS.add(cascadeVO);
        }
        return cascadeVOS;

    }

}
