package com.iflytek.cdc.admin.service.province.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.dto.CommonParamPageDTO;
import com.iflytek.cdc.admin.dto.CommonParamListDTO;
import com.iflytek.cdc.admin.dto.CommonParamSimpleDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrCommonParam;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrCommonParamMapper;
import com.iflytek.cdc.admin.service.province.TbCdcmrCommonParamService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TbCdcmrCommonParamServiceImpl extends ServiceImpl<TbCdcmrCommonParamMapper, TbCdcmrCommonParam> implements TbCdcmrCommonParamService {

    @Resource
    BatchCommonService batchCommonService;

    @Override
    public String insert(TbCdcmrCommonParam param, String loginUserName) {
        // 检查paramKey是否已存在
        LambdaQueryWrapper<TbCdcmrCommonParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbCdcmrCommonParam::getParamKey, param.getParamKey())
               .eq(TbCdcmrCommonParam::getDeleteFlag, "0");

        if (this.count(wrapper) > 0) {
            throw new MedicalBusinessException("配置KEY已存在");
        }

        param.setId(batchCommonService.uuid(this.getClass()));
        param.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        param.setUpdater(loginUserName);

        // 保存数据
        this.save(param);
        return param.getId();
    }

    @Override
    public Boolean deleteById(String id) {
        LambdaUpdateWrapper<TbCdcmrCommonParam> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TbCdcmrCommonParam::getId, id)
                     .set(TbCdcmrCommonParam::getDeleteFlag, DeleteFlagEnum.YES.getCode())
                     .set(TbCdcmrCommonParam::getUpdateTime, new Date());
        return this.update(updateWrapper);
    }

    @Override
    public Boolean updateBy(TbCdcmrCommonParam param, String loginUserName) {
        param.setUpdateTime(new Date());
        param.setUpdater(loginUserName);
        return this.updateById(param);
    }

    @Override
    public List<TbCdcmrCommonParam> getCommonParams(CommonParamPageDTO dto) {
        LambdaQueryWrapper<TbCdcmrCommonParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbCdcmrCommonParam::getDeleteFlag, DeleteFlagEnum.NO.getCode());

        if (dto.getParamType() != null) {
            wrapper.eq(TbCdcmrCommonParam::getParamType, dto.getParamType());
        }
        if (dto.getParamKey() != null) {
            wrapper.eq(TbCdcmrCommonParam::getParamKey, dto.getParamKey());
        }

        // 添加排序，按创建时间降序排列
        wrapper.orderByAsc(TbCdcmrCommonParam::getCreateTime);

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return this.list(wrapper);
    }


    @Override
    public TbCdcmrCommonParam getById(String id) {
        LambdaQueryWrapper<TbCdcmrCommonParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbCdcmrCommonParam::getId, id)
               .eq(TbCdcmrCommonParam::getDeleteFlag, DeleteFlagEnum.NO.getCode());
        return this.getOne(wrapper);
    }

    @Override
    public List<CommonParamSimpleDTO> getCommonParamSimpleList(CommonParamListDTO dto) {
        LambdaQueryWrapper<TbCdcmrCommonParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbCdcmrCommonParam::getDeleteFlag, DeleteFlagEnum.NO.getCode());

        if (dto.getParamClass() != null && !dto.getParamClass().trim().isEmpty()) {
            // 支持多个值逗号拼接，分割后使用 in 查询
            String[] paramClasses = dto.getParamClass().split(",");
            wrapper.in(TbCdcmrCommonParam::getParamClass, Arrays.asList(paramClasses));
        }
        if (dto.getParamType() != null && !dto.getParamType().trim().isEmpty()) {
            // 支持多个值逗号拼接，分割后使用 in 查询
            String[] paramTypes = dto.getParamType().split(",");
            wrapper.in(TbCdcmrCommonParam::getParamType, Arrays.asList(paramTypes));
        }

        // 添加排序，按创建时间降序排列
        wrapper.orderByAsc(TbCdcmrCommonParam::getCreateTime);

        List<TbCdcmrCommonParam> list = this.list(wrapper);
        
        return list.stream().map(param -> {
            CommonParamSimpleDTO simpleDTO = new CommonParamSimpleDTO();
            BeanUtils.copyProperties(param, simpleDTO);
            return simpleDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public TbCdcmrCommonParam getByParamKey(String paramKey) {
        LambdaQueryWrapper<TbCdcmrCommonParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbCdcmrCommonParam::getParamKey, paramKey)
               .eq(TbCdcmrCommonParam::getDeleteFlag, DeleteFlagEnum.NO.getCode());
        return this.getOne(wrapper);
    }
}