package com.iflytek.cdc.admin.capacity.api.algorithm.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InfluenceFactorResponse implements Serializable {

    private String code;
    private String message;
    private String version;
    private Result resut;

    @Data
    public static class Result{

        public InfluenceFactor selected_features;
        public BestScore best_score;

        @Data
        public static class BestScore{

            @JsonProperty("roc_auc")
            private double roc_auc;
        }

        // 定义InfluenceFactorResponse类
        @Data
       public static class InfluenceFactor {
            @JsonProperty("发病地区")
            public double 发病地区;
            @JsonProperty("湿度")
            public double 湿度;
            @JsonProperty("政治经济")
            public double 政治经济;
            @JsonProperty("基础疾病")
            public double 基础疾病;
            @JsonProperty("生活风俗")
            public double 生活风俗;
            @JsonProperty("传播途径")
            public double 传播途径;
            @JsonProperty("年龄")
            private double 年龄;
            @JsonProperty("性别")
            private double 性别;
            @JsonProperty("职业")
            private double 职业;
            @JsonProperty("温度")
            private double 温度;
            @JsonProperty("疫苗接种")
            private double 疫苗接种;
        }
    }
}
