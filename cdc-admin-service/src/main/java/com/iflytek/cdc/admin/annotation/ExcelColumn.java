package com.iflytek.cdc.admin.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * excel列名注解
 * <AUTHOR>
 * @date 2020/11/3 10:43
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelColumn {

    String name() default "";

    int column() default 0;

    boolean blank() default true;

    boolean unique() default false;

    int maxLength() default 0;

    int minLength() default 0;

    int limitLength() default 0;

    String pattern() default "*";

    boolean required() default false;

    String[] strip() default {};
}
