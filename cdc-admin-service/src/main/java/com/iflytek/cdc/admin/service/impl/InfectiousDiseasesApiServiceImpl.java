package com.iflytek.cdc.admin.service.impl;

import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.InfectiousDiseasesNameDto;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.mapper.InfectiousDiseasesMapper;
import com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.InfectiousDiseasesApiService;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InfectiousDiseasesApiServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/1 17:07
 * @Version 1.0
 */
@Service("infecDisApiService")
@Slf4j
public class InfectiousDiseasesApiServiceImpl implements InfectiousDiseasesApiService {

    @Resource
    public InfectiousDiseasesMapper infectiousDiseasesMapper;

    @Resource
    private SyncMdmDataService infecDisService;

    @Resource
    private DataAuthService dataAuthService;

    @Override
    public List<InfecDisTreeInfo> queryInfecDisTree() {
        SearchInfecInfoDTO searchInfecInfoDTO = new SearchInfecInfoDTO();
        List<InfectiousDiseases> infectiousDiseases = infectiousDiseasesMapper.queryInfecInfo(searchInfecInfoDTO);

        List<InfectiousDiseasesNameDto> infecdistypes = infectiousDiseases.stream()
                .map(InfectiousDiseasesNameDto::getTypeDto)
                .filter(item -> !StringUtils.isEmpty(item.getCodedValue()))
                .distinct()
                .collect(Collectors.toList());

        List<InfectiousDiseasesNameDto> infecdisclfys = infectiousDiseases.stream()
                .map(InfectiousDiseasesNameDto::getClassifyDto)
                .filter(item -> !StringUtils.isEmpty(item.getCodedValue()))
                .distinct()
                .collect(Collectors.toList());

        List<InfectiousDiseasesNameDto> infecdiss = infectiousDiseases.stream()
                .map(InfectiousDiseasesNameDto::getDiseaseDto)
                .filter(item -> !StringUtils.isEmpty(item.getCodedValue()))
                .distinct()
                .collect(Collectors.toList());


        Map<String, String> infecdistypeMap = new HashMap<String, String>();
        for (InfectiousDiseasesNameDto infecdistype : infecdistypes) {
            infecdistypeMap.put(infecdistype.getCodedValue(), infecdistype.getDescription());
        }
        Map<String, String> infecdisclfyMap = new HashMap<String, String>();
        for (InfectiousDiseasesNameDto infecdisclfy : infecdisclfys) {
            infecdisclfyMap.put(infecdisclfy.getCodedValue(), infecdisclfy.getDescription());
        }
        Map<String, String> infecdisMap = new HashMap<String, String>();
        for (InfectiousDiseasesNameDto infecdis : infecdiss) {
            infecdisMap.put(infecdis.getCodedValue(), infecdis.getDescription());
        }


        Map<String, Set<String>> mapTypeCode = new HashMap<String, Set<String>>();
        Map<String, Set<String>> mapTypeCodeClfy = new HashMap<String, Set<String>>();
        Map<String, Set<String>> mapCode = new HashMap<String, Set<String>>();

        handlerDisTreeMap(mapTypeCode, mapTypeCodeClfy, mapCode);


        List<InfecDisTreeInfo> infecDisTreeInfos = new ArrayList<InfecDisTreeInfo>();
        for (Map.Entry<String, Set<String>> m : mapTypeCode.entrySet()) {
            if (infecdistypeMap.get(m.getKey()) != null) {
                InfecDisTreeInfo infecDisTreeInfoFir = new InfecDisTreeInfo();
                infecDisTreeInfoFir.setCode(m.getKey());
                infecDisTreeInfoFir.setValue(infecdistypeMap.get(m.getKey()));
                Set<String> codes = m.getValue();

                List<InfecDisTreeInfo> infecDisTreeInfoSecs = new ArrayList<com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo>();
                for (String codeParaSec : codes) {
                    if (infecdisclfyMap.get(codeParaSec) != null) {
                        InfecDisTreeInfo infecDisTreeInfoSec = new InfecDisTreeInfo();
                        infecDisTreeInfoSec.setCode(codeParaSec);
                        infecDisTreeInfoSec.setValue(infecdisclfyMap.get(infecDisTreeInfoSec.getCode()) == null ? infecdisMap.get(infecDisTreeInfoSec.getCode()) : infecdisclfyMap.get(infecDisTreeInfoSec.getCode()));
                        Set<String> codeThirds = mapTypeCodeClfy.get(m.getKey() + "_" + codeParaSec);
                        if (!CollectionUtils.isEmpty(codeThirds)) {
                            List<InfecDisTreeInfo> infecDisTreeInfoThirds = new ArrayList<com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo>();
                            for (String codeParaThird : codeThirds) {
                                InfecDisTreeInfo infecDisTreeInfoThird = new InfecDisTreeInfo();
                                infecDisTreeInfoThird.setCode(codeParaThird);
                                infecDisTreeInfoThird.setValue(infecdisMap.get(infecDisTreeInfoThird.getCode()));
                                infecDisTreeInfoThirds.add(infecDisTreeInfoThird);
                            }
                            infecDisTreeInfoSec.setChildren(infecDisTreeInfoThirds);
                        }
                        infecDisTreeInfoSecs.add(infecDisTreeInfoSec);
                    }
                }

                Set<String> code3s = mapCode.get(m.getKey());
                if (!CollectionUtils.isEmpty(code3s)) {
                    for (String codeParaSec : code3s) {
                        if (!codes.contains(codeParaSec)) {
                            InfecDisTreeInfo infecDisTreeInfoSec = new InfecDisTreeInfo();
                            infecDisTreeInfoSec.setCode(codeParaSec);
                            infecDisTreeInfoSec.setValue(infecdisMap.get(infecDisTreeInfoSec.getCode()));
                            infecDisTreeInfoSecs.add(infecDisTreeInfoSec);
                        }
                    }
                }
                infecDisTreeInfoFir.setChildren(infecDisTreeInfoSecs);
                infecDisTreeInfos.add(infecDisTreeInfoFir);
            }
        }

        MapDifference<String, Set<String>> difference = Maps.difference(mapTypeCode, mapCode);
        Map<String, Set<String>> entriesOnlyOnRight = difference.entriesOnlyOnRight();
        for (Map.Entry<String, Set<String>> m : entriesOnlyOnRight.entrySet()) {
            if (infecdistypeMap.get(m.getKey()) != null) {
                InfecDisTreeInfo infecDisTreeInfoFir = new InfecDisTreeInfo();
                infecDisTreeInfoFir.setCode(m.getKey());
                infecDisTreeInfoFir.setValue(infecdistypeMap.get(m.getKey()));
                Set<String> codes = m.getValue();

                List<InfecDisTreeInfo> infecDisTreeInfoSecs = new ArrayList<com.iflytek.cdc.admin.sdk.pojo.InfecDisTreeInfo>();
                for (String codeParaSec : codes) {
                    if (infecdisMap.get(codeParaSec) != null) {
                        InfecDisTreeInfo infecDisTreeInfoSec = new InfecDisTreeInfo();
                        infecDisTreeInfoSec.setCode(codeParaSec);
                        infecDisTreeInfoSec.setValue(infecdisMap.get(codeParaSec));
                        infecDisTreeInfoSecs.add(infecDisTreeInfoSec);
                    }
                }
                infecDisTreeInfoFir.setChildren(infecDisTreeInfoSecs);
                infecDisTreeInfos.add(infecDisTreeInfoFir);
            }
        }
        infecDisTreeInfos.sort(Comparator.comparing(InfecDisTreeInfo::getCode));
        return infecDisTreeInfos;
    }

    @Override
    public List<CascadeVO> getInfectedNameList() {
        return infecDisTreeToVo(queryInfecDisTree());
    }

    @Override
    public List<CascadeVO> getInfectedNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId, Constants.DATA_AUTH_INFECTIOUS_DISEASE);

//        List<CascadeVO> resultList = new ArrayList<>();
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getInfectedDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        List<CascadeVO> result = getInfectedNameList();
//        result.forEach(restVO -> {
//            List<CascadeVO> children = restVO.getChildren();
//            children = children.stream().filter(infectedCascadeVO -> collect.contains(infectedCascadeVO.getValue())).collect(Collectors.toList());
//            restVO.setChildren(children);
//        });
//        result.forEach(restVO -> {
//            if (!(restVO.getChildren() == null || restVO.getChildren().isEmpty())) {
//                resultList.add(restVO);
//            }
//        });
//        return resultList;
    }

    @Override
    public List<CascadeVO> getInfectedNameListWithoutChildren() {
        List<CascadeVO> infectedCascadeVOList = getInfectedNameList();
        List<CascadeVO> cascadeVOList = new ArrayList<>();
        for (CascadeVO infectedCascadeVO : infectedCascadeVOList) {
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setLabel(infectedCascadeVO.getLabel());
            cascadeVO.setValue(infectedCascadeVO.getValue());

            List<CascadeVO> childList = new ArrayList<>();
            List<CascadeVO> children = infectedCascadeVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                children.forEach(subInfectedCascadeVO -> {
                    CascadeVO subCascadeVO = new CascadeVO();
                    subCascadeVO.setLabel(subInfectedCascadeVO.getLabel());
                    subCascadeVO.setValue(subInfectedCascadeVO.getValue());
                    childList.add(subCascadeVO);
                });
            }
            cascadeVO.setChildren(childList);
            cascadeVOList.add(cascadeVO);
        }
        return cascadeVOList;    }

    private List<CascadeVO> infecDisTreeToVo(List<InfecDisTreeInfo> infecDisTreeInfos) {
        List<CascadeVO> cascadeVOS = new ArrayList<>();
        infecDisTreeInfos.forEach(e -> {
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setValue(e.getCode());
            cascadeVO.setLabel(e.getValue());
            if (e.getChildren() != null) {
                cascadeVO.setChildren(infecDisTreeToVo(e.getChildren()));
            }
            cascadeVOS.add(cascadeVO);
        });
        return cascadeVOS;
    }


    @NotNull
    private void handlerDisTreeMap(Map<String, Set<String>> mapTypeCode, Map<String, Set<String>> mapTypeCodeClfy, Map<String, Set<String>> mapCode) {
        SearchInfecInfoDTO searchInfecInfoDTO = new SearchInfecInfoDTO();
        List<InfectiousDiseases> infectiousDiseases = infectiousDiseasesMapper.queryInfecInfo(searchInfecInfoDTO);

        for (InfectiousDiseases infectiousDisease : infectiousDiseases) {
            if (!StringUtils.isEmpty(infectiousDisease.getDiseasesType())) {
                if (!StringUtils.isEmpty(infectiousDisease.getDiseasesClassify())) {

                    if (null == mapTypeCode.get(infectiousDisease.getDiseasesType())) {
                        Set<String> code = new HashSet<String>();
                        code.add(infectiousDisease.getDiseasesClassify());
                        mapTypeCode.put(infectiousDisease.getDiseasesType(), code);
                    } else {
                        Set<String> code = mapTypeCode.get(infectiousDisease.getDiseasesType());
                        code.add(infectiousDisease.getDiseasesClassify());
                        mapTypeCode.put(infectiousDisease.getDiseasesType(), code);
                    }

                    if (null == mapTypeCodeClfy.get(infectiousDisease.getDiseasesType() + "_" + infectiousDisease.getDiseasesClassify())) {
                        Set<String> code = new HashSet<String>();
                        code.add(infectiousDisease.getDiseasesCode());
                        mapTypeCodeClfy.put(infectiousDisease.getDiseasesType() + "_" + infectiousDisease.getDiseasesClassify(), code);
                    } else {
                        Set<String> code = mapTypeCodeClfy.get(infectiousDisease.getDiseasesType() + "_" + infectiousDisease.getDiseasesClassify());
                        code.add(infectiousDisease.getDiseasesCode());
                        mapTypeCodeClfy.put(infectiousDisease.getDiseasesType() + "_" + infectiousDisease.getDiseasesClassify(), code);
                    }

                } else {
                    if (null == mapCode.get(infectiousDisease.getDiseasesType())) {
                        Set<String> code = new HashSet<String>();
                        code.add(infectiousDisease.getDiseasesCode());
                        mapCode.put(infectiousDisease.getDiseasesType(), code);
                    } else {
                        Set<String> code = mapCode.get(infectiousDisease.getDiseasesType());
                        code.add(infectiousDisease.getDiseasesCode());
                        mapCode.put(infectiousDisease.getDiseasesType(), code);
                    }
                }
            }
        }
    }

}
