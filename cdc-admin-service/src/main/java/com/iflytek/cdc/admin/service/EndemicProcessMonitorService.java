package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicMonitorConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.model.mr.dto.EndemicMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EndemicDiseaseInfoVO;

import java.util.List;

public interface EndemicProcessMonitorService extends IService<TbCdcmrEndemicMonitorConfig>{


    List<EndemicDiseaseInfoVO> getEndemicTreeInfo();

    String editSubEndemicInfo(TbCdcmrEndemicDiseaseInfo endemicDiseaseInfo);

    void deleteSubEndemicInfo(String id);

    List<TbCdcmrEndemicMonitorConfig> getEndemicInfoConfigs(EndemicMonitorConfigQueryDTO queryDTO);

    void editEndemicProcessDefinition(String diseaseInfoId, List<TbCdcmrEndemicMonitorConfig> endemicMonitorConfigs);

    List<DiagnoseListVO> getDiagnoseCodeList();

    List<String> getEndemicDiseaseCodeByName(String diseaseName);
}
