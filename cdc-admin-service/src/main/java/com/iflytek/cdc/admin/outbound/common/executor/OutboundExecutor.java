package com.iflytek.cdc.admin.outbound.common.executor;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.outbound.constant.OutboundConfig;
import com.iflytek.cdc.admin.outbound.constant.OutboundConstant;
import com.iflytek.cdc.admin.outbound.util.OutBoundRestTemplateDTO;
import com.iflytek.cdc.admin.outbound.util.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 外呼执行器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class OutboundExecutor {

    @Resource
    private OutBoundRestTemplateDTO outBoundRestTemplateDTO;

    @Resource
    private OutboundConfig outboundConfig;

    /**
     * 返回泛型结构
     * 
     * @param url
     * @param t
     * @param type
     * @param <T>
     * @param <X>
     * @return
     */
    public <T, X> Response<X> invokeOutbound(String url, T t, Type type) {
        try {
            String responseBody = execOutBound(url, t);
            return new Gson().fromJson(responseBody, type);
        } catch (Exception e) {
            log.error("执行外呼错误！, {}", e);
        }
        return Response.error("执行外呼错误!");
    }

    /**
     * 返回JSON结构
     * 
     * @return
     */
    public <T> Response<String> invokeOutboundJson(String url, T t) {
        try {
            return Response.success(execOutBound(url, t));
        } catch (Exception e) {
            log.error("执行外呼错误！, {}", e);
        }
        return Response.error("执行外呼错误!");
    }

    private <T> String execOutBound(String url, T t) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new LinkedHashMap<>(3);
        param.put(OutboundConstant.APP_ID, outboundConfig.getAppId());
        param.put(OutboundConstant.TIMESTAMP, timestamp);
        param.put(OutboundConstant.SIGN, getSign(outboundConfig, timestamp));
        String finalUrl = expandUrl(url, param);

        HttpHeaders headers = getHeaders();
        HttpEntity<String> formEntity = new HttpEntity<>(JSON.toJSONString(t), headers);
        log.info("执行外呼入参：{}", JSON.toJSONString(t));
        String responseBody = outBoundRestTemplateDTO.getRestTemplate()
                .postForEntity(finalUrl, formEntity, String.class).getBody();

        log.info("执行外呼结果：{}", responseBody);
        return responseBody;
    }

    /**
     * 为url补充参数
     *
     * @param url    原始地址
     * @param params 拼接参数
     * @return 最终地址
     */
    private String expandUrl(String url, Map<String, String> params) {
        StringBuilder sb = new StringBuilder(url);
        sb.append("?");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    /**
     * 获取请求头
     * 
     * @return
     */
    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        return headers;
    }

    /**
     * 获取sign
     * 
     * @param outboundConfig
     * @param timestamp
     * @return
     */
    public String getSign(OutboundConfig outboundConfig, String timestamp) {
        StringBuilder sb = new StringBuilder();
        sb.append(outboundConfig.getAppId()).append(timestamp).append(outboundConfig.getSecretKey());
        return DigestUtils.md5Hex(sb.toString().getBytes());
    }
}
