package com.iflytek.cdc.admin.mqmessage.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.admin.constant.DictRelationConstants;
import com.iflytek.cdc.admin.dto.MdmDictMessageDTO;
import com.iflytek.cdc.admin.dto.MdmDictValueMessageDTO;
import com.iflytek.cdc.admin.dto.MdmMessageDTO;
import com.iflytek.cdc.admin.service.DictRelationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.cloud.stream.annotation.EnableBinding;
//import org.springframework.cloud.stream.annotation.StreamListener;
//import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 14:49
 **/
@Slf4j
@Component
@AllArgsConstructor
//@EnableBinding({MdmMessageConsumerSink.class})
public class MdmMessageConsumer {

    @Autowired
    DictRelationService  dictRelationService;

//    @StreamListener(MdmMessageConsumerSink.MDM_DICT_MESSAGE_INPUT)
//    public void mdmMessageConsumer(Message<String> message){
//        try {
//            log.info("【消费】mdm系统字典值域变动信息message：{}", message);
//            MdmMessageDTO   mdmMessage;
//            MdmDictMessageDTO  mdmDictMessage = new MdmDictMessageDTO();
//            if(ObjectUtil.isNotNull(message)){
//              mdmMessage= JSONUtil.toBean(message.getPayload(), MdmMessageDTO.class);
//              mdmDictMessage=JSONUtil.toBean(mdmMessage.getMsg(),MdmDictMessageDTO.class);
//            }
//            //获取字典值域信息
//            List<MdmDictValueMessageDTO>  values=mdmDictMessage.getCodedValues();
//            syncDictRelation(mdmDictMessage, values);
//        }catch (Exception e){
//            log.error("mdm系统字典值域变动信息消费异常：", e);
//        }
//    }

    /**
     * 同步字典关联的数据
     * @param mdmDictMessage values
     * @return
     **/
    private void syncDictRelation(MdmDictMessageDTO mdmDictMessage, List<MdmDictValueMessageDTO> values) {
        //如果不为空
        if(CollUtil.isNotEmpty(values)){
            for (MdmDictValueMessageDTO dict: values) {
                if(dict.getDeleted().equals(DictRelationConstants.YES_DELETE)){
                    dictRelationService.deleteRelationByMdmMessage(mdmDictMessage.getCode(),dict.getCodeValue());
                }else{
                    //如果存在，则修改名称
                   if(dictRelationService.judgeIsUpdateName(mdmDictMessage.getCode(), dict.getCodeValue())){
                       dictRelationService.updateRelationNameByMdmMessage(mdmDictMessage.getCode(),dict.getCodeValue(),dict.getDescription());
                   }
                }
            }
        }
    }

}
