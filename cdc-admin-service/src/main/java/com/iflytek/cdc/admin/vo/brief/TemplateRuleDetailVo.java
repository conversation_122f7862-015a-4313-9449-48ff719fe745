package com.iflytek.cdc.admin.vo.brief;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 模板规则详细信息vo
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
public class TemplateRuleDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
    private String id;
    /**
     * 模板id
     */
    private String templateId;
    /**
     * 规则id
     */
    private String ruleId;
    /**
     * 统计周期
     */
    private String statisticsCycle;
    /**
     * 内容标题
     */
    private String contentTitle;
    /**
     * 内容标题
     */
    private String briefInfo;
    /**
     * 统计具体时间
     */
    private String statisticsTime;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
    /**
     * 内容
     */
    private String content;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 区名称
     */
    private String districtName;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 分析对象
     */
    private String analysisObject;
    /**
     * 附件标题
     */
    private String attachmentTitle;
    private String attachmentFlag;

    /**
     * 报告类型
     */
    private String briefReportType;
}
