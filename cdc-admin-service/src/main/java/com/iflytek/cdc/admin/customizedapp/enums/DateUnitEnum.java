package com.iflytek.cdc.admin.customizedapp.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DateUnitEnum {
    DAY("day", "日"),
    WEEK("week", "周"),
    MEADOW("meadow", "旬"),
    MONTH("month", "月"),
    QUARTER("quarter", "季度"),
    YEAR("year", "年")

    ;
    private String code;
    private String desc;

    DateUnitEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DateUnitEnum getByCode(String code){
         return Arrays.stream(DateUnitEnum.values()).filter(d -> d.getCode().equals(code)).findFirst().orElse(null);
    }

    /**
     * 获取任务的日期
     */
    public static Date getDate(Date date,
                               Calendar calendar,
                               String dateUnit,
                               Integer num){
        calendar.setTime(date);
        DateUnitEnum dateUnitEnum = DateUnitEnum.getByCode(dateUnit);
        switch (dateUnitEnum){
            case DAY:
                calendar.add(Calendar.DAY_OF_YEAR, num);
                break;
            case WEEK:
                calendar.add(Calendar.DAY_OF_YEAR, 7 * num);
                break;
            case MEADOW:
                calendar.add(Calendar.DAY_OF_YEAR, 10 * num);
                break;
            case MONTH:
                calendar.add(Calendar.MONTH, num);
                break;
            case YEAR:
                calendar.add(Calendar.YEAR, num);
                break;
        }
        return calendar.getTime();
    }
}
