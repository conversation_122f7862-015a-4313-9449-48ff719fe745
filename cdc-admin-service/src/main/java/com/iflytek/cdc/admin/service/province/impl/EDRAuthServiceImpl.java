package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.apiservice.CdcDataServiceApi;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFilePermissionRecord;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessRecordAuthority;
import com.iflytek.cdc.admin.enums.AuthClassEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.EDRAuthEnums;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessFilePermissionRecordMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrIllnessRecordAuthorityMapper;
import com.iflytek.cdc.admin.model.mr.dto.AuthPersonInfo;
import com.iflytek.cdc.admin.model.mr.dto.EDRAuthQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;
import com.iflytek.cdc.admin.service.province.EDRAuthService;
import com.iflytek.cdc.admin.service.province.IllnessFileApprovalAuthService;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class EDRAuthServiceImpl extends ServiceImpl<TbCdcmrIllnessRecordAuthorityMapper, TbCdcmrIllnessRecordAuthority> implements EDRAuthService {

    private static final String TABLE_NAME = "tb_cdcmr_illness_record_authority";
    private static final String PERMISSION_RECORD_TABLE_NAME = "tb_cdcmr_illness_file_permission_record";

    @Resource
    private CommonUtils commonUtils;

    @Resource
    private TbCdcmrIllnessRecordAuthorityMapper authorityMapper;

    @Resource
    private BatchUidService batchUidService;

    @Autowired
    private UapServiceApi uapServiceApi;

    @Autowired
    private TbCdcmrIllnessFilePermissionRecordMapper illnessFilePermissionRecordMapper;

    @Autowired
    private CdcDataServiceApi cdcDataServiceApi;

    @Autowired
    private IllnessFileApprovalAuthService illnessFileApprovalAuthService;

    @Override
    public PageInfo<EDRAuthInfoVO> listAuthBy(EDRAuthQueryDTO dto) {

        //根据用户设置省市区的查询参数
        commonUtils.setAreaQueryDTO(dto);

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<EDRAuthInfoVO> edrAuthInfoVOList = authorityMapper.listAuthBy(dto);

        return new PageInfo<>(edrAuthInfoVOList);
    }

    @Override
    public void editEDRAuth(TbCdcmrIllnessRecordAuthority recordAuthority) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        recordAuthority.setPersonInfo(JSONUtil.toJsonStr(recordAuthority.getPersonInfoList()));
        //id为空则插入
        if (StringUtils.isBlank(recordAuthority.getId())) {
            String id = String.valueOf(batchUidService.getUid(TABLE_NAME));
            recordAuthority.setId(id);
            recordAuthority.setCreatorId(uapUserPo.getId());
            recordAuthority.setCreator(uapUserPo.getName());
            recordAuthority.setCreateTime(new Date());
            recordAuthority.setStatus(Integer.valueOf(StatusEnum.STATUS_ON.getCode()));
            recordAuthority.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        }
        recordAuthority.setUpdateId(uapUserPo.getId());
        recordAuthority.setUpdater(uapUserPo.getName());
        recordAuthority.setUpdateTime(new Date());

        authorityMapper.upsert(recordAuthority);
    }

    @Override
    public Map<String, List<TbCdcmrIllnessRecordAuthority>> getUserAuthInfo(String userId) {

        QueryWrapper<TbCdcmrIllnessRecordAuthority> wrapper = new QueryWrapper<>();
        wrapper.eq("delete_flag", "0");
        wrapper.eq("status", 1);
        List<TbCdcmrIllnessRecordAuthority> authorityList = baseMapper.selectList(wrapper);
        if(CollectionUtil.isEmpty(authorityList)) {
            return new HashMap<>();
        }
        Gson gson = new Gson();
        //该用户拥有的所有权限
        List<TbCdcmrIllnessRecordAuthority> authPersonInfoList = authorityList.stream().filter(e -> {
            if(StringUtils.isBlank(e.getPersonInfo())){
                return false;
            }
            List<AuthPersonInfo> infoList = Arrays.asList(gson.fromJson(e.getPersonInfo(), AuthPersonInfo[].class));
            // 如果用户 ID 在列表中则保留该权限
            return infoList.stream().anyMatch(info -> info.getId().equals(userId));
        }).collect(Collectors.toList());

        return authPersonInfoList.stream()
                                 .collect(Collectors.groupingBy(TbCdcmrIllnessRecordAuthority::getAuthCode))
                                 .entrySet().stream()
                                 .collect(Collectors.toMap(Map.Entry::getKey, entry -> getUserAuthList(entry.getValue())));
    }

    /**
     * 取某一种操作下 该用户的的最高权限
     * */
    private List<TbCdcmrIllnessRecordAuthority> getUserAuthList(List<TbCdcmrIllnessRecordAuthority> authorityList) {

        //判断省市区的权限问题
        List<TbCdcmrIllnessRecordAuthority> provinceAuth = new ArrayList<>();
        List<TbCdcmrIllnessRecordAuthority> cityAuth = new ArrayList<>();
        List<TbCdcmrIllnessRecordAuthority> districtAuth = new ArrayList<>();
        authorityList.forEach(e -> {
            if(StringUtils.isBlank(e.getDistrictCode()) && StringUtils.isBlank(e.getCityCode())) {
                provinceAuth.add(e);
            }else if(StringUtils.isBlank(e.getDistrictCode())) {
                cityAuth.add(e);
            }else {
                districtAuth.add(e);
            }
        });
        //取权限最高的
        if(CollectionUtil.isNotEmpty(provinceAuth)){
            return provinceAuth;
        }
        if(CollectionUtil.isNotEmpty(cityAuth)){
            return cityAuth;
        }
        return districtAuth;
    }

    @Override
    public EDRAuthResultVO validViewAuth(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        String authCode = permissionRecord.getAuthCode();
        checkAuthCode(authCode);
        if (!AuthClassEnum.RECORD_RETRIEVAL.getCode().equals(authCode)){
            permissionRecord.setAuthCode(AuthClassEnum.RECORD_RETRIEVAL.getCode());
            EDRAuthResultVO resultVO = validRecordRetrievalAuth(loginUserId, permissionRecord);
            if (!EDRAuthEnums.SUCCESS.getCode().equals(resultVO.getCode())){
                return new EDRAuthResultVO(EDRAuthEnums.NO_CHECK_PERMISSION,null);
            }
            return new EDRAuthResultVO(EDRAuthEnums.SUCCESS,null);
        }
        return validRecordRetrievalAuth(loginUserId, permissionRecord);
    }

    private void checkAuthCode(String authCode) {
        if (!AuthClassEnum.getExistCode(authCode)) {
            throw new MedicalBusinessException("非法权限编码");
        }
    }

    private EDRAuthResultVO validRecordRetrievalAuth(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        List<TbCdcmrIllnessFilePermissionRecord> records = getViewAuthRecords(loginUserId, permissionRecord);
        if (CollectionUtils.isEmpty(records)){
            return new EDRAuthResultVO(EDRAuthEnums.NO_APPLY_RECODE,null);
        }
        List<TbCdcmrIllnessFilePermissionRecord> pendingRecord = records.stream().filter(record -> EDRAuthEnums.PENDING_STATUS.getCode().equals(record.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pendingRecord)){
            EDRAuthResultVO resultVO = new EDRAuthResultVO(EDRAuthEnums.APPLY_PENDING, null);
            resultVO.setValidStartTime(pendingRecord.get(0).getValidStartTime());
            resultVO.setValidEndTime(pendingRecord.get(0).getValidEndTime());
            return resultVO;
        }
        return EDRAuthResultVO.success();
    }

    private List<TbCdcmrIllnessFilePermissionRecord> getViewAuthRecords(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        Date date = new Date();
        LambdaQueryWrapper<TbCdcmrIllnessFilePermissionRecord> queryWrapper = new LambdaQueryWrapper<TbCdcmrIllnessFilePermissionRecord>()
                .eq(TbCdcmrIllnessFilePermissionRecord::getAuthCode, permissionRecord.getAuthCode())
                .eq(TbCdcmrIllnessFilePermissionRecord::getArchiveId, permissionRecord.getArchiveId())
                .eq(TbCdcmrIllnessFilePermissionRecord::getCreatorId, loginUserId)
                .eq(TbCdcmrIllnessFilePermissionRecord::getDeleteFlag, "0")
                .and(wrapper -> wrapper
                        .and(success -> success
                                .eq(TbCdcmrIllnessFilePermissionRecord::getStatus, EDRAuthEnums.SUCCESS_STATUS.getCode())
                                .le(TbCdcmrIllnessFilePermissionRecord::getValidStartTime, date)
                                .ge(TbCdcmrIllnessFilePermissionRecord::getValidEndTime, date)
                        )
                        .or()
                        .eq(TbCdcmrIllnessFilePermissionRecord::getStatus, EDRAuthEnums.PENDING_STATUS.getCode())
                ).select(TbCdcmrIllnessFilePermissionRecord::getId
                        ,TbCdcmrIllnessFilePermissionRecord::getAuthCode
                        ,TbCdcmrIllnessFilePermissionRecord::getStatus
                        ,TbCdcmrIllnessFilePermissionRecord::getValidStartTime
                        ,TbCdcmrIllnessFilePermissionRecord::getValidEndTime
                );
        return illnessFilePermissionRecordMapper.selectList(queryWrapper);
    }


    @Override
    public void addAuthApplyRecord(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        String authCode = permissionRecord.getAuthCode();
        checkAuthCode(authCode);
        if (!AuthClassEnum.RECORD_RETRIEVAL.getCode().equals(authCode)){
            Integer count = illnessFilePermissionRecordMapper.selectCount(
                    new LambdaQueryWrapper<TbCdcmrIllnessFilePermissionRecord>()
                            .eq(TbCdcmrIllnessFilePermissionRecord::getAuthCode, authCode)
                            .eq(TbCdcmrIllnessFilePermissionRecord::getArchiveId, permissionRecord.getArchiveId())
                            .eq(TbCdcmrIllnessFilePermissionRecord::getDeleteFlag, DeleteFlagEnum.NO.getCode())
                            .eq(TbCdcmrIllnessFilePermissionRecord::getStatus, EDRAuthEnums.PENDING_STATUS.getCode()));
            if (count>0){
                throw new MedicalBusinessException("请勿重复申请");
            }
        }
        permissionRecord.setId(String.valueOf(batchUidService.getUid(PERMISSION_RECORD_TABLE_NAME)));
        permissionRecord.setOrgId(USER_INFO.get().getOrgId());
        permissionRecord.setAuthName(AuthClassEnum.getValueByCode(authCode));
        permissionRecord.setStatus(EDRAuthEnums.PENDING_STATUS.getCode());
        permissionRecord.setCreatorId(loginUserId);
        permissionRecord.setCreator(USER_INFO.get().getName());
        permissionRecord.setCreateTime(new Date());
        permissionRecord.setUpdaterId(loginUserId);
        permissionRecord.setUpdater(USER_INFO.get().getName());
        permissionRecord.setUpdateTime(new Date());
        permissionRecord.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        illnessFilePermissionRecordMapper.insert(permissionRecord);

    }

    @Override
    public void changeAuthStatus(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        String status = permissionRecord.getStatus();
        if (EDRAuthEnums.SUCCESS_STATUS.getCode().equals(status)&&!AuthClassEnum.RECORD_RETRIEVAL.getCode().equals(permissionRecord.getAuthCode())){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id",permissionRecord.getArchiveId());
            jsonObject.put("operatingType",EDRAuthEnums.getValueByCode(permissionRecord.getAuthCode()));
            jsonObject.put("contentJson",permissionRecord.getContentJson());
            cdcDataServiceApi.editIllnessRecord(loginUserId, jsonObject);
        }
        permissionRecord.setUpdaterId(loginUserId);
        permissionRecord.setUpdater(USER_INFO.get().getName());
        permissionRecord.setUpdateTime(new Date());
        illnessFilePermissionRecordMapper.updateById(permissionRecord);
    }

    @Override
    public PageInfo<TbCdcmrIllnessFilePermissionRecord> getAuthApplyRecordList(String loginUserId, TbCdcmrIllnessFilePermissionRecord permissionRecord) {
        List<TbCdcmrIllnessFilePermissionRecord> res;
        if ("approver".equals(permissionRecord.getApproverOrApplicantFlag())){
            res = illnessFilePermissionRecordMapper.getAuthApplyRecordListByUserId(loginUserId, permissionRecord);
        }else {
            res = illnessFilePermissionRecordMapper.getAuthApplyRecordListByCreateUserId(loginUserId, permissionRecord);
        }
        res.forEach(EDRAuthServiceImpl::setPatientId);
        PageHelper.startPage(permissionRecord.getPageIndex(), permissionRecord.getPageSize());
        return new PageInfo<>(res);
    }

    private static void setPatientId(TbCdcmrIllnessFilePermissionRecord record) {
        String json = record.getOverviewJson();
        if (StringUtils.isNotBlank(json)){
            JSONObject jsonObject = JSONObject.parseObject(json);
            record.setPatientId(jsonObject.getString("patientId"));
        }
    }

    @Override
    public TbCdcmrIllnessFilePermissionRecord getPermissionRecordInfoById(String id) {
        TbCdcmrIllnessFilePermissionRecord res = illnessFilePermissionRecordMapper.selectById(id);
        setPatientId(res);
        return res;
    }
}
