package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.service.PlunOrgUpdateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 辅诊机构和cdc机构映射controller
 *
 * <AUTHOR>
 * @date 2021/10/19
 **/
@Slf4j
@Api(tags = "辅诊机构和cdc机构映射接口")
@RestController
public class NifiOrgMappingController {


    @Resource
    private PlunOrgUpdateService plunOrgUpdateService;



    @PostMapping("/{version}/pt/nifiOrg/plunOrgChangeMonitor")
    @ApiOperation("插件机构改动修改")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ResponseResult initialOrgMapping(@PathVariable String version, @RequestBody PlunOrgChangeDto orgChangeDto) {
        return plunOrgUpdateService.orgChangeMonitor(orgChangeDto);
    }
}
