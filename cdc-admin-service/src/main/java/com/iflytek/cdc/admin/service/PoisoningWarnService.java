package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.PoisoningQueryDTO;
import com.iflytek.cdc.admin.dto.PoisoningWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrPoisoning;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface PoisoningWarnService {
    PageInfo<TbCdcmrPoisoning> getPoisoningPageList(PoisoningQueryDTO poisoningQueryDTO);

    void addPoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId);

    void deletePoisoning(String id);

    void updatePoisoning(TbCdcmrPoisoning tbCdcmrPoisoning, String loginUserId);

    PageInfo<PoisoningWarnDto> getPoisoningWarnPageList(PoisoningQueryDTO poisoningQueryDTO);

    void updatePoisoningWarn(PoisoningWarnDto poisoningWarnDto, String loginUserId);

    PoisoningWarnDto getWarnById(String warnId);

    void updatePoisoningWarnStatus(PoisoningWarnDto poisoningWarnDto, String loginUserId);

    List<PoisoningWarnDto> getPoisoningWarnAllList(String poisoningCode);

    void exportPoisoningWarnRule(HttpServletResponse response);

    List<CascadeVO> getPoisoningType();

    List<CascadeVO> getPoisonNameList();

    List<CascadeVO> getPoisonNameList(String loginUserId);

}
