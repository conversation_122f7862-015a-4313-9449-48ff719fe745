package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

@ApiModel("多渠道预警规则常量")
@Data
public class MultichannelWarningRuleConstants {

    private static final MultichannelWarningRuleConstants instance = new MultichannelWarningRuleConstants();

    @ApiModelProperty("预警方法")
    private Object multichannelRisk = MultichannelWarningRuleConstants.RiskLevel.values();

    @ApiModelProperty("预警方法")
    private Object channelType = MultichannelWarningRuleConstants.MultichannelType.values();

    public static MultichannelWarningRuleConstants getInstance(){
        return instance;
    }

    /**
     * 预警等级
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum RiskLevel{

        MULTICHANNEL_RISK("8", "01", "多渠道专题风险"),
        ;
        private final String id;
        private final String value;
        private final String label;

        RiskLevel(String id, String value, String label) {
            this.id = id;
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 多渠道 - 渠道类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum MultichannelType{

        SYNDROME("syndrome", "症候群"),

        INFECTED("infected", "传染病"),

        PATHOGEN("pathogen", "病原体"),

        SYMPTOM("symptom", "学校症状监测"),

        DRUGS("drugs", "监测药品"),

        KEYWORDS("keywords", "监测关键词"),

        ;
        private final String code;
        private final String desc;

        MultichannelType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
