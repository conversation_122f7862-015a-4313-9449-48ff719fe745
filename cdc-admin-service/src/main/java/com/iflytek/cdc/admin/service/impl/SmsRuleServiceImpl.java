package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonSmsRule;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedSmsRule;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.service.SmsRuleService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SmsRuleServiceImpl implements SmsRuleService {
    @Resource
    TbCdcmrSymptomSmsRuleMapper tbCdcmrSymptomSmsRuleMapper;
    @Resource
    TbCdcmrSympSmsRuleMapper tbCdcmrSympSmsRuleMapper;
    @Resource
    TbCdcmrInfectedSmsRuleMapper tbCdcmrInfectedSmsRuleMapper;
    @Resource
    TbCdcmrPoisonSmsRuleMapper tbCdcmrPoisonSmsRuleMapper;
    @Resource
    TbCdcmrOutpatientSmsRuleMapper tbCdcmrOutpatientSmsRuleMapper;
    @Resource
    TbCdcmrUnknownReasonSmsRuleMapper tbCdcmrUnknownReasonSmsRuleMapper;
    @Resource
    TbCdcmrPreventionControlSmsRuleMapper tbCdcmrPreventionControlSmsRuleMapper;
    @Resource
    TbCdcmrCustomizedSmsRuleMapper tbCdcmrCustomizedSmsRuleMapper;
    @Resource
    BatchUidService batchUidService;

    @Override
    public void upsertSymptomSmsRule(SymptomSmsRuleParam rule, String loginUserId) {

        List<TbCdcmrSymptomSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrSymptomSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrSymptomSmsRule> existList = tbCdcmrSymptomSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getSymptomList().stream().map(SymptomNameVO::getSymptomCode).collect(Collectors.toList());
        List<String> symptomCodeList = existList.stream().map(TbCdcmrSymptomSmsRule::getSymptomCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getSymptomList().isEmpty()) {
            if (!symptomCodeList.isEmpty()) {
                tbCdcmrSymptomSmsRuleMapper.deleteBySymptomCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), symptomCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getSymptomCode())) {
                    deleteCodeList.add(e.getSymptomCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {

                tbCdcmrSymptomSmsRuleMapper.deleteBySymptomCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getSymptomList().forEach(symptomNameVO -> {
            if (symptomCodeList.contains(symptomNameVO.getSymptomCode())) {
                updateList.add(parseSymptomSmsRule(rule, symptomNameVO));
            } else {
                insertList.add(parseSymptomSmsRule(rule, symptomNameVO));
            }
        });
        updateSymptomSmsRule(updateList, loginUserId);
        addSymptomSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleList(String businessPersonId) {
        return tbCdcmrSymptomSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public void upsertInfectedSmsRule(InfectedSmsRuleParam rule, String loginUserId) {
        List<TbCdcmrInfectedSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrInfectedSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrInfectedSmsRule> existList = tbCdcmrInfectedSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getInfectedList().stream().map(InfectedNameVO::getInfectedCode).collect(Collectors.toList());
        List<String> infectedCodeList = existList.stream().map(TbCdcmrInfectedSmsRule::getInfectedCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getInfectedList().isEmpty()) {
            if (!infectedCodeList.isEmpty()) {
                tbCdcmrInfectedSmsRuleMapper.deleteByInfectedCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), infectedCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getInfectedCode())) {
                    deleteCodeList.add(e.getInfectedCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrInfectedSmsRuleMapper.deleteByInfectedCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getInfectedList().forEach(infectedNameVO -> {
            if (infectedCodeList.contains(infectedNameVO.getInfectedCode())) {
                updateList.add(parseInfectedSmsRule(rule, infectedNameVO));
            } else {
                insertList.add(parseInfectedSmsRule(rule, infectedNameVO));
            }
        });
        updateInfectedSmsRule(updateList, loginUserId);
        addInfectedSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleList(String businessPersonId) {
        return tbCdcmrInfectedSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public void upsertScSymptomSmsRule(SympSmsRuleParam rule, String loginUserId) {

        List<TbCdcmrSympSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrSympSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrSympSmsRule> existList = tbCdcmrSympSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getSchoolSymptomList().stream().map(SympNameVO::getSchoolSymptomCode).collect(Collectors.toList());
        List<String> scSymptomCodeList = existList.stream().map(TbCdcmrSympSmsRule::getSymptomCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getSchoolSymptomList().isEmpty()) {
            if (!scSymptomCodeList.isEmpty()) {
                tbCdcmrSympSmsRuleMapper.deleteBySymptomCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), scSymptomCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getSymptomCode())) {
                    deleteCodeList.add(e.getSymptomCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrSympSmsRuleMapper.deleteBySymptomCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getSchoolSymptomList().forEach(infectedNameVO -> {
            if (scSymptomCodeList.contains(infectedNameVO.getSchoolSymptomCode())) {
                updateList.add(parseSchoolSymptomSmsRule(rule, infectedNameVO));
            } else {
                insertList.add(parseSchoolSymptomSmsRule(rule, infectedNameVO));
            }
        });
        updateScSymptomSmsRule(updateList, loginUserId);
        addScSymptomSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrSympSmsRule> getScSymptomSmsRuleList(String businessPersonId) {
        return tbCdcmrSympSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public void upsertPoisonSmsRule(PoisonSmsRuleParam rule, String loginUserId) {
        List<TbCdcmrPoisonSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrPoisonSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrPoisonSmsRule> existList = tbCdcmrPoisonSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getPoisonList().stream().map(PoisonNameVO::getPoisonCode).collect(Collectors.toList());
        List<String> poisonCodeList = existList.stream().map(TbCdcmrPoisonSmsRule::getPoisonCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getPoisonList().isEmpty()) {
            if (!poisonCodeList.isEmpty()) {
                tbCdcmrPoisonSmsRuleMapper.deleteByPoisonCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), poisonCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getPoisonCode())) {
                    deleteCodeList.add(e.getPoisonCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrPoisonSmsRuleMapper.deleteByPoisonCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getPoisonList().forEach(poisonNameVO -> {
            if (poisonCodeList.contains(poisonNameVO.getPoisonCode())) {
                updateList.add(parsePoisonSmsRule(rule, poisonNameVO));
            } else {
                insertList.add(parsePoisonSmsRule(rule, poisonNameVO));
            }
        });
        updatePoisonSmsRule(updateList, loginUserId);
        addPoisonSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleList(String businessPersonId) {
        return tbCdcmrPoisonSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }


    @Override
    public List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrInfectedSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteInfectedSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrInfectedSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    @Override
    public List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrSymptomSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteSymptomSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrSymptomSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    @Override
    public List<TbCdcmrSympSmsRule> getScSymptomSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrSympSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteScSymptomSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrSympSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    @Override
    public List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrPoisonSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deletePoisonSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrPoisonSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    private void addSymptomSmsRule(List<TbCdcmrSymptomSmsRule> ruleList, String loginUserId) {
        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_symp_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrSymptomSmsRuleMapper.insert(ruleList);
        }
    }

    @Override
    public void upsertCustomizedSmsRule(CustomizedSmsRuleParam rule, String loginUserId) {
        List<TbCdcmrCustomizedSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrCustomizedSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrCustomizedSmsRule> existList = tbCdcmrCustomizedSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getCustomizedList().stream().map(CustomizedNameVO::getCustomizedCode).collect(Collectors.toList());
        List<String> customizedCodeList = existList.stream().map(TbCdcmrCustomizedSmsRule::getCustomizedCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getCustomizedList().isEmpty()) {
            if (!customizedCodeList.isEmpty()) {
                tbCdcmrCustomizedSmsRuleMapper.deleteByCustomizedCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), customizedCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getCustomizedCode())) {
                    deleteCodeList.add(e.getCustomizedCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrCustomizedSmsRuleMapper.deleteByCustomizedCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getCustomizedList().forEach(customizedNameVO -> {
            if (customizedCodeList.contains(customizedNameVO.getCustomizedCode())) {
                updateList.add(parseCustomizedSmsRule(rule, customizedNameVO));
            } else {
                insertList.add(parseCustomizedSmsRule(rule, customizedNameVO));
            }
        });
        updateCustomizedSmsRule(updateList, loginUserId);
        addCustomizedSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleList(String businessPersonId) {
        return tbCdcmrCustomizedSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrCustomizedSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteCustomizedSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrPoisonSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    private void updateSymptomSmsRule(List<TbCdcmrSymptomSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrSymptomSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }

    private void addInfectedSmsRule(List<TbCdcmrInfectedSmsRule> ruleList, String loginUserId) {
        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_infected_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrInfectedSmsRuleMapper.insert(ruleList);
        }
    }

    private void updateInfectedSmsRule(List<TbCdcmrInfectedSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrInfectedSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }

    private void updateCustomizedSmsRule(List<TbCdcmrCustomizedSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrCustomizedSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }

    private void addCustomizedSmsRule(List<TbCdcmrCustomizedSmsRule> ruleList, String loginUserId) {
        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrCustomizedSmsRuleMapper.insert(ruleList);
        }
    }

    private void addScSymptomSmsRule(List<TbCdcmrSympSmsRule> ruleList, String loginUserId) {
        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_symp_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrSympSmsRuleMapper.insert(ruleList);
        }
    }

    private void updateScSymptomSmsRule(List<TbCdcmrSympSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrSympSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }


    private void addPoisonSmsRule(List<TbCdcmrPoisonSmsRule> ruleList, String loginUserId) {

        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_poison_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrPoisonSmsRuleMapper.insert(ruleList);
        }
    }

    private void updatePoisonSmsRule(List<TbCdcmrPoisonSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrPoisonSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }


    TbCdcmrSymptomSmsRule parseSymptomSmsRule(SymptomSmsRuleParam symptomSmsRuleParam, SymptomNameVO symptomNameVO) {
        TbCdcmrSymptomSmsRule result = new TbCdcmrSymptomSmsRule();
        BeanUtil.copyProperties(symptomSmsRuleParam, result);
        result.setSymptomName(symptomNameVO.getSymptomName());
        result.setSymptomCode(symptomNameVO.getSymptomCode());
        result.setLoginUserId(symptomSmsRuleParam.getBusinessPersonId());
        return result;
    }

    TbCdcmrInfectedSmsRule parseInfectedSmsRule(InfectedSmsRuleParam infectedSmsRuleParam, InfectedNameVO infectedNameVO) {
        TbCdcmrInfectedSmsRule result = new TbCdcmrInfectedSmsRule();
        BeanUtil.copyProperties(infectedSmsRuleParam, result);
        result.setInfectedName(infectedNameVO.getInfectedName());
        result.setInfectedCode(infectedNameVO.getInfectedCode());
        result.setLoginUserId(infectedSmsRuleParam.getBusinessPersonId());
        return result;

    }

    TbCdcmrSympSmsRule parseSchoolSymptomSmsRule(SympSmsRuleParam sympSmsRuleParam, SympNameVO sympNameVO) {
        TbCdcmrSympSmsRule result = new TbCdcmrSympSmsRule();
        BeanUtil.copyProperties(sympSmsRuleParam, result);
        result.setSymptomName(sympNameVO.getSchoolSymptomName());
        result.setSymptomCode(sympNameVO.getSchoolSymptomCode());
        result.setLoginUserId(sympSmsRuleParam.getBusinessPersonId());
        return result;

    }

    TbCdcmrPoisonSmsRule parsePoisonSmsRule(PoisonSmsRuleParam poisonSmsRuleParam, PoisonNameVO poisonNameVO) {
        TbCdcmrPoisonSmsRule result = new TbCdcmrPoisonSmsRule();
        BeanUtil.copyProperties(poisonSmsRuleParam, result);
        result.setPoisonName(poisonNameVO.getPoisonName());
        result.setPoisonCode(poisonNameVO.getPoisonCode());
        result.setLoginUserId(poisonSmsRuleParam.getBusinessPersonId());
        return result;
    }

    TbCdcmrCustomizedSmsRule parseCustomizedSmsRule(CustomizedSmsRuleParam customizedSmsRuleParam, CustomizedNameVO customizedNameVO) {
        TbCdcmrCustomizedSmsRule result = new TbCdcmrCustomizedSmsRule();
        BeanUtil.copyProperties(customizedSmsRuleParam, result);
        result.setCustomizedName(customizedNameVO.getCustomizedName());
        result.setCustomizedCode(customizedNameVO.getCustomizedCode());
        result.setLoginUserId(customizedSmsRuleParam.getBusinessPersonId());
        return result;
    }

    TbCdcmrPreventionControlSmsRule parsePreventionControlSmsRule(PreventionControlSmsRuleParam smsRuleParam, PreventionControlNameVO nameVO) {
        TbCdcmrPreventionControlSmsRule result = new TbCdcmrPreventionControlSmsRule();
        BeanUtil.copyProperties(smsRuleParam, result);
        result.setPreventionControlName(nameVO.getPreventionControlName());
        result.setPreventionControlCode(nameVO.getPreventionControlCode());
        result.setLoginUserId(smsRuleParam.getBusinessPersonId());
        return result;
    }

    @Override
    public void upsertOutpatientSmsRule(OutpatientSmsRuleParam rule, String loginUserId) {

        List<TbCdcmrOutpatientSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrOutpatientSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrOutpatientSmsRule> existList = tbCdcmrOutpatientSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getOutpatientList().stream().map(OutpatientNameVO::getOutpatientTypeCode).collect(Collectors.toList());
        List<String> outpatientCodeList = existList.stream().map(TbCdcmrOutpatientSmsRule::getOutpatientTypeCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getOutpatientList().isEmpty()) {
            if (!outpatientCodeList.isEmpty()) {
                tbCdcmrOutpatientSmsRuleMapper.deleteByOutpatientCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), outpatientCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getOutpatientTypeCode())) {
                    deleteCodeList.add(e.getOutpatientTypeCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrOutpatientSmsRuleMapper.deleteByOutpatientCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getOutpatientList().forEach(outpatientNameVO -> {
            if (outpatientCodeList.contains(outpatientNameVO.getOutpatientTypeCode())) {
                updateList.add(parseOutpatientSmsRule(rule, outpatientNameVO));
            } else {
                insertList.add(parseOutpatientSmsRule(rule, outpatientNameVO));
            }
        });
        updateOutpatientSmsRule(updateList, loginUserId);
        addOutpatientSmsRule(insertList, loginUserId);
    }

    @Override
    public List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleList(String businessPersonId) {
        return tbCdcmrOutpatientSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrOutpatientSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteOutpatientSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrOutpatientSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    private void addOutpatientSmsRule(List<TbCdcmrOutpatientSmsRule> ruleList, String loginUserId) {

        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_outpatient_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrOutpatientSmsRuleMapper.insert(ruleList);
        }
    }

    private void updateOutpatientSmsRule(List<TbCdcmrOutpatientSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrOutpatientSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }


    TbCdcmrOutpatientSmsRule parseOutpatientSmsRule(OutpatientSmsRuleParam outpatientSmsRuleParam, OutpatientNameVO outpatientNameVO) {
        TbCdcmrOutpatientSmsRule result = new TbCdcmrOutpatientSmsRule();
        BeanUtil.copyProperties(outpatientSmsRuleParam, result);
        result.setOutpatientTypeName(outpatientNameVO.getOutpatientTypeName());
        result.setOutpatientTypeCode(outpatientNameVO.getOutpatientTypeCode());
        result.setLoginUserId(outpatientSmsRuleParam.getBusinessPersonId());
        return result;
    }

    @Override
    public void upsertUnknownReasonSmsRule(UnknownReasonSmsRuleParam rule, String loginUserId) {
        List<TbCdcmrUnknownReasonSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrUnknownReasonSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrUnknownReasonSmsRule> existList = tbCdcmrUnknownReasonSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getUnknownReasonList().stream().map(UnknownReasonNameVO::getDiseaseCode).collect(Collectors.toList());
        List<String> unknownReasonCodeList = existList.stream().map(TbCdcmrUnknownReasonSmsRule::getDiseaseCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getUnknownReasonList().isEmpty()) {
            if (!unknownReasonCodeList.isEmpty()) {
                tbCdcmrUnknownReasonSmsRuleMapper.deleteByUnknownReasonCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), unknownReasonCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getDiseaseCode())) {
                    deleteCodeList.add(e.getDiseaseCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrUnknownReasonSmsRuleMapper.deleteByUnknownReasonCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getUnknownReasonList().forEach(unknownReasonNameVO -> {
            if (unknownReasonCodeList.contains(unknownReasonNameVO.getDiseaseCode())) {
                updateList.add(parseUnknownReasonSmsRule(rule, unknownReasonNameVO));
            } else {
                insertList.add(parseUnknownReasonSmsRule(rule, unknownReasonNameVO));
            }
        });
        updateUnknownReasonSmsRule(updateList, loginUserId);
        addUnknownReasonSmsRule(insertList, loginUserId);
    }

    TbCdcmrUnknownReasonSmsRule parseUnknownReasonSmsRule(UnknownReasonSmsRuleParam unknownReasonSmsRuleParam, UnknownReasonNameVO unknownReasonNameVO) {
        TbCdcmrUnknownReasonSmsRule result = new TbCdcmrUnknownReasonSmsRule();
        BeanUtil.copyProperties(unknownReasonSmsRuleParam, result);
        result.setDiseaseName(unknownReasonNameVO.getDiseaseName());
        result.setDiseaseCode(unknownReasonNameVO.getDiseaseCode());
        result.setLoginUserId(unknownReasonSmsRuleParam.getBusinessPersonId());
        return result;
    }

    @Override
    public List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleList(String businessPersonId) {
        return tbCdcmrUnknownReasonSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrUnknownReasonSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deleteUnknownReasonSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrUnknownReasonSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    @Override
    public void upsertPreventionControlSmsRule(PreventionControlSmsRuleParam rule, String loginUserId) {
        List<TbCdcmrPreventionControlSmsRule> insertList = new ArrayList<>();
        List<TbCdcmrPreventionControlSmsRule> updateList = new ArrayList<>();
        List<TbCdcmrPreventionControlSmsRule> existList = tbCdcmrPreventionControlSmsRuleMapper.getListByLoginUserId(rule.getBusinessPersonId());
        List<String> updateCodeList = rule.getPreventionControlList().stream().map(PreventionControlNameVO::getPreventionControlCode).collect(Collectors.toList());
        List<String> preventionControlCodeList = existList.stream().map(TbCdcmrPreventionControlSmsRule::getPreventionControlCode).collect(Collectors.toList());
        List<String> deleteCodeList = new ArrayList<>();
        if (rule.getPreventionControlList().isEmpty()) {
            if (!preventionControlCodeList.isEmpty()) {
                tbCdcmrPreventionControlSmsRuleMapper.deleteByPreventionControlCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), preventionControlCodeList);
            }
        } else {
            existList.forEach(e -> {
                if (!updateCodeList.contains(e.getPreventionControlCode())) {
                    deleteCodeList.add(e.getPreventionControlCode());
                }
            });
            if (!deleteCodeList.isEmpty()) {
                tbCdcmrPreventionControlSmsRuleMapper.deleteByPreventionControlCodeList(loginUserId, new Date(), rule.getBusinessPersonId(), deleteCodeList);
            }
        }

        rule.getPreventionControlList().forEach(nameVO -> {
            if (preventionControlCodeList.contains(nameVO.getPreventionControlCode())) {
                updateList.add(parsePreventionControlSmsRule(rule, nameVO));
            } else {
                insertList.add(parsePreventionControlSmsRule(rule, nameVO));
            }
        });
        updatePreventionControlSmsRule(updateList, loginUserId);
        addPreventionControlSmsRule(insertList, loginUserId);
    }

    private void addPreventionControlSmsRule(List<TbCdcmrPreventionControlSmsRule> ruleList, String loginUserId) {
        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_prevention_control_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrPreventionControlSmsRuleMapper.insert(ruleList);
        }
    }

    private void updatePreventionControlSmsRule(List<TbCdcmrPreventionControlSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrPreventionControlSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }

    @Override
    public List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleList(String businessPersonId) {
        return tbCdcmrPreventionControlSmsRuleMapper.getListByLoginUserId(businessPersonId);
    }

    @Override
    public List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleListByCodeList(List<String> codeList) {
        return tbCdcmrPreventionControlSmsRuleMapper.getListByCodeList(codeList);
    }

    @Override
    public void deletePreventionControlSmsRuleByLoginUserId(String loginUserId) {
        tbCdcmrPreventionControlSmsRuleMapper.deleteByLoginUserId(loginUserId, new Date(), loginUserId);
    }

    private void addUnknownReasonSmsRule(List<TbCdcmrUnknownReasonSmsRule> ruleList, String loginUserId) {

        if (ruleList != null && !ruleList.isEmpty()) {
            ruleList.forEach(rule -> {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_unknown_reason_sms_rule")));
                rule.setCreator(loginUserId);
                rule.setCreateTime(new Date());
                rule.setDeleted(0);
            });
            tbCdcmrUnknownReasonSmsRuleMapper.insert(ruleList);
        }
    }

    private void updateUnknownReasonSmsRule(List<TbCdcmrUnknownReasonSmsRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            rule.setUpdateTime(new Date());
            rule.setUpdater(loginUserId);
        });
        tbCdcmrUnknownReasonSmsRuleMapper.updateByPrimaryKeySelective(ruleList);
    }

}
