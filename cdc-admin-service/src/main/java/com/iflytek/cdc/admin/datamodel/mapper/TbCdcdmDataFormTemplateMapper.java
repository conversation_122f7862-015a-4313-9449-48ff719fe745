package com.iflytek.cdc.admin.datamodel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.admin.datamodel.model.dto.DataFormTemplateQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcdmDataFormTemplateMapper extends BaseMapper<TbCdcdmDataFormTemplate> {

    /**
     * 通过ID查询单条数据
     *
     * @param formTemplateDetailId 主键
     * @return 实例对象
     */
    TbCdcdmDataFormTemplate queryById(@Param("formTemplateDetailId") String formTemplateDetailId);

    /**
     * 新增数据
     *
     * @param tbCdcdmDataFormTemplate 实例对象
     * @return 影响行数
     */
    int insert(TbCdcdmDataFormTemplate tbCdcdmDataFormTemplate);

    /**
     * 更新数据
     *
     * @param tbCdcdmDataFormTemplate 实例对象
     * @return 影响行数
     */
    int update(TbCdcdmDataFormTemplate tbCdcdmDataFormTemplate);

    /**
     * 更新配置表中模型的版本id
     * */
    int updateModelVersionByModelId(@Param("modelId") String modelId,
                                    @Param("modelVersionId") String modelVersionId);

    /**
     * 更新配置表中模型的版本id
     * */
    int updateEsIndexTemplateById(@Param("formTemplateDetailId") String formTemplateDetailId,
                                  @Param("esIndexTemplate") String esIndexTemplate);

    /**
     * 查询模型配置列表
     * */
    List<TbCdcdmDataFormTemplate> getAllModelTemplate(DataFormTemplateQueryDTO dto);

    /**
     * 查询模型是否在配置表中存在配置信息
     * */
    TbCdcdmDataFormTemplate getConfigByModelId(@Param("modelId") String modelId);

    /**
     * 根据疾病code获取配置的数据模型configInfo
     * 针对症候群每种疾病一个数据模型的问题
     * */
    String getConfigInfoByDiseaseCode(String diseaseCode);

}
