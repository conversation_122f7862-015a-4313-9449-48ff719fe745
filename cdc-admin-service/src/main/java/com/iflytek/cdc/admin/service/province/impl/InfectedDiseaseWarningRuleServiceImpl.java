package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.constant.InfectedDataConstants;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseMonitor;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseWarningRule;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.InfectedMonitorTypeEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseMonitorMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrInfectedDiseaseWarningRuleMapper;
import com.iflytek.cdc.admin.model.mr.vo.InfectedProcessScopeVO;
import com.iflytek.cdc.admin.service.province.InfectedDiseaseWarningRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

/**
 * <p>
 * 传染病预警-传染病预警规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class InfectedDiseaseWarningRuleServiceImpl extends ServiceImpl<TbCdcmrInfectedDiseaseWarningRuleMapper, TbCdcmrInfectedDiseaseWarningRule> implements InfectedDiseaseWarningRuleService {

    private static final String TB_CDCMR_INFECTED_DISEASE_WARNING_RULE = "tb_cdcmr_infected_disease_warning_rule";

    @Resource
    private TbCdcmrInfectedDiseaseWarningRuleMapper warningRuleMapper;

    @Resource
    private TbCdcmrInfectedDiseaseMonitorMapper infectedDiseaseMonitorMapper;
    
    @Resource
    private BatchUidService batchUidService;

    @Override
    public PageInfo<TbCdcmrInfectedDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod, String followStatus) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(warningRuleMapper.getListByInfectedDiseaseWarningId(diseaseInfoId, riskLevel, warningMethod, followStatus));
    }

    @Override
    public List<InfectedProcessScopeVO> getInfectedProcessScope(String diseaseId) {

        List<TbCdcmrInfectedDiseaseMonitor> diseaseMonitorList = infectedDiseaseMonitorMapper.getConfigByDiseaseId(diseaseId);
        if(CollectionUtil.isEmpty(diseaseMonitorList)) {
            return new ArrayList<>();
        }
        //过滤病例诊断类型
        diseaseMonitorList =  diseaseMonitorList.stream()
                                                .filter(e -> Objects.equals(InfectedMonitorTypeEnum.DIAGNOSE_STATUS.getCode(), e.getType()))
                                                .collect(Collectors.toList());

        List<InfectedProcessScopeVO> processScopeVOList = new ArrayList<>();
        diseaseMonitorList.forEach(e -> {
            InfectedProcessScopeVO scopeVO = new InfectedProcessScopeVO();
            scopeVO.setLabel(e.getTitle());
            scopeVO.setValue(InfectedDataConstants.DiagnoseStatusEnum.getValueByLabel(e.getTitle()));
            processScopeVOList.add(scopeVO);
        });
        return processScopeVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savaOrUpdateRule(TbCdcmrInfectedDiseaseWarningRule rule) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        if (StringUtils.isBlank(rule.getId())) {
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_INFECTED_DISEASE_WARNING_RULE));
            rule.setId(id);
            rule.setCreatorId(uapUserPo.getId());
            rule.setCreateTime(new Date());
            rule.setCreator(uapUserPo.getName());
            rule.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            rule.setStatus(Integer.valueOf(StatusEnum.STATUS_ON.getCode()));
        }
        rule.setUpdaterId(uapUserPo.getId());
        rule.setUpdateTime(new Date());
        rule.setUpdater(uapUserPo.getName());
        //   设置关注状态
        if (StringUtils.isNotBlank(rule.getFollowStatus())) {
            rule.setFollowStatus(rule.getFollowStatus());
        }
        baseMapper.upsert(rule);

    }

}
