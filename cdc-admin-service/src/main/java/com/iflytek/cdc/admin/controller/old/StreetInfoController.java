package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo;
import com.iflytek.cdc.admin.mapper.TbCdcmrStreetInfoMapper;
import com.iflytek.cdc.admin.model.dm.vo.AreaInfoVO;
import com.iflytek.cdc.admin.service.StreetInfoService;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "街道信息接口")

public class StreetInfoController {
    @Resource
    TbCdcmrStreetInfoMapper tbCdcmrStreetInfoMapper;
    @Resource
    private StreetInfoService streetInfoService;

    @GetMapping("/{version}/pt/streetInfo/getCascadeList")
    @ApiOperation("查询街道级联列表")
    public List<CascadeVO> getCascadeList(Integer areaLevel, String areaCode) {
        List<CascadeVO> cascadeAddressList = new ArrayList<>();
        Map<String, CascadeVO> provinceMap = new HashMap<>();
        Map<String, CascadeVO> cityMap = new HashMap<>();
        Map<String, CascadeVO> districtMap = new HashMap<>();
        List<TbCdcmrStreetInfo> infoList = tbCdcmrStreetInfoMapper.selectByAreaCode(areaLevel, areaCode);
        for (TbCdcmrStreetInfo address : infoList) {
            String provinceCode = address.getProvinceCode();
            String provinceName = address.getProvinceName();
            String cityCode = address.getCityCode();
            String cityName = address.getCityName();
            String districtCode = address.getDistrictCode();
            String districtName = address.getDistrictName();
            String streetCode = address.getStreetCode();
            String streetName = address.getStreetName();
            CascadeVO provinceNode = provinceMap.get(provinceCode);
            CascadeVO cityNode = cityMap.get(cityCode);
            CascadeVO districtNode = districtMap.get(districtCode);
            CascadeVO streetNode = new CascadeVO(streetName, streetCode, null);

            switch (areaLevel) {
                case 2:
                    if (provinceNode == null) {
                        provinceNode = new CascadeVO(provinceName, provinceCode, new ArrayList<>());
                        provinceMap.put(provinceCode, provinceNode);
                        cascadeAddressList.add(provinceNode);
                    }

                    if (cityNode == null) {
                        cityNode = new CascadeVO(cityName, cityCode, new ArrayList<>());
                        cityMap.put(cityCode, cityNode);
                        provinceNode.getChildren().add(cityNode);
                    }

                    if (districtNode == null) {
                        districtNode = new CascadeVO(districtName, districtCode, new ArrayList<>());
                        districtMap.put(districtCode, districtNode);
                        cityNode.getChildren().add(districtNode);
                    }
                    districtNode.getChildren().add(streetNode);
                    break;
                case 1:
                    if (cityNode == null) {
                        cityNode = new CascadeVO(cityName, cityCode, new ArrayList<>());
                        cityMap.put(cityCode, cityNode);
                        cascadeAddressList.add(cityNode);
                    }

                    if (districtNode == null) {
                        districtNode = new CascadeVO(districtName, districtCode, new ArrayList<>());
                        districtMap.put(districtCode, districtNode);
                        cityNode.getChildren().add(districtNode);
                    }
                    districtNode.getChildren().add(streetNode);
                    break;
                case 0:
                    if (districtNode == null) {
                        districtNode = new CascadeVO(districtName, districtCode, new ArrayList<>());
                        districtMap.put(districtCode, districtNode);
                        cascadeAddressList.add(districtNode);
                    }
                    districtNode.getChildren().add(streetNode);
                    break;
            }
        }
        return cascadeAddressList;
    }

    @GetMapping("/{version}/pt/streetInfo/loadCurrUserArea")
    @ApiOperation("查询街道级联列表")
    public LoginUserAreaVO loadCurrUserArea(@RequestParam String loginUserName) {
        return streetInfoService.loadCurrUserArea(loginUserName);
    }

    @GetMapping("/{version}/pt/streetInfo/getAreaInfo")
    @ApiOperation("获取街道列表")
    public AreaInfoVO getAreaInfo(@RequestParam String loginUserId) {
        return streetInfoService.getAreaInfo();
    }

}
