package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/27 16:30
 **/
public interface MdmDataSyncService {

    /**
     * mdm标准字典值域数据获取
     * @param originCode
     * @param pageNumber
     * @param pageSize
     * @return
     **/
    List<TermCodedValueInfo> mdmDictCodeSync(String  originCode,Integer pageNumber,Integer pageSize);

    /**
     * mdm获取数据
     * @param di
     * @param pageNumber
     * @param pageSize
     * @param codeValue
     * @return
     **/
    MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter>  getMdmCodeInfo(DictMappingInfo di, Integer pageNumber, Integer pageSize,String...codeValue);

    /**
     * mdm获取数据
     * @param di
     * @param codeValue
     * @return
     **/
    MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter>  getMdmCodeInfoByCode(DictMappingInfo di, String codeValue);

    /**
     * 查询mdm字典目录
     * @param originCode
     * @return
     **/
    MdmPageData<TermDictInfo, TermDictInfoFilter>   getMdmTermDictInfo(String originCode);


    /**
     * 获取映射编码
     * @param originCode
     * @return
     **/
    DictMappingInfo  getMappingInfo(String originCode);


    List<CascadeVO> getMdmDictResult(String dictCode, String keyword);


}
