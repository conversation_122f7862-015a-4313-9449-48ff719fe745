package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.entity.TbCdcmrScreenConfig;
import com.iflytek.cdc.admin.mapper.ScreenLayoutConfigMapper;
import com.iflytek.cdc.admin.service.ScreenLayoutConfigService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ScreenLayoutConfigServiceImpl implements ScreenLayoutConfigService {

    @Resource
    private ScreenLayoutConfigMapper screenLayoutConfigMapper;
    @Resource
    private BatchUidService batchUidService;

    @Override
    public TbCdcmrScreenConfig getScreenLayoutConfig() {
        return screenLayoutConfigMapper.getScreenLayoutConfig();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateLayoutConfig(TbCdcmrScreenConfig layoutConfig, String loginUserId, String loginUserName) {
        TbCdcmrScreenConfig record = new TbCdcmrScreenConfig();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_screen_config")));
        record.setConfigType(0);
        record.setConfigKey("layout");
        record.setConfigValue(layoutConfig.getConfigValue());
        record.setConfigKeyDec("大屏布局配置");
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setCreator(loginUserName);
        record.setCreatorId(loginUserId);
        record.setUpdater(loginUserName);
        record.setUpdaterId(loginUserId);
        screenLayoutConfigMapper.insert(record);
    }

    @Override
    public void updateDesensitization(TbCdcmrScreenConfig desensitizationConfig, String loginUserId, String loginUserName) {
        TbCdcmrScreenConfig record = new TbCdcmrScreenConfig();
        record.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_screen_config")));
        record.setConfigType(1);
        record.setConfigKey("desensitization");
        record.setConfigValue(desensitizationConfig.getConfigValue());
        record.setConfigKeyDec("大屏脱敏配置");
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setCreator(loginUserName);
        record.setCreatorId(loginUserId);
        record.setUpdater(loginUserName);
        record.setUpdaterId(loginUserId);
        screenLayoutConfigMapper.insert(record);
    }

    @Override
    public TbCdcmrScreenConfig getDesensitization() {
        return screenLayoutConfigMapper.getDesensitization();
    }
}
