package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.PreventionControlEnum;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.dto.PreventionControlWarnDto;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarn;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlWarn;
import com.iflytek.cdc.admin.mapper.TbCdcmrPreventionControlWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrPreventionControlWarnRuleMapper;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.PreventionControlWarnService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PreventionControlWarnServiceImpl implements PreventionControlWarnService {

    @Resource
    TbCdcmrPreventionControlWarnMapper tbCdcmrPreventionControlWarnMapper;

    @Resource
    TbCdcmrPreventionControlWarnRuleMapper tbCdcmrPreventionControlWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;

    @Resource
    DataAuthService dataAuthService;

    @Resource
    private ParamConfigService paramConfigService;
    @Override
    public PageInfo<PreventionControlWarnDto> getPreventionControlWarnPageList(PreventionControlQueryDto dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(tbCdcmrPreventionControlWarnMapper.getList(dto));
    }

    @Override
    public void updatePreventionControlWarn(PreventionControlWarnDto warnDto, String loginUserId) {
        TbCdcmrPreventionControlWarn tbCdcmrPreventionControlWarn = new TbCdcmrPreventionControlWarn();
        tbCdcmrPreventionControlWarn.setId(warnDto.getId());
        tbCdcmrPreventionControlWarn.setStatus(warnDto.getStatus());
        tbCdcmrPreventionControlWarn.setPreventionControlCode(warnDto.getPreventionControlCode());
        tbCdcmrPreventionControlWarn.setPreventionControlName(warnDto.getPreventionControlName());
        tbCdcmrPreventionControlWarn.setRemark(warnDto.getRemark());
        tbCdcmrPreventionControlWarn.setUpdater(loginUserId);
        tbCdcmrPreventionControlWarn.setIsDeleted(0);
        tbCdcmrPreventionControlWarn.setUpdateTime(new Date());
        tbCdcmrPreventionControlWarn.setMaxLifeCycle(warnDto.getMaxLifeCycle());
        tbCdcmrPreventionControlWarn.setSmsSendTypeCode(warnDto.getSmsSendTypeCode());
        tbCdcmrPreventionControlWarn.setSmsSendTypeDesc(warnDto.getSmsSendTypeDesc());
        tbCdcmrPreventionControlWarnMapper.updateByPrimaryKeySelective(tbCdcmrPreventionControlWarn);
        updateRules(tbCdcmrPreventionControlWarn.getId(), warnDto.getRuleList(), loginUserId);
    }

    private void updateRules(String warnId, List<TbCdcmrPreventionControlWarnRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_prevention_control_warn_rule")));
            }
            rule.setWarnId(warnId);
            rule.setStatus(1);
            rule.setIsDeleted(0);
            rule.setUpdater(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrPreventionControlWarnRule::getId).collect(Collectors.toList());
        tbCdcmrPreventionControlWarnRuleMapper.deleteOtherByIds(idList, warnId);
        tbCdcmrPreventionControlWarnRuleMapper.upsertRules(ruleList);
    }

    @Override
    public void updatePreventionControlWarnStatus(PreventionControlWarnDto warnDto, String loginUserId) {
        TbCdcmrPreventionControlWarn tbCdcmrPreventionControlWarn = new TbCdcmrPreventionControlWarn();
        tbCdcmrPreventionControlWarn.setStatus(warnDto.getStatus());
        tbCdcmrPreventionControlWarn.setId(warnDto.getId());
        tbCdcmrPreventionControlWarnMapper.updateStatusByPrimaryKey(tbCdcmrPreventionControlWarn);
    }

    @Override
    public PreventionControlWarnDto getWarnById(String warnId) {

        PreventionControlWarnDto dto = new PreventionControlWarnDto();
        TbCdcmrPreventionControlWarn warn = tbCdcmrPreventionControlWarnMapper.selectByPrimaryKey(warnId);
        dto.setId(warn.getId());
        dto.setStatus(warn.getStatus());
        dto.setIsDeleted(warn.getIsDeleted());
        dto.setRemark(warn.getRemark());
        dto.setPreventionControlCode(warn.getPreventionControlCode());
        dto.setPreventionControlName(warn.getPreventionControlName());
        dto.setMaxLifeCycle(warn.getMaxLifeCycle());
        dto.setSmsSendTypeCode(warn.getSmsSendTypeCode());
        dto.setSmsSendTypeDesc(warn.getSmsSendTypeDesc());
        dto.setRuleList(tbCdcmrPreventionControlWarnRuleMapper.getRuleListByWarnId(warnId));
        return dto;
    }

    @Override
    public List<PreventionControlWarnDto> getPreventionControlWarnAllList(String preventionControlCode) {

        List<PreventionControlWarnDto> warnList = tbCdcmrPreventionControlWarnMapper.getAllList(preventionControlCode);
        List<TbCdcmrPreventionControlWarnRule> ruleList = tbCdcmrPreventionControlWarnRuleMapper.getAllRuleList();
        warnList.forEach(preventionControlWarnDto -> preventionControlWarnDto.setRuleList(ruleList.stream().filter(tbCdcmrPreventionControlWarnRule -> tbCdcmrPreventionControlWarnRule.getWarnId().equals(preventionControlWarnDto.getId())).collect(Collectors.toList())));
        return warnList;
    }

    @Override
    public void exportPreventionControlWarnRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.PREVENTION_CONTROL_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<PreventionControlWarnRuleExportDataDto> dataList = tbCdcmrPreventionControlWarnRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 修改状态名称
        dataList.forEach(this::getMaxLifeCycle);
        dataList.forEach(this::getTimeScope);
        dataList.forEach(this::getMedicalCount);

        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.PREVENTION_CONTROL_WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<CascadeVO> getPreventionControlNameList() {
        return getPreventionControl();
    }

    @Override
    public List<CascadeVO> getPreventionControl() {
        PreventionControlEnum[] result = PreventionControlEnum.values();
        List<CascadeVO> typeList = new ArrayList<>();

        for (PreventionControlEnum e : result) {
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setLabel(e.getName());
            cascadeVO.setValue(e.getCode());
            typeList.add(cascadeVO);
        }

        return typeList;
    }

    @Override
    public List<CascadeVO> getPreventionControlNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_PREVENTION_CONTROL);
//        List<CascadeVO> poisonTypeList = getPreventionControl();
//
//        Collections.sort(poisonTypeList);
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getPreventionControlDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        List<CascadeVO> resultList =poisonTypeList.stream().filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
//        return resultList;
    }


    private void getTimeScope(PreventionControlWarnRuleExportDataDto dto) {
        if (!StringUtils.isEmpty(dto.getTimeRange())) {
            if ("1".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "天");
            } else if ("2".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "周");
            } else if ("3".equals(dto.getTimeRangeUnit())) {
                dto.setTimeRange("≤" + dto.getTimeRange() + "月");
            }
        } else {
            dto.setTimeRange("/");
        }
    }

    private void getMaxLifeCycle(PreventionControlWarnRuleExportDataDto dto) {
        if (dto.getMaxLifeCycle() != null) {
            dto.setMaxLifeCycle(dto.getMaxLifeCycle() + "天");
        } else {
            dto.setMaxLifeCycle("/");
        }
    }

    private void getMedicalCount(PreventionControlWarnRuleExportDataDto dto) {
        if (dto.getMedicalCount() != null) {
            dto.setMedicalCount("≥" + dto.getMedicalCount());
        } else {
            dto.setMedicalCount("/");
        }
    }
}
