package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.entity.TbCdcmrScreenConfig;
import com.iflytek.cdc.admin.entity.UapAuthNode;
import com.iflytek.cdc.admin.service.ScreenLayoutConfigService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RestController
@Api(tags = "大屏布局配置接口")
public class ScreenLayoutConfigController {

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private UapUserService uapUserService;

    @Resource
    ScreenLayoutConfigService screenLayoutConfigService;

    @GetMapping("/{version}/pt/screenConfig/bigScreenMenu")
    @ApiOperation("获取大屏菜单")
    public List<UapAuthNode> getBigScreenMenu() {
        return uapUserService.getBigScreenMenu();
    }

    @GetMapping("/{version}/pt/screenConfig/getLayoutConfig")
    @ApiOperation("获取已保存布局信息")
    public TbCdcmrScreenConfig getLayoutConfig() {
        return screenLayoutConfigService.getScreenLayoutConfig();
    }

    @PostMapping("/{version}/pt/screenConfig/updateLayoutConfig")
    @ApiOperation("更新大屏布局配置")
    public void updateLayoutConfig(@RequestBody TbCdcmrScreenConfig layoutConfig, String loginUserId, String loginUserName) {
        screenLayoutConfigService.updateLayoutConfig(layoutConfig, loginUserId, loginUserName);
    }

    @PostMapping("/{version}/pt/screenConfig/updateDesensitization")
    @ApiOperation("更新大屏脱敏配置")
    public void updateDesensitization(@RequestBody TbCdcmrScreenConfig desensitizationConfig, String loginUserId, String loginUserName) {
        screenLayoutConfigService.updateDesensitization(desensitizationConfig, loginUserId, loginUserName);
    }

    @GetMapping("/{version}/pt/screenConfig/getDesensitization")
    @ApiOperation("查询大屏脱敏配置")
    public TbCdcmrScreenConfig getDesensitization() {
        return screenLayoutConfigService.getDesensitization();
    }
}
