package com.iflytek.cdc.admin.outbound.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_cdcmr_signal_sms_record")
public class SignalSmsRecord {
    public static final String TB_NAME = "tb_cdcmr_signal_sms_record";

    @ApiModelProperty(value = "主键")
    private String id;
    
    @ApiModelProperty(value = "信号id")
    private String signalId;
    
    @ApiModelProperty(value = "预警类型")
    private String warningType;
    
    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "外呼批次Id")
    private String batchId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "业务人员ID")
    private String userId;

    @ApiModelProperty(value = "业务人员名称")
    private String userName;

    @ApiModelProperty(value = "请求内容")
    private String requestContent;

    @ApiModelProperty(value = "响应内容")
    private String responseContent;
    
    @ApiModelProperty(value = "话术或短信id")
    private String speechOrSmsId;

    @ApiModelProperty(value = "状态")
    private String status;

 
} 