package com.iflytek.cdc.admin.outbound.model.dto.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 查询电话记录详情结果对象
 * <AUTHOR>
 */
@Data
@Builder
public class RecordDetailRs {

    @ApiModelProperty("记录id")
    private String recordId;

    @ApiModelProperty("姓名")
    private String dwellerName;

    @ApiModelProperty("手机号")
    private String telephone;

    @ApiModelProperty("电话拨打时间")
    private String callTime;

    @ApiModelProperty("任务开始时间")
    private String taskTime;

    @ApiModelProperty("方案名称")
    private String planName;

    @ApiModelProperty("话术模板名称")
    private String speechName;

    @ApiModelProperty("通话时长（秒）")
    private String callTimeLength;

    @ApiModelProperty("电话任务状态描述")
    private String callResult;

    @ApiModelProperty("任务状态: 0：成功,2：通话中,3：无法接通,4：关机,5：用户正忙,6：空号,7：号码错误,9：停机,15：客户接听后并主动挂机," +
            "20：用户未接,22：来电提醒,22：转来电提醒,23：呼入限制,26：网络故障,28：线路故障,30：呼叫失败,300 ：任务执行失败")
    private String resultCode;

    @ApiModelProperty("接通状态: 1：正常回答,2：自动留言,3：接听不便,4：居民已死亡,5：家属不能代答,6：不愿配合,7：号码错误,8：中断")
    private String endNode;

    @ApiModelProperty("音频地址（临时地址，建议保存，平台一周后清除）")
    private String localUrl;

    @ApiModelProperty("短信模板名称")
    private String smsName;

    @ApiModelProperty("短信内容")
    private String smsContent;

    @ApiModelProperty("会话内容")
    private String dialog;
}
