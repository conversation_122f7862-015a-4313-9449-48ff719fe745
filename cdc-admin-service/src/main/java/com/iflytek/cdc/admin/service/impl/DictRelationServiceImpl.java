package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.DictRelationConstants;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.constant.UidTableName;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.mapper.DictRelationMapper;
import com.iflytek.cdc.admin.mapper.InfectiousDiseasesMapper;
import com.iflytek.cdc.admin.sdk.entity.DictRelationFilter;
import com.iflytek.cdc.admin.sdk.pojo.DictValueInfo;
import com.iflytek.cdc.admin.sdk.pojo.InfectedValueInfo;
import com.iflytek.cdc.admin.service.DictRelationAsyncService;
import com.iflytek.cdc.admin.service.DictRelationService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.util.ExcelUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientResponseException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/25 13:53
 **/
@Slf4j
@Service("dictRelationService")
public class DictRelationServiceImpl implements DictRelationService {

    @Resource
    public BatchUidService batchUidService;

    @Resource
    public DictRelationMapper dMapper;

    @Resource
    public TermDictApi termDictApi;

    @Resource
    public RedisTemplate<String, Object> redisTemplate;

    @Resource
    public DictRelationAsyncService dictRelationAsyncService;

    @Resource
    public InfectiousDiseasesMapper infectiousDiseasesMapper;

    @Resource
    private MdmDataSyncService mdmDataSyncService;

    @Resource
    private ParamConfigService paramConfigService;

    @Override
    public List<DictRelationGroupDTO> queryDirectory(Integer pageNumber, Integer pageSize) {
        //定义mdm查询条件
        List<DictRelationGroupDTO> directoryDTOList = new ArrayList<>();
        try {
            List<TermDictInfo> allCodeValues = getDictInfo(pageNumber, pageSize);
            HashSet<DictRelationGroupDTO> directorySet = new HashSet<>();
            //先填充大分类数据
            for (TermDictInfo dict : allCodeValues) {
                DictRelationGroupDTO directory = new DictRelationGroupDTO();
                //获取分类名称
                directory.setDirectoryLabel(dict.getBusinessTypeName());
                //获取分类值
                directory.setDirectoryValue(dict.getBusinessTypeCode());
                directorySet.add(directory);
            }
            directoryDTOList.addAll(directorySet);
            //再填充目录数据
            for (DictRelationGroupDTO directory : directoryDTOList) {
                List<DictRelationDirectoryDTO> dirS = new ArrayList<>();
                for (TermDictInfo value : allCodeValues) {
                    if (value.getBusinessTypeCode().equals(directory.getDirectoryValue())) {
                        DictRelationDirectoryDTO dir = new DictRelationDirectoryDTO();
                        dir.setDirectoryLabel(value.getName());
                        dir.setDirectoryValue(value.getCode());
                        dirS.add(dir);
                    }
                }
                directory.setChildren(dirS);
            }
            return directoryDTOList;
        } catch (Exception e) {
            log.error("查询主数据平台字典编码信息异常：{}", e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    public List<TermDictInfo> getDictInfo(Integer pageNumber, Integer pageSize) {
        Long total = 1L;
        //获取得数目没有达到总条数时
        List<TermDictInfo> allCodeValues = new ArrayList<>();
        while (allCodeValues.size() < total) {
            //查询值域信息
            MdmPageData<TermDictInfo, TermDictInfoFilter> mdmData = getTermDictInfo(pageNumber, pageSize);
            //获取值域值
            List<TermDictInfo> codedValueInfos = mdmData.getEntities();
            //获取目标信息
            MdmPageRequest<TermDictInfoFilter> nextData = mdmData.getNext();
            //设置总条数
            total = nextData.getTotal();
            //添加数据到总数据
            allCodeValues.addAll(codedValueInfos);
            pageNumber++;
        }
        return allCodeValues;
    }

    public MdmPageData<TermDictInfo, TermDictInfoFilter> getTermDictInfo(Integer pageNumber, Integer pageSize) {
        //定义mdm查询条件
        MdmPageRequest<TermDictInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(pageNumber);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermDictInfoFilter filter = new TermDictInfoFilter();
        filter.setEnabled(Integer.valueOf(DictRelationConstants.YES_DELETE));
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            MdmPageData<TermDictInfo, TermDictInfoFilter> mdmData = termDictApi.getTermDictList(pageRequest);
            if (mdmData == null || mdmData.getNext() == null || mdmData.getEntities() == null) {
                throw new MedicalBusinessException("未查询到主数据平台字典目录信息");
            }
            return mdmData;
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }

    @Override
    public void insertDirectory(DictRelationDirectory directory, String loginUserId) {
        //判断目录是否被mdm删除
        judgeDirectory(directory);
        //如果没有被删除，根据编码获取目录的详细信息
        DictRelationDirectory exitDirectory = dMapper.queryDirectory(directory.getOriginDictCode(), directory.getTargetDictCode(), DictRelationConstants.NO_DELETE);
        //如果目录已存在，不允许新建
        if (exitDirectory != null) {
            throw new MedicalBusinessException("业务字典关联目录已存在,请重新选择");
        }
        //设置信息
        directory.setId(String.valueOf(batchUidService.getUid(UidTableName.RELATION_DIRECTORY)));
        directory.setCreateUser(loginUserId);
        directory.setUpdateUser(loginUserId);
        directory.setIsDeleted(DictRelationConstants.NO_DELETE);
        //新增字典目录
        dMapper.insertDirectory(directory);
    }

    private void judgeDirectory(DictRelationDirectory directory) {
        //判断mdm该字典有没有被删除或者禁用了
        MdmPageRequest<TermDictInfoFilter> pageRequest = new MdmPageRequest<>();
        MdmPageData<TermDictInfo, TermDictInfoFilter> pageData;
        TermDictInfoFilter filter = new TermDictInfoFilter();
        filter.setCode(directory.getOriginDictCode());
        pageRequest.setFilter(filter);
        //调用mdm获取数据
        pageData = termDictApi.getTermDictList(pageRequest);
        //如果为空，则被删除或者被禁用
        if (CollUtil.isEmpty(pageData.getEntities())) {
            throw new MedicalBusinessException(directory.getOriginDictName() + "被删除或已被禁用,无法操作");
        }
        filter.setCode(directory.getTargetDictCode());
        pageRequest.setFilter(filter);
        pageData = termDictApi.getTermDictList(pageRequest);
        if (CollUtil.isEmpty(pageData.getEntities())) {
            throw new MedicalBusinessException(directory.getTargetDictName() + "被删除或已被禁用，无法操作");
        }
    }

    @Override
    public DictRelationDirectory queryDirectoryById(String directoryId) {
        return dMapper.queryDirectoryById(directoryId);
    }

    @Override
    public List<DictRelationDirectory> queryDirectoryList(String directoryName) {
        return dMapper.queryDirectoryList(DictRelationConstants.NO_DELETE, directoryName);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteDirectory(String id, String loginUserId) {
        if (StringUtils.isBlank(id)) {
            throw new MedicalBusinessException("请选择需要删除的字典关联目录");
        }
        //删除目录
        dMapper.deleteDirectory(DictRelationConstants.YES_DELETE, id, loginUserId);
        //删除关联关系
        dMapper.deleteRelationById(id);
    }

    @Override
    public PageInfo<DictValue> queryOriginDictValueList(SearchDictValueDTO sd) {
        PageInfo<DictValue> pageInfo = new PageInfo<>();
        //根据目录id查询关联目录详细信息
        DictRelationDirectory directory = dMapper.queryDirectoryById(sd.getDirectoryId());
        //如果原始便编码为空
        if (StringUtils.isBlank(sd.getOriginDictValueCode())) {
            //则查询原始编码
            sd.setDictCode(directory.getOriginDictCode());
        } else {
            //否则查询关联编码
            sd.setDictCode(directory.getTargetDictCode());
        }
        judgeDirectory(directory);
        //如果关联状态的查询条件为空
        if (StringUtils.isBlank(sd.getRelationStatus())) {
            getNoStatusPage(sd, pageInfo);
        } else {
            //如果查询已关联的数据
            if (sd.getRelationStatus().equals(DictRelationConstants.YES_DELETE)) {
                if (StringUtils.isBlank(sd.getOriginDictValueCode())) {
                    pageInfo = getOriginDictRelation(sd);
                } else {
                    pageInfo = getTargetDictRelation(sd);
                }

            }
            //如果查询未关联的数据
            if (sd.getRelationStatus().equals(DictRelationConstants.NO_DELETE)) {
                pageInfo = getNoRelationPage(sd);
            }
        }
        return pageInfo;
    }


    private void getNoStatusPage(SearchDictValueDTO sd, PageInfo<DictValue> pageInfo) {
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmPageData = getMdmCodeInfo(sd, sd.getPageIndex(), sd.getPageSize());
        List<TermCodedValueInfo> codedValueInfos = mdmPageData.getEntities();
        //获取目标信息
        MdmPageRequest<TermCodedValueInfoFilter> nextData = mdmPageData.getNext();
        //设置分页参数
        pageInfo.setPageNum(sd.getPageIndex());
        pageInfo.setPageSize(sd.getPageSize());
        pageInfo.setTotal(nextData.getTotal());
        pageInfo.setPages(getPages(Math.toIntExact(nextData.getTotal()), sd.getPageSize()));
        List<DictValue> dictValueList = new ArrayList<>();
        //格式化数据，判断关联状态
        for (TermCodedValueInfo codeValue : codedValueInfos) {
            DictValue dictValue = new DictValue();
            List<DictRelation> relations;
            //如果原始编码为空
            if (StringUtils.isBlank(sd.getOriginDictValueCode())) {
                //查询原始编码数据
                relations = dMapper.queryDictRelation(sd.getDirectoryId(), codeValue.getCodedValue(), null);
            } else {
                //查询关联编码数据
                relations = dMapper.queryDictRelation(sd.getDirectoryId(), sd.getOriginDictValueCode(), codeValue.getCodedValue());
            }
            //判断状态，如果关联表的数据经过过滤后长度为0，则未关联 反之为已关联
            dictValue.setRelationStatus(CollUtil.isEmpty(relations) ? DictRelationConstants.NO_DELETE : DictRelationConstants.YES_DELETE);
            dictValue.setDictValueCode(codeValue.getCodedValue());
            dictValue.setDictValueName(codeValue.getDescription());
            dictValueList.add(dictValue);
        }
        pageInfo.setList(dictValueList);
    }

    private PageInfo<DictValue> getNoRelationPage(SearchDictValueDTO sd) {
        long time = System.currentTimeMillis();
        PageInfo<DictValue> pageInfo;
        List<DictValue> relations;
        LinkedList<DictValue> mdmLocalCodes;
        //拼接redis的key
        String redisKey = Constants.REDIS_KEY + sd.getDictValueCode() + sd.getDictValueName() + sd.getDictCode();
        //查询redis缓存数据
        Object redisData = redisTemplate.opsForValue().get(redisKey);
        //再查询已关联的数据
        relations = getDictValues(sd);
        mdmLocalCodes = new LinkedList<>(JSONUtil.toList(JSONUtil.parseArray(redisData), DictValue.class));
        //如果redis没查询到数据，则查询mdm数据
        if (CollUtil.isEmpty(mdmLocalCodes)) {
            List<DictValue> localData = new ArrayList<>();
            List<TermCodedValueInfo> valueInfoList = queryTermCodeValueInfo(sd, DictRelationConstants.PAGE_INDEX, DictRelationConstants.TERM_DICT_PAGE_SIZE);
            for (TermCodedValueInfo termCodedValueInfo : valueInfoList) {
                DictValue dr = new DictValue();
                dr.setDictValueCode(termCodedValueInfo.getCodedValue());
                dr.setDictValueName(termCodedValueInfo.getDescription());
                dr.setRelationStatus(DictRelationConstants.NO_DELETE);
                localData.add(dr);
            }
            mdmLocalCodes = new LinkedList<>(localData);
            //将数据放入redis中
            redisTemplate.opsForValue().set(redisKey, JSONUtil.toJsonStr(mdmLocalCodes));
            redisTemplate.expire(redisKey, 10, TimeUnit.MINUTES);
        }
        Iterator<DictValue> mdmIter = mdmLocalCodes.iterator();
        List<DictValue> mdmExistData = new ArrayList<>();
        //迭代全部数据
        while (mdmIter.hasNext()) {
            DictValue mdm = mdmIter.next();
            //如果已关联的数据中没有则是未关联的数据
            if (!relations.contains(mdm)) {
                mdmExistData.add(mdm);
            }
        }
        //手动分页
        pageInfo = listPage(mdmExistData, sd.getPageIndex(), sd.getPageSize());
        log.info("查询共消耗时间:{}", System.currentTimeMillis() - time);
        return pageInfo;
    }

    private List<DictValue> getDictValues(SearchDictValueDTO sd) {
        List<DictValue> relations;
        if (StringUtils.isBlank(sd.getOriginDictValueCode())) {
            relations = dMapper.originDictRelationList(sd);
        } else {
            relations = dMapper.targetDictRelationList(sd);
        }
        return relations;
    }

    public <T> PageInfo<T> listPage(List<T> list, Integer pageIndex, Integer pageSize) {
        if (CollUtil.isEmpty(list)) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo<T> pageInfo = new PageInfo<>();
        try {
            //记录总数
            int total = list.size();
            //页数
            Integer pageTotal = getPages(total, pageSize);
            //开始索引
            int fromIndex;
            //结束索引
            int toIndex;
            if (!pageIndex.equals(pageTotal)) {
                fromIndex = (pageIndex - 1) * pageSize;
                toIndex = fromIndex + pageSize;
            } else {
                fromIndex = (pageIndex - 1) * pageSize;
                toIndex = total;
            }
            List<T> subList = list.subList(fromIndex, toIndex);
            pageInfo.setPageNum(pageIndex);
            pageInfo.setPageSize(pageSize);
            pageInfo.setTotal(total);
            pageInfo.setPages(pageTotal);
            pageInfo.setList(subList);
        } catch (Exception e) {
            log.error("查询未关联字典值域异常：{}", e);
            throw new MedicalBusinessException("系统异常");
        }
        return pageInfo;
    }

    public List<TermCodedValueInfo> queryTermCodeValueInfo(SearchDictValueDTO sd, Integer pageNumber, Integer pageSize) {
        //定义查询时间
        long startTime = System.currentTimeMillis();
        try {
            Long total = 1L;
            LinkedList<TermCodedValueInfo> allCodeValues = new LinkedList<>();
            //获取得数目没有达到总条数时
            while (allCodeValues.size() < total) {
                //查询值域信息
                MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = getMdmCodeInfo(sd, pageNumber, pageSize);
                //获取值域值
                List<TermCodedValueInfo> codedValueInfos = mdmData.getEntities();
                //获取目标信息
                MdmPageRequest<TermCodedValueInfoFilter> nextData = mdmData.getNext();
                //设置总条数
                total = nextData.getTotal();
                //添加数据到总数据
                allCodeValues.addAll(codedValueInfos);
                pageNumber++;
            }
            log.info("查询mdm数据共需要时间：{}", System.currentTimeMillis() - startTime);
            return allCodeValues;
        } catch (Exception e) {
            log.error("查询主数据平台字典编码信息异常：{}", e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    public MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> getMdmCodeInfo(SearchDictValueDTO sd, Integer pageIndex, Integer pageSize) {
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(pageIndex);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode(sd.getDictCode());
        filter.setCodedValue(sd.getDictValueCode());
        filter.setCodedValueDesc(sd.getDictValueName());
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            return termDictApi.getTermCodedValueList(pageRequest);
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }

    public PageInfo<DictValue> getOriginDictRelation(SearchDictValueDTO sd) {
        //查询已关联的数据
        List<DictValue> relationList = dMapper.originDictRelationList(sd).stream().collect(
                //根据字典值进行去重
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DictValue::getDictValueCode))), ArrayList::new)
        );
        //如果不为空
        if (CollUtil.isNotEmpty(relationList)) {
            //状态设置为已关联
            relationList.forEach(e -> e.setRelationStatus(DictRelationConstants.YES_DELETE));
        }
        return listPage(relationList, sd.getPageIndex(), sd.getPageSize());
    }

    public PageInfo<DictValue> getTargetDictRelation(SearchDictValueDTO sd) {
        //查询关联数据
        List<DictValue> relationList = dMapper.targetDictRelationList(sd);
        //去重
        HashSet<DictValue> hashSet = new HashSet<>(relationList);
        //如果不为空。则将字典状态改为已关联
        if (CollUtil.isNotEmpty(hashSet)) {
            relationList.forEach(e -> e.setRelationStatus(DictRelationConstants.YES_DELETE));
        }
        //手动分页
        return listPage(new ArrayList<>(hashSet), sd.getPageIndex(), sd.getPageSize());
    }

    /**
     * 计算有多少页数
     *
     * @param total, pageSize
     **/
    public Integer getPages(Integer total, Integer pageSize) {
        try {
            int result = total % pageSize;
            int page = total / pageSize;
            return result == 0 ? page : page + 1;
        } catch (Exception e) {
            log.error("计算分页页数异常：{}", e);
            throw new MedicalBusinessException("系统异常");
        }

    }

    @Override
    public void insertRelation(DictRelation dr, String loginUserId) {
        //根据字典目录id查询字典目录信息
        DictRelationDirectory directory = dMapper.queryDirectoryById(dr.getDirectoryId());
        //判断字典目录是否存在
        judgeDirectory(directory);
        judgeRelation(dr, directory);
        dr.setCreateUser(loginUserId);
        dr.setUpdateUser(loginUserId);
        dr.setId(String.valueOf(batchUidService.getUid(UidTableName.RELATION_DETAIL)));
        dMapper.insertRelation(dr);
    }

    private void judgeRelation(DictRelation dr, DictRelationDirectory directory) {
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> data;
        //定义查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        TermCodedValueInfoFilter codeFilter = new TermCodedValueInfoFilter();
        codeFilter.setDictCode(directory.getOriginDictCode());
        codeFilter.setCodedValue(dr.getOriginDictValueCode());
        pageRequest.setFilter(codeFilter);
        //获取mdm数据
        data = termDictApi.getTermCodedValueList(pageRequest);
        if (CollUtil.isEmpty(data.getEntities())) {
            throw new MedicalBusinessException(dr.getOriginDictValueName() + "已被删除，无法操作");
        }
        //定义目标值mdm查询条件
        codeFilter.setDictCode(directory.getTargetDictCode());
        codeFilter.setCodedValue(dr.getTargetDictValueCode());
        pageRequest.setFilter(codeFilter);
        //获取mdm目标值数据
        data = termDictApi.getTermCodedValueList(pageRequest);
        if (CollUtil.isEmpty(data.getEntities())) {
            throw new MedicalBusinessException(dr.getTargetDictValueName() + "已被删除，无法操作");
        }
    }

    @Override
    public void deleteRelation(DeleteRelationDTO relationDTO) {
        dMapper.deleteRelation(relationDTO);
    }

    @Override
    public void exportRelation(SearchExportRelationDTO sd, HttpServletResponse response) {
        //判断字典目录是否被删除或被禁用
        for (String id : sd.getDirectoryIds()) {
            DictRelationDirectory directory = dMapper.queryDirectoryById(id);
            judgeDirectory(directory);
        }
        try {
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + DictRelationConstants.EXCEL_NAME);
            List<ExportDictRelation> exportList = dMapper.queryExportRelation(sd);

            //校验是否超出文件导出最大值
            paramConfigService.checkExportMax(exportList);
            //文件导出
            ExcelUtils.excelExport(exportList, response, null);
        } catch (Exception e) {
            log.error("导出业务关联字典异常:{}", e);
            throw new MedicalBusinessException("导出业务关联字典异常");
        }

    }

    @Override
    public void updateRelationName(UpdateRelationDTO ud, String loginUserId) {
        ud.setUpdateUser(loginUserId);
        dMapper.updateRelationName(ud);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<DictValueInfo> queryDictValueInfo(DictRelationFilter filter) {
        if (StringUtils.isBlank(filter.getOriginDictCode()) || StringUtils.isBlank(filter.getTargetDictCode())) {
            throw new MedicalBusinessException("字典编码不能为空");
        }
        if (StringUtils.isBlank(filter.getDictValueCode()) && StringUtils.isBlank(filter.getDictValueName())) {
            throw new MedicalBusinessException("字典值不能为空");
        }
        //判断字典编码是否存在
        DictRelationDirectory relationDirectory = dMapper.queryDirectory(filter.getOriginDictCode(), filter.getTargetDictCode(), DictRelationConstants.NO_DELETE);
        if (relationDirectory == null) {
            throw new MedicalBusinessException("字典关联关系不存在");
        }
        //判断字典目录在mdm是否还存在
        judgeDirectory(relationDirectory);
        //判断数据是否存在
        dictRelationAsyncService.syncMdmData(filter.getOriginDictCode(), filter.getDictValueCode(), filter.getDictValueName(), relationDirectory, "origin");
        if (StringUtils.isNotBlank(filter.getDictValueCode())) {
            filter.setDictValueName(null);
        }
        List<DictValueInfo> targetValueInfos = dMapper.queryDictValueInfo(relationDirectory.getId(), filter.getDictValueCode(), filter.getDictValueName());
        if (CollUtil.isNotEmpty(targetValueInfos)) {
            for (DictValueInfo valueInfo : targetValueInfos) {
                dictRelationAsyncService.syncMdmData(filter.getTargetDictCode(), valueInfo.getDictValueCode(), valueInfo.getDictValueName(), relationDirectory, "target");
            }
        }
        return dMapper.queryDictValueInfo(relationDirectory.getId(), filter.getDictValueCode(), filter.getDictValueName());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<DictValueInfo> queryOriginDictValueInfo(DictRelationFilter filter) {
        if (StringUtils.isBlank(filter.getOriginDictCode()) || StringUtils.isBlank(filter.getTargetDictCode())) {
            throw new MedicalBusinessException("字典编码不能为空");
        }
        if (StringUtils.isBlank(filter.getDictValueCode()) && StringUtils.isBlank(filter.getDictValueName())) {
            throw new MedicalBusinessException("字典值不能为空");
        }
        //判断字典编码是否存在
        DictRelationDirectory relationDirectory = dMapper.queryDirectory(filter.getOriginDictCode(), filter.getTargetDictCode(), DictRelationConstants.NO_DELETE);
        if (relationDirectory == null) {
            throw new MedicalBusinessException("字典关联关系不存在");
        }
        //判断字典目录在mdm是否还存在
        judgeDirectory(relationDirectory);
        //判断数据是否存在
        dictRelationAsyncService.syncMdmData(filter.getTargetDictCode(), filter.getDictValueCode(), filter.getDictValueName(), relationDirectory, "target");
        if (StringUtils.isNotBlank(filter.getDictValueCode())) {
            filter.setDictValueName(null);
        }
        List<DictValueInfo> targetValueInfos = dMapper.queryOriginDictValueInfo(relationDirectory.getId(), filter.getDictValueCode(), filter.getDictValueName());
        if (CollUtil.isNotEmpty(targetValueInfos)) {
            for (DictValueInfo valueInfo : targetValueInfos) {
                dictRelationAsyncService.syncMdmData(filter.getOriginDictCode(), valueInfo.getDictValueCode(), valueInfo.getDictValueName(), relationDirectory, "origin");
            }
        }
        return dMapper.queryOriginDictValueInfo(relationDirectory.getId(), filter.getDictValueCode(), filter.getDictValueName());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<InfectedValueInfo> queryInfectedValueDetail(DictRelationFilter filter) {
        List<InfectedValueInfo> infectedValueInfoList = new ArrayList<>();
        //根据诊断查询病种
        List<DictValueInfo> dictValueInfos = queryOriginDictValueInfo(filter);
        if (CollUtil.isNotEmpty(dictValueInfos) && StrUtil.isNotBlank(dictValueInfos.get(0).getDictValueCode())) {
            //存在病种信息
            InfectedValueInfo infectedValueInfo = new InfectedValueInfo();
            infectedValueInfo.setInfectedSubCode(dictValueInfos.get(0).getDictValueCode());
            infectedValueInfo.setInfectedSubName(dictValueInfos.get(0).getDictValueName());
            //获取病种类别以及病种业务类型
            InfectiousDiseases infectiousDiagnosis = infectiousDiseasesMapper.findInfectiousDiagnosisByInfectedCode(infectedValueInfo.getInfectedSubCode());
            if (ObjectUtil.isNotEmpty(infectiousDiagnosis)) {
                infectedValueInfo.setInfectedTypeCode(infectiousDiagnosis.getDiseasesType());
                infectedValueInfo.setInfectedTypeName(getTargetName(MdmDataSyncConstants.INFECTYPE_CODE, infectiousDiagnosis.getDiseasesType()));
                infectedValueInfo.setInfectedCode(infectiousDiagnosis.getDiseasesClassify());
                infectedValueInfo.setInfectedName(getTargetName(MdmDataSyncConstants.INFECCLFY_CODE, infectiousDiagnosis.getDiseasesClassify()));
            }
            infectedValueInfoList.add(infectedValueInfo);
        }

        return infectedValueInfoList;
    }

    /**
     * 根基code获取名称
     *
     * @param originCode 字典List
     * @param codeValue        码值
     * @return String
     */
    public String getTargetName(String originCode, String codeValue) {
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(originCode);
        String name = "";

        if (StrUtil.isNotBlank(codeValue)) {
            MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfoByCode(di, codeValue);
            List<TermCodedValueInfo> codedValueInfos = mdmData.getEntities();
            if (CollUtil.isNotEmpty(codedValueInfos)) {
                name = codedValueInfos.get(0).getDescription();
            }
        }

        return name;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteRelationByMdmMessage(String dictCode, String dictValueCode) {
        if (StringUtils.isBlank(dictCode) || StringUtils.isBlank(dictValueCode)) {
            log.error("mdm的mq消息内容异常");
            return;
        }
        //先查询关联目录
        List<String> originDirectoryIds = dMapper.queryOriginDirectoryIds(dictCode, DictRelationConstants.NO_DELETE);
        //先查询关联目录
        List<String> targetDirectoryIds = dMapper.queryTargetDirectoryIds(dictCode, DictRelationConstants.NO_DELETE);
        //删除关联数据
        if (CollUtil.isNotEmpty(originDirectoryIds)) {
            dMapper.deleteOriginRelationByMdms(dictValueCode, originDirectoryIds);
        }
        if (CollUtil.isNotEmpty(targetDirectoryIds)) {
            dMapper.deleteTargetRelationByMdms(dictValueCode, targetDirectoryIds);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateRelationNameByMdmMessage(String dictCode, String dictValueCode, String dictValueCodeName) {
        if (StringUtils.isBlank(dictCode) || StringUtils.isBlank(dictValueCode)) {
            log.error("mdm的mq消息内容异常");
            return;
        }
        //先查询关联目录
        List<DictRelationDirectory> directors = dMapper.queryDirectorys(dictCode, DictRelationConstants.NO_DELETE);
        if (CollUtil.isNotEmpty(directors)) {
            //判断是origin还是target
            for (DictRelationDirectory directory : directors) {
                if (directory.getOriginDictCode().equals(dictCode)) {
                    //修改原始名称
                    dMapper.updateOriginNameByMdm(dictValueCode, directory.getId(), dictValueCodeName);
                } else if (directory.getTargetDictCode().equals(dictCode)) {
                    //修改目标名称
                    dMapper.updateTargetNameByMdm(dictValueCode, directory.getId(), dictValueCodeName);
                }
            }
        }
    }

    @Override
    public Boolean judgeIsUpdateName(String dictCode, String dictValueCode) {
        //根据code获取id
        if (StringUtils.isBlank(dictCode) || StringUtils.isBlank(dictValueCode)) {
            log.error("mdm的mq消息内容异常");
            return false;
        }
        List<String> ids = dMapper.queryRelationId(dictCode, dictValueCode, DictRelationConstants.NO_DELETE);
        return !CollUtil.isEmpty(ids);
    }


    @Override
    public ResponseResult executeSyncMdmData(String code, String loginUserId, boolean flag) {
        //生成batchId
        String batchId = String.valueOf(batchUidService.getUid(UidTableName.RELATION_DIRECTORY));
        dictRelationAsyncService.createTask(batchId, loginUserId);
        return new ResponseResult(batchId);
    }


    @Override
    public String syncMdmDataGetStatus(String code, String loginUserId, String batchId) {
        return Objects.requireNonNull(redisTemplate.opsForValue().get(batchId)).toString();
    }
}
