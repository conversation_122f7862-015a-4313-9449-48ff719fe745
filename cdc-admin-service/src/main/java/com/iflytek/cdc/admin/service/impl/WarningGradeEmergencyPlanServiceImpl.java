package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.cdc.admin.dto.WarningGradeEmergencyPlanVO;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan;
import com.iflytek.cdc.admin.mapper.TbCdcAttachmentMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeEmergencyPlanMapper;
import com.iflytek.cdc.admin.service.WarningGradeEmergencyPlanService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarningGradeEmergencyPlanServiceImpl implements WarningGradeEmergencyPlanService {
    @Resource
    TbCdcmrWarningGradeEmergencyPlanMapper tbCdcmrWarningGradeEmergencyPlanMapper;
    @Resource
    BatchUidService batchUidService;
    @Resource
    TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Override
    public List<WarningGradeEmergencyPlanVO> getByConfigId(String configId) {
        List<TbCdcmrWarningGradeEmergencyPlan> plans = tbCdcmrWarningGradeEmergencyPlanMapper.getByConfigId(configId);
        List<WarningGradeEmergencyPlanVO> resultList = new ArrayList<>();
        if (plans != null && plans.size() > 0) {
            List<String> attachmentIds = plans.stream().map(TbCdcmrWarningGradeEmergencyPlan::getAttachmentId).collect(Collectors.toList());
            List<TbCdcAttachment> attachmentList = tbCdcAttachmentMapper.selectByPrimaryKeys(attachmentIds);
            plans.forEach(e -> {
                WarningGradeEmergencyPlanVO planVO = new WarningGradeEmergencyPlanVO();
                BeanUtil.copyProperties(e, planVO);
                attachmentList.forEach(attachment -> {
                    if (attachment.getId().equals(e.getAttachmentId())) {
                        planVO.setAttachmentName(attachment.getAttachmentName());
                        planVO.setAttachmentPath(attachment.getAttachmentPath());
                        resultList.add(planVO);
                    }
                });
            });
        }
        return resultList;
    }

    @Override
    public void updateWarningGradeEmergencyPlan(List<TbCdcmrWarningGradeEmergencyPlan> planList, String loginUserId, String configId) {
        if (planList != null && planList.size() > 10) {
            throw new MedicalBusinessException("超出附件数量限制!");
        }
        planList.forEach(plan -> {
            if (plan.getId() == null) {
                plan.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_warning_emergency_plan")));
                plan.setConfigId(configId);
            }
        });

        tbCdcmrWarningGradeEmergencyPlanMapper.deleterByConfigId(configId);
        tbCdcmrWarningGradeEmergencyPlanMapper.insertPlans(planList);
    }

    @Override
    public List<TbCdcAttachment> getByTypeAndDisease(String configType, String diseaseCode) {
        List<String> attachmentIds = tbCdcmrWarningGradeEmergencyPlanMapper.getAttachmentIds(configType, diseaseCode);
        if (attachmentIds != null && attachmentIds.size() > 0) {
            return tbCdcAttachmentMapper.selectByPrimaryKeys(attachmentIds);
        } else {
            return new ArrayList<>();
        }
    }
}
