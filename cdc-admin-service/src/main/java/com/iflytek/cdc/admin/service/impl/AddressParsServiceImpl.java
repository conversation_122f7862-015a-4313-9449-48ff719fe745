package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.UploadExcelColumn;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.ExportAddressDTO;
import com.iflytek.cdc.admin.dto.SearchAddressDTO;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.mapper.AddressParsMapper;
import com.iflytek.cdc.admin.mapper.RegionMapper;
import com.iflytek.cdc.admin.service.AddressParsService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/27 14:23
 **/
@Service
@Slf4j
public class AddressParsServiceImpl implements AddressParsService {
    @Resource
    private ParamConfigService paramConfigService;

    private final AddressParsMapper aMapper;

    @Resource
    RegionMapper regionMapper;

    public AddressParsServiceImpl(AddressParsMapper aMapper) {
        this.aMapper = aMapper;
    }

    @Override
    public PageInfo<StandardAddress> queryStandardAddressList(SearchAddressDTO sd) {
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        List<StandardAddress> addressStandards = aMapper.queryStandardAddressList(sd);
        return new PageInfo<>(addressStandards);
    }

    @Override
    public PageInfo<RelationAddress> queryRelationAddressList(SearchAddressDTO sd) {
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        List<RelationAddress> relationAddresses = aMapper.queryRelationAddressList(sd);
        return new PageInfo<>(relationAddresses);
    }

    @Override
    public void deleteRelationAddress(String id) {
        aMapper.deleteRelationAddress(id);
    }

    @Override
    public void exportAddress(ExportAddressDTO ed, HttpServletResponse response) {
        XSSFWorkbook wb = null;
        OutputStream out = null;
        List<ExportAddress> content = aMapper.queryExportList(ed);
        paramConfigService.checkExportMax(content);
        try {
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + Constants.EXCEL_ADDRESS_TEMPLATE_NAME);
            //获取excel模板存在的路径
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(Constants.EXCEL_ADDRESS_TEMPLATE_PATH);
            //生成工作簿
            if (is != null) {
                wb = new XSSFWorkbook(is);
                //内容map
                Map<String, Object> fileContentMap = new HashMap<>(20);
                List<String> uploadFileName = new ArrayList<>();
                //反射获取需要导出的字段名称
                extracted(new ExportAddress(), fileContentMap, uploadFileName);
                XSSFSheet sheet = wb.getSheetAt(0);
                //填充excel内容
                for (int i = 1; i <= content.size(); i++) {
                    XSSFRow row = sheet.createRow(i);
                    for (int j = 0; j < uploadFileName.size(); j++) {
                        //获取每一行的单元格
                        XSSFCell cell = row.createCell(j);
                        if ((int) fileContentMap.get(uploadFileName.get(j)) == j) {
                            StringBuilder sb = new StringBuilder();
                            //反射get方法获取字段的值
                            sb.append("get").append(uploadFileName.get(j).substring(0, 1).toUpperCase()).append(uploadFileName.get(j).substring(1));
                            Method method = content.get(i - 1).getClass().getMethod(sb.toString());
                            Object fieldValue = method.invoke(content.get(i - 1));
                            //填充内容
                            if (fieldValue != null) {
                                //设置单元的值
                                cell.setCellValue(fieldValue.toString());
                            }
                        }
                    }
                }
                out = response.getOutputStream();
                wb.write(out);
                out.flush();
            }
        } catch (Exception e) {
            log.error("导出标准地址数据异常", e);
            throw new MedicalBusinessException("导出标准地址数据异常");
        } finally {
            try {
                wb.close();
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public List<CascadeVO> getAllRegion() {
        List<Area> list = regionMapper.findAll();
        //先获取最外级省
        List<CascadeVO> cascadeVOList = list.stream().filter(a -> !StringUtils.isBlank(a.getProvince())).map(
                a -> CascadeVO.fromArea(a, a.getProvince())
        ).collect(Collectors.toList());
        //再获取市

        //省级用户 直接全塞
        cascadeVOList.forEach(areaVO -> {
            List<CascadeVO> cityList = list.stream().filter(a -> a.getParentId().equals(Long.parseLong(areaVO.getValue())))
                    .map(a -> CascadeVO.fromArea(a, a.getCity())).collect(Collectors.toList());
            //省级和市级用户都能看到完整的一个或若干个市下的所有区级行政规划，都会用到此方法来填充第三层数据
            cityList.forEach(city -> {
                List<CascadeVO> districtList = list.stream().filter(c -> c.getParentId().equals(Long.parseLong(city.getValue())))
                        .map(d -> CascadeVO.fromArea(d, d.getDistrict())).collect(Collectors.toList());
                city.setChildren(districtList);
            });
            areaVO.setChildren(cityList);

        });

        return cascadeVOList;
    }

    /**
     * 判断字段是否有excel中需要导入的内容
     *
     * @return
     **/
    private void extracted(ExportAddress ea, Map<String, Object> fileContentMap, List<String> uploadFileName) {
        Field[] fields = ea.getClass().getDeclaredFields();
        //获取上传的excel字段
        for (Field field : fields) {
            //判断是否有该注解
            boolean annotationPresent = field.isAnnotationPresent(UploadExcelColumn.class);
            if (annotationPresent) {
                //将字段的名称和排序放入到map中
                fileContentMap.put(field.getName(), field.getAnnotation(UploadExcelColumn.class).sort());
                uploadFileName.add(field.getName());
            }
        }
    }
}
