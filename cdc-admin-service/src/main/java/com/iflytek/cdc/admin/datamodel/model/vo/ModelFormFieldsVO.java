package com.iflytek.cdc.admin.datamodel.model.vo;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据模型表单的字段VO
 * */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelFormFieldsVO {

    @ApiModelProperty(value = "模型id")
    private String modelId;

    @ApiModelProperty(value = "模型name")
    private String modelName;

    @ApiModelProperty(value = "模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "模型表单列表")
    private List<ModelFormVO> modelFormList;

    @Data
    public static class ModelFormVO {

        @ApiModelProperty(value = "模型表单id")
        private String modelFormId;

        @ApiModelProperty(value = "模型表单名称")
        private String formName;

        @ApiModelProperty(value = "表单排序标识")
        private Integer orderFlag;

        @ApiModelProperty(value = "前端生成表单唯一标识")
        private String field;

        @ApiModelProperty(value = "模型表单列表")
        private List<FormFieldsVO> formFieldList;
    }

    @Data
    public static class FormFieldsVO {

        @ApiModelProperty(value = "表单中字段的名称")
        private String title;

        @ApiModelProperty(value = "前端生成字段唯一标识")
        private String field;

        @ApiModelProperty(value = "表单中字段所属表id")
        private String tableId;

        @ApiModelProperty(value = "表单中字段id")
        private String columnId;

        @ApiModelProperty(value = "字段所处表名称")
        private TbCdcdmMetadataTableInfo table;

        @ApiModelProperty(value = "字段名称")
        private TbCdcdmMetadataTableColumnInfo column;

    }
}
