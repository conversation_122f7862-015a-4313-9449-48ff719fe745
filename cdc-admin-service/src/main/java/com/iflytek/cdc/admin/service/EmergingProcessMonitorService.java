package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingMonitorConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.model.mr.dto.EmergingMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EmergingDiseaseInfoVO;

import java.util.List;

public interface EmergingProcessMonitorService extends IService<TbCdcmrEmergingMonitorConfig>{


    List<EmergingDiseaseInfoVO> getEmergingTreeInfo();

    String editSubEmergingInfo(TbCdcmrEmergingDiseaseInfo emergingDiseaseInfo);

    void deleteSubEmergingInfo(String id);

    List<TbCdcmrEmergingMonitorConfig> getEmergingInfoConfigs(EmergingMonitorConfigQueryDTO queryDTO);

    void editEmergingProcessDefinition(String diseaseInfoId, List<TbCdcmrEmergingMonitorConfig> emergingMonitorConfigs);

    List<String> getEmergingDiseaseCodeByName(String diseaseName);
}
