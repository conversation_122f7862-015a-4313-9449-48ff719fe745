package com.iflytek.cdc.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.datamodel.service.ValueDomainService;
import com.iflytek.cdc.admin.dto.DiseaseLevelPair;
import com.iflytek.cdc.admin.dto.EventChargePersonDTO;
import com.iflytek.cdc.admin.dto.EventChargePersonQuery;
import com.iflytek.cdc.admin.entity.TbCdcmrEventChargePerson;
import com.iflytek.cdc.admin.entity.UapUserPo;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.enums.DataDictTypeEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.EventLevelEnum;
import com.iflytek.cdc.admin.enums.EventTypeEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrEventChargePersonMapper;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.EventChargePersonService;
import com.iflytek.cdc.admin.service.province.DimensionService;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.cdc.admin.vo.EventChargePersonVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EventChargePersonServiceImpl  extends ServiceImpl<TbCdcmrEventChargePersonMapper, TbCdcmrEventChargePerson> implements EventChargePersonService {
    @Resource
    private TbCdcmrEventChargePersonMapper tbCdcmrEventChargePersonMapper;
    @Resource
    private BatchUidService batchUidService;
    @Resource
    private ValueDomainService valueDomainService;

    @Resource
    private CommonUtils commonUtils;

    @Resource
    private DimensionService dimensionService;

    public static final ThreadLocal<UapUserPo> USER_INFO = new ThreadLocal<>();
    @Override
    public void changeStatus(EventChargePersonDTO input) {
        TbCdcmrEventChargePerson entity = loadById(input.getId());
        if (entity == null) {
            throw new MedicalBusinessException("请先编辑配置");
        }
        entity.setStatus(Integer.valueOf(input.getIsEnable()));
        tbCdcmrEventChargePersonMapper.updateById(entity);
    }

    @Override
    public void batchSave(List<EventChargePersonDTO> inputs) {

        List<String> ids = inputs.stream()
                .map(EventChargePersonDTO::getId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        Map<String, TbCdcmrEventChargePerson> existedMap = new HashMap<>();
        if (ids.size() > 0){
            //查询已存在的责任人配置
            existedMap = tbCdcmrEventChargePersonMapper.bulkLoadByIds(ids).stream()
                    .collect(Collectors.toMap(TbCdcmrEventChargePerson::getId, t -> t));
        }
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        List<TbCdcmrEventChargePerson> entities = new ArrayList<>();
        for (EventChargePersonDTO input : inputs){
            TbCdcmrEventChargePerson entity = ofEventChargePersonDTO(input, uapUserPo, existedMap.get(input.getId()));
            entities.add(entity);
        }
        tbCdcmrEventChargePersonMapper.mergeInto(entities);
    }

    @Override
    public PageInfo<EventChargePersonVO> listDiseaseConfig(EventChargePersonQuery query) {
        List<EventChargePersonVO> resultList = new ArrayList<>();
        List<EventLevelEnum> eventLevelList = Arrays.asList(EventLevelEnum.values());

        List<EventTypeEnum> eventTypesToProcess;
        if (StringUtils.isNotEmpty(query.getEventType())) {
            EventTypeEnum matchedEnum = Arrays.stream(EventTypeEnum.values())
                    .filter(e -> Objects.equals(e.getCode(), query.getEventType()))
                    .findFirst()
                    .orElse(null);
            eventTypesToProcess = matchedEnum != null
                    ? Collections.singletonList(matchedEnum)
                    : new ArrayList<>();
        } else {
            eventTypesToProcess = Arrays.asList(EventTypeEnum.values());
        }
        
        for (EventTypeEnum eventTypeEnum : eventTypesToProcess) {
            List<TbCdcdmDataDictValue> diagnosisList = new ArrayList<>();
            if (EventTypeEnum.INFECTED.getCode().equals(eventTypeEnum.getCode())) {
                List<TbCdcdmDataDictValue> infectedDiagnosisList = new ArrayList<>();
                List<TbCdcdmDataDictValue> emergingDiagnosisList = new ArrayList<>();
                List<TreeNode> infectedTreeNode = dimensionService.getInfectedInfo(null, null, false);
                List<TreeNode> emergingTreeNode = dimensionService.getEmergingInfo();
                valueDomainService.convertToDictValueList(infectedTreeNode, infectedDiagnosisList);
                valueDomainService.convertToDictValueList(emergingTreeNode, emergingDiagnosisList);
                diagnosisList.addAll(infectedDiagnosisList);
                diagnosisList.addAll(emergingDiagnosisList);
            }
            else {
                diagnosisList = valueDomainService.getValueBy(null, null, eventTypeEnum.getValue(), DataDictTypeEnum.DOMAIN_VALUE.getValue());
            }

// 构造 diseaseCode + eventLevel 配对列表
            List<DiseaseLevelPair> diseaseLevelPairs = new ArrayList<>();
            for (TbCdcdmDataDictValue diagnosis : diagnosisList) {
                for (EventLevelEnum level : eventLevelList) {
                    diseaseLevelPairs.add(new DiseaseLevelPair(diagnosis.getCode(), level.getCode()));
                }
            }

// 查询所有 diseaseCode + eventLevel 配对对应的配置项
            List<TbCdcmrEventChargePerson> configList = tbCdcmrEventChargePersonMapper
                    .findByDiseaseCodeLevelPairs(diseaseLevelPairs);

            // 将结果缓存到Map中，便于快速查询
            Map<String, TbCdcmrEventChargePerson> configMap = new HashMap<>();
            for (TbCdcmrEventChargePerson config : configList) {
                String key = config.getDiseaseCode() + "_" + config.getEventLevelId();
                configMap.put(key, config);
            }
            for (TbCdcdmDataDictValue diagnosis : diagnosisList) {
                for (EventLevelEnum level : eventLevelList) {
                    String key = diagnosis.getCode() + "_" + level.getCode();
                    TbCdcmrEventChargePerson config = configMap.get(key);

                    EventChargePersonVO vo = new EventChargePersonVO();
                    vo.setDiseaseCode(diagnosis.getCode());
                    vo.setDiseaseName(diagnosis.getName());
                    vo.setEventLevelId(level.getCode());
                    vo.setEventLevel(level.getName());
                    vo.setEventType(eventTypeEnum.getCode());
                    vo.setEventTypeName(eventTypeEnum.getDesc());

                    if (config != null) {
                        vo.setDealPersonInfo(config.getDealPersonInfo());
                        vo.setDealPersonType(config.getDealPersonType());
                        vo.setIsEnable(String.valueOf(config.getStatus()));
                        vo.setId(config.getId());
                    }

                    if ((query.getDiseaseCode() == null || query.getDiseaseCode().equals(vo.getDiseaseCode())) &&
                            (query.getEventLevelId() == null || query.getEventLevelId().equals(vo.getEventLevelId()))) {
                        resultList.add(vo);
                    }
                }
            }
        }

        // 排序
        resultList.sort(Comparator
                .comparing(EventChargePersonVO::getEventType, Comparator.nullsLast(String::compareTo))
                .thenComparing(EventChargePersonVO::getDiseaseName, Comparator.nullsLast(String::compareTo))
                .thenComparing(EventChargePersonVO::getEventLevelId, Comparator.nullsLast(String::compareTo)));

        return commonUtils.pageList(resultList, query.getPageIndex(), query.getPageSize());
    }

    public TbCdcmrEventChargePerson loadById(String id) {
        return tbCdcmrEventChargePersonMapper.loadById(id);
    }

    private TbCdcmrEventChargePerson ofEventChargePersonDTO(EventChargePersonDTO input,
                                                            UapUserPo uapUserPo,
                                                            TbCdcmrEventChargePerson curr){
        TbCdcmrEventChargePerson entity = new TbCdcmrEventChargePerson();
        BeanUtils.copyProperties(input, entity);
        if (StringUtils.isEmpty(input.getDiseaseCode())) {
            entity.setDiseaseCode(input.getDiseaseName());
        }
        entity.setDealPersonInfo(JSONUtil.toJsonStr(input.getDealPersonInfo()));
        entity.setCreator(uapUserPo.getName());
        entity.setCreatorId(uapUserPo.getId());
        entity.setUpdater(uapUserPo.getName());
        entity.setUpdaterId(uapUserPo.getId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setId(curr != null ? input.getId() : String.valueOf(batchUidService.getUid(TbCdcmrEventChargePerson.TABLE_NAME)));
        entity.setDeleteFlag(curr != null ? curr.getDeleteFlag() : DeleteFlagEnum.NO.getCode());
        entity.setStatus(StringUtils.isNotEmpty(input.getIsEnable()) ? Integer.valueOf(input.getIsEnable()) : 1);
        return entity;
    }

    @Override
    public List<EventChargePersonVO> getDealPersonInfoBy(List<EventChargePersonQuery> dtoList) {
        if(CollectionUtils.isEmpty(dtoList)){
            return new ArrayList<>();
        }
        return tbCdcmrEventChargePersonMapper.getDealPersonInfoBy(dtoList);
    }
}
