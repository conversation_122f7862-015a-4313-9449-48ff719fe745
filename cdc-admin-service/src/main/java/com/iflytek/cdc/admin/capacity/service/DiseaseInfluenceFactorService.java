package com.iflytek.cdc.admin.capacity.service;

import com.iflytek.cdc.admin.capacity.api.algorithm.response.InfluenceFactorResponse;
import com.iflytek.cdc.admin.dto.InfluenceFactorRequest;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * 疾病传播影响因素接口
 */
public interface DiseaseInfluenceFactorService {

    //疾病传播影响因素数据导入
    void importDataByFile(InputStream inputStream) throws IOException;

    //疾病传播影响因素分析结果
    InfluenceFactorResponse.Result.InfluenceFactor influenceFactorAnalysis(InfluenceFactorRequest influence, String loginUserId);

    /**
     * 模板下载
     */
    byte[] downloadTemplate();

    /**
     * 疾病影响因素集合
     * @return
     */
    List<String> findDiseaseInfluenceFactor();
}
