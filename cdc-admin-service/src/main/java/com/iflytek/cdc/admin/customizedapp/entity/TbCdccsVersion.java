package com.iflytek.cdc.admin.customizedapp.entity;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 版本控制;
 * <AUTHOR> fengwang35
 * @date : 2024-9-11
 */
@ApiModel(value = "版本控制")
@TableName("tb_cdccs_version")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsVersion extends BaseEntity implements Serializable{
    public static final String TABLE_NAME = "tb_cdccs_version";
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id ;
    @ApiModelProperty(value = "版本名称")
    private String versionName;
    /** 业务key */
    @ApiModelProperty(value = "业务key")
    private String businessKey ;
    /** 业务id */
    @ApiModelProperty(value = "业务id")
    private String businessId ;
    /** 业务类型 */
    @ApiModelProperty(value = "业务类型")
    private String businessType ;
    /** 发布时间 */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;
    /** 版本 */
    @ApiModelProperty(value = "版本")
    private String version ;
    /** 状态 */
    @ApiModelProperty(value = "状态")
    private String status ;
    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark ;


}