package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.service.WarningGradeConfigRuleService;
import com.iflytek.cdc.admin.service.WarningGradeConfigService;
import com.iflytek.cdc.admin.service.WarningGradeEmergencyPlanService;
import com.iflytek.cdc.admin.service.WarningGradeService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class WarningGradeConfigServiceImpl implements WarningGradeConfigService {

    @Resource
    TbCdcmrWarningGradeConfigMapper tbCdcmrWarningGradeConfigMapper;
    @Resource
    SyndromeInfoMapper syndromeInfoMapper;
    @Resource
    InfectiousDiseasesMapper infectiousDiseasesMapper;
    @Resource
    TbCdcmrPoisoningMapper tbCdcmrPoisoningMapper;
    @Resource
    TbCdcmrSympSymptomMapper tbCdcmrSympSymptomMapper;
    @Resource
    TbCdcmrUnknownReasonDiseaseMapper tbCdcmrUnknownReasonDiseaseMapper;
    @Resource
    TbCdcmrPreventionControlWarnMapper tbCdcmrPreventionControlWarnMapper;
    @Resource
    TbCdcmrOutpatientTypeMapper tbCdcmrOutpatientTypeMapper;
    @Resource
    TbCdcmrCustomizedWarnMapper tbCdcmrCustomizedWarnMapper;
    @Resource
    WarningGradeConfigRuleService warningGradeConfigRuleService;
    @Resource
    WarningGradeEmergencyPlanService warningGradeEmergencyPlanService;
    @Resource
    BatchUidService batchUidService;
    @Resource
    WarningGradeService warningGradeService;


    @Override
    public PageInfo<SyndromeGradeConfigVO> syndromeWarningGradeConfigPageList(SyndromeGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<SyndromeGradeConfigVO> resultList = syndromeInfoMapper.querySyndromeInfoByGrade(param.getSyndromeName(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<SymptomGradeConfigVO> symptomWarningGradeConfigPageList(SymptomGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<SymptomGradeConfigVO> resultList = tbCdcmrSympSymptomMapper.querySymptomInfoByGrade(param.getSymptomName(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<InfectedGradeConfigVO> infectedWarningGradeConfigPageList(InfectedGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<InfectedGradeConfigVO> resultList = infectiousDiseasesMapper.queryInfectedInfoByGrade(param.getDiseasesName(), param.getDiseasesType(), param.getDiseasesClassify(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<PoisonGradeConfigVO> poisonWarningGradeConfigPageList(PoisonGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<PoisonGradeConfigVO> resultList = tbCdcmrPoisoningMapper.queryPoisonInfoByGrade(param.getPoisoningName(), param.getPoisoningTypeCode(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<UnknownReasonGradeConfigVO> unknownReasonWarningGradeConfigPageList(UnknownReasonGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<UnknownReasonGradeConfigVO> resultList = tbCdcmrUnknownReasonDiseaseMapper.queryUnknownReasonInfoByGrade(param.getDiseaseName(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<PreventionControlGradeConfigVO> preventionControlWarningGradeConfigPageList(PreventionControlGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<PreventionControlGradeConfigVO> resultList = tbCdcmrPreventionControlWarnMapper.queryPreventionControlInfoByGrade(param.getPreventionControlName(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<OutpatientGradeConfigVO> outpatientWarningGradeConfigPageList(OutpatientGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<OutpatientGradeConfigVO> resultList = tbCdcmrOutpatientTypeMapper.queryOutpatientInfoByGrade(param.getOutpatientTypeCode(), param.getStatus());
        return new PageInfo<>(resultList);
    }

    @Override
    public PageInfo<CustomizedGradeConfigVO> customizedWarningGradeConfigPageList(CustomizedGradeConfigParam param) {
        PageHelper.startPage(param.getPageIndex(), param.getPageSize());
        List<CustomizedGradeConfigVO> resultList = tbCdcmrCustomizedWarnMapper.queryCustomizedInfoByGrade(param.getCustomizedName(), param.getStatus(), param.getWarningType());
        return new PageInfo<>(resultList);
    }

    @Override
    @Transactional
    public void updateWarningGradeConfig(WarningGradeConfigVO configVO, String loginUserId) {
        TbCdcmrWarningGradeConfig config = configVO.getConfig();
        if (config.getId() == null) {
            config.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_warning_grade_config")));
        }
        config.setUpdateUser(loginUserId);
        config.setUpdateTime(new Date());
        tbCdcmrWarningGradeConfigMapper.upsertConfig(config);
        warningGradeConfigRuleService.updateWarningGradeConfigRule(configVO.getRuleList(), loginUserId, config.getId());
        warningGradeEmergencyPlanService.updateWarningGradeEmergencyPlan(configVO.getPlanList(), loginUserId, config.getId());

    }

    @Override
    public void updateWarningGradeConfigStatus(TbCdcmrWarningGradeConfig config, String loginUserId) {
        if (config.getId() == null) {
            config.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_warning_grade_config")));
        }
        config.setUpdateUser(loginUserId);
        config.setUpdateTime(new Date());
        tbCdcmrWarningGradeConfigMapper.upsertConfig(config);
    }

    @Override
    public List<WarningGradeConfigVO> getConfigByType(String configType) {
        List<TbCdcmrWarningGradeConfig> warningGradeConfigVOList = tbCdcmrWarningGradeConfigMapper.getListByConfigType(configType);
        List<WarningGradeConfigVO> configVOList = new ArrayList<>();
        warningGradeConfigVOList.forEach(e -> {
            List<TbCdcmrWarningGradeConfigRule> ruleList = warningGradeConfigRuleService.getAllByConfigId(e.getId());
            WarningGradeConfigVO warningGradeConfigVO = new WarningGradeConfigVO();
            warningGradeConfigVO.setConfig(e);
            warningGradeConfigVO.setRuleList(ruleList);
            configVOList.add(warningGradeConfigVO);
        });
        return configVOList;
    }


}
