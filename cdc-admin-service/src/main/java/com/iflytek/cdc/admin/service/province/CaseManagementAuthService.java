package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord;
import com.iflytek.cdc.admin.model.mr.dto.AuthCommonDto;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;

import java.util.List;

public interface CaseManagementAuthService {

    EDRAuthResultVO validViewAuth(String loginUserId, AuthCommonDto permissionRecord);

    void addAuthApplyRecord(String loginUserId, TbCdcmrCaseManagementPermissionRecord permissionRecord);

    void changeAuthStatus(String loginUserId, TbCdcmrCaseManagementPermissionRecord permissionRecord);

    PageInfo<TbCdcmrCaseManagementPermissionRecord> getAuthApplyRecordList(String loginUserId, AuthCommonDto permissionRecord);

    TbCdcmrCaseManagementPermissionRecord getPermissionRecordInfoById(String id);

    List<TbCdcmrCaseManagementPermissionRecord> getCreateUserList(String loginUserId,String moduleType);

    List<TbCdcmrCaseManagementPermissionRecord> getUpdateUserList(String loginUserId,String moduleType);
}
