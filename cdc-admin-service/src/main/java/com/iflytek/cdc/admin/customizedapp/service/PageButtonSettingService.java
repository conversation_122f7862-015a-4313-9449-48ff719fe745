package com.iflytek.cdc.admin.customizedapp.service;


import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPageButtonSetting;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface PageButtonSettingService extends ICdcService<TbCdccsPageButtonSetting> {
    /**
     * 通过页面数据保存或修改
     */
    void saveByPage(TbCdccsPage page, List<TbCdccsPageButtonSetting> buttonSettingList);

    /**
     * 通过pageId 查询
     */
    List<TbCdccsPageButtonSetting> listByPageId(String pageId);

    void deleteByPageId(String pageId);
}
