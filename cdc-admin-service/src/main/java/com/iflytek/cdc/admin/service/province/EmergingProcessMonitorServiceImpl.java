package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingDiseaseInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEmergingMonitorConfigMapper;
import com.iflytek.cdc.admin.model.mr.dto.EmergingMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.EmergingDiseaseInfoVO;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEmergingMonitorConfig;
import com.iflytek.cdc.admin.service.EmergingProcessMonitorService;

import javax.annotation.Resource;
import java.util.*;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class EmergingProcessMonitorServiceImpl extends ServiceImpl<TbCdcmrEmergingMonitorConfigMapper, TbCdcmrEmergingMonitorConfig> implements EmergingProcessMonitorService {

    @Resource
    private TbCdcmrEmergingDiseaseInfoMapper diseaseInfoMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CommonUtils commonUtils;

    @Resource
    private TbCdcmrEmergingMonitorConfigMapper monitorConfigMapper;

    private static final String TB_CDCMR_EMERGING_DISEASE_INFO = "tb_cdcmr_emerging_disease_info";

    private static final String TB_CDCMR_EMERGING_MONITOR_CONFIG = "tb_cdcmr_emerging_monitor_config";

    @Value("${generateStrLength:8}")
    private int length;

    @Override
    public List<EmergingDiseaseInfoVO> getEmergingTreeInfo() {
        return diseaseInfoMapper.getEmergingTreeInfo();
    }

    @Override
    public String editSubEmergingInfo(TbCdcmrEmergingDiseaseInfo emergingDiseaseInfo) {
        
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_DISEASE_INFO));
        //非空为更新，为空则新增
        if(emergingDiseaseInfo.getId() == null){
            //新增id、code后端生成
            emergingDiseaseInfo.setId(id);
            //查目前疾病code
            List<String> diseaseCodeList = diseaseInfoMapper.getAllDiseaseCodeBy();
            emergingDiseaseInfo.setDiseaseCode(commonUtils.generateSyndromeCode(length, diseaseCodeList));
        }

        //状态默认开启，默认未删除
        emergingDiseaseInfo.setStatus(emergingDiseaseInfo.getStatus() == null ? Integer.valueOf(StatusEnum.STATUS_ON.getCode()) : emergingDiseaseInfo.getStatus());
        emergingDiseaseInfo.setDeleteFlag(emergingDiseaseInfo.getDeleteFlag() == null ? DeleteFlagEnum.NO.getCode() : emergingDiseaseInfo.getDeleteFlag());
        emergingDiseaseInfo.setCreateTime(new Date());
        emergingDiseaseInfo.setCreator(uapUserPo.getName());
        emergingDiseaseInfo.setCreatorId(uapUserPo.getId());
        emergingDiseaseInfo.setUpdateTime(new Date());
        emergingDiseaseInfo.setUpdater(uapUserPo.getName());
        emergingDiseaseInfo.setUpdaterId(uapUserPo.getId());
        diseaseInfoMapper.insertOrUpdate(Collections.singletonList(emergingDiseaseInfo));

        return id;
    }

    @Override
    public void deleteSubEmergingInfo(String id) {

        diseaseInfoMapper.updateDeleteFlagById(id);
    }

    @Override
    public List<TbCdcmrEmergingMonitorConfig> getEmergingInfoConfigs(EmergingMonitorConfigQueryDTO queryDTO) {

        return monitorConfigMapper.queryByDiseaseInfoId(queryDTO);
    }

    @Override
    public void editEmergingProcessDefinition(String diseaseInfoId, List<TbCdcmrEmergingMonitorConfig> emergingMonitorConfigs) {
        UapUserPo uapUserPo = USER_INFO.get();
        //软删除该疾病下的所有规则
        monitorConfigMapper.updateByEmergingInfoId(diseaseInfoId);

        String loginUserId = null;
        String loginUserName = null;
        if(uapUserPo != null){
            loginUserId = uapUserPo.getId();
            loginUserName = uapUserPo.getName();
        }
        //重新set id的值，并插入配置表
        for (TbCdcmrEmergingMonitorConfig monitorConfig : emergingMonitorConfigs) {

            monitorConfig.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_EMERGING_MONITOR_CONFIG)));
            //状态默认开启，默认未删除
            monitorConfig.setStatus(Constants.STATUS_ON);
            monitorConfig.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            monitorConfig.setCreateTime(new Date());
            monitorConfig.setCreator(loginUserName);
            monitorConfig.setCreatorId(loginUserId);
            monitorConfig.setUpdateTime(new Date());
            monitorConfig.setUpdater(loginUserName);
            monitorConfig.setUpdaterId(loginUserId);
            monitorConfig.setDiseaseInfoId(diseaseInfoId);
        }
        if(CollectionUtils.isNotEmpty(emergingMonitorConfigs)) {
            monitorConfigMapper.batchInsert(emergingMonitorConfigs);
        }
    }

    @Override
    public List<String> getEmergingDiseaseCodeByName(String diseaseName) {

        String diseaseCode = diseaseInfoMapper.getEmergingDiseaseCodeByName(diseaseName);
        return StringUtils.isNotBlank(diseaseCode) ? Collections.singletonList(diseaseCode) : new ArrayList<>();
    }
}
