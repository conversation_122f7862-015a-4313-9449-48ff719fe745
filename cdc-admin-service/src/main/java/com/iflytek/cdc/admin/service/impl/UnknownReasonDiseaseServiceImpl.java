package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonDiseaseMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrUnknownReasonWarnRuleMapper;
import com.iflytek.cdc.admin.service.UnknownReasonDiseaseService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnknownReasonDiseaseServiceImpl implements UnknownReasonDiseaseService {
    @Resource
    TbCdcmrUnknownReasonDiseaseMapper tbCdcmrUnknownReasonDiseaseMapper;

    @Resource
    TbCdcmrUnknownReasonWarnMapper tbCdcmrUnknownReasonWarnMapper;

    @Resource
    TbCdcmrUnknownReasonWarnRuleMapper tbCdcmrUnknownReasonWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;

    @Override
    public PageInfo<TbCdcmrUnknownReasonDisease> getPageList(UnknownReasonQueryDto dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(tbCdcmrUnknownReasonDiseaseMapper.getList(dto));
    }

    @Override
    public void add(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId) {
        if (CollectionUtils.isNotEmpty(tbCdcmrUnknownReasonDiseaseMapper.getByDiseaseCode(tbCdcmrUnknownReasonDisease.getDiseaseCode()))) {
            throw new MedicalBusinessException("已存在相同疾病编码!");
        }
        if (CollectionUtils.isNotEmpty(tbCdcmrUnknownReasonDiseaseMapper.getByDiseaseName(tbCdcmrUnknownReasonDisease.getDiseaseName()))) {
            throw new MedicalBusinessException("已存在相同疾病名称!");
        }
        tbCdcmrUnknownReasonDisease.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_unknown_reason_disease")));
        tbCdcmrUnknownReasonDisease.setCreateUser(loginUserId);
        tbCdcmrUnknownReasonDisease.setCreateTime(new Date());
        tbCdcmrUnknownReasonDisease.setIsDeleted(0);
        tbCdcmrUnknownReasonDiseaseMapper.insert(tbCdcmrUnknownReasonDisease);
        addUnknownReasonWarnByDisease(tbCdcmrUnknownReasonDisease, loginUserId);
    }

    private void addUnknownReasonWarnByDisease(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId) {
        TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn = new TbCdcmrUnknownReasonWarn();
        tbCdcmrUnknownReasonWarn.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_unknown_reason_warn")));
        tbCdcmrUnknownReasonWarn.setStatus(0);
        tbCdcmrUnknownReasonWarn.setDiseaseCode(tbCdcmrUnknownReasonDisease.getDiseaseCode());
        tbCdcmrUnknownReasonWarn.setDiseaseName(tbCdcmrUnknownReasonDisease.getDiseaseName());
        tbCdcmrUnknownReasonWarn.setRemark(null);
        tbCdcmrUnknownReasonWarn.setUpdater(loginUserId);
        tbCdcmrUnknownReasonWarn.setIsDeleted(0);
        tbCdcmrUnknownReasonWarn.setUpdateTime(new Date());
        tbCdcmrUnknownReasonWarn.setIncubation(null);
        tbCdcmrUnknownReasonWarnMapper.insert(tbCdcmrUnknownReasonWarn);
    }

    @Override
    public void delete(String id) {
        TbCdcmrUnknownReasonDisease unknownReasonDisease = tbCdcmrUnknownReasonDiseaseMapper.selectByPrimaryKey(id);
        if(unknownReasonDisease==null){
            throw new MedicalBusinessException("不明原因疾病不存在");
        }
        TbCdcmrUnknownReasonWarn warn = tbCdcmrUnknownReasonWarnMapper.getByDiseaseCode(unknownReasonDisease.getDiseaseCode());
        if (!tbCdcmrUnknownReasonWarnRuleMapper.getRuleListByWarnId(warn.getId()).isEmpty()) {
            throw new MedicalBusinessException("还有在使用的规则，请先删除规则后再进行删除！");
        }
        tbCdcmrUnknownReasonWarnMapper.deleteById(warn.getId());
        tbCdcmrUnknownReasonDiseaseMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void update(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId) {
        List<TbCdcmrUnknownReasonDisease> existDisease = tbCdcmrUnknownReasonDiseaseMapper.getByDiseaseCode(tbCdcmrUnknownReasonDisease.getDiseaseCode());
        List<String> idList = existDisease.stream().map(TbCdcmrUnknownReasonDisease::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList) && !idList.contains(tbCdcmrUnknownReasonDisease.getId())) {
            throw new MedicalBusinessException("已存在相同疾病编码!");
        }

        existDisease = tbCdcmrUnknownReasonDiseaseMapper.getByDiseaseName(tbCdcmrUnknownReasonDisease.getDiseaseName());
        List<String> idList2 = existDisease.stream().map(TbCdcmrUnknownReasonDisease::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList2) && !idList2.contains(tbCdcmrUnknownReasonDisease.getId())) {
            throw new MedicalBusinessException("已存在相同疾病名称!");
        }

        TbCdcmrUnknownReasonDisease updateUnknownReasonDisease = new TbCdcmrUnknownReasonDisease();
        updateUnknownReasonDisease.setId(tbCdcmrUnknownReasonDisease.getId());
        updateUnknownReasonDisease.setUpdateUser(loginUserId);
        updateUnknownReasonDisease.setUpdateTime(new Date());
        updateUnknownReasonDisease.setDiseaseCode(tbCdcmrUnknownReasonDisease.getDiseaseCode());
        updateUnknownReasonDisease.setDiseaseName(tbCdcmrUnknownReasonDisease.getDiseaseName());
        updateUnknownReasonDisease.setRemark(tbCdcmrUnknownReasonDisease.getRemark());
        updateUnknownReasonDisease.setStatus(tbCdcmrUnknownReasonDisease.getStatus());
        updateUnknownReasonWarnByUnknownReasonDisease(updateUnknownReasonDisease, loginUserId);
        tbCdcmrUnknownReasonDiseaseMapper.updateByPrimaryKeySelective(updateUnknownReasonDisease);
    }

    private void updateUnknownReasonWarnByUnknownReasonDisease(TbCdcmrUnknownReasonDisease updateUnknownReasonDisease, String loginUserId) {
        TbCdcmrUnknownReasonWarn tbCdcmrUnknownReasonWarn = tbCdcmrUnknownReasonWarnMapper.getByDiseaseCode(updateUnknownReasonDisease.getDiseaseCode());
        tbCdcmrUnknownReasonWarn.setDiseaseCode(updateUnknownReasonDisease.getDiseaseCode());
        tbCdcmrUnknownReasonWarn.setDiseaseName(updateUnknownReasonDisease.getDiseaseName());
        tbCdcmrUnknownReasonWarn.setUpdater(loginUserId);
        tbCdcmrUnknownReasonWarn.setUpdateTime(new Date());
        tbCdcmrUnknownReasonWarnMapper.updateByPrimaryKeySelective(tbCdcmrUnknownReasonWarn);
    }
}
