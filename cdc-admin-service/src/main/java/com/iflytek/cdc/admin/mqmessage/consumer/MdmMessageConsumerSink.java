package com.iflytek.cdc.admin.mqmessage.consumer;

//import org.springframework.cloud.stream.annotation.Input;
//import org.springframework.messaging.SubscribableChannel;

/**
 * <AUTHOR>
 * @date 2021/9/15 14:31
 **/
public interface MdmMessageConsumerSink {


    String MDM_DICT_MESSAGE_INPUT="mdm-dict-message-input";


//    @Input(MDM_DICT_MESSAGE_INPUT)
//    SubscribableChannel  mdmDictMessageInput();


}
