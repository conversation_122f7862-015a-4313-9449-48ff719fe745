package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.constant.ParamConfigConstants;
import com.iflytek.cdc.admin.dto.RiskLevelDetailQueryDTO;
import com.iflytek.cdc.admin.dto.WarningRiskLevelDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.ParamConfigMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningRiskLevelDetailMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningRiskLevelMapper;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelDetailService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import com.iflytek.cdc.admin.vo.epi.SignalProcessingLimitConfigVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class WarningRiskLevelDetailServiceImpl extends ServiceImpl<TbCdcmrWarningRiskLevelDetailMapper, TbCdcmrWarningRiskLevelDetail>  implements WarningRiskLevelDetailService {
    @Resource
    private TbCdcmrWarningRiskLevelDetailMapper tbCdcmrWarningRiskLevelDetailMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrWarningRiskLevelMapper tbCdcmrWarningRiskLevelMapper;
    
    @Resource
    private TbCdcmrSyndromeDiseaseInfoMapper syndromeDiseaseInfoMapper;
    
    @Resource
    private ParamConfigMapper paramConfigMapper;

    //SignalProcessingLimit
    private static final String SIGNAL_PROCESSING_LIMIT_GROUP = "13";
    private static final String SIGNAL_PROCESSING_LIMIT_DEFAULT_CODE = "默认时限";

    @Override
    public List<RiskLevelDetailVO> listRiskLevelDetail(RiskLevelDetailQueryDTO queryDTO) {

        //批量查询疾病的风险等级信息
        List<RiskLevelDetailVO> riskLevelDetailVOList = tbCdcmrWarningRiskLevelDetailMapper.listRiskLevelDetail(queryDTO);
        //获取疾病id集合
        List<String> diseaseIdList = riskLevelDetailVOList.stream().map(RiskLevelDetailVO::getDiseaseId).collect(Collectors.toList());
        return queryDefaultTimeLimit(riskLevelDetailVOList, diseaseIdList);
    }

    @Override
    public RiskLevelDetailVO loadByDiseaseId(String diseaseId,
                                             String riskLevelCode,
                                             String warningType) {
        //根据 疾病id/code 以及预警类型获取风险等级详情
        RiskLevelDetailVO riskLevelDetailVO = tbCdcmrWarningRiskLevelDetailMapper.loadByDiseaseIdAndLevelCode(diseaseId, riskLevelCode, warningType);
        if(riskLevelDetailVO == null){
            return null;
        }
        return queryDefaultTimeLimit(Collections.singletonList(riskLevelDetailVO), Collections.singletonList(riskLevelDetailVO.getDiseaseId())).get(0);
    }

    /**
     * 根据 疾病id查询配置的默认时限
     */
    private List<RiskLevelDetailVO> queryDefaultTimeLimit(List<RiskLevelDetailVO> riskLevelDetailVOList, 
                                                          List<String> diseaseIdList){

        //根据 疾病id列表 查询疾病名称
        List<SyndromeDiseaseInfoVO> diseaseInfoList = syndromeDiseaseInfoMapper.loadByIdList(diseaseIdList);
        
        riskLevelDetailVOList.forEach(e -> {

            diseaseInfoList.forEach(diseaseInfo -> {
                //匹配上的数据进行时限判断赋值
                if(Objects.equals(e.getDiseaseId(), diseaseInfo.getId())) {
                    //根据疾病名称查询配置的处理时限
                    SignalProcessingLimitConfigVO configVO = getSignalProcessingLimitConfigVO(diseaseInfo.getDiseaseName());
                    if (configVO != null) {
                        e.setCheckTimeLimit(e.getCheckTimeLimit() == null ? configVO.getCheckTimeLimitDefault() : e.getCheckTimeLimit());
                        e.setInvestTimeLimit(e.getInvestTimeLimit() == null ? configVO.getInvestTimeLimitDefault() : e.getInvestTimeLimit());
                        e.setJudgeTimeLimit(e.getJudgeTimeLimit() == null ? configVO.getJudgeTimeLimitDefault() : e.getJudgeTimeLimit());
                    }
                }
            });
        });
        
        return riskLevelDetailVOList;
    }

    @Override
    public TbCdcmrWarningRiskLevelDetail saveRiskLevelDetail(WarningRiskLevelDTO dto) {
        UapUserPo uapUserPo = USER_INFO.get();
        TbCdcmrWarningRiskLevelDetail entity = new TbCdcmrWarningRiskLevelDetail();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdaterId(uapUserPo.getId());
        entity.setUpdateTime(new Date());
        entity.setUpdater(uapUserPo.getName());
        entity.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        if (StringUtils.isNotEmpty(dto.getDiseaseId())){
            RiskLevelDetailVO riskLevelDetailVO = tbCdcmrWarningRiskLevelDetailMapper.loadByDiseaseIdAndLevelId(dto.getDiseaseId(), dto.getRiskLevelId());
            if (riskLevelDetailVO != null){
                entity.setId(riskLevelDetailVO.getId());
            }
        }
        if (StringUtils.isEmpty(entity.getId())){
            entity.setId(String.valueOf(batchUidService.getUid(TbCdcmrWarningRiskLevelDetail.TABLE_NAME)));
            entity.setCreatorId(uapUserPo.getId());
            entity.setCreateTime(new Date());
            entity.setCreator(uapUserPo.getName());
        }
        saveOrUpdate(entity);
        return entity;
    }

    @Override
    public List<TbCdcmrWarningRiskLevelDetail> getRiskLevelDetailBy(List<String> idList) {

        return tbCdcmrWarningRiskLevelDetailMapper.getRiskLevelDetailBy(idList);
    }

    @Override
    public List<SignalProcessingLimitConfigVO> getSignalProcessingLimitConfig(List<String> signalNames) {
        if(CollectionUtils.isEmpty(signalNames)) {
            return new ArrayList<>();
        }

        return signalNames.stream()
                          .map(this::getSignalProcessingLimitConfigVO)
                          .collect(Collectors.toCollection(ArrayList::new));
    }

    @Override
    public SignalProcessingLimitConfigVO getSignalProcessingLimitConfigVO(String signalName) {
        ParamConfig paramConfig = paramConfigMapper.paramConfigInfo(signalName, ParamConfigConstants.NO_DELETE, SIGNAL_PROCESSING_LIMIT_GROUP);
        if (paramConfig == null) {
            paramConfig = paramConfigMapper.paramConfigInfo(SIGNAL_PROCESSING_LIMIT_DEFAULT_CODE, ParamConfigConstants.NO_DELETE, SIGNAL_PROCESSING_LIMIT_GROUP);
        }
        String configValue = Optional.ofNullable(paramConfig).map(ParamConfig::getConfigValue).orElse(null);
        return JSONUtil.toBean(configValue, SignalProcessingLimitConfigVO.class);
    }
}
