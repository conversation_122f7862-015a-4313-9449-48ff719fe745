package com.iflytek.cdc.admin.service.async;

/**
 * @ClassName AsyncMdmServiceStrategy
 * @Description 动态加载异步mdm操作实现类
 * <AUTHOR>
 * @Date 2021/8/10 10:19
 * @Version 1.0
 */
public interface AsyncMdmServiceStrategy {

    /**
     * 根据对应的编码类型，选择对应的异步实现方式
     * @param code
     * @param batchId
     * @param loginUserId
     */
    public void executeAsyncMdmData(String code,String batchId,String loginUserId);
}
