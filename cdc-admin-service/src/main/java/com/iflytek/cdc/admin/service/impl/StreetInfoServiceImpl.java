package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo;
import com.iflytek.cdc.admin.mapper.TbCdcmrStreetInfoMapper;
import com.iflytek.cdc.admin.model.dm.vo.AreaInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.StreetInfoService;
import com.iflytek.cdc.admin.common.vo.AreaMappingVO;
import com.iflytek.cdc.admin.common.vo.LoginUserAreaVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class StreetInfoServiceImpl implements StreetInfoService {

    @Resource
    private TbCdcmrStreetInfoMapper tbCdcmrStreetInfoMapper;
    @Resource
    private UapServiceApi uapServiceApi;


    @Override
    public List<AreaMappingVO> listByAreaCodes(Integer areaLevel, List<String> codes) {
        return tbCdcmrStreetInfoMapper.listByAreaCodes(areaLevel, codes);
    }

    @Override
    public LoginUserAreaVO loadCurrUserArea(String loginUserName) {
        LoginUserAreaVO currUserArea = uapServiceApi.getCurrUserArea(loginUserName);
        List<AreaMappingVO> areaMappingVOS = listByAreaCodes(currUserArea.getAreaLevel(), currUserArea.getAreaCodes());
        currUserArea.setData(areaMappingVOS);
        return currUserArea;
    }

    @Override
    public AreaInfoVO getAreaInfo() {
        LoginUserAreaVO currUserArea = uapServiceApi.getCurrUserArea(USER_INFO.get().getLoginName());
        List<TbCdcmrStreetInfo> streetInfo = tbCdcmrStreetInfoMapper.listBy(currUserArea.getProvinceCodes(), currUserArea.getCityCodes(), currUserArea.getDistrictCodes());
        AreaInfoVO areaInfoVO = new AreaInfoVO();
        areaInfoVO.setLevel(currUserArea.getAreaLevel());
        if (currUserArea.getAreaLevel().equals(AreaLevelEnum.PROVINCE.getLevel())){
            areaInfoVO.setList(TreeNode.groupByList(streetInfo,
                    TbCdcmrStreetInfo::getProvinceName,
                    TbCdcmrStreetInfo::getProvinceCode,
                    (cityEntry) -> TreeNode.groupByList(
                            cityEntry.getValue(),
                            TbCdcmrStreetInfo::getCityName,
                            TbCdcmrStreetInfo::getCityCode,
                            (districtEntry) -> TreeNode.groupByList(
                                    districtEntry.getValue(),
                                    TbCdcmrStreetInfo::getDistrictName,
                                    TbCdcmrStreetInfo::getDistrictCode,
                                    (streetEntry) -> TreeNode.groupByList(
                                            streetEntry.getValue(),
                                            TbCdcmrStreetInfo::getStreetName,
                                            TbCdcmrStreetInfo::getStreetCode,
                                            null)))));
        }
        if (currUserArea.getAreaLevel().equals(AreaLevelEnum.CITY.getLevel())){
            areaInfoVO.setList(TreeNode.groupByList(streetInfo,
                    TbCdcmrStreetInfo::getCityName,
                    TbCdcmrStreetInfo::getCityCode,
                    (cityEntry) -> TreeNode.groupByList(
                            cityEntry.getValue(),
                            TbCdcmrStreetInfo::getDistrictName,
                            TbCdcmrStreetInfo::getDistrictCode,
                            (districtEntry) -> TreeNode.groupByList(
                                    districtEntry.getValue(),
                                    TbCdcmrStreetInfo::getStreetName,
                                    TbCdcmrStreetInfo::getStreetCode,
                                    null))));
        }
        if (currUserArea.getAreaLevel().equals(AreaLevelEnum.DISTRICT.getLevel())){
            areaInfoVO.setList(TreeNode.groupByList(streetInfo,
                    TbCdcmrStreetInfo::getDistrictName,
                    TbCdcmrStreetInfo::getDistrictCode,
                    (cityEntry) -> TreeNode.groupByList(
                            cityEntry.getValue(),
                            TbCdcmrStreetInfo::getStreetName,
                            TbCdcmrStreetInfo::getStreetCode,
                            null)));
        }
        return areaInfoVO;
    }
}
