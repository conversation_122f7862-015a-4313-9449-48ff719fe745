package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.AnalysisDimQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueRollupRelEditDto;
import com.iflytek.cdc.admin.service.province.AnalysisDimensionService;
import com.iflytek.cdc.admin.vo.AnalysisDimDetailInfoVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupValueRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimValueVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionEditVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionVO;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 分析维度管理-接口类
 */
@RestController
@RequestMapping("/pt/{version}/analysisDimension")
@Api(tags = "分析维度管理-接口类")
public class AnalysisDimensionController {

    @Resource
    private AnalysisDimensionService dimensionService;

    @PostMapping("/add")
    @ApiOperation("添加维度")
    @OperationLogAnnotation(operationName = "添加维度")
    public void addDimension(@RequestBody AnalysisDimensionEditVO dimension) {
        dimensionService.addDim(dimension);
    }

    @PostMapping("/get")
    @ApiOperation("根据 ID 获取维度")
    public AnalysisDimensionVO getDimension(@RequestParam String id) {
        return dimensionService.getDimById(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新维度")
    @OperationLogAnnotation(operationName = "更新维度")
    public void updateDimension(@RequestBody AnalysisDimensionEditVO dimension) {
        dimensionService.updateDimById(dimension);
    }

    @PostMapping("/delete")
    @ApiOperation("删除维度")
    @OperationLogAnnotation(operationName = "删除维度")
    public void deleteDimension(@RequestParam String id) {
        dimensionService.deleteDimById(id);
    }

    @PostMapping("/pageList")
    @ApiOperation("分页查询")
    public PageInfo<AnalysisDimensionVO> pageList(@RequestBody AnalysisDimQueryDto input) {
        return dimensionService.pageList(input);
    }

    @PostMapping("/detailInfo")
    @ApiOperation("获取维度详情")
    public AnalysisDimDetailInfoVO getDimDetailInfo(@RequestBody AnalysisDimValueQueryDto queryDto) {
        return dimensionService.getDimDetailInfo(queryDto);
    }

    @PostMapping("/enableAnalysis/edit")
    @ApiOperation("禁用维度值")
    @OperationLogAnnotation(operationName = "禁用维度值")
    public void editAnalysisFlag(@RequestBody AnalysisDimValueVO dimValueVO) {
        dimensionService.editAnalysisFlag(dimValueVO);
    }

    @PostMapping("/rollupDim/getByDimId")
    @ApiOperation("查询上卷维度")
    public List<AnalysisDimRollupRelVO> getRollupDims(@RequestParam String dimId) {
        return dimensionService.getRollupDims(dimId);
    }

    @PostMapping("/rollupDim/add")
    @ApiOperation("新增上卷维度")
    @OperationLogAnnotation(operationName = "新增上卷维度")
    public void addRollupDim(@RequestBody AnalysisDimRollupRelVO rollupRelVOs) {
        dimensionService.addRollupDim(rollupRelVOs);
    }

    @PostMapping("/rollupDim/update")
    @ApiOperation("更新上卷维度")
    @OperationLogAnnotation(operationName = "更新上卷维度")
    public void updateRollupDim(@RequestBody AnalysisDimRollupRelVO rollupRelVOs) {
        dimensionService.updateRollupDim(rollupRelVOs);
    }

    @PostMapping("/rollupDim/delete")
    @ApiOperation("删除上卷维度")
    @OperationLogAnnotation(operationName = "删除上卷维度")
    public void deleteRollupDim(@RequestParam String id) {
        dimensionService.deleteRollupDim(id);
    }

    @PostMapping("/rollupDimValue/getByDimValue")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "analysisDimId", value = "分析维度ID"),
            @ApiImplicitParam(name = "dataAttrValueId", value = "分析维度值ID")
    })
    @ApiOperation("根据维度值获取上卷维度")
    public List<AnalysisDimRollupValueRelVO> getRollupValueRelBy(@RequestParam String analysisDimId, @RequestParam String dataAttrValueId) {
        return dimensionService.getRollupValueRelBy(analysisDimId, dataAttrValueId);
    }

    @PostMapping("/rollupDimValue/edit")
    @ApiOperation("编辑上卷维度值")
    @Transactional(rollbackFor = Exception.class)
    @OperationLogAnnotation(operationName = "编辑上卷维度值")
    public void editRollupDimValue(@RequestBody AnalysisDimValueRollupRelEditDto dto) {
        dimensionService.editRollupDimValue(dto.getAnalysisDimId(), dto.getDataAttrValueId(), dto.getDimValueRollupRelDtos());
    }
}
