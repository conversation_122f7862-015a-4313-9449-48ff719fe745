package com.iflytek.cdc.admin.customizedapp.constants;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.customizedapp.enums.DateUnitEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class TaskSettingConstants {
    public static final TaskSettingConstants taskSettingConstants = new TaskSettingConstants();

    @ApiModelProperty("提醒周期")
    public DateUnitEnum[] dateUnitEnums = DateUnitEnum.values();
    @ApiModelProperty("提醒方式")
    private TaskReminderType[] taskReminderTypes = TaskReminderType.values();
    @ApiModelProperty("提醒对象")
    private TaskReminderObject[] taskReminderObjects = TaskReminderObject.values();
    @ApiModelProperty("任务类型")
    private TaskType[] taskTypes = TaskType.values();


    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum TaskReminderType{
        PHONE("phone", "电话"),
        MESSAGE("message", "系统消息"),
        SMS("sms", "短信"),
        ;
        private final String code;
        private final String desc;

        TaskReminderType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum TaskReminderObject{
        ROLE("role", "指定角色"),
        USER("user", "指定用户"),
        ;
        private final String code;
        private final String desc;

        TaskReminderObject(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum TaskType{
        VISIT("visit", "随访"),
        INVESTIGATION("investigation", "个案调查"),
        ;
        private final String code;
        private final String desc;

        TaskType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
