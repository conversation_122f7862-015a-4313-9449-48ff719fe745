package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.MedicalWarn;
import com.iflytek.cdc.admin.sdk.api.MedicalWarnApi;
import com.iflytek.cdc.admin.sdk.entity.QueryWarnRuleInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.MedicalWarnRuleVo;
import com.iflytek.cdc.admin.service.MedicalWarnService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 传染病预警规则维护API
 */
@RestController
@Api(value = "/", description = "传染病预警规则维护", tags = "传染病预警规则维护")
public class MedicalWarnController {

    @Autowired
    private MedicalWarnApi medicalWarnApi;

    @Autowired
    private MedicalWarnService medicalWarnService;


    @ApiOperation("传染病预警规则维护-病种数据分页列表")
    @PostMapping("/{version}/pt/medicalWarn/pageList")
    public ResponseResult medicalWarnPageList(@PathVariable String version, @RequestBody MedicalWarnPageListDto medicalWarnPageListDto, @RequestParam("loginUserId") String loginUserId) {
        return medicalWarnService.medicalWarnPageList(medicalWarnPageListDto, loginUserId);
    }

    @ApiOperation("传染病预警规则维护-病种规则状态修改")
    @PostMapping("/{version}/pt/medicalWarn/modifyStatus")
    public ResponseResult medicalWarnModifyStatus(@PathVariable String version, @RequestParam("id") String id, @RequestParam("status") String status) {
        return medicalWarnService.medicalWarnModifyStatus(id, status);
    }

    @ApiOperation("传染病预警规则维护-病种规则状态是否可修改")
    @GetMapping("/{version}/pt/medicalWarn/isCanModify")
    public Boolean isCanModify(@PathVariable String version, @RequestParam("id") String id) {
        return medicalWarnService.isCanModify(id);
    }

    @ApiOperation("传染病预警规则维护-导出")
    @PostMapping("/{version}/pt/medicalWarn/export")
    @LogExportAnnotation
    public void medicalWarnExport(HttpServletResponse response, @PathVariable String version, @RequestParam("loginUserId") String loginUserId) throws UnsupportedEncodingException {
        medicalWarnService.medicalWarnExport(response, loginUserId);
    }


    @ApiOperation("传染病预警规则维护-编辑查看")
    @GetMapping("/{version}/pt/medicalWarnRule/query")
    public ResponseResult medicalWarnRuleList(@PathVariable String version, @RequestParam("id") String id, @RequestParam("loginUserId") String loginUserId) {
        return medicalWarnService.medicalWarnRuleList(id, loginUserId);
    }

    @ApiOperation("传染病预警规则维护-保存规则")
    @PostMapping("/{version}/pt/medicalWarnRule/saveOrUpdateMedicalWarnRule")
    public ResponseResult saveOrUpdateMedicalWarnRule(@PathVariable String version, @RequestBody MedicalWarnRuleSaveOrUpdateDto dto, @RequestParam("loginUserId") String loginUserId) {
        return medicalWarnService.saveOrUpdateMedicalWarnRule(dto, loginUserId);
    }


    @ApiOperation("传染病预警规则维护-获取规则列表")
    @PostMapping("/{version}/pt/medicalWarnRule/queryAll")
    public List<MedicalWarn> queryAll(@RequestBody QueryWarnRuleInfoFilter dto) {
        if (dto == null) {
            throw new MedicalBusinessException(Constants.PARAM_ERROR);
        }
        return medicalWarnService.queryAll(dto);
    }

    @ApiOperation("传染病预警规则维护-查询规则详情")
    @GetMapping("/{version}/pt/medicalWarn/queryById")
    public MedicalWarn queryById(@RequestParam String id, @RequestParam String loginUserId) {
        return medicalWarnService.queryById(id, loginUserId);
    }

    @ApiOperation("传染病预警规则维护-编辑规则详情")
    @PostMapping("/{version}/pt/medicalWarn/editById")
    public ResponseResult editById(@RequestBody MedicalWarnEditDto editDto, @RequestParam String loginUserId) {
        editDto.setUpdator(loginUserId);
        return medicalWarnService.editById(editDto);
    }

    @ApiOperation("传染病预警规则维护-规则单病例开关")
    @PostMapping("/{version}/pt/medicalWarn/singleCaseSwitch")
    public ResponseResult singleCaseSwitch(@RequestBody MedicalWarnEditDto editDto, @RequestParam String loginUserId) {
        editDto.setUpdator(loginUserId);
        return medicalWarnService.singleCaseSwitch(editDto, loginUserId);
    }

    @ApiOperation("查询短信发送时间类型列表")
    @PostMapping("/{version}/pt/sendSMSTypeList")
    public List<SendSMSTypeDto> sendSMSTypeList(@RequestParam @ApiParam("处理状态 1-立即发送 2-指定时间发送") Integer type) {
        return medicalWarnService.getTypeList(type);
    }
}
