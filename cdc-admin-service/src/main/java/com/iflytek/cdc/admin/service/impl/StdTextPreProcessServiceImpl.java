package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.TbCdcmrStdTextConfig;
import com.iflytek.cdc.admin.enums.StdConfigRuleTypeEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrStdTextConfigMapper;
import com.iflytek.cdc.admin.service.StdTextPreProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StdTextPreProcessServiceImpl implements StdTextPreProcessService {

    @Value("${cdc.std.dataClean.regex.rules:[^a-zA-Z0-9\\u4e00-\\u9fa5]}")
    private String dataCleanRegexRules;

    @Resource
    private TbCdcmrStdTextConfigMapper tbCdcmrStdTextConfigMapper;
    private Map<String, List<TbCdcmrStdTextConfig>> stdTextConfMap = new HashMap<>();

    //@PostConstruct
    private void buildStdConfig() {
       cacheConfig();
    }

    @Override
    public List<TbCdcmrStdTextConfig> getConfigByType(String configType) {
        return stdTextConfMap.getOrDefault(configType, new ArrayList<>());
    }

    @Override
    public int cacheConfig() {
        List<TbCdcmrStdTextConfig> tbCdcmrStdTextConfigs = tbCdcmrStdTextConfigMapper.selectAll();
        if (CollUtil.isEmpty(tbCdcmrStdTextConfigs)) {
            return 0;
        }
        stdTextConfMap = tbCdcmrStdTextConfigs.stream().collect(Collectors.groupingBy(e -> e.getStdType() + e.getItemType()));
        return stdTextConfMap.size();
    }

    @Override
    public boolean isValid(String configType, String text) {
        List<TbCdcmrStdTextConfig> configList = getConfigByType(configType).stream().filter(e -> StdConfigRuleTypeEnum.IGNORE.getCode().equals(e.getRuleType()))
                                                                                    .collect(Collectors.toList());
        if (CollUtil.isEmpty(configList)) {
            return true;
        }
        Set<String> valueSet = configList.stream().map(TbCdcmrStdTextConfig::getItemValue).collect(Collectors.toSet());
        return !valueSet.contains(text);
    }

    @Override
    public String replace(String configType, String text) {
        List<TbCdcmrStdTextConfig> configList = getConfigByType(configType).stream().filter(e -> StdConfigRuleTypeEnum.REPLACE.getCode().equals(e.getRuleType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(configList)) {
            return text;
        }
        Map<String, String> mapping = new HashMap<>(configList.size());
        configList.forEach(e -> mapping.put(e.getItemValue(), e.getReplaceValue()));
        String changeText = text;
        for (Map.Entry<String, String> next : mapping.entrySet()) {
            changeText = changeText.replace(next.getKey(), next.getValue());
        }
        return changeText;
    }

    @Override
    public String subString(String configType, String text) {
        List<TbCdcmrStdTextConfig> configList = getConfigByType(configType).stream().filter(e -> StdConfigRuleTypeEnum.SUBSTR.getCode().equals(e.getRuleType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(configList)) {
            return text;
        }

        List<String> keys = configList.stream().sorted(Comparator.comparing(TbCdcmrStdTextConfig::getOrder)).map(TbCdcmrStdTextConfig::getItemValue).collect(Collectors.toList());
        String changeText = text;
        for (String key : keys) {
            int index = text.indexOf(key);
            if (index > 0) {
                changeText = text.substring(0, Math.min(index + key.length(), text.length()));
                break;
            }
        }
        return changeText;
    }

    @Override
    public String textPreProcess(String stdCategory, String types, String text) {
        String poiTypes = Constants.STD_TYPE_COMMON;
        if (StringUtils.isNotBlank(types)) {
            poiTypes += Constants.TAG_DELIMITER + types;
        }
        String newText = text;
        List<String> poiTypeList = CharSequenceUtil.splitTrim(poiTypes, Constants.TAG_DELIMITER);
        for (String poiType : poiTypeList) {
            newText = textPreProcess(stdCategory + poiType, newText);
        }
        return newText;
    }

    @Override
    public String textPreProcess(String configType, String text) {
        //数据清洗
        String cleanTest = removeSpecialCharacters(text);
        //小于1个字符，则忽略掉
        if (StringUtils.isBlank(cleanTest)
            || cleanTest.length() <= 1) {
            return null;
        }
        //停用词
        if (NumberUtil.isNumber(cleanTest)
            || !isValid(configType, cleanTest)) {
            return null;
        }
        //替换
        String newText = replace(configType, cleanTest);
        //截取
        return subString(configType, newText);
    }

    public String removeSpecialCharacters(String input) {
        try {
            Pattern pattern = Pattern.compile(dataCleanRegexRules);
            Matcher matcher = pattern.matcher(input);
            return matcher.replaceAll("");
        } catch (Exception ex) {
            log.error("数据清洗错误：{}, 清洗规则：{}", input, dataCleanRegexRules);
        }
        return input;
    }
}
