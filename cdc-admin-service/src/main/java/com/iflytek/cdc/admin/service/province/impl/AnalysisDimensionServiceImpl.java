package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.datamodel.service.ValueDomainService;
import com.iflytek.cdc.admin.dto.AnalysisDimQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueRollupRelDto;
import com.iflytek.cdc.admin.dto.AnalysisTargetDto;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimRollupValueRel;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimValue;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimRollupRel;
import com.iflytek.cdc.admin.entity.TbCdcmrAnalysisDimension;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimValueMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimRollupRelMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimRollupValueRelMapper;
import com.iflytek.cdc.admin.model.DataForest;
import com.iflytek.cdc.admin.model.DataTreeNode;
import com.iflytek.cdc.admin.common.apiservice.BatchCommonService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.StreamUtils;
import com.iflytek.cdc.admin.vo.AnalysisDimDetailInfoVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupValueRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupValueRelVO.RollupValueRel;
import com.iflytek.cdc.admin.vo.AnalysisDimValueVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionEditVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.iflytek.cdc.admin.mapper.TbCdcmrAnalysisDimensionMapper;
import com.iflytek.cdc.admin.service.province.AnalysisDimensionService;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class AnalysisDimensionServiceImpl implements AnalysisDimensionService {

    /**
     * 获取序号用的类级别的非公平锁，可用 synchronized 替代
     */
    private static final ReentrantLock DIM_SEQ_LOCK = new ReentrantLock();

    /**
     * 获取序号用的类级别的非公平锁，可用 synchronized 替代
     */
    private static final ReentrantLock REL_SEQ_LOCK = new ReentrantLock();

    @Resource
    TbCdcmrAnalysisDimensionMapper analysisDimensionMapper;
    @Resource
    TbCdcmrAnalysisDimRollupRelMapper analysisDimRollupRelMapper;

    @Resource
    TbCdcmrAnalysisDimValueMapper analysisDimValueMapper;
    @Resource
    TbCdcmrAnalysisDimRollupValueRelMapper analysisDimRollupValueRelMapper;

    @Resource
    BatchCommonService batchCommonService;

    @Resource
    private ValueDomainService valueDomainService;

    private final DataTreeNode.NodeAdapter<TbCdcdmDataDictValue> nodeAdapter = new DataTreeNode.NodeAdapter<TbCdcdmDataDictValue>() {
        @Override
        public String getId(TbCdcdmDataDictValue dictValue) {
            return dictValue.getId();
        }

        @Override
        public String getLabel(TbCdcdmDataDictValue dictValue) {
            return dictValue.getName();
        }

        @Override
        public String getValue(TbCdcdmDataDictValue dictValue) {
            return dictValue.getCode();
        }

        @Override
        public String getParentId(TbCdcdmDataDictValue dictValue) {
            return dictValue.getParentId();
        }
    };

    private int getDimNextSeq() {
        DIM_SEQ_LOCK.lock();
        try {
            Integer currentSeq = analysisDimensionMapper.loadLastNum();
            if (currentSeq == null) {
                return 1;
            }
            return currentSeq + 1;
        } finally {
            DIM_SEQ_LOCK.unlock();
        }
    }

    private int getRelNextSeq(String analysisDimId) {
        REL_SEQ_LOCK.lock();
        try {
            if (analysisDimId == null) {
                return 1;
            }
            Integer currentSeq = analysisDimRollupRelMapper.loadLastNum(analysisDimId);
            if (currentSeq == null) {
                return 1;
            }
            return currentSeq + 1;
        } finally {
            REL_SEQ_LOCK.unlock();
        }
    }

    private void checkDuplicateDimName(String name, String existedId) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        Wrapper<TbCdcmrAnalysisDimension> wrapper;
        if (StrUtil.isBlank(existedId)) {
            wrapper = Wrappers.<TbCdcmrAnalysisDimension>lambdaQuery()
                    .eq(TbCdcmrAnalysisDimension::getDimensionName, name)
                    .eq(TbCdcmrAnalysisDimension::getDeleteFlag, DeleteFlagEnum.NO.getCode());
        } else {
            wrapper = Wrappers.<TbCdcmrAnalysisDimension>lambdaQuery()
                    .eq(TbCdcmrAnalysisDimension::getDimensionName, name)
                    .ne(TbCdcmrAnalysisDimension::getId, existedId)
                    .eq(TbCdcmrAnalysisDimension::getDeleteFlag, DeleteFlagEnum.NO.getCode());
        }
        if (analysisDimensionMapper.selectCount(wrapper) > 0) {
            throw new MedicalBusinessException("名称重复，请重新定义维度");
        }
    }

    @Override
    public void addDim(AnalysisDimensionEditVO editVO) {
        this.checkDuplicateDimName(editVO.getDimensionName(), null);

        TbCdcmrAnalysisDimension entity = new TbCdcmrAnalysisDimension();
        BeanUtils.copyProperties(editVO, entity);
        entity.setId(batchCommonService.uuid(TbCdcmrAnalysisDimension.class));

        // 根据序号填充编码，作为页面上的维度ID展示
        entity.setSeqNum(this.getDimNextSeq());
        entity.setDimCode(String.valueOf(entity.getSeqNum()));

        entity.setStatus(Constants.STATUS_ON);
        entity.setDeleteFlag(DeleteFlagEnum.NO.getCode());

        UapUserPo uapUserPo = USER_INFO.get();
        String uapUserPoId = uapUserPo.getId();
        String uapUserPoName = uapUserPo.getName();
        Date now = new Date();

        entity.setCreatorId(uapUserPoId);
        entity.setCreator(uapUserPoName);
        entity.setCreateTime(now);

        entity.setUpdaterId(uapUserPoId);
        entity.setUpdater(uapUserPoName);
        entity.setUpdateTime(now);

        handleAnalysisTargetListToStr(editVO,entity);
        analysisDimensionMapper.insert(entity);
    }

    @Override
    public AnalysisDimensionVO getDimById(String id) {
        AnalysisDimensionVO vo = analysisDimensionMapper.selectVoById(id);
        if (vo != null) {
            List<AnalysisDimRollupRelVO> rollupDims = this.getRollupDims(id);
            vo.setRollupDimensions(rollupDims);
            handleAnalysisTargetToList(vo);
        }
        return vo;
    }

    @Override
    public void updateDimById(AnalysisDimensionEditVO editVO) {
        this.checkDuplicateDimName(editVO.getDimensionName(), editVO.getId());

        UapUserPo uapUserPo = USER_INFO.get();
        String uapUserPoId = uapUserPo.getId();
        String uapUserPoName = uapUserPo.getName();
        Date now = new Date();

        TbCdcmrAnalysisDimension entity = new TbCdcmrAnalysisDimension();
        BeanUtils.copyProperties(editVO, entity);

        entity.setUpdaterId(uapUserPoId);
        entity.setUpdater(uapUserPoName);
        entity.setUpdateTime(now);

        handleAnalysisTargetListToStr(editVO,entity);
        analysisDimensionMapper.updateById(entity);
    }

    @Override
    public void deleteDimById(String id) {
        analysisDimRollupRelMapper.softDelByDimId(id);
        analysisDimensionMapper.softDelete(id);
    }

    @Override
    public PageInfo<AnalysisDimensionVO> pageList(AnalysisDimQueryDto queryDto) {
        PageHelper.startPage(queryDto.getPageIndex(), queryDto.getPageSize());
        List<AnalysisDimensionVO> analysisDimensions = analysisDimensionMapper.pageList(queryDto);
        List<String> dimIds = analysisDimensions.stream().map(AnalysisDimensionVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dimIds)) {
            return new PageInfo<>(Collections.emptyList());
        }
        // 上卷维度
        Map<String, List<AnalysisDimRollupRelVO>> relMap = analysisDimRollupRelMapper.selectByDimIds(dimIds)
                                                                                     .stream()
                                                                                     .collect(Collectors.groupingBy(AnalysisDimRollupRelVO::getAnalysisDimId));
        for (AnalysisDimensionVO analysisDimensionVO : analysisDimensions) {
            List<AnalysisDimRollupRelVO> rollupDims = relMap.getOrDefault(analysisDimensionVO.getId(), new ArrayList<>());
            rollupDims.sort(Comparator.comparing(vo -> Optional.ofNullable(vo.getSeqNum()).orElse(0)));
            analysisDimensionVO.setRollupDimensions(rollupDims);
            handleAnalysisTargetToList(analysisDimensionVO);
        }
        return new PageInfo<>(analysisDimensions);
    }

    @Override
    public List<AnalysisDimRollupRelVO> getRollupDims(String dimId) {
        List<AnalysisDimRollupRelVO> rollupRelVOS = analysisDimRollupRelMapper.selectByDimIds(Collections.singletonList(dimId));
        rollupRelVOS.sort(Comparator.comparing(vo -> Optional.ofNullable(vo.getSeqNum()).orElse(0)));
        return rollupRelVOS;
    }

    @Override
    public AnalysisDimDetailInfoVO getDimDetailInfo(AnalysisDimValueQueryDto queryDto) {
        AnalysisDimensionVO dimById = this.getDimById(queryDto.getId());
        if (dimById == null) {
            return null;
        }

        String analysisDimId = dimById.getId();

        AnalysisDimDetailInfoVO dimDetailVO = new AnalysisDimDetailInfoVO();
        dimDetailVO.setDimensionVO(dimById);

        List<TbCdcdmDataDictValue> dataDictValues = getDimDataDictValues(queryDto, dimById);
        if (CollUtil.isNotEmpty(dataDictValues)) {
            List<String> dataAttrValueIds = dataDictValues.stream().map(TbCdcdmDataDictValue::getId).collect(Collectors.toList());

            LambdaQueryWrapper<TbCdcmrAnalysisDimValue> queryWrapper = Wrappers.<TbCdcmrAnalysisDimValue>lambdaQuery()
                    .eq(TbCdcmrAnalysisDimValue::getAnalysisDimId, analysisDimId)
                    .in(TbCdcmrAnalysisDimValue::getDataAttrValueId, dataAttrValueIds);
            List<TbCdcmrAnalysisDimValue> dimValues = analysisDimValueMapper.selectList(queryWrapper);

            DataForest<AnalysisDimValueVO> dataForest = this.buildDimDetailInfoVOS(dimById, dataDictValues, dimValues);
            dimDetailVO.setDimValues(dataForest);
        } else {
            dimDetailVO.setDimValues(new DataForest<>());
        }
        return dimDetailVO;
    }

    private List<TbCdcdmDataDictValue> getDimDataDictValues(AnalysisDimValueQueryDto queryDto, AnalysisDimensionVO dimById) {
        String dataAttrId = dimById.getDataAttrId();
        TbCdcdmDataDict domainDict = valueDomainService.getValueDomainDictById(dataAttrId);
        String domainDictType = domainDict.getType();

        PageInfo<TbCdcdmDataDictValue> domainDetailPageData = valueDomainService.getValueDomainDetailList(dataAttrId,
                                                                                                          domainDictType,
                                                                                                          queryDto.getPageIndex(),
                                                                                                          queryDto.getPageSize());
        return domainDetailPageData.getList();
    }

    private DataForest<AnalysisDimValueVO> buildDimDetailInfoVOS(AnalysisDimensionVO dimensionVO,
                                                                 List<TbCdcdmDataDictValue> dataDictValues,
                                                                 List<TbCdcmrAnalysisDimValue> dimValues) {
        final Map<String, TbCdcmrAnalysisDimValue> dimValueMap = dimValues.stream()
                .collect(StreamUtils.toFMap(TbCdcmrAnalysisDimValue::getDataAttrValueId));
        // 定义 valueMapper 为节点 data 属性赋值
        return DataForest.buildTree(dataDictValues, nodeAdapter, t -> {
            String id = t.getId();
            TbCdcmrAnalysisDimValue analysisDimValue = dimValueMap.get(id);

            AnalysisDimValueVO dimValueVO = new AnalysisDimValueVO();
            if (analysisDimValue != null) {
                dimValueVO.setId(analysisDimValue.getId());
                dimValueVO.setAnalysisFlag(analysisDimValue.getAnalysisFlag());
            } else {
                dimValueVO.setAnalysisFlag(Constants.YES_USE);
            }
            dimValueVO.setAnalysisDimId(dimensionVO.getId());
            dimValueVO.setDataAttrValueId(id);
            return dimValueVO;
        });
    }

    @Override
    public void addRollupDim(AnalysisDimRollupRelVO rollupRelVO) {
        TbCdcmrAnalysisDimRollupRel entity = new TbCdcmrAnalysisDimRollupRel();
        BeanUtils.copyProperties(rollupRelVO, entity);
        entity.setId(batchCommonService.uuid(TbCdcmrAnalysisDimRollupRel.class));
        if (entity.getSeqNum() == null) {
            entity.setSeqNum(this.getRelNextSeq(entity.getAnalysisDimId()));
        }
        entity.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        analysisDimRollupRelMapper.insert(entity);
    }

    @Override
    public void updateRollupDim(AnalysisDimRollupRelVO rollupRelVO) {
        TbCdcmrAnalysisDimRollupRel entity = new TbCdcmrAnalysisDimRollupRel();
        BeanUtils.copyProperties(rollupRelVO, entity);
        entity.setUpdateTime(new Date());
        analysisDimRollupRelMapper.updateById(entity);
    }

    @Override
    public void deleteRollupDim(String id) {
        analysisDimRollupRelMapper.softDelById(id);
    }

    @Override
    public void editAnalysisFlag(AnalysisDimValueVO dimValueVO) {
        Wrapper<TbCdcmrAnalysisDimValue> wrapper = Wrappers.<TbCdcmrAnalysisDimValue>lambdaQuery()
                                                           .eq(TbCdcmrAnalysisDimValue::getAnalysisDimId, dimValueVO.getAnalysisDimId())
                                                           .eq(TbCdcmrAnalysisDimValue::getDataAttrValueId, dimValueVO.getDataAttrValueId());
        if (Objects.equals(dimValueVO.getAnalysisFlag(), Constants.YES_USE)) {
            analysisDimValueMapper.delete(wrapper);
        } else {
            TbCdcmrAnalysisDimValue existedEntity = analysisDimValueMapper.selectOne(wrapper);
            if (existedEntity == null) {
                existedEntity = new TbCdcmrAnalysisDimValue();
                BeanUtils.copyProperties(dimValueVO, existedEntity);
                existedEntity.setId(batchCommonService.uuid(TbCdcmrAnalysisDimValue.class));
                existedEntity.setCreateTime(new Date());
                existedEntity.setAnalysisFlag(Constants.NO_USE);
                existedEntity.setUpdateTime(new Date());
                analysisDimValueMapper.insert(existedEntity);
            } else {
                existedEntity.setAnalysisFlag(Constants.NO_USE);
                existedEntity.setUpdateTime(new Date());
                analysisDimValueMapper.updateById(existedEntity);
            }
        }
    }

    @Override
    public List<AnalysisDimRollupValueRelVO> getRollupValueRelBy(String analysisDimId, String dataAttrValueId) {
        AnalysisDimensionVO analysisDim = this.getDimById(analysisDimId);
        if (analysisDim == null) {
            return new ArrayList<>(0);
        }

        Wrapper<TbCdcmrAnalysisDimRollupValueRel> valueQueryWrapper =
                Wrappers.<TbCdcmrAnalysisDimRollupValueRel>lambdaQuery()
                        .eq(TbCdcmrAnalysisDimRollupValueRel::getAnalysisDimId, analysisDimId)
                        .eq(TbCdcmrAnalysisDimRollupValueRel::getDimAttrValueId, dataAttrValueId)
                        .eq(TbCdcmrAnalysisDimRollupValueRel::getDeleteFlag, DeleteFlagEnum.NO.getCode());

        Map<String, List<TbCdcmrAnalysisDimRollupValueRel>> dimRollupValueRelMap =
                analysisDimRollupValueRelMapper.selectList(valueQueryWrapper).stream()
                                               .collect(Collectors.groupingBy(TbCdcmrAnalysisDimRollupValueRel::getRollupDimId));

        ArrayList<AnalysisDimRollupValueRelVO> rollupValueRelVOS = new ArrayList<>();

        List<AnalysisDimRollupRelVO> rollupDimensions = analysisDim.getRollupDimensions();
        if (CollectionUtils.isNotEmpty(rollupDimensions)) {

            for (AnalysisDimRollupRelVO rollupDim : rollupDimensions) {
                String currDimId = rollupDim.getRollupDimId();
                AnalysisDimensionVO currDimension = getDimById(currDimId);
                String currDataAttrId = currDimension.getDataAttrId();
                List<String> relationIds = dimRollupValueRelMap.getOrDefault(currDimId, new ArrayList<>())
                                                               .stream()
                                                               .map(TbCdcmrAnalysisDimRollupValueRel::getRollupAttrValueId)
                                                               .collect(Collectors.toList());

                List<TbCdcdmDataDictValue> dictValueList = valueDomainService.getDataDictValueList(currDataAttrId);
                if (CollectionUtils.isNotEmpty(dictValueList)) {
                    AnalysisDimRollupValueRelVO rollupValueRelVO = new AnalysisDimRollupValueRelVO();
                    rollupValueRelVO.setRollupDimId(currDimId);
                    rollupValueRelVO.setRollupDimName(currDimension.getDimensionName());
                    rollupValueRelVO.setRollupAttrId(currDimension.getDataAttrId());

                    List<RollupValueRel> valueRels = dictValueList.stream().map(dataDictValue -> {
                        RollupValueRel rollupValueRel = new RollupValueRel();
                        rollupValueRel.setRollupAttrValueId(dataDictValue.getId());
                        rollupValueRel.setRollupAttrValueName(dataDictValue.getName());
                        rollupValueRel.setCheckFlag(relationIds.contains(dataDictValue.getId()) ? Constants.YES_USE : Constants.NO_USE);
                        return rollupValueRel;
                    }).collect(Collectors.toList());
                    rollupValueRelVO.setRollupValueRels(valueRels);
                    rollupValueRelVOS.add(rollupValueRelVO);
                }
            }
        }

        return rollupValueRelVOS;
    }

    @Override
    public void editRollupDimValue(String analysisDimId, String dataAttrValueId, List<AnalysisDimValueRollupRelDto> rollupValueRelVOs) {

        Wrapper<TbCdcmrAnalysisDimRollupValueRel> valueQueryWrapper =
                Wrappers.<TbCdcmrAnalysisDimRollupValueRel>lambdaQuery()
                        .eq(TbCdcmrAnalysisDimRollupValueRel::getAnalysisDimId, analysisDimId)
                        .eq(TbCdcmrAnalysisDimRollupValueRel::getDimAttrValueId, dataAttrValueId);

        analysisDimRollupValueRelMapper.delete(valueQueryWrapper);

        if (CollectionUtils.isNotEmpty(rollupValueRelVOs)) {
            for (AnalysisDimValueRollupRelDto rollupValueRelVO : rollupValueRelVOs) {
                if (StrUtil.equals(dataAttrValueId, rollupValueRelVO.getRollupAttrValueId())) {
                    throw new MedicalBusinessException("上卷维度的值不能与该维度值相同");
                }
                TbCdcmrAnalysisDimRollupValueRel dimRollupValueRel = new TbCdcmrAnalysisDimRollupValueRel();
                dimRollupValueRel.setId(batchCommonService.uuid(TbCdcmrAnalysisDimRollupValueRel.class));
                dimRollupValueRel.setAnalysisDimId(analysisDimId);
                dimRollupValueRel.setDimAttrValueId(dataAttrValueId);
                dimRollupValueRel.setRollupDimId(rollupValueRelVO.getRollupDimId());
                dimRollupValueRel.setRollupAttrValueId(rollupValueRelVO.getRollupAttrValueId());
                dimRollupValueRel.setDeleteFlag(DeleteFlagEnum.NO.getCode());
                dimRollupValueRel.setCreateTime(new Date());
                dimRollupValueRel.setUpdateTime(new Date());
                analysisDimRollupValueRelMapper.insert(dimRollupValueRel);
            }
        }
    }

    public void handleAnalysisTargetToList(AnalysisDimensionVO vo){
        if (vo != null){
            String analysisTarget = vo.getAnalysisTarget();
            if (StrUtil.isBlank(analysisTarget)){
                return;
            }
            List<AnalysisTargetDto> analysisTargetList = new Gson().fromJson(
                    vo.getAnalysisTarget(),
                    new TypeToken<List<AnalysisTargetDto>>() {
                    }.getType()
            );
            vo.setAnalysisTargetList(analysisTargetList);
            vo.setAnalysisTarget(null);
        }
    }

    public void handleAnalysisTargetListToStr(AnalysisDimensionEditVO editVO,TbCdcmrAnalysisDimension entity){
        List<AnalysisTargetDto> analysisTargetList = editVO.getAnalysisTargetList();
        if (analysisTargetList != null && !analysisTargetList.isEmpty()){
            String analysisTargetJson = new Gson().toJson(analysisTargetList);
            entity.setAnalysisTargetStr(analysisTargetJson);
        }
    }
}
