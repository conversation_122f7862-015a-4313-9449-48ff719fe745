package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.CommonParamPageDTO;
import com.iflytek.cdc.admin.dto.CommonParamListDTO;
import com.iflytek.cdc.admin.dto.CommonParamSimpleDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrCommonParam;
import com.iflytek.cdc.admin.service.province.TbCdcmrCommonParamService;
import com.iflytek.cdc.admin.dto.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "通用配置参数管理")
@RestController
@RequestMapping("/api/province/common/param")
public class CommonParamController {

    @Resource
    private TbCdcmrCommonParamService tbCdcmrCommonParamService;

    @ApiOperation("新增配置")
    @PostMapping("/add")
    public ResponseResult<String> save(@RequestBody TbCdcmrCommonParam param, @RequestParam String loginUserName) {
        return new ResponseResult<>(tbCdcmrCommonParamService.insert(param, loginUserName));
    }

    @ApiOperation("更新配置")
    @PostMapping("/update")
    public ResponseResult<Boolean> update(@RequestBody TbCdcmrCommonParam param, @RequestParam String loginUserName) {
        return new ResponseResult<>(tbCdcmrCommonParamService.updateBy(param, loginUserName));
    }

    @ApiOperation("删除配置")
    @PostMapping("/delete/{id}")
    public ResponseResult<Boolean> delete(@PathVariable String id) {
        return new ResponseResult<>(tbCdcmrCommonParamService.deleteById(id));
    }

    @ApiOperation("获取配置详情")
    @GetMapping("/{id}")
    public ResponseResult<TbCdcmrCommonParam> getById(@PathVariable String id) {
        return new ResponseResult<>(tbCdcmrCommonParamService.getById(id));
    }

    @ApiOperation("根据paramKey获取配置详情")
    @GetMapping("/key")
    public ResponseResult<TbCdcmrCommonParam> getByParamKey(@RequestParam String paramKey) {
        return new ResponseResult<>(tbCdcmrCommonParamService.getByParamKey(paramKey));
    }

    @ApiOperation("分页查询配置")
    @PostMapping("/page")
    public ResponseResult<PageInfo<TbCdcmrCommonParam>> page(CommonParamPageDTO dto) {

        List<TbCdcmrCommonParam> commonParams = tbCdcmrCommonParamService.getCommonParams(dto);

        return new ResponseResult<>(new PageInfo<>(commonParams));
    }

    @ApiOperation("根据配置分类和配置类型查询配置列表")
    @PostMapping("/list")
    public ResponseResult<List<CommonParamSimpleDTO>> list(@RequestBody CommonParamListDTO dto) {
        List<CommonParamSimpleDTO> list = tbCdcmrCommonParamService.getCommonParamSimpleList(dto);
        return new ResponseResult<>(list);
    }

} 