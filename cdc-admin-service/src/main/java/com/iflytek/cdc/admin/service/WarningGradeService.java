package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.WarningGradeQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade;

import java.util.List;

public interface WarningGradeService {

    PageInfo<TbCdcmrWarningGrade> getWarningGradePageList(WarningGradeQueryDTO warningGradeQueryDTO);

    void addWarningGrade(TbCdcmrWarningGrade tbCdcmrWarningGrade, String loginUserId);

    void deleteWarningGrade(String gradeCode);

    void updateWarningGrade(TbCdcmrWarningGrade tbCdcmrWarningGrade, String loginUserId);

    List<TbCdcmrWarningGrade> getWarningGradeList();

    List<TbCdcmrWarningGrade> getConfigByTypeAndCode(String configType, String diseaseCode);


}
