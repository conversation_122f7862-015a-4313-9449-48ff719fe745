package com.iflytek.cdc.admin.datamodel.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableSqlLog;
import com.iflytek.cdc.admin.datamodel.model.dto.MetadataTableInfoQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.TableColumnInfoExcelVO;
import com.iflytek.cdc.admin.datamodel.service.MetaDataService;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableInfoService;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableSqlLogService;
import com.iflytek.cdc.admin.enums.TableStatusEnum;
import com.iflytek.cdc.admin.util.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 元数据
 * */
@RestController
@Api(tags = "表名表字段查询")
public class MetaDataController {

    @Resource
    private MetaDataService metaDataService;

    @Resource
    private MetadataTableInfoService tableInfoService;

    @Resource
    private MetadataTableSqlLogService metadataTableSqlLogService;

    @GetMapping("/pt/{version}/metaData/getAllTable")
    @ApiOperation("获取所有的表")
    public List<TbCdcdmMetadataTableInfo> getAllTable(@RequestParam(required = false) String tableName){

        return metaDataService.getAllTable(tableName);
    }

    @PostMapping("/pt/{version}/metaData/listByTableNames")
    @ApiOperation("根据表名集合获取表")
    public List<TbCdcdmMetadataTableInfo> listByTableNames(@RequestBody List<String> tableNames){
        return metaDataService.listByTableNames(tableNames);
    }

    @GetMapping("/pt/{version}/metaData/getFieldByTableName")
    @ApiOperation("通过表名获取 表所有字段")
    public List<TbCdcdmMetadataTableColumnInfo> getFieldByTableName(@RequestParam String tableName,
                                                                    @RequestParam(required = false) String columnName){

        return metaDataService.getFieldByTableName(tableName, columnName);
    }

    @PostMapping("/pt/{version}/metaData/getFieldByTableNames")
    @ApiOperation("通过表名获取 表所有字段")
    public List<TbCdcdmMetadataTableColumnInfo> getFieldByTableNames(@RequestBody List<String> tableNames){
        return metaDataService.getFieldByTableNames(tableNames);
    }
    @GetMapping("/pt/{version}/metaData/loadTable")
    @ApiOperation("根据id加载实体模型")
    public TbCdcdmMetadataTableInfo loadTable(@RequestParam String id){
        return tableInfoService.loadCascade(id);
    }

    @GetMapping("/pt/{version}/metaData/deleteTable")
    @ApiOperation("删除实体模型")
    public void deleteTable(@RequestParam String id){
        tableInfoService.deleteById(id);
    }

    @PostMapping("/pt/{version}/metaData/pageList")
    @ApiOperation("实体模型列表")
    public PageInfo<TbCdcdmMetadataTableInfo> pageList(@RequestBody MetadataTableInfoQueryDTO queryDTO){
        return tableInfoService.pageList(queryDTO);
    }


    @PostMapping("/pt/{version}/metaData/createTable")
    @ApiOperation("创建实体模型")
    public TbCdcdmMetadataTableInfo createTable(@RequestBody TbCdcdmMetadataTableInfo input){
        return tableInfoService.create(input);
    }

    @PostMapping("/pt/{version}/metaData/updateTable")
    @ApiOperation("修改实体模型")
    public TbCdcdmMetadataTableInfo updateTable(@RequestBody TbCdcdmMetadataTableInfo input){
        return tableInfoService.update(input);
    }

    @PostMapping("/pt/{version}/metaData/createEntityTable")
    @ApiOperation("创建实体表")
    public void createEntityTable(@RequestParam String id){
        tableInfoService.createEntityTable(id);
    }

    @PostMapping("/pt/{version}/metaData/syncEntityTable")
    @ApiOperation("同步实体表")
    public void syncEntityTable(@RequestParam String id){
        tableInfoService.syncEntityTable(id);
    }

    @PostMapping("/pt/{version}/metaData/downloadTemplate")
    @ApiOperation("模板下载")
    public ResponseEntity<byte[]> downloadTemplate(){
        String fileName = Constants.TABLE_COLUMN_INFO_FILENAME;
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileName);
        return new ResponseEntity<>(tableInfoService.downloadTemplate(), httpHeaders, HttpStatus.CREATED);
    }
    
    @PostMapping("/pt/{version}/metaData/readExecl")
    @ApiOperation("读取列信息表格")
    public List<TableColumnInfoExcelVO> readExecl(@RequestParam("file") MultipartFile file) throws IOException {
        return tableInfoService.readExcel(file.getInputStream());
    }

    @PostMapping("/pt/{version}/metaData/importByFile")
    @ApiOperation("根据文件导入")
    public void importByFile(@RequestParam("file") MultipartFile file) throws IOException {
         tableInfoService.importByFile(file.getInputStream());
    }

    @PostMapping("/pt/{version}/metaData/listToExecuteSql")
    @ApiOperation("待执行sql")
    public List<TbCdcdmMetadataTableSqlLog> listToExecuteSql(){
        return metadataTableSqlLogService.listToExecute();
    }

    @PostMapping("/pt/{version}/metaData/executeSqlFail")
    @ApiOperation("执行sql 失败")
    public void executeSqlFail(@RequestBody List<TbCdcdmMetadataTableSqlLog> sqlLogs){
        tableInfoService.updateSyncStatus(sqlLogs, TableStatusEnum.SYNC_FAILED.getCode());
    }

    @PostMapping("/pt/{version}/metaData/executeSqlSuccess")
    @ApiOperation("执行sql 成功")
    public void executeSqlSuccess(@RequestBody List<TbCdcdmMetadataTableSqlLog> sqlLogs){
        tableInfoService.updateSyncStatus(sqlLogs, TableStatusEnum.SYNCED.getCode());
    }
}
