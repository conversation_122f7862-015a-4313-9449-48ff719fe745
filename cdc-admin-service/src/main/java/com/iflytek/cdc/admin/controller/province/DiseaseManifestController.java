package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseManifestQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiseaseManifestRulesVO;
import com.iflytek.cdc.admin.service.province.DiseaseManifestService;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "省统筹 - 症状监测列表以及配置")
@RequestMapping("/pt/{version}/diseaseManifest")
@RestController
public class DiseaseManifestController {

    @Resource
    private DiseaseManifestService diseaseManifestService;

    @PostMapping("/getDiseaseManifestInfoList")
    @ApiOperation("查询症状监测列表")
    public PageInfo<TbCdcmrDiseaseManifestInfo> getDiseaseManifestInfoList(@RequestBody DiseaseManifestQueryDTO dto){

        return diseaseManifestService.getDiseaseManifestInfoList(dto);
    }

    @PostMapping("/addDiseaseManifestInfo")
    @ApiOperation("新增症状监测信息")
    @OperationLogAnnotation(operationName = "新增症状监测信息")
    public void addDiseaseManifestInfo(@RequestBody TbCdcmrDiseaseManifestInfo manifestInfo){

        diseaseManifestService.addDiseaseManifestInfo(manifestInfo);
    }

    @PostMapping("/updateDiseaseManifestInfo")
    @ApiOperation("更新某条症状监测信息")
    @OperationLogAnnotation(operationName = "更新某条症状监测信息")
    public void updateDiseaseManifestInfo(@RequestBody TbCdcmrDiseaseManifestInfo manifestInfo){

        diseaseManifestService.updateDiseaseManifestInfo(manifestInfo);
    }

    @GetMapping("/getDiseaseManifestRules")
    @ApiOperation("查询症状的规则组（不传则查全部）")
    public List<DiseaseManifestRulesVO> getDiseaseManifestRules(@RequestParam(required = false) String diseaseManifestId){

        return diseaseManifestService.getDiseaseManifestRules(diseaseManifestId);
    }

    @PostMapping("/editDiseaseManifestRules")
    @ApiOperation("编辑规则组")
    @OperationLogAnnotation(operationName = "编辑规则组")
    public void editDiseaseManifestRules(@RequestParam String diseaseManifestId,
                                         @RequestBody List<TbCdcmrDiseaseManifestRule> manifestRules){

        diseaseManifestService.editDiseaseManifestRules(diseaseManifestId, manifestRules);
    }

}
