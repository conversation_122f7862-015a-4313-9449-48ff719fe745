package com.iflytek.cdc.admin.customizedapp.enums;

import lombok.Getter;

@Getter
public enum VersionStatusEnum {
    WAITING_PUBLISH("waiting_publish", "待发布"),
    PUBLISHED("published", "已发布"),
    CANCELLED("cancelled", "已作废"),
    ;

    private final String code;

    private final String desc;

    VersionStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
