package com.iflytek.cdc.admin.capacity.controller;


import com.iflytek.cdc.admin.capacity.api.algorithm.AlgorithmApi;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.KnowledgeBaseSearchResponse;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.RiskAssessResponse;
import com.iflytek.cdc.admin.capacity.model.dto.*;
import com.iflytek.cdc.admin.capacity.model.vo.AwarenessResultVO;
import com.iflytek.cdc.admin.capacity.model.vo.DispositionAdviceResponseVO;
import com.iflytek.cdc.admin.capacity.model.vo.EventClassifyResponseVO;
import com.iflytek.cdc.admin.capacity.service.AlgorithmApiService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/{version}/pt/capacity")
public class CapacityController {
    @Resource
    private AlgorithmApiService algorithmApiService;

    @Resource
    private AlgorithmApi algorithmApi;

    /**
     * 态势感知推演包含参数干预
     */
    @PostMapping("/algorithm/awareness")
    @ResponseBody
    @ApiOperation("态势感知推演")
    public String awareness(@RequestBody AwarenessRequestDTO awarenessRequest, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.awareness(awarenessRequest, loginUserId);
    }

    /**
     * 态势感知推演包含参数干预
     */
    @GetMapping("/algorithm/deleteToken")
    @ResponseBody
    @ApiOperation("删除Token")
    public void deleteToken(){
        algorithmApiService.deleteToken();
    }

    /**
     * 结果获取
     */
    @PostMapping("/algorithm/loadAwarenessResult")
    @ResponseBody
    @ApiOperation("结果获取")
    public AwarenessResultVO loadAwarenessResult(@RequestParam String taskId, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.loadAwarenessResult(taskId, loginUserId);
    }

    /**
     * 风险等级研判
     */
    @PostMapping("/algorithm/eventClassify")
    @ResponseBody
    @ApiOperation("风险等级研判")
    public EventClassifyResponseVO eventClassify(@RequestBody EventClassifyRequestDTO requestDTO, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.eventClassify(requestDTO, loginUserId);
    }

    /**
     * 风险处置建议
     */
    @PostMapping("/algorithm/dispositionAdvice")
    @ResponseBody
    @ApiOperation("风险处置建议")
    public DispositionAdviceResponseVO dispositionAdvice(@RequestBody DispositionAdviceRequestDTO requestDTO, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.dispositionAdvice(requestDTO, loginUserId);
    }

    /**
     * 疾病趋势预测
     */
    @PostMapping("/algorithm/cdcDiseaseTrendPrediction")
    @ResponseBody
    @ApiOperation("疾病趋势预测")
    public List<Integer> cdcDiseaseTrendPrediction(@RequestBody  DiseaseTrendRequestDTO requestDTO, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.cdcDiseaseTrendPrediction(requestDTO, loginUserId);
    }

    /**
     * 态势推演-时间序列模型
     */
    @PostMapping("/algorithm/arima")
    @ResponseBody
    @ApiOperation("态势推演-时间序列模型")
    public List<Integer> arima(@RequestBody ArimaRequestDTO requestDTO, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.arima(requestDTO, loginUserId);
    }

    /**
     * 态势推演-时空统计模型
     */
    @PostMapping("/algorithm/stgnn")
    @ResponseBody
    @ApiOperation("态势推演-时空统计模型")
    public List<Integer> stgnn(@RequestBody STGNNRequestDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.stgnn(request, loginUserId);
    }

    /**
     * 态势推演-神经网络模型
     */
    @PostMapping("/algorithm/lstm")
    @ResponseBody
    @ApiOperation("态势推演-神经网络模型")
    public List<Integer> lstm(@RequestBody LSTMRequestDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.lstm(request, loginUserId);
    }

    /**
     * 态势推演-传染病特定组合模型
     */
    @PostMapping("/algorithm/seir")
    @ResponseBody
    @ApiOperation("态势推演-传染病特定组合模型")
    public List<Integer> seir(@RequestBody SEIRRequestDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.seir(request, loginUserId);
    }

    /**
     * 态势推演-混合模型
     */
    @PostMapping("/algorithm/ensemble")
    @ResponseBody
    @ApiOperation("态势推演-混合模型")
    public List<Integer> ensemble(@RequestBody EnsembleRequestDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.ensemble(request, loginUserId);
    }


    /**
     * 辅助大屏-获取街道预测数据
     */
    @PostMapping("/algorithm/predictSpread")
    @ResponseBody
    @ApiOperation("辅助大屏-获取街道预测数据")
    public  Map<String, Map<String, Integer>> predictSpread(@RequestBody PredictSpreadDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.predictSpread(request,loginUserId);
    }

    /**
     * 辅助研判-风险评估
     */
    @PostMapping("/algorithm/riskAssess")
    @ResponseBody
    @ApiOperation("辅助研判-风险评估")
    public RiskAssessResponse riskAssess(@RequestBody RiskAssessRequestDTO request,@RequestParam(required = false) String loginUserId){
        return algorithmApiService.riskAssess(request, loginUserId);
    }

    /**
     * 辅助研判-事件知识库查询
     */
    @PostMapping("/algorithm/knowledgeBaseSearch")
    @ResponseBody
    @ApiOperation("辅助研判-事件知识库查询")
    public KnowledgeBaseSearchResponse knowledgeBaseSearch(@RequestBody KnowledgeBaseSearchDTO request, @RequestParam(required = false) String loginUserId){
        return algorithmApiService.knowledgeBaseSearch(request, loginUserId);
    }
}
