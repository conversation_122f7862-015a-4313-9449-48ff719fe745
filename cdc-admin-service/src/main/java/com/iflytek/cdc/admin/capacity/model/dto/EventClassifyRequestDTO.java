package com.iflytek.cdc.admin.capacity.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

/**
 * 事件分级请求
 */
@Data
@ApiModel("事件分级请求参数")
public class EventClassifyRequestDTO {
    @ApiModelProperty(value = "接口类型")
    private Integer jiekouType;//接口类型
    @ApiModelProperty(value = "关键词")
    private String keyword;//关键词
    @ApiModelProperty(value = "事件的地址等级")
    private String addressCode;//事件的地址等级
    @ApiModelProperty(value = "事件的患病/发病人数")
    private Integer diseaseNumber;//事件的患病/发病人数，缺失填-1
    @ApiModelProperty(value = "事件的死亡人数")
    private Integer deathNumber;//事件的死亡人数，缺失填-1
    

    @Getter
    public enum AddressCodeEnum{
        PROVINCE("省级"),
        CITY("地市"),
        DISTRICT("县区"),
        STREET("街道"),
        ORG("集体单位");

        private String name;

        AddressCodeEnum(String name) {
            this.name = name;
        }
    }
    
}
