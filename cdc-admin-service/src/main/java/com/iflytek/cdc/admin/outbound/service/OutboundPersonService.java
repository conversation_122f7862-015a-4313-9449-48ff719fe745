package com.iflytek.cdc.admin.outbound.service;

import com.iflytek.cdc.admin.outbound.entity.OutboundPerson;
import com.iflytek.cdc.admin.outbound.model.vo.OutboundPersonVO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface OutboundPersonService extends ICdcService<OutboundPerson> {

    /**
     * 根据recordId 查询
     */
    List<OutboundPersonVO> listByRecordIds(List<String> recordIds);

    /**
     * 手工的变更结果
     */
    void updateManualResult(String personId, String result);

}
