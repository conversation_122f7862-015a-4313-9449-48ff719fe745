package com.iflytek.cdc.admin.datamodel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据模型配置
 * */
@Data
public class DataModelTemplateDTO {

    @ApiModelProperty(value = "配置表id")
    private String formTemplateDetailId;

    @ApiModelProperty(value = "系统的appCode（模型在该系统使用）")
    private String formTemplateCode;

    @ApiModelProperty(value = "系统名称")
    private String formTemplateName;

    @ApiModelProperty(value = "配置信息")
    private String configInfo;

    @ApiModelProperty(value = "配置json")
    private String configJson;

    @ApiModelProperty(value = "模型id")
    private String modelId;

    @ApiModelProperty(value = "模型name")
    private String modelName;

    @ApiModelProperty(value = "模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "串联模型中多个表的字段")
    private String keyField;

    @ApiModelProperty(value = "模型主表的id")
    private String masterTableId;

    @ApiModelProperty(value = "模型主表的code")
    private String masterTableCode;

    @ApiModelProperty(value = "模型主表的名称")
    private String masterTable;

    @ApiModelProperty(value = "模型的字段配置")
    private String fieldsConfig;

    @ApiModelProperty(value = "该模型在系统中所处分组模块")
    private String modelGroup;

    @ApiModelProperty(value = "前端读取配置")
    private String webConfig;

    @ApiModelProperty(value = "关联疾病")
    private String relatedDisease;

    @ApiModelProperty(value = "业务主键")
    private String businessKey;

    /**
     * 检索表
     */
    @ApiModelProperty(value = "检索表")
    private String retrieveTable ;

    @ApiModelProperty(value = "es 索引名")
    private String esIndexName;

}
