package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.ExportApplicationQueryDTO;
import com.iflytek.cdc.admin.dto.ExportApplicationRecordDTO;
import com.iflytek.cdc.admin.dto.ExportApprovalDTO;
import com.iflytek.cdc.admin.dto.ExportApprovalListDTO;
import com.iflytek.cdc.admin.job.ExportApprovalDelayedJobHandler;
import com.iflytek.cdc.admin.service.ExportApplicationService;
import com.iflytek.cdc.admin.service.ExportApprovalService;
import com.iflytek.cdc.admin.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "导出审核")
public class ExportApprovalController {

    @Resource
    private ExportApprovalService exportApprovalService;

    @Resource
    private ExportApplicationService exportApplicationService;

    @Resource
    private FileService fileService;

    @Resource
    private ExportApprovalDelayedJobHandler exportApprovalDelayedJobHandler;

    @PostMapping("/pt/{version}/exportApproval/submit")
    @ApiOperation("提交审核申请")
    @Transactional(rollbackFor = Exception.class)
    public Boolean submit(@RequestParam String loginUserId, @RequestBody ExportApprovalDTO exportApprovalDTO) {
        return exportApprovalService.submitApproval(loginUserId, exportApprovalDTO);
    }

    @PostMapping("/pt/{version}/exportApproval/queryList")
    @ApiOperation("获取导出审核列表")
    public PageInfo<ExportApprovalListDTO> queryList(@RequestParam String loginUserId, @RequestBody ExportApplicationQueryDTO queryDTO) {
        queryDTO.setLoginUserId(loginUserId);
        return exportApprovalService.queryManualApprovalList(queryDTO);
    }

    @GetMapping("/pt/{version}/exportApproval/{id}")
    @ApiOperation("查询导出记录详情")
    public ExportApplicationRecordDTO getExportApplicationRecord(@PathVariable String id) {
        return exportApplicationService.getExportApplicationRecord(id);
    }

    @GetMapping("/pt/{version}/exportApproval/download")
    @ApiOperation("下载文件")
    @LogExportAnnotation
    public ResponseEntity<byte[]> download(@RequestParam(value = "id") String id, @RequestParam("loginUserId") String loginUserId) {
        return fileService.download(id);
    }

    @PostMapping("/pt/{version}/exportApproval/autoJob/trigger")
    @ApiOperation("人工触发自动审核延期申请的任务")
    public void triggerAutoJob() {
        exportApprovalDelayedJobHandler.execute(null);
    }
}
