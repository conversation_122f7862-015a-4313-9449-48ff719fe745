package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.dto.GaodeContourCacheInfoVO;
import com.iflytek.cdc.admin.service.GaodeContourCacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "研判大屏 -- 高德轮廓缓存")
@Slf4j
public class GaodeContourCacheController {

    @Resource
    GaodeContourCacheService gaodeContourCacheService;

    @GetMapping("/{version}/pb/screen/gaode/contour/getCacheData")
    @ApiOperation("获取高德轮廓缓存")
    public GaodeContourCacheInfoVO getCacheData(@RequestParam(required = false, defaultValue = "") String keywords,
                                                @RequestParam(required = false, defaultValue = "") String subdistrict,
                                                @RequestParam(required = false, defaultValue = "") String extensions) {
        return gaodeContourCacheService.getCacheData(keywords, subdistrict, extensions);
    }

}
