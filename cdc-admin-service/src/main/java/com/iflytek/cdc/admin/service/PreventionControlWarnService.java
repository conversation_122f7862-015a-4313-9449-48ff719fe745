package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.PreventionControlQueryDto;
import com.iflytek.cdc.admin.dto.PreventionControlWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Service
public interface PreventionControlWarnService {
    PageInfo<PreventionControlWarnDto> getPreventionControlWarnPageList(PreventionControlQueryDto dto);

    void updatePreventionControlWarn(PreventionControlWarnDto warnDto, String loginUserId);

    void updatePreventionControlWarnStatus(PreventionControlWarnDto warnDto, String loginUserId);

    PreventionControlWarnDto getWarnById(String warnId);

    List<PreventionControlWarnDto> getPreventionControlWarnAllList(String preventionControlCode);

    void exportPreventionControlWarnRule(HttpServletResponse response);

    List<CascadeVO> getPreventionControlNameList();

    List<CascadeVO> getPreventionControl();

    List<CascadeVO> getPreventionControlNameList(String loginUserId);
}
