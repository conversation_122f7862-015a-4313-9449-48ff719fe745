package com.iflytek.cdc.admin.service.async;

/**
 * @ClassName AsyncMdmDataService
 * @Description 异步执行获取MDM数据实现类
 * <AUTHOR>
 * @Date 2021/8/10 10:05
 * @Version 1.0
 */
public interface AsyncMdmDataService {

    /**
     * 执行异步获取mdm数据方法
     * @param batchId
     * @param loginUserId
     */
    void executeAsyncMdmData(String batchId,String loginUserId);

    /**
     * 执行对应的异步线程
     * @param strategy
     * @param batchId
     * @param loginUserId
     */
    void executeAsyncMdmThread(String strategy, String batchId, String loginUserId);

}
