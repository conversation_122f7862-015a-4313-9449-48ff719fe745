package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.outbound.entity.OutboundTemplate;
import com.iflytek.cdc.admin.outbound.mapper.OutboundTemplateMapper;
import com.iflytek.cdc.admin.outbound.service.OutboundTemplateService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OutboundTemplateServiceImpl extends CdcServiceBaseImpl<OutboundTemplateMapper, OutboundTemplate> implements OutboundTemplateService {
    @Override
    public List<OutboundTemplate> listByTypeAndCategory(String type, String category) {
        LambdaQueryWrapper<OutboundTemplate> queryWrapper = lambdaQueryWrapper();
        if(StringUtils.isNotEmpty(type)){
            queryWrapper.eq(OutboundTemplate::getType, type);
        }
        queryWrapper.eq(OutboundTemplate::getCategory, category);
        return list(queryWrapper);
    }
}
