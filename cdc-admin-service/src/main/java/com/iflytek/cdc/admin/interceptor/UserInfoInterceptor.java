//package com.iflytek.cdc.admin.interceptor;
//
//import com.iflytek.cdc.admin.apiservice.UapServiceApi;
//import com.iflytek.cdc.admin.uap.UapUserPo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;
//
///**
// * 登录人员信息拦截器
// * date : 2024-05-14
// * */
//@Slf4j
//@Component
//public class UserInfoInterceptor implements HandlerInterceptor {
//
//    @Resource
//    private UapServiceApi uapServiceApi;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//
//        String requestUri = request.getRequestURI();
//        if (requestUri.contains("swagger") || StringUtils.isBlank(request.getParameter("loginUserId"))
//                || "null".equals(request.getParameter("loginUserId"))) {
//            USER_INFO.set(new UapUserPo());
//        }else {
//            String loginUserId = request.getParameter("loginUserId");
//            USER_INFO.set(uapServiceApi.getUser(loginUserId));
//        }
//        return true;
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//        USER_INFO.remove();
//    }
//}
