package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.InfectiousDiseasesDto;
import com.iflytek.cdc.admin.dto.InfectiousDiseasesNameDto;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;


import java.util.List;

/**
 * @ClassName SyncMdmData
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:37
 * @Version 1.0
 */
public interface SyncMdmDataService<T> {

    /**
     * 添加传染病规则
     */
    void addInfectiousDiseases(String loginUserId, InfectiousDiseasesDto record);

    List<T> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO);

    PageData<T> queryInfecPageInfo(SearchInfecInfoDTO searchInfecInfoDTO);

    void updateSyncMdmData(String loginUserId, List<T> t, List<TermCodedValueInfo> mdmCodeValues);

    void insertSyncMdmData(String loginUserId, List<T> t, List<TermCodedValueInfo> mdmCodeValues);

    void updateInfecInfo(String loginUserId, T t);

    List<TermCodedValueInfo> getMdmDataSync(String code);

    void syncMdmData(String batchId,String loginUserId);

    boolean judgeIsUse(String id);
}
