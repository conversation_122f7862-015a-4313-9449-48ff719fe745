package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.SearchSyndromeInfoDTO;
import com.iflytek.cdc.admin.entity.SyndromeInfo;
import com.iflytek.cdc.admin.sdk.entity.SyndromeInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.SyndInfo;
import com.iflytek.cdc.admin.service.SyndromeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/26 15:24
 **/
@RestController
@Api(tags = "症候群标准信息维护服务")
public class SyndromeController {

    @Resource
    private SyndromeService  syndromeService;


    @PostMapping("/{version}/pt/syndrome/querySyndromeInfoPage")
    @ApiOperation("查询症候群标准信息分页列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<SyndromeInfo>  querySyndromeInfoPage(@PathVariable String version, @RequestBody SearchSyndromeInfoDTO sd){
        return  syndromeService.querySyndromeInfoPage(sd);
    }

    @PostMapping("/{version}/pt/syndrome/insertSyndromeInfo")
    @ApiOperation("症候群标准信息维护")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void insertSyndromeInfo(@PathVariable String version, @RequestBody SyndromeInfo  si, @RequestParam String loginUserId, BindingResult  bindingResult){
        syndromeService.insertSyndromeInfo(loginUserId,si);
    }

    @PostMapping("/{version}/pt/syndrome/updateSyndromeInfo")
    @ApiOperation("症候群标准信息维护")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateSyndromeInfo(@PathVariable String version, @RequestBody SyndromeInfo  si, @RequestParam String loginUserId, BindingResult  bindingResult){
        syndromeService.updateSyndromeInfo(loginUserId,si);
    }

    @PostMapping("/{version}/pt/syndrome/mdmDataSync")
    @ApiOperation("症候群主数据同步")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void  mdmDataSync(@PathVariable String version, @RequestParam String loginUserId){
        syndromeService.syndromeDataSync(loginUserId);
    }

    @GetMapping("/{version}/pt/syndrome/getSyndromeStatus")
    @ApiOperation("启用前判断是否可以启用")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  boolean  getSyndromeStatus(@PathVariable String version,@RequestParam String id){
       return syndromeService.judgeIsUse(id);
    }

    @PostMapping("/{version}/pt/syndrome/syndromeInfoPage")
    @ApiOperation("查询症候群标准信息分页列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<SyndInfo>  syndromeInfoPage(@PathVariable String version, @RequestBody SyndromeInfoFilter sd){
        return  syndromeService.syndromeInfoPage(sd);
    }

    @GetMapping("/{version}/pt/syndrome/syndInfoByCode")
    @ApiOperation("根据编码查询症候群信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public  SyndInfo  syndInfoByCode(@PathVariable String version,@RequestParam String  code){
        return syndromeService.syndInfoByCode(code);
    }
    
}
