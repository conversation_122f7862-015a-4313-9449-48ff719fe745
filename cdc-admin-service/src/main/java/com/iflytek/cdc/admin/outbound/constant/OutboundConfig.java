package com.iflytek.cdc.admin.outbound.constant;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 外呼配置信息
 * <AUTHOR>
 */
@Data
@Component
@RefreshScope
public class OutboundConfig {

    @Value("${fpva.cdc.outbound.network.url:}")
    private String publicUrl;

    @Value("${fpva.cdc.outbound.appId:}")
    private String appId;

    @Value("${fpva.cdc.outbound.secretKey:}")
    private String secretKey;

    @Value("${fpva.cdc.outbound.recordUrl:}")
    private String recordUrl;

    /**
     * 外呼开放接口（用方案）
     * 应用场景：采用固定模版，没有特殊的变量要求；方案可以是一个多节点的每个节点可以是电话，也可以是短信，需要提前在讯飞随访平台中定义好方案信息
     */
    @Value("${fpva.cdc.outbound.api.planTask:speech-api/v1/pb/openapi/plan-task}")
    private String planTask;

    /**
     * 外呼开放接口（用话术）
     * 应用场景：单次随访，只有电话随访，可以附带短信，可以自定义变量
     */
    @Value("${fpva.cdc.outbound.api.callTask:speech-api/v1/pb/openapi/call-task}")
    private String callTask;

    /**
     * 外呼开放接口（用短信）
     * 应用场景：单次随访，只有短信通知，可自定义变量
     */
    @Value("${fpva.cdc.outbound.api.smsTask:speech-api/v1/pb/openapi/sms-task}")
    private String smsTask;

    /**
     * 查询话术自定义变量
     */
    @Value("${fpva.cdc.outbound.api.speechVariable:speech-api/v1/pb/openapi/speech-variable}")
    private String speechVariable;

    /**
     * 查询电话批次状态
     */
    @Value("${fpva.cdc.outbound.api.batchDetail:report-api/v1/pb/openapi/batch-detail}")
    private String batchDetail;

    /**
     * 查询批次汇总数据
     */
    @Value("${fpva.cdc.outbound.api.batchList:report-api/v1/pb/openapi/batch-list}")
    private String batchList;

    /**
     * 查询电话记录详情
     */
    @Value("${fpva.cdc.outbound.api.recordDetail:report-api/v1/pb/openapi/record-detail}")
    private String recordDetail;

}
