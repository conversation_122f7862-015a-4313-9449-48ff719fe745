package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.controller.old.AddressStandardizeController;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressFuzzyNormalizationDTO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressStandardVo;
import com.iflytek.cdc.admin.dto.addressstandardize.AdministrativeRegionQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrStreetInfo;
import com.iflytek.cdc.admin.mapper.TbCdcmrStreetInfoMapper;
import com.iflytek.cdc.admin.service.AdministrativeRegionService;
import com.iflytek.cdc.admin.service.AmapService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.fsp.flylog.sdk.java.core.brave.utils.StringUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cursor.Cursor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
@Service
@Slf4j
public class AdministrativeRegionServiceImpl implements AdministrativeRegionService {

    public static final String SHORT_DATE_FORMAT = "yyyy-MM-dd";
    public static final int BATCH_SIZE = 1000;

    @Resource
    private ParamConfigService paramConfigService;

    @Resource
    SqlSessionFactory sqlSessionFactory;

    @Resource
    AmapService amapService;

    @Resource
    TbCdcmrStreetInfoMapper tbCdcmrStreetInfoMapper;

    @Resource
    AddressStandardizeController addressStandardizeController;

    @Override
    public PageData<TbCdcmrStreetInfo> getAdministrativeRegionDetail(AdministrativeRegionQueryDTO administrativeRegionQueryDTO) {

        administrativeRegionQueryDTO.setSplitCityCodeList(getSplitList(administrativeRegionQueryDTO.getCityCode()));
        administrativeRegionQueryDTO.setSplitDistrictCodeList(getSplitList(administrativeRegionQueryDTO.getDistrictCode()));
        PageMethod.startPage(administrativeRegionQueryDTO.getPageIndex(), administrativeRegionQueryDTO.getPageSize());
        List<TbCdcmrStreetInfo> result = tbCdcmrStreetInfoMapper.getAdministrativeRegionDetail(administrativeRegionQueryDTO);
        PageInfo<TbCdcmrStreetInfo> pageInfo = new PageInfo<>(result);
        PageData<TbCdcmrStreetInfo> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(result);
        return pageData;
    }

    public  List<String> getSplitList(String source){
        if (StringUtil.isEmpty(source)) {
            return new ArrayList<>();
        }
        return Arrays.asList(source.split(","));
    }

    @Override
    public byte[] exportRegionList(AdministrativeRegionQueryDTO administrativeRegionQueryDTO) {

        administrativeRegionQueryDTO.setSplitCityCodeList(getSplitList(administrativeRegionQueryDTO.getCityCode()));
        administrativeRegionQueryDTO.setSplitDistrictCodeList(getSplitList(administrativeRegionQueryDTO.getDistrictCode()));
        List<TbCdcmrStreetInfo> result = tbCdcmrStreetInfoMapper.getAdministrativeRegionDetail(administrativeRegionQueryDTO);

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(result);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");

        makeExcelColumn(result, sheet);
        ByteArrayOutputStream outputStream = generateOutputStream(workbook);

        return outputStream.toByteArray();
    }

    private void makeExcelColumn(List<TbCdcmrStreetInfo> result, XSSFSheet sheet){

        Row firstRow = sheet.createRow(0);
        int index = 0;
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "省", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "市", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "区县", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "现乡镇/街道名称", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "是否变更名称", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "原乡镇/街道名称", index++);
        sheet.setColumnWidth(index, 4000);
        createCellAndSetValue(firstRow, "乡镇/街道别名", index++);
        for (int i = 0; i < result.size(); i++) {
            index = 0;
            TbCdcmrStreetInfo vo = result.get(i);
            Row row = sheet.createRow(i + 1);
            createCellAndSetValue(row, vo.getProvinceName(), index++);
            createCellAndSetValue(row, vo.getCityName(), index++);
            createCellAndSetValue(row, vo.getDistrictName(), index++);
            createCellAndSetValue(row, vo.getStreetName(), index++);
            if("0".equals(vo.getIsNameChanged())){
                createCellAndSetValue(row, "否", index++);
            }else{
                createCellAndSetValue(row, "是", index++);
            }
            createCellAndSetValue(row, vo.getOriginalStreetName(), index++);
            createCellAndSetValue(row, vo.getAliasStreetName(), index++);
        }
    }

    private void createCellAndSetValue(Row row, String value, int index) {
        Cell cell = row.createCell(index);
        if (StringUtils.isBlank(value)) {
            cell.setCellValue("--");
        } else {
            cell.setCellValue(value);
        }
    }

    private ByteArrayOutputStream generateOutputStream(Workbook workbook) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("文件处理失败:", e);
            throw new MedicalBusinessException("11451108", "文件处理失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncResult(Date startTime, Date endTime) {

        log.info("同步开始：{}, {}", startTime, endTime);

        tbCdcmrStreetInfoMapper.updateOldStreetCode(startTime, endTime);
        tbCdcmrStreetInfoMapper.mergeStreetInfo(startTime, endTime);
        log.info("-------------------同步行政区划完成-----------------");

        // 经纬度标化
        List<String> updatedStreetCodes = tbCdcmrStreetInfoMapper.selectUpdatedStreetCodes(startTime, endTime);
        for (List<String> partition : Lists.partition(updatedStreetCodes, BATCH_SIZE)) {
            List<TbCdcmrStreetInfo> tbCdcmrStreetInfos = tbCdcmrStreetInfoMapper.selectByStreetIds(partition);
            this.generateStreetGeoCoordinates(tbCdcmrStreetInfos);
        }
    }

    @Override
    public void streetAddressStd(Date startTime, Date endTime) {
        log.info("同步开始：{}, {}", startTime, endTime);

        try (Cursor<TbCdcmrStreetInfo> cursor = sqlSessionFactory.openSession()
                                                                 .getMapper(TbCdcmrStreetInfoMapper.class)
                                                                 .selectCursorByUpdateTime(startTime, endTime)) {
            List<TbCdcmrStreetInfo> currStreetInfos = new ArrayList<>();

            for (TbCdcmrStreetInfo tbCdcmrStreetInfo : cursor) {
                currStreetInfos.add(tbCdcmrStreetInfo);
                if (currStreetInfos.size() >= BATCH_SIZE) {
                    this.generateStreetGeoCoordinates(currStreetInfos);
                    currStreetInfos.clear();
                }
            }
            this.generateStreetGeoCoordinates(currStreetInfos);

        } catch (Exception e) {
            log.error("街道地址(经纬度)标化(高德API)：", e);
        }

        log.info("-------------------结束街道地址(经纬度)标化(高德API)-----------------");

    }

    private List<TbCdcmrStreetInfo> generateStreetGeoCoordinates(List<TbCdcmrStreetInfo> streetInfos) {
        List<TbCdcmrStreetInfo> updatedStreetInfos = new ArrayList<>();

        for (TbCdcmrStreetInfo streetInfo : streetInfos) {
            String provinceName = streetInfo.getProvinceName();
            String cityName = streetInfo.getCityName();
            String districtName = streetInfo.getDistrictName();
            String streetName = streetInfo.getStreetName();
            String streetAddressDetail = String.join("", provinceName, cityName, districtName, streetName);

            AddressFuzzyNormalizationDTO dto = AddressFuzzyNormalizationDTO.builder()
                                                                           .provinceName(provinceName)
                                                                           .cityName(cityName)
                                                                           .districtCode(streetInfo.getDistrictCode())
                                                                           .addressDetail(streetAddressDetail)
                                                                           .refresh(false)
                                                                           .build();

            try {
                AddressStandardVo stdVo = amapService.fuzzyAddressNormalizationSimple(dto,"");
                String addressLongitude = stdVo.getAddressLongitude();
                String addressLatitude = stdVo.getAddressLatitude();

                if (!AddressStandardVo.isEmptyValue(stdVo)
                        && StringUtils.isNotBlank(addressLongitude)
                        && StringUtils.isNotBlank(addressLongitude)) {
                    BigDecimal streetLongitude = new BigDecimal(addressLongitude);
                    BigDecimal streetLatitude = new BigDecimal(addressLatitude);
                    if (!Objects.equals(streetLongitude, streetInfo.getStreetLongitude()) || !Objects.equals(
                            streetLatitude,
                            streetInfo.getStreetLatitude())) {
                        streetInfo.setStreetLongitude(streetLongitude);
                        streetInfo.setStreetLatitude(streetLatitude);
                        updatedStreetInfos.add(streetInfo);
                    }
                }
            } catch (NumberFormatException e) {
                log.error( "经纬度数据处理错误: {}", streetInfo, e);
            } catch (Exception e) {
                log.error( "街道经纬度调用API失败: {}", streetInfo, e);
            }
        }

        if (CollectionUtil.isNotEmpty(updatedStreetInfos)) {
            tbCdcmrStreetInfoMapper.upsertStreetGeoCodes(updatedStreetInfos);
        }

        return updatedStreetInfos;
    }

}
