package com.iflytek.cdc.admin.capacity.api.algorithm;

import com.google.common.reflect.TypeToken;
import com.iflytek.cdc.admin.capacity.api.algorithm.config.AlgorithmApiConfig;
import com.iflytek.cdc.admin.capacity.api.algorithm.request.*;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.*;
import com.iflytek.cdc.admin.capacity.model.dto.KnowledgeBaseSearchDTO;
import com.iflytek.cdc.admin.capacity.model.dto.PredictSpreadDTO;
import com.iflytek.cdc.admin.capacity.model.dto.RiskAssessRequestDTO;
import com.iflytek.cdc.admin.util.CdcJsonUtils;
import com.iflytek.cdc.admin.util.UrlUtils;
import lombok.extern.log4j.Log4j;
import org.springframework.util.CollectionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 态势感知调用类 算法
 */
@Log4j
public class AlgorithmApi {

    private final RestTemplate restTemplate;
    
    private final AlgorithmApiConfig algorithmApiConfig;

    public AlgorithmApi(RestTemplate restTemplate, AlgorithmApiConfig algorithmApiConfig){
        this.restTemplate = restTemplate;
        this.algorithmApiConfig = algorithmApiConfig;
    }

    /**
     * token获取
     */
    public AuthResponse getToken(AuthRequest requestParam){
        String url = UrlUtils.expandUrl(getUrlWithRoot(algorithmApiConfig.getAuthUrl()), requestParam);
        return restTemplate.postForObject(url, requestParam, AuthResponse.class);
    }
    
    /**
     * 态势感知存储
     */
    public PlatformResponseBase<String> cdcPolicySimulation(String accessToken,
                                                            AwarenessRequest request){
        String url = getFullUrlWithToken(algorithmApiConfig.getCdcPolicySimulationUrl(), accessToken);
        System.out.println(url);
        Integer popSize = request.getPopSize();
        int popScale = request.getPopScale() == null ? 1 : request.getPopScale();
        while (popSize >= 10000){
            popSize = popSize / 10;
            popScale *= 10;
        }
        request.setPopSize(popSize);
        request.setPopScale(popScale);
        if (request.getVaccineProbPre() !=null
                && request.getVaccineRelSusPre() != null
                && request.getVaccineRelSympPre() != null){
            request.setSimpleVaccinePre(true);
        }
        if (CollectionUtils.isEmpty(request.getVaccineDaysPre())){
            request.setVaccineDaysPre(Collections.singletonList(1));
        }
        return CdcJsonUtils.toBean(restTemplate.postForObject(url, request, PlatformResponseBase.class), new TypeToken<PlatformResponseBase<String>>(){}.getType());
    }

    /**
     * 态势感知结果
     */
    public PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>> queryCdcAwareness(String accessToken, AwarenessResultRequest request){
        String url = getFullUrlWithToken(algorithmApiConfig.getQueryCdcAwarenessUrl(), accessToken)  ;
        url = UrlUtils.expandUrl(url, request);
        System.out.println(url);
        return CdcJsonUtils.toBean(restTemplate.getForObject(url, PlatformResponseBase.class), new TypeToken<PlatformResponseBase<AlgorithmResponseBase<AwarenessResponse>>>(){}.getType());
    }

    /**
     * 事件分级接口
     */
    public PlatformResponseBase<EventClassifyResponse> cdcEventClassify(String accessToken, EventClassifyRequest request){
        String url = getFullUrlWithToken(algorithmApiConfig.getCdcEventClassifyUrl(), accessToken)  ;
        System.out.println(url);
        return CdcJsonUtils.toBean(restTemplate.postForObject(url, request, PlatformResponseBase.class), new TypeToken<PlatformResponseBase<EventClassifyResponse>>(){}.getType());
    }

    /**
     * 应急处置建议
     */
    public PlatformResponseBase<AlgorithmResponseBase<DispositionAdviceResponse>>  cdcDispositionAdvice(String accessToken, DispositionAdviceRequest requestParam){
        String url = getFullUrlWithToken(algorithmApiConfig.getCdcDispositionAdviceUrl(), accessToken)  ;
        return CdcJsonUtils.toBean(restTemplate.postForObject(url, requestParam, PlatformResponseBase.class), new TypeToken<PlatformResponseBase<AlgorithmResponseBase<DispositionAdviceResponse>>>(){}.getType());
    }

    /**
     * 未来的疾病趋势
     */
    public PlatformResponseBase<AlgorithmResponseBase<List<Integer>>>  cdcDiseaseTrendPrediction(String accessToken, DiseaseTrendRequest request){
        String url = getFullUrlWithToken(algorithmApiConfig.getCdcDiseaseTrendPredictionUrl(), accessToken)  ;
        return CdcJsonUtils.toBean(restTemplate.postForObject(url, request, PlatformResponseBase.class), new TypeToken<PlatformResponseBase<AlgorithmResponseBase<List<Integer>>>>(){}.getType());
    }

    /**
     * 态势感知-时间序列模型
     */
    public AlgorithmResponseBase<List<Integer>> arima(ArimaRequest request){
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getArimaUrl(), request, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<List<Integer>>>(){}.getType());
    }

    /**
     * 态势感知-时空统计模型
     */
    public AlgorithmResponseBase<List<Integer>> stgnn(STGNNRequest request){
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getStgnnUrl(), request, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<List<Integer>>>(){}.getType());
    }

    /**
     * 态势感知-神经网络模型
     */
    public AlgorithmResponseBase<List<Integer>> lstm(LSTMRequest request){
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getLstmUrl(), request, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<List<Integer>>>(){}.getType());
    }

    /**
     * 态势感知-混合模型
     */
    public AlgorithmResponseBase<List<Integer>> seir(SEIRRequest request){
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getSeirUrl(), request, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<List<Integer>>>(){}.getType());
    }

    /**
     * 态势感知-混合模型
     */
    public AlgorithmResponseBase<List<Integer>> ensemble(EnsembleRequest request){
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getEnsembleUrl(), request, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<List<Integer>>>(){}.getType());
    }

    /**
     * 获取带有token的url的全路径
     */
    public String getFullUrlWithToken(String url, String accessToken){
       return getUrlWithRoot(url)  + "?access_token=" + accessToken + "&app_key=" + algorithmApiConfig.getClientId();
    }

    /**
     * 获取url的全路径
     */
    public String getUrlWithRoot(String url){
        return algorithmApiConfig.getRootUrl() + url;
    }

    /**
     * 疾病影响因素-随机森林模型
     * @param request
     * @return
     */
    public InfluenceFactorResponse randomForest(FeatureSelectionRequest request){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getFeatureSelectionUrl(), entity, InfluenceFactorResponse.class), new TypeToken<InfluenceFactorResponse>(){}.getType());
    }


    /**
     * 辅助大屏——街道感染人数预测
     * @param request
     * @return
     */
    public AlgorithmResponseBase<Map<String, Map<String, Integer>>> spreadEpidemic(PredictSpreadDTO request){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
        // 创建 HttpEntity 对象
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getSpreadEpidemicUrl(), entity, AlgorithmResponseBase.class), new TypeToken<AlgorithmResponseBase<Map<String, Map<String, Integer>>>>(){}.getType());
    }


    /**
     * 辅助大屏——风险评估
     * @param request
     * @return
     */
    public RiskAssessResponse riskAssess(RiskAssessRequestDTO request){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
        request.setR0(1);
        request.setDalyResult(0);
        // 创建 HttpEntity 对象
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getRiskAssessUrl(), entity, RiskAssessResponse.class), new TypeToken<RiskAssessResponse>(){}.getType());
    }


    /**
     * 辅助大屏——事件知识库
     * @param request
     * @return
     */
    public KnowledgeBaseSearchResponse knowledgeBaseSearch(KnowledgeBaseSearchDTO request){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        return CdcJsonUtils.toBean(restTemplate.postForObject(algorithmApiConfig.getKnowledgeBaseSearchUrl(), entity, KnowledgeBaseSearchResponse.class), new TypeToken<KnowledgeBaseSearchResponse>(){}.getType());
    }
}
