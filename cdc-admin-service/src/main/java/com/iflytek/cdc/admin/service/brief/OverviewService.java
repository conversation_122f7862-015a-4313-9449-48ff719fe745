package com.iflytek.cdc.admin.service.brief;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.OverviewSearchDto;
import com.iflytek.cdc.admin.entity.brief.BriefInfoEntity;
import com.iflytek.cdc.admin.entity.brief.PushRecordEntity;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.BriefInfoVo;

public interface OverviewService {
    ApiResult<PageInfo<BriefInfoEntity>> list(OverviewSearchDto searchDto);

    ApiResult<PageInfo<PushRecordEntity>> pushList(OverviewSearchDto searchDto);

    BriefInfoVo getById(String id);

    void deleteInfoById(String id);
}
