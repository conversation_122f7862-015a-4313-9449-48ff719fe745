package com.iflytek.cdc.admin.service.async;


import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName AsyncMdmServiceStrategyImpl
 * @Description 动态加载异步mdm操作实现类
 * <AUTHOR>
 * @Date 2021/8/10 10:22
 * @Version 1.0
 */

@Service("asyncMdmServiceStrategy")
public class AsyncMdmServiceStrategyImpl implements AsyncMdmServiceStrategy {

    @Resource
    private AsyncDataStrategy asyncDataStrategy;

    @Override
    public void executeAsyncMdmData(String code,String batchId,String loginUserId) {
        asyncDataStrategy.getSyncBean(code).executeAsyncMdmData(batchId,loginUserId);
    }
}
