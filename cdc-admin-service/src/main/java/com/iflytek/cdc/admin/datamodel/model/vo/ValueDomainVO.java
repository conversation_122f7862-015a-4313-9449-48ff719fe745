package com.iflytek.cdc.admin.datamodel.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ValueDomainVO {

    private String id;

    private String dataAttrId;

    private String label;

    private String value;

    private List<ValueDomainVO> children;

    public ValueDomainVO(String id, String dataAttrId, String label, String value){
        this.id = id;
        this.dataAttrId = dataAttrId;
        this.label = label;
        this.value = value;
        this.children = new ArrayList<>();
    }

    public void addChild(ValueDomainVO valueDomainVO){
        this.children.add(valueDomainVO);
    }
}
