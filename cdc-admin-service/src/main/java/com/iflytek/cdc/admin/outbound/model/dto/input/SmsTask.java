package com.iflytek.cdc.admin.outbound.model.dto.input;

import com.iflytek.cdc.admin.outbound.model.dto.input.param.Dweller;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Var;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 外呼开放接口（用短信）
 * 应用场景：单次随访，只有短信通知，可自定义变量
 * <AUTHOR>
 */
@Data
public class SmsTask {

    @ApiModelProperty(value = "要外呼的居民信息", required = true)
    private List<Dweller> dwellers;

    @ApiModelProperty(value = "短信id（医疗提供）", required = true)
    private String smsId;

    @ApiModelProperty("任务名称（不填将默认生成一键随访 yyyyMMddHHmmss）")
    private String planName;

    @ApiModelProperty("短信动态参数")
    private List<Var> vars;

    @ApiModelProperty("时间类型(1：即呼，2：指定日期)")
    private  Integer type;

    @ApiModelProperty("指定日期(type = 2 时生效) 格式：yyyy-MM-dd HH:mm:ss")
    private String date;

}
