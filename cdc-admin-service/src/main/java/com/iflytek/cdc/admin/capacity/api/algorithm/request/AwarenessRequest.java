package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class AwarenessRequest {
    private String taskID; // 用户ID，唯一标识
    private String startDay; // 疫情开始日，日期统一格式例如：2022-03-01
    private String endDay; // 疫情结束日
    private Integer popSize; // 总人口数
    private Integer popInfected; // 初始感染数
    private Double beta; // 传染率，即一个易感者接触到一个携带者，被传染的概率，0~1之间
    private Integer popScale;// 人口缩放比率
    private Double asympFactor; // 无症状者传染率相对水平，0~1之间
    private boolean ifCalibrate; // 参数校准：是否做参数校准
    private Datafile datafile; // 真实传染数据的文件路径，excel或csv格式，表内有两列date和new_diagnoses，分别代表日期和新增病例数，用于“校准”参数
    private String predDay; // 疫情态势推演的结束日，用于“校准”后或“政策仿真”的疫情态势推演
    private boolean ifContour; // 政策辅助推荐：是否绘制等高线

    private Double latency;//潜伏期 正数
    private Double mySympProb;//有症状感染者比例,0~1之间, mySympProb+mySevereProb+myDeathProb<=1, mySympProb>=mySevereProb>=myDeathProb
    private Double mySevereProb;//重症患者比例,0~1之间, mySympProb+mySevereProb+myDeathProb<=1, mySympProb>=mySevereProb>=myDeathProb
    private Integer quarPeriod;//重症患者隔离天数，正整数
    private Double myDeathProb;//重症患者病死率

    private List<String> houseDaysPre; // 已发生的干预：改变家庭传播率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> houseChangesPre; // 已发生的干预：改变家庭传播率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] 分别表示改变到原先值的50%和100%水平，里面是Double类型，大于0
    private List<String> schoolDaysPre; // 已发生的干预：改变学校传播率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> schoolChangesPre; // 已发生的干预：改变学校传播率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] 分别表示改变到原先值的50%和100%水平，里面是Double类型，大于0
    private List<String> workDaysPre; // 已发生的干预：改变工作场所传播率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> workChangesPre; // 已发生的干预：改变工作场所传播率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] 分别表示改变到原先值的50%和100%水平，里面是Double类型，大于0
    private List<String> communityDaysPre; // 已发生的干预：改变公共场所传播率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> communityChangesPre; // 已发生的干预：改变公共场所传播率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] ，分别表示改变到原先值的50%和100%水平，里面是Double类型，大于0
    private Boolean simpleVaccinePre; // 已发生的干预：是否包含简单疫苗
    private Double vaccineProbPre; // 已发生的干预：接种疫苗的概率，0~1之间
    private List<Integer> vaccineDaysPre; // 已发生的干预：采取接种疫苗措施的天数，例如：[2,10]，正整数
    private Double vaccineRelSusPre;//降低易感
    private Double vaccineRelSympPre; // 降低发病

    private List<String> maskDaysPre; // 已发生的干预：改变佩戴口罩率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> maskChangesPre; // 已发生的干预：改变佩戴口罩率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] 分别表示50%佩戴口罩和100%全员佩戴口罩，里面是float类型，0~1之间
    private List<String> socialDistanceDaysPre; // 已发生的干预：改变社交距离的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> socialDistanceChangesPre; // 已发生的干预：改变社交距离的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1]，分别表示改变到原先值的50%和100%水平，里面是float类型，大于等于0
    private List<String> crowdGatherDaysPre; // 已发生的干预：改变人群聚集水平的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> crowdGatherChangesPre; // 已发生的干预：改变人群聚集的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1]，分别表示改变到原先值的50%和100%水平，里面是float类型，大于等于0
    private List<String> disinfectionDaysPre; // 已发生的干预：改变环境消杀率的日期，例如：["2022-03-27", "2022-06-15"]
    private List<Double> disinfectionChangesPre; // 已发生的干预：改变环境消杀率的水平，列表和上个参数一一对应，两个参数必须同时出现，例如：[0.5,1] 分别表示50%消杀和100%消杀，里面是float类型，0~1之间


    @Data
    public static  class Datafile{
        private List<String> date;//日期
        @SerializedName("new_infectious")
        private List<Integer> new_infectious;//新增感染数
        @SerializedName("cum_infectious")
        private List<Integer> cum_infectious;//累积感染数
        @SerializedName("new_diagnoses")
        private List<Integer> new_diagnoses;//新增病例数
        @SerializedName("cum_diagnoses")
        private List<Integer> cum_diagnoses;//累积病例数
        @SerializedName("new_deaths")
        private List<Integer> new_deaths;//新增死亡数
        @SerializedName("cum_deaths")
        private List<Integer> cum_deaths;//累计死亡数
    }
}
