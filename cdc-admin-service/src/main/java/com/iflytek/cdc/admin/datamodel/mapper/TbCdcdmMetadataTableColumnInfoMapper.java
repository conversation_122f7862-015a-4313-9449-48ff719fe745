package com.iflytek.cdc.admin.datamodel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcdmMetadataTableColumnInfoMapper extends BaseMapper<TbCdcdmMetadataTableColumnInfo> {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmMetadataTableColumnInfo record);

    int insertSelective(TbCdcdmMetadataTableColumnInfo record);

    TbCdcdmMetadataTableColumnInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcdmMetadataTableColumnInfo record);

    int updateByPrimaryKey(TbCdcdmMetadataTableColumnInfo record);

    List<TbCdcdmMetadataTableColumnInfo> getFieldByTableName(@Param("tableName") String tableName,
                                                             @Param("columnName") String columnName);

    List<TbCdcdmMetadataTableColumnInfo> getFieldByTableNames(@Param("tableNames") List<String> tableName);

    List<TbCdcdmMetadataTableColumnInfo> getColumnInfoByIds(@Param("ids") List<String> ids);

    List<TbCdcdmMetadataTableColumnInfo> getAllColumnInfo();
}