package com.iflytek.cdc.admin.service.async;

import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.entity.InfectiousDiagnosis;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.cdc.admin.thread.MdmSyncThreadPool;
import com.iflytek.cdc.admin.thread.RedisHandlerService;
import com.iflytek.cdc.admin.thread.Status;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName AsyncMdmDataServiceImpl
 * @Description 传染病诊断异步获取数据
 * <AUTHOR>
 * @Date 2021/8/10 10:09
 * @Version 1.0
 */
@Service("asyncMdmDataInfecDia")
@Slf4j
public class InfecDiaAsyncMdmDataServiceImpl implements AsyncMdmDataService {

    @Resource
    private SyncMdmDataService infecDiaService;

    @Resource
    private MdmSyncThreadPool mdmSyncThreadPool;

    @Resource
    private RedisHandlerService redisHandlerService;

    @Override
    public void executeAsyncMdmData(String batchId,String loginUserId) {
        long startTime = System.currentTimeMillis();
        List<TermCodedValueInfo> mdmCodeValues = infecDiaService.getMdmDataSync(MdmDataSyncConstants.INFECDIA_CODE);
        if(CollectionUtils.isEmpty(mdmCodeValues)){
            log.warn("同步MDM传染病诊断数据为空");
            redisHandlerService.setStatus(batchId, Status.DONE);
            return;
        }
        long endTime = System.currentTimeMillis();
        log.warn("同步MDM诊断数据运行时间： " + (endTime - startTime) + "ms");
        SearchInfecInfoDTO searchInfecInfoDTO = new SearchInfecInfoDTO();
        searchInfecInfoDTO.setPageSize(1000);
        //获取本地数据
        List<InfectiousDiagnosis> infectiousDiagnosis;
        int pageIndex = 1;
        List<InfectiousDiagnosis> allCodeValues=new ArrayList<>();
        Long  total=1L;
        do {
            searchInfecInfoDTO.setPageIndex(pageIndex);
            PageData<InfectiousDiagnosis> diasPageData = infecDiaService.queryInfecPageInfo(searchInfecInfoDTO);
            infectiousDiagnosis = diasPageData.getData();
            total =diasPageData.getTotal();
            allCodeValues.addAll(infectiousDiagnosis);
            pageIndex++;
        } while (allCodeValues.size()<total);
        infecDiaService.insertSyncMdmData(loginUserId, allCodeValues, mdmCodeValues);
        infecDiaService.updateSyncMdmData(loginUserId, allCodeValues, mdmCodeValues);
        long endTime1 = System.currentTimeMillis();
        log.warn("更新基础管理诊断数据运行时间： " + (endTime1 - startTime) + "ms");
    }

    @Override
    public void executeAsyncMdmThread(String stragety, String batchId, String loginUserId) {
        mdmSyncThreadPool.executeMdmSync(stragety, batchId, loginUserId);
    }
}
