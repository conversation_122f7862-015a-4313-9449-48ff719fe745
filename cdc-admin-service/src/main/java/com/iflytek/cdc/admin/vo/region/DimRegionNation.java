package com.iflytek.cdc.admin.vo.region;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

/**
 * 行政区划维表
 */
@Data
public class DimRegionNation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 地区代码
     */
    private String regionCode;

    /**
     * 父级行政区划ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 街道编码
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 乡村
     */
    private String village;

    /**
     * 初始
     */
    private String initial;

    /**
     * 区号
     */
    private String areacode;

    /**
     * ETL创建时间
     */
    private Date etlCreateDatetime;

    /**
     * ETL更新时间
     */
    private Date etlUpdateDatetime;
}
