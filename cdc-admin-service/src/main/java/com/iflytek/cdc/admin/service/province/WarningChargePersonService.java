package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleSaveDTO;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonQuery;
import com.iflytek.cdc.admin.dto.epi.AreaDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson;
import com.iflytek.cdc.admin.model.mr.dto.DealPersonQueryDTO;
import com.iflytek.cdc.admin.vo.SignalPushConfigurationVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.cdc.admin.vo.epi.DictVO;
import com.iflytek.cdc.admin.vo.epi.DutyUserVO;
import com.iflytek.cdc.admin.vo.epi.EcdOrgUserVO;
import com.iflytek.cdc.admin.vo.epi.EmergencyPlanVO;
import com.iflytek.cdc.admin.dto.epi.EmergencyPlanRequest;
import java.util.List;

public interface WarningChargePersonService {
    /**
     * 保存处理人配置
     */
    TbCdcmrWarningChargePerson save(WarningChargePersonDTO input);

    /**
     * 批量保存处理人配置
     */
    void batchSave(List<WarningChargePersonDTO> input);

    /**
     * 查询传染病疾病配置
     */
    List<WarningChargePersonVO> listDiseaseConfig(WarningChargePersonQuery query);

    /**
     * 根据id查询
     */
    TbCdcmrWarningChargePerson loadById(String id);

    /**
     * 根据疾病code以及风险等级 批量查询对应的处理人信息
     * */
    List<WarningChargePersonVO> getDealPersonInfoBy(List<DealPersonQueryDTO> dtoList);

    WarningChargePersonVO loadByDiseaseCode(String diseaseCode, String warningType);

    /**
     * 获取研判角色列表
     */
    List<DictVO> getExpertTypeList();

    /**
     * 获取研判专家列表
     */
    List<EcdOrgUserVO> getExpertList(List<String> expertCategories);

    /**
     * 获取应急值班人员
     */
    List<DutyUserVO> getDutyUserList(List<AreaDTO> areaDTOS);

    /**
     * 获取应急预案
     */
    PageData<EmergencyPlanVO> getEmergencyPlan(EmergencyPlanRequest request);


    Integer savePushRule(List<SignalPushRuleDTO> inputs);

    void saveOrUpdateAutoPushRule(List<SignalPushRuleSaveDTO> input);

    SignalPushConfigurationVO loadSignPushConfigByDiseaseCode(String topicId);

    List<TbCdcmrSignalPushConfiguration> getSignalPushRule(List<SignalPushRuleDTO> inputs);

    void changeStatus(WarningChargePersonDTO input);

    Integer stopSignalPushRuleMessage(List<SignalPushRuleDTO> input);
    void scanSignalPushConfiguration();
}
