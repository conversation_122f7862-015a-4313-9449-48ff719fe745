package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.entity.MedicalWarn;
import com.iflytek.cdc.admin.sdk.entity.QueryWarnRuleInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.MedicalWarnRuleVo;
import io.swagger.models.auth.In;


import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface MedicalWarnService {

    ResponseResult saveOrUpdateMedicalWarnRule(MedicalWarnRuleSaveOrUpdateDto dto, String loginUserId);

    /**
     * 添加传染病规则
     */
    void addMedicalWarn(MedicalWarn medicalWarn, String loginUserId);

    /**
     * 根据传染病病种添加规则
     */
    void addByInfectiousDisease(InfectiousDiseases infectiousDiseases);

    ResponseResult medicalWarnRuleList(String wranId, String loginUserId);

    ResponseResult medicalWarnPageList(MedicalWarnPageListDto medicalWarnPageListDto, String loginUserId);

    ResponseResult medicalWarnModifyStatus(String id, String status);


    ResponseResult syncMdmData(String code, String loginUserId);

    Boolean isCanModify(String id);

    void medicalWarnExport(HttpServletResponse response, String loginUserId);

    List<MedicalWarn> queryAll(QueryWarnRuleInfoFilter dto);

    MedicalWarn queryById(String id,String loginUserId);

    ResponseResult editById(MedicalWarnEditDto editDto);

    ResponseResult singleCaseSwitch(MedicalWarnEditDto editDto,String loginUserId);

    List<SendSMSTypeDto> getTypeList(Integer type);
}
