package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeConfigRuleMapper;
import com.iflytek.cdc.admin.service.WarningGradeConfigRuleService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarningGradeConfigRuleServiceImpl implements WarningGradeConfigRuleService {
    @Resource
    TbCdcmrWarningGradeConfigRuleMapper tbCdcmrWarningGradeConfigRuleMapper;
    @Resource
    BatchUidService batchUidService;

    @Override
    public List<TbCdcmrWarningGradeConfigRule> getByConfigId(String configId) {
        return tbCdcmrWarningGradeConfigRuleMapper.getByConfigId(configId);
    }

    @Override
    public List<TbCdcmrWarningGradeConfigRule> getAllByConfigId(String configId) {
        return tbCdcmrWarningGradeConfigRuleMapper.getAllByConfigId(configId);
    }

    @Override
    public void updateWarningGradeConfigRule(List<TbCdcmrWarningGradeConfigRule> ruleList, String loginUserId, String configId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_warning_grade_config_rule")));
            }
            rule.setIsDeleted(0);
            rule.setConfigId(configId);
            rule.setUpdateUser(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrWarningGradeConfigRule::getId).collect(Collectors.toList());
        tbCdcmrWarningGradeConfigRuleMapper.deleteOtherByIds(idList, configId);
        tbCdcmrWarningGradeConfigRuleMapper.upsertRules(ruleList);
    }


}
