package com.iflytek.cdc.admin.service;
import com.iflytek.cdc.admin.entity.CommonMdmDictInfo;


/**
 * mdm 字典数据同步统一接口
 * <AUTHOR>
 * @date 2021/8/6 15:46
 **/
public interface CommonDataSyncService {

    /**
     * mdm主数据同步
     * @param loginUserId
     * @param dataCode
     * @return
     **/
    <T extends CommonMdmDictInfo> void   mdmDataSyncs(String  loginUserId,String dataCode);


    /**
     * 判断启用状态
     * @param id
     * @param dataCode
     * @return
     **/
    <T extends CommonMdmDictInfo>  boolean  getDictStatus(String id,String dataCode);

}
