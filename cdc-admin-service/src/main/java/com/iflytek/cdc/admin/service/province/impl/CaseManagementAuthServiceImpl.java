package com.iflytek.cdc.admin.service.province.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCaseManagementPermissionRecord;
import com.iflytek.cdc.admin.enums.AuthClassEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.EDRAuthEnums;
import com.iflytek.cdc.admin.mapper.AuthCommonMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrCaseManagementPermissionRecordMapper;
import com.iflytek.cdc.admin.model.mr.dto.AuthCommonDto;
import com.iflytek.cdc.admin.model.mr.vo.EDRAuthResultVO;
import com.iflytek.cdc.admin.service.province.CaseManagementAuthService;
import com.iflytek.cdc.admin.vo.AuthCommonVo;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class CaseManagementAuthServiceImpl implements CaseManagementAuthService {

    private static final String TABLE_NAME = "tb_cdcmr_case_management_permission_record";
    private static final String CASE_MANAGEMENT = "caseManagement";

    @Autowired
    private TbCdcmrCaseManagementPermissionRecordMapper recordMapper;
    @Autowired
    private BatchUidService batchUidService;
    @Autowired
    private AuthCommonMapper authCommonMapper;

    @Override
    public EDRAuthResultVO validViewAuth(String loginUserId, AuthCommonDto permissionRecord){
        permissionRecord.setSystemType(CASE_MANAGEMENT);
        permissionRecord.setCurrentTime(new Date());
        List<AuthCommonVo> records = authCommonMapper.getViewAuthRecords(permissionRecord,loginUserId);
        if (CollectionUtils.isEmpty(records)){
            return new EDRAuthResultVO(EDRAuthEnums.NO_APPLY_RECODE,null);
        }
        List<AuthCommonVo> pendingRecord = records.stream().filter(record -> EDRAuthEnums.PENDING_STATUS.getCode().equals(record.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pendingRecord)){
            EDRAuthResultVO resultVO = new EDRAuthResultVO(EDRAuthEnums.APPLY_PENDING, null);
            resultVO.setValidStartTime(pendingRecord.get(0).getValidStartTime());
            resultVO.setValidEndTime(pendingRecord.get(0).getValidEndTime());
            return resultVO;
        }
        return EDRAuthResultVO.success();
    }

    @Override
    public void addAuthApplyRecord(String loginUserId, TbCdcmrCaseManagementPermissionRecord permissionRecord){
        String authCode = permissionRecord.getAuthCode();
        permissionRecord.setId(String.valueOf(batchUidService.getUid(TABLE_NAME)));
        permissionRecord.setOrgId(USER_INFO.get().getOrgId());
        permissionRecord.setAuthName(AuthClassEnum.getValueByCode(authCode));
        permissionRecord.setStatus(EDRAuthEnums.PENDING_STATUS.getCode());
        permissionRecord.setCreatorId(loginUserId);
        permissionRecord.setCreator(USER_INFO.get().getName());
        permissionRecord.setCreateTime(new Date());
        permissionRecord.setUpdaterId(loginUserId);
        permissionRecord.setUpdater(USER_INFO.get().getName());
        permissionRecord.setUpdateTime(new Date());
        permissionRecord.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        recordMapper.insert(permissionRecord);
    }

    @Override
    public void changeAuthStatus(String loginUserId, TbCdcmrCaseManagementPermissionRecord permissionRecord) {
        permissionRecord.setUpdaterId(loginUserId);
        permissionRecord.setUpdater(USER_INFO.get().getName());
        permissionRecord.setUpdateTime(new Date());
        recordMapper.updateById(permissionRecord);
    }

    @Override
    public PageInfo<TbCdcmrCaseManagementPermissionRecord> getAuthApplyRecordList(String loginUserId, AuthCommonDto permissionRecord) {
        List<TbCdcmrCaseManagementPermissionRecord> res;
        if ("approver".equals(permissionRecord.getApproverOrApplicantFlag())){
            res = recordMapper.getAuthApplyRecordListByUserId(loginUserId, permissionRecord);
        }else {
            res = recordMapper.getAuthApplyRecordListByCreateUserId(loginUserId, permissionRecord);
        }
        dealPendingRecord(res);
        PageHelper.startPage(permissionRecord.getPageIndex(), permissionRecord.getPageSize());
        return new PageInfo<>(res);
    }

    private void dealPendingRecord(List<TbCdcmrCaseManagementPermissionRecord>res){
        if (CollectionUtils.isEmpty(res)){
            return;
        }
        res.stream().filter(record -> EDRAuthEnums.PENDING_STATUS.getCode().equals(record.getStatus())).forEach(record -> {
            record.setUpdater(null);
            record.setUpdaterId(null);
            record.setUpdateTime(null);
        });
    }

    @Override
    public TbCdcmrCaseManagementPermissionRecord getPermissionRecordInfoById(String id) {
        return recordMapper.selectById(id);
    }

    @Override
    public List<TbCdcmrCaseManagementPermissionRecord> getCreateUserList(String loginUserId,String moduleType) {
        return  recordMapper.getCreateUserList(loginUserId,moduleType);
    }

    @Override
    public List<TbCdcmrCaseManagementPermissionRecord> getUpdateUserList(String loginUserId,String moduleType) {
        return recordMapper.getUpdateUserList(loginUserId,moduleType);
    }
}
