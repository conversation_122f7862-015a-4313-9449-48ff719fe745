package com.iflytek.cdc.admin.controller.province;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.datamodel.service.ValueDomainService;
import com.iflytek.cdc.admin.entity.DisLexicon;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDict;
import com.iflytek.cdc.admin.entity.dm.TbCdcdmDataDictValue;
import com.iflytek.cdc.admin.mapper.DisLexiconMapper;
import com.iflytek.cdc.admin.model.dm.dto.AddNewDataDictDTO;
import com.iflytek.cdc.admin.model.dm.dto.DataDictNameQueryDTO;
import com.iflytek.cdc.admin.model.dm.dto.DictValueQueryDTO;
import com.iflytek.cdc.admin.model.dm.dto.DragSortDataDTO;
import com.iflytek.cdc.admin.model.dm.vo.DataDictListVO;
import com.iflytek.cdc.admin.model.dm.vo.DataDictObjectVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.service.DisLexiconMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.annotation.Resource;
import java.util.List;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

/**
 * 值域管理
 * */
@Slf4j
@RestController
@Api(tags = "值域管理")
@AllArgsConstructor
public class ValueDomainController {

    @Resource
    private ValueDomainService valueDomainService;
    @Resource
    private DisLexiconMapper disLexiconMapper;
    @Resource
    private UapServiceApi uapServiceApi;

    @GetMapping("/pt/{version}/valueDomain/getValueDomainList")
    @ApiOperation("获取值域列表")
    public PageInfo<DataDictListVO> getValueDomainList(@RequestParam(required = false) String id,
                                                       @RequestParam(required = false) String name,
                                                       @RequestParam(required = false) Integer attrCount,
                                                       @RequestParam(defaultValue = "1") Integer pageIndex,
                                                       @RequestParam(defaultValue = "20") Integer pageSize){

        return valueDomainService.getValueDomainList(id, name, attrCount, pageIndex, pageSize);
    }

    @PostMapping("/pt/{version}/valueDomain/editValueDomain")
    @ApiOperation("新增or编辑 值域")
    public String editValueDomain(@RequestBody TbCdcdmDataDict tbCdcdmDataDict){

        UapUserPo uapUserPo = USER_INFO.get();
        return valueDomainService.editValueDomain(tbCdcdmDataDict, uapUserPo);
    }

    @GetMapping("/pt/{version}/valueDomain/getValueDomainDetailList")
    @ApiOperation("获取值域详情列表")
    public PageInfo<TbCdcdmDataDictValue> getValueDomainDetailList(@RequestParam String dataDictId,
                                                                   @RequestParam String type,
                                                                   @RequestParam(defaultValue = "1") Integer pageIndex,
                                                                   @RequestParam(defaultValue = "20") Integer pageSize){

        return valueDomainService.getValueDomainDetailList(dataDictId, type, pageIndex, pageSize);
    }

    @GetMapping("/pt/{version}/valueDomain/getValueByKey")
    @ApiOperation("根据值域id或者name获取对应值")
    public List<TreeNode> getValueByKey(@RequestParam(required = false) String id,
                                        @RequestParam(required = false) String code,
                                        @RequestParam(required = false) String name) {

        return valueDomainService.getValueByKey(id, code, name);
    }

    @PostMapping("/pt/{version}/valueDomain/getDictValueBy")
    @ApiOperation("根据值域id/code/name获取对应值")
    public DataDictObjectVO getDictValueBy(@RequestBody DictValueQueryDTO dto) {

        return valueDomainService.getDictValueBy(dto);
    }

    @PostMapping("/pt/{version}/valueDomain/addValueDomainDetail")
    @ApiOperation("新增值域详情")
    public void addValueDomainDetail(@RequestBody List<TbCdcdmDataDictValue> dictValues) {

        valueDomainService.addValueDomainDetail(dictValues);
    }

    @PostMapping("/pt/{version}/valueDomain/updateValueDomainDetail")
    @ApiOperation("更新值域详情")
    public void updateValueDomainDetail(@RequestBody List<TbCdcdmDataDictValue> dictValues) {

        valueDomainService.updateValueDomainDetail(dictValues);
    }

    @GetMapping("/pt/{version}/valueDomain/deleteValueDomainDetail")
    @ApiOperation("删除值域详情")
    public void deleteValueDomainDetail(@RequestParam String id,
                                        @RequestParam String attrId) {

        valueDomainService.deleteValueDomainDetail(id, attrId);
    }

    @PostMapping("/pt/{version}/valueDomain/listNameByCode")
    @ApiOperation("根据值域id，值code 查询name")
    public List<TbCdcdmDataDictValue> listNameByCode(@RequestBody DataDictNameQueryDTO dto) {

        return valueDomainService.listNameByCode(dto);
    }

    @PostMapping("/pt/{version}/valueDomain/listByDataDictIds")
    @ApiOperation("根据值域id集合查询值域")
    public List<TbCdcdmDataDictValue> listByDataDictIds(@RequestBody List<String> dataDictIds) {
        return valueDomainService.listByDataDictIds(dataDictIds);
    }

    @PostMapping("/pt/{version}/valueDomain/addValueDomainBy")
    @ApiOperation("新增值域 - 单层值域使用（研发使用）")
    public void addValueDomainBy(@RequestBody AddNewDataDictDTO dto) {

        valueDomainService.addValueDomainBy(dto);
    }

    @PostMapping("/pt/{version}/valueDomain/addMulValueDomainBy")
    @ApiOperation("新增值域 - 多层值域使用（研发使用）")
    public void addMulValueDomainBy(@RequestParam("file") MultipartFile file) {

        valueDomainService.addMulValueDomainBy(file);
    }

    @GetMapping("/pt/{version}/valueDomain/downloadImportTemplate")
    @ApiOperation("下载值域导入模板（研发使用）")
    public ResponseEntity<byte[]> downloadImportTemplate() {
        try {
            byte[] templateData = valueDomainService.generateImportTemplate();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "值域导入模板.xlsx");
            
            return new ResponseEntity<>(templateData, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取疾病病种词库下拉列表
     */
    @GetMapping("/pt/{version}/valueDomain/getDiseaseList")
    @ApiOperation("获取疾病病种词库下拉列表")
    public List<DisLexicon> getDiseaseList() {
        return disLexiconMapper.selectList(new QueryWrapper<>());
    }

    /**
     * 保存拖拽排序
     */
    @PostMapping("/pt/{version}/valueDomain/saveDragSort")
    @ApiOperation("保存拖拽排序")
    public void saveDragSort(@RequestBody DragSortDataDTO dto) {
        valueDomainService.saveDragSort(dto);
    }
}
