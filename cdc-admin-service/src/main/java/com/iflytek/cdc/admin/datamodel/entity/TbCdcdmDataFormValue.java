package com.iflytek.cdc.admin.datamodel.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 页面表单数据表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-2
 */
@ApiModel(value = "页面表单数据表")
@Data
public class TbCdcdmDataFormValue implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "主键ID")
    private String id ;

    /**
     * 表单页面配置Detail ID
     */
    @ApiModelProperty(name = "表单页面配置Detail ID")
    private String formTemplateDetailId ;

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型版本ID
     */
    @ApiModelProperty(name = "数据模型版本ID")
    private String modelVersionId ;

    /**
     * 数据模型配置表单ID
     */
    @ApiModelProperty(name = "数据模型配置表单ID")
    private String modelFormId ;

    /**
     * 数据模型表单唯一标识值
     */
    @ApiModelProperty(name = "数据模型表单唯一标识值")
    private String modelFormIdentityId ;

    /**
     * 表单名称
     */
    @ApiModelProperty(name = "表单名称")
    private String modelFormName ;

    /**
     * 页面数据
     */
    @ApiModelProperty(name = "页面数据")
    private String dataJson ;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String creator ;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    private Date updateTime ;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    private String updater ;

}
