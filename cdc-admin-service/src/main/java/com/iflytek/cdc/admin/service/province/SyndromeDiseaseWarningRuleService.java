package com.iflytek.cdc.admin.service.province;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.SyndromeWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseWarningRule;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;

import java.util.List;

public interface SyndromeDiseaseWarningRuleService extends IService<TbCdcmrSyndromeDiseaseWarningRule> {

    /**
     * 编辑症候群规则
     */
    void savaOrUpdateRule(TbCdcmrSyndromeDiseaseWarningRule rule);

    /**
     * 根据风险等级id 和疾病id查询
     */
    PageInfo<TbCdcmrSyndromeDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod, String followStatus);

    /**
     * 构建症候群树
     * */
    List<TreeNode> getSyndromeTree(SyndromeWaringRuleQueryDTO dto);

}
