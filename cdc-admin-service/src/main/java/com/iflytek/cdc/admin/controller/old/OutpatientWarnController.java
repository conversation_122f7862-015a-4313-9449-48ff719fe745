package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.OutpatientQueryDTO;
import com.iflytek.cdc.admin.dto.OutpatientWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType;
import com.iflytek.cdc.admin.service.OutpatientWarnService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
public class OutpatientWarnController {

    @Resource
    OutpatientWarnService outpatientWarnService;

    @ApiOperation("门诊类维护-门诊列表查询")
    @PostMapping("/{version}/pt/outpatient/pageList")
    public PageInfo<TbCdcmrOutpatientType> outpatientPageList(@RequestBody OutpatientQueryDTO OutpatientQueryDTO) {
        return outpatientWarnService.getOutpatientPageList(OutpatientQueryDTO);
    }

    @ApiOperation("门诊类维护-新增门诊类")
    @PostMapping("/{version}/pt/outpatient/add")
    public void addOutpatient(@RequestBody TbCdcmrOutpatientType tbCdcmrOutpatient,String loginUserId) {
        outpatientWarnService.addOutpatient(tbCdcmrOutpatient,loginUserId);
    }

    @ApiOperation("门诊类维护-删除症状")
    @PostMapping("/{version}/pt/outpatient/delete")
    public void deleteOutpatient(@RequestParam String id) {
        outpatientWarnService.deleteOutpatient(id);
    }

    @ApiOperation("门诊类维护-编辑门诊")
    @PostMapping("/{version}/pt/outpatient/update")
    public void updateOutpatient(@RequestBody TbCdcmrOutpatientType tbCdcmrOutpatient,String loginUserId) {
        outpatientWarnService.updateOutpatient(tbCdcmrOutpatient,loginUserId);
    }


    @ApiOperation("门诊类维护-门诊规则预警列表查询")
    @PostMapping("/{version}/pt/outpatientWarn/pageList")
    public PageInfo<OutpatientWarnDto> outpatientWarnPageList(@RequestBody OutpatientQueryDTO OutpatientQueryDTO) {
        return outpatientWarnService.getOutpatientWarnPageList(OutpatientQueryDTO);
    }

    @ApiOperation("门诊类维护-查询门诊类型分类列表")
    @GetMapping("/{version}/pt/outpatientWarn/getOutpatientType")
    public List<CascadeVO> getOutpatientType() {
        return outpatientWarnService.getOutpatientType();
    }


    @ApiOperation("门诊类维护-编辑门诊预警")
    @PostMapping("/{version}/pt/outpatientWarn/update")
    public void updateOutpatientWarn(@RequestBody OutpatientWarnDto OutpatientWarnDto,String loginUserId) {
        outpatientWarnService.updateOutpatientWarn(OutpatientWarnDto,loginUserId);
    }

    @ApiOperation("门诊类维护-修改症状预警状态")
    @PostMapping("/{version}/pt/outpatientWarn/updateStatus")
    public void updateOutpatientWarnStatus(@RequestBody OutpatientWarnDto OutpatientWarnDto,String loginUserId) {
        outpatientWarnService.updateOutpatientWarnStatus(OutpatientWarnDto,loginUserId);
    }

    @ApiOperation("门诊类维护-获取门诊预警")
    @GetMapping("/{version}/pt/outpatientWarn/getWarnById")
    public OutpatientWarnDto getWarnById(@RequestParam String warnId) {
        return outpatientWarnService.getWarnById(warnId);
    }


    @ApiOperation("门诊类维护-门诊规则预警列表查询")
    @GetMapping("/{version}/pt/outpatientWarn/getAllList")
    public List<OutpatientWarnDto> outpatientWarnAllList(@RequestParam(required = false) String outpatientCode ) {
        return outpatientWarnService.getOutpatientWarnAllList(outpatientCode);
    }

    @ApiOperation("门诊类维护-门诊规则预警列表导出")
    @GetMapping("/{version}/pt/outpatientWarnRule/export")
    @LogExportAnnotation
    public void exportOutpatientWarnRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        outpatientWarnService.exportOutpatientWarnRule(response);
    }
}
