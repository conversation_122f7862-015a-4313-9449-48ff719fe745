package com.iflytek.cdc.admin.outbound.model.dto.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 话术自定义变量
 * <AUTHOR>
 */
@Data
@Builder
public class SpeechVariable {

    @ApiModelProperty("变量名称")
    private String remarks;

    @ApiModelProperty("变量内容示例")
    private String content;

    @ApiModelProperty("变量所在的话术内容")
    private String original;

    @ApiModelProperty("变量字符长度限制")
    private Integer limit;

}
