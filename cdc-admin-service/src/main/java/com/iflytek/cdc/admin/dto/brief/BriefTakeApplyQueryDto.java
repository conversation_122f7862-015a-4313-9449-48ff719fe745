package com.iflytek.cdc.admin.dto.brief;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
public class BriefTakeApplyQueryDto extends BaseInfoDto {

    /**
     * 任务Id
     */
    private String id;

    /**
     * 申请时间 开始
     */
    private Date applyStartDate;

    /**
     * 申请时间 结束
     */
    private Date applyEndDate;

    /**
     * 统计周期
     */
    private String statisticsCycle;

    /**
     * 报告类型
     */
    private String briefReportType;

    /**
     * 审批结果
     */
    private Integer approveStatus;

    /**
     * 审批时间 开始
     */
    private Date approveStartDate;

    /**
     * 审批时间 结束
     */
    private Date approveEndDate;

}
