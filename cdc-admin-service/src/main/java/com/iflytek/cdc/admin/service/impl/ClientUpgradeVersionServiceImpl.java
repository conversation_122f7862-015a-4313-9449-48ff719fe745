package com.iflytek.cdc.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.ParamConfigConstants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.ClientUpgradeDistrict;
import com.iflytek.cdc.admin.entity.ClientUpgradeVersion;
import com.iflytek.cdc.admin.enums.QueryTypeEnum;
import com.iflytek.cdc.admin.mapper.ClientUpgradeDistrictMapper;
import com.iflytek.cdc.admin.mapper.ClientUpgradeUrlMapper;
import com.iflytek.cdc.admin.mapper.ClientUpgradeVersionMapper;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionVO;
import com.iflytek.cdc.admin.sdk.util.VersionCompareUtil;
import com.iflytek.cdc.admin.service.ClientUpgradeVersionService;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.cdc.admin.util.MyCollectionUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName ClientUpgradeVersionServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/2 15:32
 * @Version 1.0
 */
@Service
@Slf4j
@AllArgsConstructor
public class ClientUpgradeVersionServiceImpl implements ClientUpgradeVersionService {

    @Resource
    private ClientUpgradeVersionMapper clientUpgradeVersionMapper;

    @Resource
    private ClientUpgradeDistrictMapper clientUpgradeDistrictMapper;

    private BatchUidService batchUidService;

    private UapOrgService orgService;

    private UapUserApi uapUserApi;

    @Resource
    private ClientUpgradeUrlMapper clientUpgradeUrlMapper;

    @Override
    public PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDto(ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {

        List<ClientUpgradeVersionDto> clientUpgradeVersionDtos = null;
        List<String> orgCodes = getOrgCodes(clientUpgradeParamDto, loginUserId);
        if (CollectionUtils.isEmpty(orgCodes)) {
            return new PageData<ClientUpgradeVersionDto>();
        }
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        if (QueryTypeEnum.ORG.getPriority().equals(clientUpgradeParamDto.getQueryType())) {
            List<ClientUpgradeVersionDto> clientUpgradeVersionPreVersion = clientUpgradeVersionMapper.queryPreVersionByOrgId(clientUpgradeParamDto);
            Map<String, List<String>> versionPreVersionMap = new HashMap<String, List<String>>();
            for (ClientUpgradeVersionDto clientUpgradeVersionCountParam : clientUpgradeVersionPreVersion) {
                List<String> versions;
                if (versionPreVersionMap.get(clientUpgradeVersionCountParam.getOrgCode()) != null) {
                    versions = versionPreVersionMap.get(clientUpgradeVersionCountParam.getOrgCode());
                    versions.add(clientUpgradeVersionCountParam.getVersionCode());
                } else {
                    versions = new ArrayList<String>();
                    versions.add(clientUpgradeVersionCountParam.getVersionCode());
                }
                versionPreVersionMap.put(clientUpgradeVersionCountParam.getOrgCode(), versions);
            }
            List<ClientUpgradeVersionDto> clientUpgradeVersionCount = clientUpgradeVersionMapper.queryVersionCountByOrgId(clientUpgradeParamDto);
            Map<String, String> countOrgIdMap = new HashMap<String, String>();
            for (ClientUpgradeVersionDto clientUpgradeVersionCountParam : clientUpgradeVersionCount) {
                countOrgIdMap.put(clientUpgradeVersionCountParam.getOrgCode(), clientUpgradeVersionCountParam.getVersionCount());
            }
            PageMethod.startPage(clientUpgradeParamDto.getPageIndex(), clientUpgradeParamDto.getPageSize());
            clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryCurrentVersionByOrgId(clientUpgradeParamDto);
            for (ClientUpgradeVersionDto clientUpgradeVersionCountParam : clientUpgradeVersionDtos) {
                List<String> versionCodes = versionPreVersionMap.get(clientUpgradeVersionCountParam.getOrgCode());
                clientUpgradeVersionCountParam.setVersionCode(versionCodes.get(0));
                clientUpgradeVersionCountParam.setPreVersionCode(versionCodes.size() > 1 ? versionCodes.get(1) : null);
                String count = countOrgIdMap.get(clientUpgradeVersionCountParam.getOrgCode());
                clientUpgradeVersionCountParam.setVersionCount(count == null ? "0" : count);
            }

        } else if (QueryTypeEnum.VERSION.getPriority().equals(clientUpgradeParamDto.getQueryType())) {
            PageMethod.startPage(clientUpgradeParamDto.getPageIndex(), clientUpgradeParamDto.getPageSize());
            clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryClientUpgradeDtoByVersionCode(clientUpgradeParamDto);
        }
        PageInfo<ClientUpgradeVersionDto> pageInfo = new PageInfo<>(clientUpgradeVersionDtos);
        PageData<ClientUpgradeVersionDto> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDtoHis(ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {
        PageMethod.startPage(clientUpgradeParamDto.getPageIndex(), clientUpgradeParamDto.getPageSize());
        List<String> orgCodes = getOrgCodes(clientUpgradeParamDto, loginUserId);
        if (CollectionUtils.isEmpty(orgCodes)) {
            return new PageData<ClientUpgradeVersionDto>();
        }
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        clientUpgradeParamDto.setIsUpgradeVersion("0");
        List<ClientUpgradeVersionDto> clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryClientUpgradeVersionDto(clientUpgradeParamDto);
        PageInfo<ClientUpgradeVersionDto> pageInfo = new PageInfo<>(clientUpgradeVersionDtos);
        PageData<ClientUpgradeVersionDto> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public List<ClientUpgradeVersionDto> queryClientUpgradeByVersionCode(ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {
        List<String> orgCodes = getOrgCodes(clientUpgradeParamDto, loginUserId);
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Lists.newArrayList();
        }
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        List<ClientUpgradeVersionDto> clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryClientUpgradeVersionDto(clientUpgradeParamDto);
        return clientUpgradeVersionDtos;
    }

    @Override
    public ClientUpgradeVersionDto queryOrgClientUpgradeById(ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {
        List<String> orgCodes = getOrgCodes(clientUpgradeParamDto, loginUserId);
        if (CollectionUtils.isEmpty(orgCodes)) {
            return new ClientUpgradeVersionDto();
        }
        clientUpgradeParamDto.setOrgCodes(orgCodes);
        List<ClientUpgradeVersionDto> clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryClientUpgradeDtoById(clientUpgradeParamDto);
        if (CollectionUtils.isEmpty(clientUpgradeVersionDtos)) {
            ClientUpgradeVersionDto clientUpgradeVersionDto = new ClientUpgradeVersionDto();
            OrgInfoDto[] orgInfos = new OrgInfoDto[0];
            clientUpgradeVersionDto.setOrgInfos(orgInfos);
            return clientUpgradeVersionDto;
        }
        List<OrgInfoDto> orgInfoList = new ArrayList<OrgInfoDto>();
        for (ClientUpgradeVersionDto clientUpgradeVersionDto : clientUpgradeVersionDtos) {
            OrgInfoDto orgInfoDto = new OrgInfoDto();
            orgInfoDto.setAreaCode(clientUpgradeVersionDto.getAreaCode());
            orgInfoDto.setAreaName(clientUpgradeVersionDto.getAreaName());
            orgInfoDto.setOrgCode(clientUpgradeVersionDto.getOrgCode());
            orgInfoDto.setOrgName(clientUpgradeVersionDto.getOrgName());
            orgInfoList.add(orgInfoDto);
        }
        ClientUpgradeVersionDto clientUpgradeVersionDto = clientUpgradeVersionDtos.get(0);
        OrgInfoDto[] orgInfos = orgInfoList.toArray(new OrgInfoDto[0]);
        clientUpgradeVersionDto.setOrgInfos(orgInfos);
        return clientUpgradeVersionDto;
    }

    private List<String> getOrgCodes(ClientUpgradeParamDto clientUpgradeParamDto, String loginUserId) {
        List<String> orgCodes;
        if (!StringUtils.isEmpty(clientUpgradeParamDto.getOrgCode())) {
            List<String> dowOrgCodes = orgService.queryDownOrgCode(loginUserId);
            if (dowOrgCodes.contains(clientUpgradeParamDto.getOrgCode())) {
                orgCodes = new ArrayList<String>();
                orgCodes.add(clientUpgradeParamDto.getOrgCode());
            } else {
                return null;
            }
        } else {
            orgCodes = orgService.queryDownOrgCode(loginUserId);
        }
        return orgCodes;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam, String loginUserId) {
        ClientUpgradeParamDto clientUpgradeParamDto = new ClientUpgradeParamDto();
        clientUpgradeParamDto.setId(clientUpgradeVersionDtoParam.getId());
        List<ClientUpgradeVersionDto> clientUpgradeVersionDtos = clientUpgradeVersionMapper.queryClientUpgradeDtoById(clientUpgradeParamDto);
        if (CollectionUtils.isEmpty(clientUpgradeVersionDtos)) {
            log.warn("clientUpgradeVersionDtos，该数据集不存在：{}，{}", clientUpgradeVersionDtoParam.getOrgCode(), clientUpgradeVersionDtoParam.getVersionCode());
            throw new MedicalBusinessException("不存在对应数据集");
        }
        ClientUpgradeVersionDto clientUpgradeVersionDto = clientUpgradeVersionDtos.get(0);
        //当修改成其它版本号需要校验版本号是否唯一性
        if (!clientUpgradeVersionDto.getVersionCode().equals(clientUpgradeVersionDtoParam.getVersionCode())) {
            checkUniqueVersion(clientUpgradeVersionDtoParam);
        }
        TUapUser uapUser = getTuapUser(loginUserId);
        UpdateDistrictDto updateDistrictDto = new UpdateDistrictDto();
        updateDistrictDto.setVersionId(clientUpgradeVersionDtoParam.getId());
        updateDistrictDto.setUpdateUser(uapUser.getId());
        updateDistrictDto.setIsDelete(ParamConfigConstants.NO_DELETE);
        List<String> orgCodes = getOrgCodes(clientUpgradeVersionDtoParam);
        if (CollectionUtils.isEmpty(orgCodes)) {
            throw new MedicalBusinessException("机构参数为空");
        }
        List<String> orgCodeOwns = orgService.queryDownOrgCode(loginUserId);
        List<ClientUpgradeUrlDto> clientOrgCodes = clientUpgradeDistrictMapper.getOrgCodeById(updateDistrictDto);
        List<String> orgIds = clientOrgCodes.stream().map(ClientUpgradeUrlDto::getOrgCode).collect(Collectors.toList());
        //取当前拥有的权限和已经生成版本机构交集
        orgCodeOwns.retainAll(orgIds);
        //删除已拥有的下级权限生成的版本机构,不影响上级机构
        updateDistrictDto.setOrgCodes(orgCodeOwns);
        updateDistrictDto.setIsDelete(ParamConfigConstants.YES_DELETE);
        clientUpgradeDistrictMapper.deleteOrgByCode(updateDistrictDto);

        clientUpgradeVersionDtoParam.setIsDelete(ParamConfigConstants.NO_DELETE);
        clientUpgradeVersionDtoParam.setUpdateUser(uapUser.getId());
        clientUpgradeVersionMapper.updateClientUpgradeVersionDto(clientUpgradeVersionDtoParam);

        clientUpgradeVersionDtoParam.setCreateUser(uapUser.getId());
        List<ClientUpgradeDistrict> clientUpgradeDistricts = getClientUpgradeDistricts(clientUpgradeVersionDtoParam);
        clientUpgradeDistrictMapper.addBatchClientUpgradeDistrictDto(clientUpgradeDistricts);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam, String loginUserId) {
        TUapUser uapUser = getTuapUser(loginUserId);
        UpdateDistrictDto updateDistrictDto = new UpdateDistrictDto();
        updateDistrictDto.setVersionId(clientUpgradeVersionDtoParam.getId());
        updateDistrictDto.setUpdateUser(uapUser.getId());
        updateDistrictDto.setIsDelete(ParamConfigConstants.YES_DELETE);
        List<String> orgCodes = orgService.queryDownOrgCode(loginUserId);
        if (CollectionUtils.isEmpty(orgCodes)) {
            throw new MedicalBusinessException("机构参数为空");
        }
        updateDistrictDto.setOrgCodes(orgCodes);
        List<ClientUpgradeUrlDto> clientOrgCodes = clientUpgradeDistrictMapper.getOrgCodeById(updateDistrictDto);

        List<String> orgIds = clientOrgCodes.stream().map(ClientUpgradeUrlDto::getOrgCode).collect(Collectors.toList());
        updateDistrictDto.setOrgCodes(orgIds);
        //删除可以操作的机构
        clientUpgradeDistrictMapper.deleteOrgByCode(updateDistrictDto);

        updateDistrictDto.setOrgCodes(null);
        //查询当前版本是不是还有记录
        List<ClientUpgradeUrlDto> existClientOrgCodes = clientUpgradeDistrictMapper.getOrgCodeById(updateDistrictDto);
        //没有记录则删除
        if (CollectionUtils.isEmpty(existClientOrgCodes)) {
            clientUpgradeVersionMapper.deleteVersionById(clientUpgradeVersionDtoParam.getId(), uapUser.getId(), ParamConfigConstants.YES_DELETE);
        }
    }

    private List<String> getOrgCodes(@RequestBody ClientUpgradeVersionDto clientUpgradeVersionDto) {
        List<String> orgCodes = new ArrayList<String>();
        OrgInfoDto[] orgInfoDtos = clientUpgradeVersionDto.getOrgInfos();
        List<OrgInfoDto> orgInfoDtoList = new ArrayList<OrgInfoDto>(Arrays.asList(orgInfoDtos));
        for (OrgInfoDto orgInfoDto : orgInfoDtoList) {
            orgCodes.add(orgInfoDto.getOrgCode());
        }
        return orgCodes;
    }

    public String[] toStrArray(String str) {
        return toStrArray(",", str);
    }

    public String[] toStrArray(String split, String str) {
        return str.split(split);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam, String loginUserId) {
        List<OrgInfoDto> orgInfoDtos = Arrays.asList(clientUpgradeVersionDtoParam.getOrgInfos());
        if (CollectionUtils.isEmpty(orgInfoDtos)) {
            throw new MedicalBusinessException("机构参数为空");
        }
        checkUniqueVersion(clientUpgradeVersionDtoParam);
        clientUpgradeVersionDtoParam.setId(String.valueOf(batchUidService.getUid(TableName.CLIENT_UPGRADE_VERSION)));
        TUapUser uapUser = getTuapUser(loginUserId);
        clientUpgradeVersionDtoParam.setCreateUser(uapUser.getId());
        clientUpgradeVersionDtoParam.setIsDelete(ParamConfigConstants.NO_DELETE);
        clientUpgradeVersionMapper.addClientUpgradeVersionDto(clientUpgradeVersionDtoParam);
        List<ClientUpgradeDistrict> clientUpgradeDistricts = getClientUpgradeDistricts(clientUpgradeVersionDtoParam);
        clientUpgradeDistrictMapper.addBatchClientUpgradeDistrictDto(clientUpgradeDistricts);
    }

    private void checkUniqueVersion(ClientUpgradeVersionDto clientUpgradeVersionDtoParam) {
        List<ClientUpgradeVersionDto> clientUpgradeVersionCount = clientUpgradeVersionMapper.queryClientUpgradeVersionCountDto(clientUpgradeVersionDtoParam);
        if (clientUpgradeVersionCount != null && clientUpgradeVersionCount.size() > 0) {
            log.warn("该机构升级版本号已存在：{}，{}", clientUpgradeVersionDtoParam.getOrgCode(), clientUpgradeVersionDtoParam.getVersionCode());
            throw new MedicalBusinessException("该升级版本号已存在");
        }
    }

    private List<ClientUpgradeDistrict> getClientUpgradeDistricts(@RequestBody ClientUpgradeVersionDto clientUpgradeVersionDto) {
        List<ClientUpgradeDistrict> clientUpgradeDistricts = new ArrayList<ClientUpgradeDistrict>();
        OrgInfoDto[] orgInfoDtos = clientUpgradeVersionDto.getOrgInfos();
        List<OrgInfoDto> orgInfoDtoList = new ArrayList<OrgInfoDto>(Arrays.asList(orgInfoDtos));
        for (OrgInfoDto orgInfoDto : orgInfoDtoList) {
            ClientUpgradeDistrict clientUpgradeDistrict = new ClientUpgradeDistrict();
            clientUpgradeDistrict.setId(String.valueOf(batchUidService.getUid(TableName.CLIENT_UPGRADE_DISTRICT)));
            clientUpgradeDistrict.setVersionId(clientUpgradeVersionDto.getId());
            clientUpgradeDistrict.setIsDelete(ParamConfigConstants.NO_DELETE);
            clientUpgradeDistrict.setCreateUser(clientUpgradeVersionDto.getCreateUser());
            clientUpgradeDistrict.setOrgCode(orgInfoDto.getOrgCode());
            clientUpgradeDistrict.setOrgName(orgInfoDto.getOrgName());
            clientUpgradeDistrict.setAreaCode(orgInfoDto.getAreaCode());
            clientUpgradeDistrict.setAreaName(orgInfoDto.getAreaName());
            clientUpgradeDistricts.add(clientUpgradeDistrict);
        }
        return clientUpgradeDistricts;
    }

    private TUapUser getTuapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (userCache == null) {
            log.warn("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }

    @Override
    public List<PackageVersionVO> queryClientUpgradeVersion(ClientUpgradeFilter clientUpgradeFilter) {
        List<PackageVersionVO> upgradeList = new ArrayList<>();
        PackageVersionVO packageVersionVO = new PackageVersionVO();
        ClientUpgradeVersion result;
        // 参数校验
        if (clientUpgradeFilter == null || StringUtils.isBlank(clientUpgradeFilter.getCurrentVersion()) || StringUtils.isBlank(clientUpgradeFilter.getAssembly())
                || StringUtils.isBlank(clientUpgradeFilter.getAreaCode()) || StringUtils.isBlank(clientUpgradeFilter.getClientType())) {
            throw new MedicalBusinessException("参数异常,请传入合适的参数");
        }
        // 获取安装包管理信息
        List<ClientUpgradeUrlDto> clientUpgradeUrlDtos = clientUpgradeUrlMapper.getPackageFileUrlByAreaCode(clientUpgradeFilter.getAreaCode());
        if (!MyCollectionUtil.isNotNullAndSizeMoreThanZero(clientUpgradeUrlDtos)) {
            log.warn("未查询到符合条件的安装包管理信息", JSONUtil.toJsonStr(clientUpgradeFilter));
            return upgradeList;
        }
        String serverUrl = clientUpgradeUrlDtos.get(0).getPackageFileUrl();

        // 查询所有符合条件的客户端的版本号信息
        List<ClientUpgradeVersion> allList = clientUpgradeVersionMapper.getAllClientVersion(clientUpgradeFilter);
        if (!MyCollectionUtil.isNotNullAndSizeMoreThanZero(allList)) {
            log.warn("未查询到符合条件的版本信息", JSONUtil.toJsonStr(clientUpgradeFilter));
            return upgradeList;
        }
        // 获取版本号集合
        List<String> versionCodeList = allList.stream().map(ClientUpgradeVersion::getVersionCode).collect(Collectors.toList());
        // 筛选出最大的版本号
        String maxVersionCode = VersionCompareUtil.sort(versionCodeList);
        // 比较两个版本 获取版本号最大的
        try {
            if (VersionCompareUtil.compareVersionV2(maxVersionCode, clientUpgradeFilter.getCurrentVersion()) >= 1) {
                // 返回maxVersionCode对应的版本
                result = allList.stream().filter(s -> maxVersionCode.equals(s.getVersionCode())).collect(Collectors.toList()).get(0);
                packageVersionVO.setVersion(maxVersionCode);
                //  版本子类型 1-增量版本  2-全量版本 3-安装包 默认安装包
                packageVersionVO.setSubType(3);
                packageVersionVO.setDescriptionFile(serverUrl + Constants.PACKAGE_URL + clientUpgradeFilter.getAreaCode() + "/" + result.getVersionCode() + "/" + result.getDescriptionFileUrl());
                packageVersionVO.setResourceFile(serverUrl + Constants.PACKAGE_URL + clientUpgradeFilter.getAreaCode() + "/" + result.getVersionCode() + "/" + result.getResourceFileUrl());
                upgradeList.add(packageVersionVO);
            }
        } catch (Exception e) {
            log.error("###### 比较版本号大小出现异常 ######");
            e.printStackTrace();
        }
        return upgradeList;
    }
}
