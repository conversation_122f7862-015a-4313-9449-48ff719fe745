package com.iflytek.cdc.admin.service.province.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.iflytek.cdc.admin.constant.SyndromeWarningRuleConstants;
import com.iflytek.cdc.admin.dto.WarningChargePersonDTO;
import com.iflytek.cdc.admin.dto.WarningRiskLevelDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeMonitorConfig;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeMonitorConfigMapper;
import com.iflytek.cdc.admin.model.mr.dto.SyndromeMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import com.iflytek.cdc.admin.service.province.SyndromeProcessMonitorService;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelDetailService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class SyndromeProcessMonitorServiceImpl implements SyndromeProcessMonitorService {

    @Resource
    private TbCdcmrSyndromeDiseaseInfoMapper diseaseInfoMapper;

    @Resource
    private TbCdcmrSyndromeMonitorConfigMapper monitorConfigMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private WarningRiskLevelDetailService warningRiskLevelDetailService;

    @Resource
    private WarningChargePersonService warningChargePersonService;

    @Resource
    private CommonUtils commonUtils;

    private static final String TB_CDCMR_SYNDROME_DISEASE_INFO = "tb_cdcmr_syndrome_disease_info";

    private static final String TB_CDCMR_SYNDROME_MONITOR_CONFIG = "tb_cdcmr_syndrome_monitor_config";

    @Value("${generateStrLength:8}")
    private int length;

    @Override
    public List<SyndromeDiseaseInfoVO> getSyndromeTreeInfo() {

        return diseaseInfoMapper.getSyndromeTreeInfo();
    }

    @Override
    public SyndromeDiseaseInfoVO loadById(String id) {
        return diseaseInfoMapper.loadById(id);
    }

    @Override
    public SyndromeDiseaseInfoVO loadByCode(String code) {
        return diseaseInfoMapper.loadByCode(code);
    }

    @Override
    @Transactional
    public String editSubSyndromeInfo(TbCdcmrSyndromeDiseaseInfo syndromeDiseaseInfo) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_SYNDROME_DISEASE_INFO));
        //非空为更新，为空则新增
        if(syndromeDiseaseInfo.getId() == null){
            //新增id、code后端生成
            syndromeDiseaseInfo.setId(id);
            //查目前疾病code
            List<String> diseaseCodeList = diseaseInfoMapper.getAllDiseaseCodeBy();
            syndromeDiseaseInfo.setDiseaseCode(commonUtils.generateSyndromeCode(length, diseaseCodeList));
        }

        //状态默认开启，默认未删除
        syndromeDiseaseInfo.setStatus(syndromeDiseaseInfo.getStatus() == null ? Integer.valueOf(StatusEnum.STATUS_ON.getCode()) : syndromeDiseaseInfo.getStatus());
        syndromeDiseaseInfo.setDeleteFlag(syndromeDiseaseInfo.getDeleteFlag() == null ? DeleteFlagEnum.NO.getCode() : syndromeDiseaseInfo.getDeleteFlag());
        syndromeDiseaseInfo.setCreateTime(new Date());
        syndromeDiseaseInfo.setCreator(uapUserPo.getName());
        syndromeDiseaseInfo.setCreatorId(uapUserPo.getId());
        syndromeDiseaseInfo.setUpdateTime(new Date());
        syndromeDiseaseInfo.setUpdater(uapUserPo.getName());
        syndromeDiseaseInfo.setUpdaterId(uapUserPo.getId());
        diseaseInfoMapper.insertOrUpdate(Collections.singletonList(syndromeDiseaseInfo));

        return id;
    }

    @Override
    public void deleteSubSyndromeInfo(String id) {

        //删除操作做软删处理，更新删除标识
        diseaseInfoMapper.updateDeleteFlagById(id);
    }

    @Override
    public List<TbCdcmrSyndromeMonitorConfig> getSyndromeInfoConfigs(SyndromeMonitorConfigQueryDTO queryDTO) {

        return monitorConfigMapper.queryByDiseaseInfoId(queryDTO);
    }

    @Override
    @Transactional
    public void editSyndromeProcessDefinition(String diseaseInfoId, List<TbCdcmrSyndromeMonitorConfig> syndromeMonitorConfigs) {

        UapUserPo uapUserPo = USER_INFO.get();
        //软删除该疾病下的所有规则
        monitorConfigMapper.updateBySyndromeInfoId(diseaseInfoId);

        String loginUserId = null;
        String loginUserName = null;
        if(uapUserPo != null){
            loginUserId = uapUserPo.getId();
            loginUserName = uapUserPo.getName();
        }
        //重新set id的值，并插入配置表
        for (TbCdcmrSyndromeMonitorConfig monitorConfig : syndromeMonitorConfigs) {

            monitorConfig.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_SYNDROME_MONITOR_CONFIG)));
            //状态默认开启，默认未删除
            monitorConfig.setStatus(StatusEnum.STATUS_ON.getCode());
            monitorConfig.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            monitorConfig.setCreateTime(new Date());
            monitorConfig.setCreator(loginUserName);
            monitorConfig.setCreatorId(loginUserId);
            monitorConfig.setUpdateTime(new Date());
            monitorConfig.setUpdater(loginUserName);
            monitorConfig.setUpdaterId(loginUserId);
        }
        if(CollectionUtils.isNotEmpty(syndromeMonitorConfigs)) {
            monitorConfigMapper.batchInsert(syndromeMonitorConfigs);
        }
    }

    @Override
    public List<DiagnoseListVO> getDiagnoseCodeList() {

        //todo : 等后续数仓出诊断编码表需要做修改，目前暂时用后管表替代
        return monitorConfigMapper.getDiagnoseCodeList();
    }

    @Override
    public List<String> getSyndromeDiseaseCodeByName(String diseaseName) {

        String diseaseCode = diseaseInfoMapper.getSyndromeDiseaseCodeByName(diseaseName);
        return StringUtils.isNotBlank(diseaseCode) ? Collections.singletonList(diseaseCode) : new ArrayList<>();
    }

}
