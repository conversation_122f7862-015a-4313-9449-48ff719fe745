package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public interface CustomizedWarningRuleService {
    void addCustomizedWarningRule(TbCdcmrCustomizedWarningRule tbCdcmrCustomizedWarningRule);

    void updateCustomizedWarningRule(CustomizedWarningRuleVO customizedWarningRuleVO, String loginUserId);

    void deleteCustomizedWarningRule(String warnId);

    PageInfo<CustomizedWarningRuleVO> getCustomizedWarningRuleList(CustomizedWarnQueryDTO customizedWarnQueryDTO);

    CustomizedWarningRuleVO getCustomizedWarningRuleById(String id);

    List<CustomizedWarningRuleVO> getAllEnabledWarningRule(String warningType);

    List<CustomizedWarningRuleVO> getAllEnabledWarningRuleWithoutItem();

    void updateCustomizedWarningRuleStatus(Integer status, String id, String loginUserId);

    void exportCustomizedWarningRule(HttpServletResponse response);

}
