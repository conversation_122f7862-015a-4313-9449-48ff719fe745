package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.UserManualService;
import com.iflytek.storage.util.StringUtils;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户手册API
 *
 * <AUTHOR>
 * @date 2021/9/15 17:04
 **/
@RestController
public class UserManualController {

    @Autowired
    private UserManualService userManualService;

    @Autowired
    private MdmDataSyncService mdmDataSyncService;

    @ApiOperation("上传用户手册")
    @PostMapping("/{version}/pt/userManual/upload")
    public ResponseResult userManualUpload(@PathVariable String version, @RequestPart("file") MultipartFile file, @RequestParam("versionCode") String versionCode)  {
        return userManualService.userManualUpload(file,versionCode);
    }

    @ApiOperation("根据版本id查询用户手册")
    @GetMapping("/{version}/pt/userManual/query")
    public ResponseResult userManualQuery(@RequestParam("versionCode") String versionCode){
        if (StringUtils.isBlank(versionCode)){
            return new ResponseResult("请使用正确的版本号");
        }
        return userManualService.userManualQuery(versionCode);
    }

    @ApiOperation("测试mdm调用")
    @GetMapping("/{version}/pt/userManual/testMdmUse")
    public ResponseResult testMdmUse(){
        DictMappingInfo di = mdmDataSyncService.getMappingInfo("cdcmr-infecdis-code");
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmCodeInfo = mdmDataSyncService.getMdmCodeInfo(di, 1, 1000);
        List<TermCodedValueInfo> entities = mdmCodeInfo.getEntities();
        return new ResponseResult(entities);
    }
}
