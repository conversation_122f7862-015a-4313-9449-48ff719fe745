package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsPageMapper;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppPageQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.PageButtonSettingService;
import com.iflytek.cdc.admin.customizedapp.service.PageService;
import com.iflytek.cdc.admin.customizedapp.service.PageViewColService;
import com.iflytek.cdc.admin.customizedapp.service.TaskSettingService;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.datamodel.service.DataModelExtendService;
import com.iflytek.cdc.admin.datamodel.service.EntityErModelService;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 页面;(tb_cdccs_page)表服务实现类
 */
@Service
public class PageServiceImpl extends CdcServiceBaseImpl<TbCdccsPageMapper, TbCdccsPage> implements PageService {
    @Resource
    private TbCdccsPageMapper tbCdccsPageMapper;

    @Resource
    private PageViewColService tbCdccsPageViewColService;

    @Resource
    private PageButtonSettingService tbCdccsPageButtonSettingService;

    @Resource
    private DataModelExtendService dataModelExtendService;

    @Resource
    private EntityErModelService entityErModelService;

    @Resource
    private TaskSettingService taskSettingService;

    @Override
    public void fulfillChildProperty(TbCdccsPage tbCdccsPage) {
        super.fulfillChildProperty(tbCdccsPage);
        tbCdccsPage.setPageViewColList(tbCdccsPageViewColService.listByPageId(tbCdccsPage.getId()));
        tbCdccsPage.setButtonSettingList(tbCdccsPageButtonSettingService.listByPageId(tbCdccsPage.getId()));
        //tbCdccsPage.setTaskSettingList(taskSettingService.listByPageId(tbCdccsPage.getId()));
    }

    @Override
    public TbCdccsPage loadCascadeByCode(String code) {
        LambdaQueryWrapper<TbCdccsPage> tbCdccsPageLambdaQueryWrapper = lambdaQueryWrapper();
        tbCdccsPageLambdaQueryWrapper.eq(TbCdccsPage::getPageCode, code);
        TbCdccsPage tbCdccsPage = getOne(tbCdccsPageLambdaQueryWrapper);
        if (tbCdccsPage == null){
            throw new MedicalBusinessException("数据错误");
        }
        fulfillChildProperty(tbCdccsPage);
        return tbCdccsPage;
    }

    @Override
    public List<TbCdccsPage> list(AppPageQueryDTO queryDTO) {
        LambdaQueryWrapper<TbCdccsPage> lambdaQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDTO.getAppId())){
            lambdaQuery.eq(TbCdccsPage::getAppId, queryDTO.getAppId());
        }
        if (StringUtils.isNotEmpty(queryDTO.getQueryKey())){
            lambdaQuery.or().like(TbCdccsPage::getPageCode, queryDTO.getQueryKey())
                    .like(TbCdccsPage::getPageName, queryDTO.getQueryKey());
        }
        return tbCdccsPageMapper.selectList(lambdaQuery);
    }

    @Override
    public void beforeCreate(TbCdccsPage entity) {
        super.beforeCreate(entity);
        duplicate(entity, "页面编码重复", TbCdccsPage::getPageCode, TbCdccsPage::getAppId);

    }

    @Override
    public void beforeUpdate(TbCdccsPage entity) {
        super.beforeUpdate(entity);
        duplicate(entity, "页面编码重复", TbCdccsPage::getPageCode, TbCdccsPage::getAppId);
    }

    @Override
    public void afterCreate(TbCdccsPage entity) {
        super.afterCreate(entity);
        saveChildProperty(entity);
    }

    @Override
    public void afterUpdate(TbCdccsPage entity) {
        super.afterUpdate(entity);
        saveChildProperty(entity);
    }

    @Override
    public void afterDelete(String id) {
        super.afterDelete(id);
        tbCdccsPageButtonSettingService.deleteByPageId(id);
        tbCdccsPageViewColService.deleteByPageId(id);
    }

    /**
     * 分页数据查询
     *
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    @Override
    public PageInfo<TbCdccsPage> pageList(AppPageQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(tbCdccsPageMapper.pageList(queryDTO));
    }

    @Override
    public TbCdcdmEntityErModel loadErModelByPageId(String pageId) {
        TbCdccsPage byId = getById(pageId);
        String erModelId = dataModelExtendService.loadErModelIdByModelId(byId.getDataModelId());
        return entityErModelService.load(erModelId);
    }

    @Override
    @Transactional
    public void deleteByAppId(String appId) {
        List<TbCdccsPage> list = list(lambdaQueryWrapper().eq(TbCdccsPage::getAppId, appId));
        list.forEach(t -> {
            deleteById(t.getId());
        });
    }

    @Override
    public void saveByCode(TbCdccsPage page) {
        if (StringUtils.isNotEmpty(page.getPageCode())){
            TbCdccsPage tbCdccsPage = getBaseMapper().selectOne(lambdaQueryWrapper().eq(TbCdccsPage::getPageCode, page.getPageCode()));
            if (tbCdccsPage != null){
                page.setId(tbCdccsPage.getId());
                update(page);
            }else {
                create(page);
            }
        }else {
            create(page);
        }
    }

    /**
     * 保存子表的数据
     */
    private void saveChildProperty(TbCdccsPage entity){
        if (entity.getPageViewColList() != null){
            tbCdccsPageViewColService.saveByPage(entity, entity.getPageViewColList());
        }
        if (entity.getButtonSettingList() != null){
            tbCdccsPageButtonSettingService.saveByPage(entity, entity.getButtonSettingList());
        }
//        if(entity.getTaskSettingList() != null){
//            taskSettingService.saveByPage(entity, entity.getTaskSettingList());
//        }
    }

}