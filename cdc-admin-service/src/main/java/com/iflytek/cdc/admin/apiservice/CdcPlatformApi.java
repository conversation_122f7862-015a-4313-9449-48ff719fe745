package com.iflytek.cdc.admin.apiservice;

import com.alibaba.fastjson.JSONArray;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.AreaMappingVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class CdcPlatformApi {
    @Value("${cdc-platform-api:http://cdc-platform-api}")
    private String apiUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private UapServiceApi uapServiceApi;

    /**
     * 获取区域
     */
    public List<AreaMappingVO> listByRegionNames(List<String> names){
        String url = String.format("%s/pt/v1/listByRegionNames", apiUrl);
        return postGetList(url, names, AreaMappingVO.class);
    }

    /**
     * 获取区域
     */
    public List<AreaMappingVO> listByRegionCodes(List<String> codes){
        String url = String.format("%s/pt/v1/listByRegionCodes", apiUrl);
        return postGetList(url, codes, AreaMappingVO.class);
    }

    /**
     * 获取集合类数据
     */
    private <T> List<T> postGetList(String url, Object request, Class<T> responseType){
        JSONArray objects = restTemplate.postForObject(url, request, JSONArray.class);
        if (objects == null){
            return new ArrayList<>();
        }
        return objects.toJavaList(responseType);
    }
}
