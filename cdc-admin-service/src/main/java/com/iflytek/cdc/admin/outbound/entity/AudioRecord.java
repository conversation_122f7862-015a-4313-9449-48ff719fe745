package com.iflytek.cdc.admin.outbound.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import com.iflytek.cdc.admin.outbound.constant.enums.AddressTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 流调对象音频文件表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcmr_audio_record")
public class AudioRecord extends BaseEntity {

    public static final String TABLE_NAME = "tb_cdcmr_audio_record";
    /**
     * 音频文件地址
     */
    private String audioPath;

    /**
     * 音频实时转写结果
     */
    private String audioTempText;

    /**
     * 音频解析结果
     */
    private String audioFinalText;

    /**
     * 关联业务id
     */
    private String businessId;

    /**
     * 对象存储中的object_name
     */
    private String objectName;

    /**
     * 语音转写任务id
     */
    private String transformTaskId;

    /**
     * 转写完成标志 0-未完成 1-完成
     */
    private Integer transformFinishFlag;

    /**
     * 录音文件名
     */
    private String audioName;

    private Integer audioIndex;

    /**
     * 音频录制时所在地
     */
    private String audioAddress;

    private String audioAddressType;

    /**
     * 音频录音时长
     */
    private Integer audioTime;

    /**
     * 电话流调 0-否 1-是
     */
    private String isCall;

    private String callPath;

    public String getAudioAddress() {
        if(!StringUtils.isEmpty(audioAddress)){
            return AddressTypeEnum.getByName(audioAddress);
        }
        return audioAddress;
    }
}