package com.iflytek.cdc.admin.constant;

public enum SendSMSTypeEnum {

    INSTANT("-1", "立即发送"),
    ZER<PERSON>("0", "00:00"),
    ONE("1", "01:00"),
    <PERSON>W<PERSON>("2", "02:00"),
    <PERSON>H<PERSON><PERSON>("3", "03:00"),
    <PERSON><PERSON><PERSON>("4", "04:00"),
    <PERSON>IVE("5", "05:00"),
    <PERSON><PERSON>("6", "06:00"),
    SEVE<PERSON>("7", "07:00"),
    <PERSON><PERSON>HT("8", "08:00"),
    NINE("9", "09:00"),
    TEN("10", "10:00"),
    <PERSON><PERSON><PERSON>N("11", "11:00"),
    TWELVE("12", "12:00"),
    THIRTEEN("13", "13:00"),
    FOURTEEN("14", "14:00"),
    FIFTEEN("15", "15:00"),
    SIXTEEN("16", "16:00"),
    SEVENTEEN("17", "17:00"),
    EIGHTEEN("18", "18:00"),
    NINETEEN("19", "19:00"),
    TWENTY("20", "20:00"),
    TWENTY_ONE("21", "21:00"),
    TWENTY_TWO("22", "22:00"),
    TWENTY_THREE("23", "23:00");

    private String code;
    private String desc;

    SendSMSTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

}
