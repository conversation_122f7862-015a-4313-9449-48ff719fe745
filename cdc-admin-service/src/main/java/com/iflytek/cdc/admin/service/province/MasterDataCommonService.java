package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Service
public class MasterDataCommonService {

    /**
     * 查询主数据的树形结构
     * */
    public List<TreeNode> getMasterData(MasterDataQueryDTO dto) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(dto.getMasterDataType());
        return masterDataService.getMasterData(dto);
    }


    /**
     * 查询某个主数据的树
     * */
    public TreeNode getMasterDataByRootId(String masterDataType, String rootId) {
        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(masterDataType);
        return masterDataService.getMasterDataByRootId(rootId);
    }

    /**
     * 获取主数据基础信息以及其配置信息
     * */
    public <T extends CommonMasterData> T getMasterDataConfigInfo(@RequestBody MasterDataQueryDTO dto) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(dto.getMasterDataType());
        return masterDataService.getMasterDataConfigInfo(dto);
    }

    /**
     * 新增主数据
     * */
    public String addMasterData(MasterDataRecordDTO dto) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(dto.getMasterDataType());
        return masterDataService.addMasterData(dto);
    }

    /**
     * 编辑主数据信息
     * */
    public void editMasterDataBaseInfo(MasterDataEditRecordDTO dto) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(dto.getMasterDataType());
        masterDataService.editMasterDataBaseInfo(dto);
    }

    /**
     * 删除主数据信息
     * */
    public void deleteMasterDataBaseInfo(MasterDataDeleteDTO dto) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(dto.getMasterDataType());
        masterDataService.deleteMasterDataBaseInfo(dto);
    }

    /**
     * 同步主数据
     * */
    public void syncMasterData(@RequestParam String masterDataType) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(masterDataType);
        masterDataService.syncMasterData();
    }

    /**
     * 上传主数据 - 用于初始化数据
     * */
    public void uploadFile(String masterDataType, MultipartFile file) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(masterDataType);
        masterDataService.uploadFile(file);
    }

    /**
     * 通过code获取疾病信息
     * */
    public CommonMasterData getDiseaseInfoByCode(String masterDataType, String masterDataCode) {

        IMasterDataService masterDataService = MasterDataFactory.getMedicalService(masterDataType);
        return masterDataService.getDiseaseInfoByCode(masterDataCode);
    }

}
