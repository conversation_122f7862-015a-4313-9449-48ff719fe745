package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.PoisoningQueryDTO;
import com.iflytek.cdc.admin.dto.PoisoningWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrPoisoning;
import com.iflytek.cdc.admin.enums.PoisoningMonitorObjectEnum;
import com.iflytek.cdc.admin.service.PoisoningWarnService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
public class PoisoningWarnController {
    
    @Resource
    PoisoningWarnService poisoningWarnService;

    @ApiOperation("中毒类维护-中毒列表查询")
    @PostMapping("/{version}/pt/poisoning/pageList")
    public PageInfo<TbCdcmrPoisoning> poisoningPageList(@RequestBody PoisoningQueryDTO PoisoningQueryDTO) {
        return poisoningWarnService.getPoisoningPageList(PoisoningQueryDTO);
    }

    @ApiOperation("中毒类维护-新增症状")
    @PostMapping("/{version}/pt/poisoning/add")
    public void addPoisoning(@RequestBody TbCdcmrPoisoning tbCdcmrPoisoning,String loginUserId) {
        poisoningWarnService.addPoisoning(tbCdcmrPoisoning,loginUserId);
    }

    @ApiOperation("中毒类维护-删除症状")
    @PostMapping("/{version}/pt/poisoning/delete")
    public void deletePoisoning(@RequestParam String id) {
        poisoningWarnService.deletePoisoning(id);
    }

    @ApiOperation("中毒类维护-编辑中毒")
    @PostMapping("/{version}/pt/poisoning/update")
    public void updatePoisoning(@RequestBody TbCdcmrPoisoning tbCdcmrPoisoning,String loginUserId) {
        poisoningWarnService.updatePoisoning(tbCdcmrPoisoning,loginUserId);
    }


    @ApiOperation("中毒类维护-中毒规则预警列表查询")
    @PostMapping("/{version}/pt/poisoningWarn/pageList")
    public PageInfo<PoisoningWarnDto> poisoningWarnPageList(@RequestBody PoisoningQueryDTO PoisoningQueryDTO) {
        return poisoningWarnService.getPoisoningWarnPageList(PoisoningQueryDTO);
    }


    @ApiOperation("中毒类维护-编辑中毒预警")
    @PostMapping("/{version}/pt/poisoningWarn/update")
    public void updatePoisoningWarn(@RequestBody PoisoningWarnDto PoisoningWarnDto,String loginUserId) {
        poisoningWarnService.updatePoisoningWarn(PoisoningWarnDto,loginUserId);
    }

    @ApiOperation("中毒类维护-修改症状预警状态")
    @PostMapping("/{version}/pt/poisoningWarn/updateStatus")
    public void updatePoisoningWarnStatus(@RequestBody PoisoningWarnDto PoisoningWarnDto,String loginUserId) {
        poisoningWarnService.updatePoisoningWarnStatus(PoisoningWarnDto,loginUserId);
    }

    @ApiOperation("中毒类维护-获取中毒预警")
    @GetMapping("/{version}/pt/poisoningWarn/getWarnById")
    public PoisoningWarnDto getWarnById(@RequestParam String warnId) {
        return poisoningWarnService.getWarnById(warnId);
    }

    @ApiOperation("中毒类维护-获取监测对象列表")
    @GetMapping("/{version}/pt/poisoningWarn/getMonitorObjectList")
    public List<Map<String,Object>> getMonitorObjectList() {
        return PoisoningMonitorObjectEnum.getAllToList();
    }

    @ApiOperation("中毒类维护-查询中毒分类列表")
    @GetMapping("/{version}/pt/poisoningWarn/getPoisoningType")
    public List<CascadeVO> getPoisoningType() {
        return poisoningWarnService.getPoisoningType();
    }


    @ApiOperation("中毒类维护-中毒规则预警列表查询")
    @GetMapping("/{version}/pt/poisoningWarn/getAllList")
    public List<PoisoningWarnDto> poisoningWarnAllList(@RequestParam(required = false) String poisoningCode ) {
        return poisoningWarnService.getPoisoningWarnAllList(poisoningCode);
    }

    @ApiOperation("中毒类维护-中毒规则预警列表导出")
    @GetMapping("/{version}/pt/poisoningWarnRule/export")
    @LogExportAnnotation
    public void exportPoisoningWarnRule(HttpServletResponse response, @RequestParam("loginUserId") String loginUserId) {
        poisoningWarnService.exportPoisoningWarnRule(response);
    }

}
