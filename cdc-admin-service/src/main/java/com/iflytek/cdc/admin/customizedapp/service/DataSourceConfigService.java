package com.iflytek.cdc.admin.customizedapp.service;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsDataSourceConfig;
import com.iflytek.cdc.admin.customizedapp.model.vo.DataSourceConfigVO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface DataSourceConfigService extends ICdcService<TbCdccsDataSourceConfig> {

    /**
     * 查询所有的配置
     */
    List<DataSourceConfigVO> listAll();
}
