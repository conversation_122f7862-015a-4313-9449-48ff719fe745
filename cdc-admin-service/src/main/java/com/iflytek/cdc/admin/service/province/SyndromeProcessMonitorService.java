package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrSyndromeMonitorConfig;
import com.iflytek.cdc.admin.model.mr.dto.SyndromeMonitorConfigQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiagnoseListVO;
import com.iflytek.cdc.admin.model.mr.vo.SyndromeDiseaseInfoVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SyndromeProcessMonitorService {

    /**
     * 查询症候群结构树
     * */
    List<SyndromeDiseaseInfoVO> getSyndromeTreeInfo();

    /**
     * 根据疾病id查询
     */
    SyndromeDiseaseInfoVO loadById(String id);


    /**
     * 根据疾病编码查询
     */
    SyndromeDiseaseInfoVO loadByCode(String code);

    /**
     * 编辑子类症候群信息
     * 新增或更新
     * */
    String editSubSyndromeInfo(TbCdcmrSyndromeDiseaseInfo syndromeDiseaseInfo);

    /**
     * 删除子类症候群信息
     * 做软删操作
     * */
    void deleteSubSyndromeInfo(String id);

    /**
     * 查看某个症候群监测病例定义的配置
     * */
    List<TbCdcmrSyndromeMonitorConfig> getSyndromeInfoConfigs(SyndromeMonitorConfigQueryDTO queryDTO);

    /**
     * 编辑症候群监测病例定义规则
     * 规则更新时，先做软删除，再重新插入数据
     * */
    void editSyndromeProcessDefinition(String diseaseInfoId, List<TbCdcmrSyndromeMonitorConfig> syndromeMonitorConfigs);

    /**
     * 获取诊断编码列表
     * */
    List<DiagnoseListVO> getDiagnoseCodeList();

    /**
     * 根据症候群名称获取症候群code
     * */
    List<String> getSyndromeDiseaseCodeByName(String diseaseName);

}
