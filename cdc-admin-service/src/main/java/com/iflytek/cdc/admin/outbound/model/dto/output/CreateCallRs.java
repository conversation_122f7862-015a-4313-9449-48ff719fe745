package com.iflytek.cdc.admin.outbound.model.dto.output;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 创建外呼任务-返回结果
 * <AUTHOR>
 */
@Data
@Builder
public class CreateCallRs {

    /**
     * 外呼任务批次号
     */
    @ApiModelProperty("批次号")
    private List<String> batch;

    /**
     * 外呼过滤信息
     */
    @ApiModelProperty("外呼平台过滤信息")
    private List<Map<String,Object>> filterDwellers;

}
