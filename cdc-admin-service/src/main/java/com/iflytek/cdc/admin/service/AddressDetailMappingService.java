package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.entity.AddressDetailMapping;
import com.iflytek.cdc.admin.entity.AddressStandard;

import java.util.Date;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
public interface AddressDetailMappingService extends IService<AddressDetailMapping> {

    /**
     * 存在就更新 不存在就新增
     *
     * @param addressDetailMapping
     */
    void saveOrUpdateAddressDetailMapping(AddressDetailMapping addressDetailMapping);

    void manualSimilarity(Date startDate, Date endDate, Integer status, Double similarity);

    void mappingSimilarity(AddressStandard addressStandard, AddressDetailMapping addressDetailMapping);
}
