package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.iflytek.cdc.admin.apiservice.EpiServiceProApi;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleSaveDTO;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.common.dto.workbench.SignalPushRuleDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonDTO;
import com.iflytek.cdc.admin.dto.WarningChargePersonQuery;
import com.iflytek.cdc.admin.dto.epi.AreaDTO;
import com.iflytek.cdc.admin.dto.epi.EmergencyPlanRequest;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfigMessageRelation;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningChargePerson;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.MessageConfigEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrSignalPushConfigMessageRelationMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrSignalPushConfigurationMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrWarningChargePersonMapper;
import com.iflytek.cdc.admin.model.mr.dto.DealPersonQueryDTO;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.DateUtils;
import com.iflytek.cdc.admin.vo.SignalPushConfigurationVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.cdc.admin.vo.epi.DictVO;
import com.iflytek.cdc.admin.vo.epi.DutyUserVO;
import com.iflytek.cdc.admin.vo.epi.EcdOrgUserVO;
import com.iflytek.cdc.admin.vo.epi.EmergencyPlanVO;
import com.iflytek.cdc.admin.workbench.dto.message.MessageAddDTO;
import com.iflytek.cdc.admin.workbench.entity.Message;
import com.iflytek.cdc.admin.workbench.entity.MessageConfig;
import com.iflytek.cdc.admin.workbench.mapper.MessageMapper;
import com.iflytek.cdc.admin.workbench.service.MessageService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WarningChargePersonServiceImpl implements WarningChargePersonService {
    @Resource
    private BatchUidService batchUidService;
    @Resource
    private TbCdcmrWarningChargePersonMapper tbCdcmrWarningChargePersonMapper;
    @Resource
    private EpiServiceProApi epiServiceProApi;
    @Resource
    private MessageService messageService;
    @Resource
    MessageMapper messageMapper;
    @Resource
    TbCdcmrSignalPushConfigMessageRelationMapper tbCdcmrSignalPushConfigMessageRelationMapper;
    @Resource
    private TbCdcmrSignalPushConfigurationMapper tbCdcmrSignalPushConfigurationMapper;
    public static final ThreadLocal<UapUserPo> USER_INFO = new ThreadLocal<>();
    @Override
    public TbCdcmrWarningChargePerson save(WarningChargePersonDTO input) {
        UapUserPo uapUserPo = USER_INFO.get();
        TbCdcmrWarningChargePerson entity = ofWarningChargePersonDTO(input, uapUserPo, loadById(input.getId()));
        tbCdcmrWarningChargePersonMapper.mergeInto(Collections.singletonList(entity));
        return entity;
    }

    @Override
    public void batchSave(List<WarningChargePersonDTO> inputs) {

        List<String> ids = inputs.stream()
                                 .map(WarningChargePersonDTO::getId)
                                 .filter(StringUtils::isNotEmpty)
                                 .collect(Collectors.toList());
        Map<String, TbCdcmrWarningChargePerson> existedMap = new HashMap<>();
        if (ids.size() > 0){
            //查询已存在的责任人配置
            existedMap = tbCdcmrWarningChargePersonMapper.bulkLoadByIds(ids).stream()
                    .collect(Collectors.toMap(TbCdcmrWarningChargePerson::getId, t -> t));
        }
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        List<TbCdcmrWarningChargePerson> entities = new ArrayList<>();
        for (WarningChargePersonDTO input : inputs){
            TbCdcmrWarningChargePerson entity = ofWarningChargePersonDTO(input, uapUserPo, existedMap.get(input.getId()));
            entities.add(entity);
        }
        tbCdcmrWarningChargePersonMapper.mergeInto(entities);
    }

    @Override
    public void changeStatus(WarningChargePersonDTO input) {
        TbCdcmrWarningChargePerson entity = loadById(input.getId());
        if (entity == null) {
            throw new MedicalBusinessException("请先编辑配置");
        }

        if (input.getIsEnabled().equals(Constants.NO_USE)){
            TbCdcmrSignalPushConfiguration pushConfig = tbCdcmrSignalPushConfigurationMapper.selectByWarningChargePersonTableId(entity.getId());
            if(pushConfig!=null){
                // 逻辑删除旧消息
                List<Message> messageList = messageMapper.listBySignalPushConfigurationId(input.getId());
                if (CollectionUtils.isNotEmpty(messageList)) {
                    messageList.forEach(m -> {
                        m.setDeleteFlag(CommonConstants.DELETE_FLAG_1);
                        m.setUpdateTime(LocalDateTime.now());
                    });
                    messageMapper.updateBatchById(messageList);
                }
            }
        }
        entity.setStatus(input.getIsEnabled());
        tbCdcmrWarningChargePersonMapper.updateById(entity);
    }

    @Override
    public List<WarningChargePersonVO> listDiseaseConfig(WarningChargePersonQuery query) {

        if (query.getWarningType().equals(WarningTypeProEnum.INFECTED.getCode())
                || query.getWarningType().equals(WarningTypeProEnum.SYNDROME.getCode())
                || query.getWarningType().equals(WarningTypeProEnum.EMERGING.getCode())
                || query.getWarningType().equals(WarningTypeProEnum.ENDEMIC.getCode())) {
            return tbCdcmrWarningChargePersonMapper.listDiseaseConfig(query);
        } else if (query.getWarningType().equals(WarningTypeProEnum.MULTICHANNEL.getCode())) {
            return tbCdcmrWarningChargePersonMapper.listMultichannelDiseaseConfig(query);
        } else {
//            多渠道综合预警
            return tbCdcmrWarningChargePersonMapper.listIntegratedDiseaseConfig(query);
        }
    }

    @Override
    public TbCdcmrWarningChargePerson loadById(String id) {
        return tbCdcmrWarningChargePersonMapper.loadById(id);
    }

    private TbCdcmrWarningChargePerson ofWarningChargePersonDTO(WarningChargePersonDTO input,
                                                                UapUserPo uapUserPo,
                                                                TbCdcmrWarningChargePerson curr){
        TbCdcmrWarningChargePerson entity = new TbCdcmrWarningChargePerson();
        BeanUtils.copyProperties(input, entity);
        entity.setDealPersonInfo(JSONUtil.toJsonStr(input.getDealPersonInfo()));
        entity.setCreator(uapUserPo.getName());
        entity.setCreatorId(uapUserPo.getId());
        entity.setUpdater(uapUserPo.getName());
        entity.setUpdaterId(uapUserPo.getId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setId(curr != null ? input.getId() : String.valueOf(batchUidService.getUid(TbCdcmrWarningChargePerson.TABLE_NAME)));
        entity.setDeleteFlag(curr != null ? curr.getDeleteFlag() : DeleteFlagEnum.NO.getCode());
        entity.setStatus(StringUtils.isNotEmpty(input.getIsEnabled()) ? input.getIsEnabled() : "1");
        return entity;
    }

    @Override
    public List<WarningChargePersonVO> getDealPersonInfoBy(List<DealPersonQueryDTO> dtoList) {

        if(CollectionUtils.isEmpty(dtoList)){
            return new ArrayList<>();
        }
        //综合预警特殊处理
        if (Objects.equals(dtoList.get(0).getWarningType(), WarningTypeProEnum.INTEGRATED.getCode())) {

            WarningChargePersonVO personVO = tbCdcmrWarningChargePersonMapper.getIntegratedWarningDealPersonInfo(WarningTypeProEnum.INTEGRATED.getCode());
            return personVO == null ? new ArrayList<>() : Collections.singletonList(personVO);
        }
        return tbCdcmrWarningChargePersonMapper.getDealPersonInfoBy(dtoList);
    }

    @Override
    public WarningChargePersonVO loadByDiseaseCode(String diseaseCode, String warningType) {
        return tbCdcmrWarningChargePersonMapper.loadByDiseaseCode(diseaseCode, warningType);
    }

    @Override
    public SignalPushConfigurationVO loadSignPushConfigByDiseaseCode(String topicId) {
        return tbCdcmrSignalPushConfigurationMapper.loadSignPushConfigByDiseaseCode(topicId);
    }

    @Override
    public List<DictVO> getExpertTypeList() {
        return epiServiceProApi.getExpertTypeList();
    }

    @Override
    public List<EcdOrgUserVO> getExpertList(List<String> expertCategories) {
        return epiServiceProApi.getExpertList(expertCategories);
    }

    @Override
    public List<DutyUserVO> getDutyUserList(List<AreaDTO> areaDTOS) {
        return epiServiceProApi.getDutyUserList(areaDTOS);
    }

    @Override
    public PageData<EmergencyPlanVO> getEmergencyPlan(EmergencyPlanRequest request) {
        return PageData.of(epiServiceProApi.getEmergencyPlan(request), request.getPageIndex(), request.getPageSize());
    }


    @Override
    public Integer savePushRule(List<SignalPushRuleDTO> dtos) {
        if (dtos == null || dtos.isEmpty()) {
            return 0;
        }

        List<TbCdcmrSignalPushConfigMessageRelation> relationList = new ArrayList<>();

        for (SignalPushRuleDTO dto : dtos) {
            MessageConfig messageConfig = messageService.getConfigById(dto.getMessageConfigId());
            if (messageConfig == null) {
                continue;
            }
//            如果是转发任务流程的消息提醒，则要新增一条TbCdcmrSignalPushConfiguration
            if (StringUtils.isEmpty(dto.getId())){
                TbCdcmrSignalPushConfiguration config = new TbCdcmrSignalPushConfiguration();
                UapUserPo uapUserPo = USER_INFO.get() == null ? new UapUserPo() : USER_INFO.get();
                config.setId(String.valueOf(batchUidService.getUid(TbCdcmrSignalPushConfiguration.TABLE_NAME)));
                config.setIsRepeat(dto.getIsRepeat());
                config.setRepeatStartTime(dto.getRepeatStartTime());
                config.setRepeatEndTime(dto.getRepeatEndTime());
                config.setRepeatFrequency(dto.getRepeatFrequency());
                config.setMessageConfigId(dto.getMessageConfigId());

                config.setCreator(uapUserPo.getName());
                config.setCreatorId(uapUserPo.getId());
                config.setUpdater(uapUserPo.getName());
                config.setUpdaterId(uapUserPo.getId());
                Date now = new Date();
                config.setUpdateTime(now);
                config.setCreateTime(now);
                tbCdcmrSignalPushConfigurationMapper.insert(config);
                dto.setId(config.getId());
            }

            Message message;
//            if (Constants.COMMON_STRNUM_ONE.equals(dto.getIsRepeat())
//                    && dto.getRepeatStartTime() != null
//                    && dto.getRepeatEndTime() != null) {
//                Duration step = getFrequencyStep(dto.getRepeatFrequency());
//                LocalDateTime start = LocalDateTime.ofInstant(dto.getRepeatStartTime().toInstant(), ZoneId.systemDefault());
//                LocalDateTime end = LocalDateTime.ofInstant(dto.getRepeatEndTime().toInstant(), ZoneId.systemDefault());
//
//                List<MessageAddDTO> messageAddDTOList = new ArrayList<>();
//                for (LocalDateTime current = start; !current.isAfter(end); current = current.plus(step)) {
//                    messageAddDTOList.add(insertMessage(dto, messageConfig, current));
//                }
//                message = messageService.insertBatch(messageAddDTOList);
//            }
            message = messageService.insertSignalPushMessage(insertMessage(dto, messageConfig, LocalDateTime.now()));

            TbCdcmrSignalPushConfigMessageRelation relation = new TbCdcmrSignalPushConfigMessageRelation();
            relation.setId(String.valueOf(batchUidService.getUid(TbCdcmrSignalPushConfigMessageRelation.TABLE_NAME)));
            BeanUtils.copyProperties(message, relation);
            relation.setSignalPushConfigId(dto.getId());
            relation.setTaskId(dto.getTaskId());
            relation.setCreateTime(new Date());
            relation.setStatus(Constants.STATUS_ON);
            relationList.add(relation);
        }

        // 批量插入
        if (!relationList.isEmpty()) {
            return tbCdcmrSignalPushConfigMessageRelationMapper.insertBatch(relationList);
        }
        else {
            return 0;
        }
    }

    // 根据频次获取时间间隔
    private Duration getFrequencyStep(String frequency) {
        if (frequency == null) return Duration.ofHours(1); // 默认1小时
        switch (frequency) {
            case "15min":
                return Duration.ofMinutes(15);
            case "30min":
                return Duration.ofMinutes(30);
            default:
                return Duration.ofHours(1); // 默认1小时
        }
    }
    private MessageAddDTO insertMessage(SignalPushRuleDTO dto, MessageConfig messageConfig, LocalDateTime sendTime) {
        MessageAddDTO addDTO = new MessageAddDTO();
        addDTO.setAppCode(messageConfig.getAppCode());
        addDTO.setMessageType(messageConfig.getMessageType());
        addDTO.setMessageName(messageConfig.getMessageName());
        addDTO.setSourceSystemCode(messageConfig.getSourceSystemCode());
        addDTO.setSourceSystemName(messageConfig.getSourceSystemName());
        addDTO.setSystemRelativePath(messageConfig.getSystemRelativePath());

        addDTO.setSendTime(DateUtil.formatDateTime(Date.from(sendTime.atZone(ZoneId.systemDefault()).toInstant())));

        addDTO.setMessageContent(messageConfig.getMessageName() + " 信号id：" + dto.getTaskId());
        addDTO.setSenderId(dto.getSenderId());
        addDTO.setSender(dto.getSender());
        addDTO.setReceiverId(dto.getReceiverId());
        addDTO.setReceiver(dto.getReceiver());
        if (StringUtils.isNotEmpty(dto.getId())) {
            addDTO.setSignalPushConfigurationId(dto.getId());
        }
        JSONObject param = new JSONObject();
        param.put("id", dto.getTaskId());
        param.put("formWorkPlat", true);
        addDTO.setRequestParam(param.toJSONString());
        addDTO.setMessageConfigId(dto.getMessageConfigId());
        return addDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateAutoPushRule(List<SignalPushRuleSaveDTO> inputs) {
        if (CollectionUtils.isEmpty(inputs)) {
            return;
        }
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        List<TbCdcmrSignalPushConfiguration> insertList = new ArrayList<>();
        List<TbCdcmrSignalPushConfiguration> updateList = new ArrayList<>();

        for (SignalPushRuleSaveDTO dto : inputs) {
            TbCdcmrSignalPushConfiguration entity = new TbCdcmrSignalPushConfiguration();
            SignalPushRuleDTO input = new SignalPushRuleDTO();
            BeanUtils.copyProperties(dto, input);
            input.setRepeatStartTime(dto.getRepeatStartTime() != null ? DateUtils.concatTimestamp(DateUtil.today(), dto.getRepeatStartTime()) : null);
            input.setRepeatEndTime(dto.getRepeatEndTime() != null ? DateUtils.concatTimestamp(DateUtil.today(), dto.getRepeatEndTime()) : null);
            BeanUtils.copyProperties(input, entity);

            Date now = new Date();
            entity.setUpdateTime(now);
            entity.setUpdater(uapUserPo.getName());
            entity.setUpdaterId(uapUserPo.getId());
            entity.setMessageConfigId(input.getMessageConfigId());
            DealPersonQueryDTO dealPersonQueryDTO = new DealPersonQueryDTO();
            dealPersonQueryDTO.setDiseaseCode(input.getDiseaseCode());
            dealPersonQueryDTO.setRiskLevelDetailId(input.getRiskLevelDetailId());
//            读取多渠道综合预警责任人
            if (dto.getMessageConfigId().equals(MessageConfigEnum.INTEGRATED_SIGNAL_CHECK.getId())) {
                WarningChargePersonVO tbCdcmrWarningChargePerson = tbCdcmrWarningChargePersonMapper.getIntegratedWarningDealPersonInfo(WarningTypeProEnum.INTEGRATED.getCode());
                if (tbCdcmrWarningChargePerson != null) {
                    entity.setWarningChargePersonTableId(tbCdcmrWarningChargePerson.getId());
                }
            }
            else {
                TbCdcmrWarningChargePerson tbCdcmrWarningChargePerson = tbCdcmrWarningChargePersonMapper.getDealPersonInfoByDiseaseCodeAndRiskLevelDetailId(dealPersonQueryDTO);
                if (tbCdcmrWarningChargePerson != null) {
                    entity.setWarningChargePersonTableId(tbCdcmrWarningChargePerson.getId());
                }
            }
            if (StringUtils.isEmpty(input.getId())) {
                // 新增
                entity.setId(String.valueOf(batchUidService.getUid(TbCdcmrSignalPushConfiguration.TABLE_NAME)));
                entity.setCreateTime(now);
                entity.setCreator(uapUserPo.getName());
                entity.setCreatorId(uapUserPo.getId());
                insertList.add(entity);
            } else {
                // 更新
                TbCdcmrSignalPushConfiguration existing = tbCdcmrSignalPushConfigurationMapper.selectById(input.getId());
                updateList.add(entity);
                if (existing != null) {
                    // 逻辑删除旧消息
                    List<Message> messageList = messageMapper.listBySignalPushConfigurationId(input.getId());
                    if (CollectionUtils.isNotEmpty(messageList)) {
                        messageList.forEach(m -> {
                            m.setDeleteFlag(CommonConstants.DELETE_FLAG_1);
                            m.setUpdateTime(LocalDateTime.now());
                        });
                        messageMapper.updateBatchById(messageList);
                    }
                }
            }
        }

        // 批量保存和更新
        if (!insertList.isEmpty()) {
            tbCdcmrSignalPushConfigurationMapper.batchInsert(insertList);
        }
        if (!updateList.isEmpty()) {
            tbCdcmrSignalPushConfigurationMapper.batchUpdate(updateList);
        }
    }
    @Override
    public List<TbCdcmrSignalPushConfiguration> getSignalPushRule(List<SignalPushRuleDTO> inputs) {
        if(CollectionUtils.isEmpty(inputs)){
            return new ArrayList<>();
        }
        return tbCdcmrSignalPushConfigurationMapper.selectByDiseaceCodeAndRiskLevelDetailId(inputs);
    }
    /**
     * 扫描SignalPushConfiguration表，根据规则插入数据进message表
     * @return
     */
    @Scheduled(cron = "0 */15 * * * ?") // 每15分钟执行一次
    public void scanSignalPushConfiguration() {
        // 当前调度执行的开始时间（当前小时的15分钟倍数）
        LocalDateTime scheduleStart = LocalDateTime.now()
                .withMinute((LocalDateTime.now().getMinute() / 15) * 15)
                .withSecond(0)
                .withNano(0);
        // 当前调度窗口的结束时间（15分钟后）
        LocalDateTime scheduleEnd = scheduleStart.plusMinutes(15);

        List<TbCdcmrSignalPushConfiguration> list = tbCdcmrSignalPushConfigurationMapper.selectAll();
        for (TbCdcmrSignalPushConfiguration config : list) {
            List<TbCdcmrSignalPushConfigMessageRelation> relationList = tbCdcmrSignalPushConfigMessageRelationMapper.listBySignalPushConfigurationId(config.getId());

            if (StringUtils.isNotEmpty(config.getIsRepeat())
                    && Constants.COMMON_STRNUM_ONE.equals(config.getIsRepeat())
                    && config.getRepeatStartTime() != null
                    && config.getRepeatEndTime() != null) {

                // 只取时间部分
                LocalTime repeatStartTime = config.getRepeatStartTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalTime();
                LocalTime repeatEndTime = config.getRepeatEndTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalTime();

                // 拼接今天的日期
                LocalDate today = LocalDate.now();
                LocalDateTime repeatStart = LocalDateTime.of(today, repeatStartTime);
                LocalDateTime repeatEnd = LocalDateTime.of(today, repeatEndTime);

                // 获取频率间隔
                Duration step = getFrequencyStep(config.getRepeatFrequency());

                for (TbCdcmrSignalPushConfigMessageRelation relation : relationList) {
                    if (relation.getStatus() == Constants.STATUS_ON) {
                        SignalPushRuleDTO dto = new SignalPushRuleDTO();
                        dto.setReceiverId(relation.getReceiverId());
                        dto.setReceiver(relation.getReceiver());
                        dto.setSender(relation.getSender());
                        dto.setSenderId(relation.getSenderId());
                        dto.setTaskId(relation.getTaskId());
                        dto.setId(config.getId());
                        dto.setMessageConfigId(relation.getMessageConfigId());
                        MessageConfig messageConfig = messageService.getConfigById(relation.getMessageConfigId());
                        if(messageConfig == null){
                            continue;
                        }
                        List<MessageAddDTO> messageAddDTOList = new ArrayList<>();

                        // 遍历当日 repeatStart ~ repeatEnd，按 step 判断是否落在当前 schedule 窗口
                        for (LocalDateTime current = repeatStart; !current.isAfter(repeatEnd); current = current.plus(step)) {
                            if (!current.isBefore(scheduleStart) && current.isBefore(scheduleEnd)) {
                                messageAddDTOList.add(insertMessage(dto, messageConfig, current));
                            }
                        }

                        if (!messageAddDTOList.isEmpty()) {
                            messageService.insertBatch(messageAddDTOList);
                        }
                    }
                }
            }
        }
    }
    @Override
    public Integer stopSignalPushRuleMessage(List<SignalPushRuleDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return 0;
        }
//        将TbCdcmrSignalPushConfigMessageRelation中receiverid与dto的senderid相同、taskid和messageConfigId相同的记录status置0
        tbCdcmrSignalPushConfigMessageRelationMapper.updateStatusOffByReceiverAndTaskIdAndConfig(dtos);
        //     将消息表中同个receiverid、taskid、和messageconfigid的消息deleteflag置1
        List<Message> messageList = messageMapper.listByReceiverAndTaskAndMessageConfig(dtos);
        if (CollectionUtils.isNotEmpty(messageList)) {
            messageList.forEach(m -> {
                m.setDeleteFlag(CommonConstants.DELETE_FLAG_1);
                m.setUpdateTime(LocalDateTime.now());
            });
            messageMapper.updateBatchById(messageList);
        }
        return 0;
    }
}
