package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.dto.WarningRiskLevelDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 传染病预警-风险等级配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
public interface WarningRiskLevelService extends IService<TbCdcmrWarningRiskLevel> {

    List<TbCdcmrWarningRiskLevel> listRiskLevelByWarningType();

}
