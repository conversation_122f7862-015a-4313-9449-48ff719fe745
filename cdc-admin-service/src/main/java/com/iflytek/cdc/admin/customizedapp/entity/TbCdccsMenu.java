package com.iflytek.cdc.admin.customizedapp.entity;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 菜单;
 * <AUTHOR> fengwang35
 * @date : 2024-9-10
 */
@ApiModel(value = "菜单")
@TableName("tb_cdccs_menu")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsMenu extends BaseEntity implements Serializable{
    public static final String TABLE_NAME = "tb_cdccs_menu";

    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id ;
    /** 应用id */
    @ApiModelProperty(value = "应用id")
    private String appId ;
    /** 应用名称 */
    @ApiModelProperty(value = "应用名称")
    private String appName ;
    /** 分组id */
    @ApiModelProperty(value = "分组id")
    private String groupId ;
    /** 菜单名称 */
    @ApiModelProperty(value = "菜单名称")
    private String menuName ;
    /** 菜单编码 */
    @ApiModelProperty(value = "菜单编码")
    private String menuCode ;
    /** 页面编码 */
    @ApiModelProperty(value = "页面编码")
    private String pageCode ;
    /** 页面名称 */
    @ApiModelProperty(value = "页面名称")
    private String pageName ;

    @ApiModelProperty(value = "页面类型，自定义或内置")
    private String pageType;
    /** 父菜单id */
    @ApiModelProperty(value = "父菜单id")
    private String parentId ;
    /** 父菜单名称 */
    @ApiModelProperty(value = "父菜单名称")
    private String parentName ;

    @ApiModelProperty(value = "权限地址")
    private String url;

    @ApiModelProperty(value = "地址类型")
    private Integer urlType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "类型")
    private Integer type;


}