package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.EndemicWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseInfo;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicDiseaseInfoMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicDiseaseWarningRuleMapper;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.model.mr.vo.TreeNodeProperty;

import com.iflytek.cdc.admin.vo.EndemicWarningRuleDetailVO;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseWarningRule;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrEndemicDiseaseWarningRuleMapper;
import com.iflytek.cdc.admin.service.province.EndemicDiseaseWarningRuleService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class EndemicDiseaseWarningRuleServiceImpl extends ServiceImpl<TbCdcmrEndemicDiseaseWarningRuleMapper, TbCdcmrEndemicDiseaseWarningRule> implements EndemicDiseaseWarningRuleService {
    
    private static final String TB_CDCMR_ENDEMIC_DISEASE_WARNING_RULE = "tb_cdcmr_endemic_disease_warning_rule";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrEndemicDiseaseWarningRuleMapper endemicDiseaseWarningRuleMapper;

    @Resource
    private TbCdcmrEndemicDiseaseInfoMapper endemicDiseaseInfoMapper;


    @Override
    public PageInfo<TbCdcmrEndemicDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo<>(endemicDiseaseWarningRuleMapper.getListByEndemicDiseaseWarningId(diseaseInfoId, riskLevel, warningMethod));
    }

    @Override
    public List<TreeNode> getEndemicTree(EndemicWaringRuleQueryDTO dto) {

        //查询新发突发传染病疾病列表
        List<TbCdcmrEndemicDiseaseInfo> diseaseInfoList = endemicDiseaseInfoMapper.listAll();

        //疾病规则数量
        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> ruleCount = this.getDiseaseRuleCount();

        //构建新发突发传染病树
        List<TreeNode> rootNode = TreeNode.buildTreeByNodeList(diseaseInfoList,
                TbCdcmrEndemicDiseaseInfo::getId,
                TbCdcmrEndemicDiseaseInfo::getDiseaseParentId,
                TbCdcmrEndemicDiseaseInfo::getDiseaseName,
                TbCdcmrEndemicDiseaseInfo::getDiseaseCode);

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);
        //将规则数量添加到对应的节点
        TreeNodeProperty.mergeTreeProperty(rootNode, ruleCount);
        //按照id排序
        rootNode = rootNode.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());

        if (StringUtils.isNotBlank(dto.getDiseaseInfoId())){
            return Collections.singletonList(TreeNode.getMatchNodeById(rootNode, t -> t.getId().equals(dto.getDiseaseInfoId())));
        }
        return rootNode;
    }

    private Map<String, List<TreeNodeProperty.RiskRuleCountVO>> getDiseaseRuleCount(){

        Map<String, List<TreeNodeProperty.RiskRuleCountVO>> res = new HashMap<>();
        //查询传染病 每个风险等级下 规则的数量
        List<EndemicWarningRuleDetailVO> riskRuleCountList = endemicDiseaseWarningRuleMapper.getRiskRuleCountBy();
        //按照疾病进行分组
        Map<String, List<EndemicWarningRuleDetailVO>> listMap = riskRuleCountList.stream()
                .collect(Collectors.groupingBy(EndemicWarningRuleDetailVO::getId));
        listMap.forEach((k,v) ->{
            List<TreeNodeProperty.RiskRuleCountVO> riskLevelList = new ArrayList<>();
            for (EndemicWarningRuleDetailVO detailVO : v) {
                TreeNodeProperty.RiskRuleCountVO riskLevelVO = new TreeNodeProperty.RiskRuleCountVO();
                if(StrUtil.isNotBlank(detailVO.getRiskLevel())){
                    riskLevelVO.setRiskLevelCount(detailVO.getRiskLevelCount());
                    riskLevelVO.setRiskLevel(detailVO.getRiskLevel());
                    riskLevelList.add(riskLevelVO);
                }
            }
            res.put(k, riskLevelList);
        });
        return res;
    }
    
    @Override
    public void savaOrUpdateRule(TbCdcmrEndemicDiseaseWarningRule rule) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        if (StringUtils.isBlank(rule.getId())) {
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_ENDEMIC_DISEASE_WARNING_RULE));
            rule.setId(id);
            rule.setCreatorId(uapUserPo.getId());
            rule.setCreateTime(new Date());
            rule.setCreator(uapUserPo.getName());
            rule.setDeleteFlag(DeleteFlagEnum.NO.getCode());
            rule.setStatus(Integer.valueOf(StatusEnum.STATUS_ON.getCode()));
        }
        rule.setUpdaterId(uapUserPo.getId());
        rule.setUpdateTime(new Date());
        rule.setUpdater(uapUserPo.getName());
        baseMapper.upsert(rule);
    }
}
