package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseWarning;
import com.iflytek.cdc.admin.enums.WarningTypeEnum;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.model.mr.vo.InfectedDiseaseWarningVO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrInfectedDiseaseMonitor;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMasterDateLogs;
import com.iflytek.cdc.admin.enums.InfectedMonitorTypeEnum;
import com.iflytek.cdc.admin.mapper.province.*;
import com.iflytek.cdc.admin.model.mr.dto.*;
import com.iflytek.cdc.admin.model.mr.vo.InfectedMasterDataConfigInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.IMasterDataService;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.util.CommonUtils;
import com.iflytek.cdc.admin.util.ExcelUtils;
import com.iflytek.cdc.admin.util.RedisHelperUtil;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service("infectedMasterDataCategory")
public class InfectedMasterDataServiceImpl implements IMasterDataService {

    public static final String TB_CDCMR_INFECTED_DISEASE_INFO = "tb_cdcmr_infected_disease_info";

    public static final String TB_CDCMR_DISEASE_WARNING = "tb_cdcmr_disease_warning";

    public static final String TB_CDCMR_INFECTED_DISEASE_MONITOR = "tb_cdcmr_infected_disease_monitor";

    public static final String TB_CDCMR_MASTER_DATE_LOGS = "tb_cdcmr_master_date_logs";

    public static final String ADMIN = "admin";

    @Value("${redisKey:infectedMasterDataId}")
    private String redisKey;

    @Resource
    private TbCdcmrInfectedDiseaseInfoMapper diseaseInfoMapper;

    @Resource
    private TbCdcmrInfectedDiseaseMonitorMapper diseaseMonitorMapper;

    @Resource
    private TbCdcmrMasterDateLogsMapper tbCdcmrMasterDateLogsMapper;

    @Resource
    private TbCdcmrDiseaseWarningMapper diseaseWarningMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CommonUtils commonUtils;

    @Resource
    private RedisTemplate<String, Long> redisTemplate;

    /**
     * 查询主数据的树形结构
     * */
    @Override
    public List<TreeNode> getMasterData(MasterDataQueryDTO dto) {

        List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = diseaseInfoMapper.getAllInfectedInfoBy(null, dto.getMasterDataTypeCode());

        //构建树形结构
        List<TreeNode> root = TreeNode.buildSortTreeByNodeList(diseaseInfoList,
                                                               TbCdcmrInfectedDiseaseInfo::getId,
                                                               TbCdcmrInfectedDiseaseInfo::getParentDiseaseId,
                                                               TbCdcmrInfectedDiseaseInfo::getDiseaseName,
                                                               TbCdcmrInfectedDiseaseInfo::getDiseaseCode,
                                                               TbCdcmrInfectedDiseaseInfo::getOrderFlag);
        //将所有叶子节点的children设为null
        TreeNode.setChildrenToNull(root);
        //排序
        TreeNode.sortTreeBy(root, TreeNode::getOrderFlag);

        return root;
    }

    @Override
    public TreeNode getMasterDataByRootId(String rootId) {
        List<TreeNode> masterData = getMasterData(new MasterDataQueryDTO());
        return TreeNode.getMatchNodeById(masterData, t -> t.getId().equals(rootId));
    }

    /**
     * 查询主数据信息以及其配置信息
     * */
    @Override
    public <T extends CommonMasterData> T getMasterDataConfigInfo(MasterDataQueryDTO dto) {

        String infectedDiseaseInfoId = dto.getMasterDataId();
        //传染病基础信息
        InfectedMasterDataConfigInfoVO configInfoVO = diseaseInfoMapper.getInfectedBaseInfoById(infectedDiseaseInfoId);
        if(configInfoVO == null){
            return null;
        }
        //传染病配置信息
        List<TbCdcmrInfectedDiseaseMonitor> diseaseMonitorList = diseaseMonitorMapper.getConfigByDiseaseId(infectedDiseaseInfoId);
        //诊断状态筛选
        List<TbCdcmrInfectedDiseaseMonitor> diagnoseStatusList = diseaseMonitorList.stream()
                                                                                   .filter(e -> Objects.equals(e.getType(), InfectedMonitorTypeEnum.DIAGNOSE_STATUS.getCode()))
                                                                                   .collect(Collectors.toList());
        //疫情暴发分级筛选
        List<TbCdcmrInfectedDiseaseMonitor> epidemicLevelList = diseaseMonitorList.stream()
                                                                                  .filter(e -> Objects.equals(e.getType(), InfectedMonitorTypeEnum.EPIDEMIC_LEVEL.getCode()))
                                                                                  .collect(Collectors.toList());
        configInfoVO.setDiagnoseStatusList(diagnoseStatusList);
        configInfoVO.setEpidemicLevelList(epidemicLevelList);

        return (T) configInfoVO;
    }

    /**
     * 新增主数据
     * */
    @Override
    @Transactional
    public String addMasterData(MasterDataRecordDTO dto) {

        List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = new ArrayList<>();
        //用户信息拦截
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        List<CommonMasterData> dataRecordList = dto.getRecordList();
        if(CollectionUtil.isNotEmpty(dataRecordList)){
            dataRecordList.forEach(e -> {
                TbCdcmrInfectedDiseaseInfo disease = this.buildInfectedDiseaseInfo(e, uapUserPo);
                diseaseInfoList.add(disease);
            });
        }
        if(CollectionUtil.isNotEmpty(diseaseInfoList)) {
            diseaseInfoMapper.insertBatch(diseaseInfoList);
            //新增单个数据
            this.syncMasterData(diseaseInfoList.get(0));
            return diseaseInfoList.get(0).getId();
        }
        return null;
    }

    /**
     * 传染病基础数据构建
     * */
    private TbCdcmrInfectedDiseaseInfo buildInfectedDiseaseInfo(CommonMasterData record, UapUserPo uapUserPo){

        //根据传染病维表初始化key值
        long key = this.initKey(redisKey);
        //利用redis缓存实现自增
        String id = String.format("%05d", key);
        TbCdcmrInfectedDiseaseInfo diseaseInfo = new TbCdcmrInfectedDiseaseInfo();
        diseaseInfo.setId(id);
        diseaseInfo.setLevelType(record.getLevelType());
        diseaseInfo.setDiseaseCode(record.getMasterDataCode());
        diseaseInfo.setDiseaseName(record.getMasterDataName());
        diseaseInfo.setParentDiseaseId(record.getParentDataId());
        diseaseInfo.setParentDiseaseCode(record.getParentDataCode());
        diseaseInfo.setParentDiseaseName(record.getParentDataName());
        diseaseInfo.setAlias(Optional.ofNullable(record.getMasterDataAlias()).orElse(record.getMasterDataName()));
        diseaseInfo.setCreateTime(new Date());
        diseaseInfo.setUpdateTime(new Date());
        diseaseInfo.setStatus(Constants.STATUS_ON);
        diseaseInfo.setDeleteFlag(Constants.STR_ZERO);
        diseaseInfo.setCreatorId(uapUserPo.getId());
        diseaseInfo.setCreator(uapUserPo.getName());
        diseaseInfo.setUpdaterId(uapUserPo.getId());
        diseaseInfo.setUpdater(uapUserPo.getName());

        return diseaseInfo;
    }

    /**
     * 查询传染病主数据表 初始化redis的key
     */
    public Long initKey(String key){

        int max = Optional.ofNullable(diseaseInfoMapper.getMaxId()).orElse(0);
        return this.initRedisKey(key, max);
    }

    /**
     * 初始化redis键值
     */
    public Long initRedisKey(String key, long value) {
        Object currValueObj = redisTemplate.opsForValue().get(key);
        Long currValue = null;

        // 统一转换为Long类型
        if (currValueObj != null) {
            currValue = NumberUtils.toLong(currValueObj.toString());
        }

        if (currValue == null || currValue <= value) {
            long nextValue = value + 1;
            redisTemplate.opsForValue().set(key, nextValue);
            return nextValue;
        }

        // 4. 否则直接 INCR
        return redisTemplate.opsForValue().increment(key, 1);
    }

    /**
     * 编辑疾病信息
     * */
    @Override
    @Transactional
    public void editMasterDataBaseInfo(MasterDataEditRecordDTO dto) {

        UapUserPo uapUserPo = USER_INFO.get();
        //先记录传染病编辑日志
        Gson gson = new Gson();
        String newData = gson.toJson(dto);
        this.saveModifyLogs(new MasterDataQueryDTO(dto.getMasterDataType(), dto.getId()), newData, uapUserPo);

        TbCdcmrInfectedDiseaseInfo diseaseInfo = new TbCdcmrInfectedDiseaseInfo();
        BeanUtils.copyProperties(dto, diseaseInfo);
        //基础信息修改
        diseaseInfo.setLevelType(dto.getLevelType());
        diseaseInfo.setDiseaseCode(dto.getMasterDataCode());
        diseaseInfo.setDiseaseName(dto.getMasterDataName());
        diseaseInfo.setAlias(dto.getMasterDataAlias());
        diseaseInfo.setParentDiseaseId(dto.getParentDataId());
        diseaseInfo.setParentDiseaseCode(dto.getParentDataCode());
        diseaseInfo.setParentDiseaseName(dto.getParentDataName());
        diseaseInfo.setUpdateTime(new Date());
        if(uapUserPo != null){
            diseaseInfo.setUpdaterId(uapUserPo.getId());
            diseaseInfo.setUpdater(uapUserPo.getName());
        }
        //更新传染病基础信息
        diseaseInfoMapper.update(diseaseInfo);
        //当配置列表非空时，更新传染病配置信息
        if(CollectionUtil.isNotEmpty(dto.getInfectedConfigList())) {
            //根据主数据id删除其配置，做软删除
            diseaseMonitorMapper.updateByDiseaseId(diseaseInfo.getId());
            List<TbCdcmrInfectedDiseaseMonitor> insertDataList = new ArrayList<>();
            List<TbCdcmrInfectedDiseaseMonitor> diseaseMonitorList = dto.getInfectedConfigList();
            diseaseMonitorList.forEach(e -> {
                TbCdcmrInfectedDiseaseMonitor record = this.buildInfectedDiseaseMonitor(e, uapUserPo);
                insertDataList.add(record);
            });
            if(CollectionUtil.isNotEmpty(insertDataList)) {
                diseaseMonitorMapper.insertBatch(insertDataList);
            }
        }
        //传染病主数据更新后，直接同步到disease_warning表
        this.syncMasterData(diseaseInfo);
    }

    /**
     * 传染病配置数据构建
     * */
    private TbCdcmrInfectedDiseaseMonitor buildInfectedDiseaseMonitor(TbCdcmrInfectedDiseaseMonitor diseaseMonitor, UapUserPo uapUserPo){

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_INFECTED_DISEASE_MONITOR));
        TbCdcmrInfectedDiseaseMonitor record = new TbCdcmrInfectedDiseaseMonitor();
        BeanUtils.copyProperties(diseaseMonitor, record);
        record.setId(id);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setStatus(Constants.STATUS_ON);
        record.setDeleteFlag(Constants.STR_ZERO);
        if(uapUserPo != null){
            record.setCreatorId(uapUserPo.getId());
            record.setCreator(uapUserPo.getName());
            record.setUpdaterId(uapUserPo.getId());
            record.setUpdater(uapUserPo.getName());
        }
        return record;
    }

    /**
     * 传染病主数据修改日志记录
     * */
    private void saveModifyLogs(MasterDataQueryDTO dto, String newData, UapUserPo uapUserPo){

        Gson gson = new Gson();
        //原始数据结构化
        InfectedMasterDataConfigInfoVO infoVO = this.getMasterDataConfigInfo(dto);
        String originData = gson.toJson(infoVO);
        TbCdcmrMasterDateLogs log = new TbCdcmrMasterDateLogs();

        String id = String.valueOf(batchUidService.getUid(TB_CDCMR_MASTER_DATE_LOGS));
        log.setId(id);
        log.setType(dto.getMasterDataType());
        log.setOriginData(originData);
        log.setNewData(newData);
        log.setCreateTime(new Date());
        if(uapUserPo != null) {
            log.setCreatorId(uapUserPo.getId());
            log.setCreator(uapUserPo.getName());
        }
        tbCdcmrMasterDateLogsMapper.insert(log);
    }

    /**
     * 删除疾病信息
     * */
    @Override
    @Transactional
    public void deleteMasterDataBaseInfo(MasterDataDeleteDTO dto) {

        //判断删除级别
        if(dto.getIsDeleteCurr()) {
            //如果只删除当前层级 : 先判断是否有下级(有-软删该条数据，所有下级向上平移；无-软删该条数据)
            List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = diseaseInfoMapper.getSubInfectedDiseaseInfoByIds(Collections.singletonList(dto.getId()));
            //如果当前删除的疾病有子类 则当前疾病子类的父类id、code、name全部设置为当前节点的 父类id、code、name
            if(CollectionUtil.isNotEmpty(diseaseInfoList)) {
                TbCdcmrInfectedDiseaseInfo info = diseaseInfoMapper.getDiseaseInfoById(dto.getId());
                diseaseInfoMapper.updateSubDiseaseParent(info);
            }
            //软删除当前疾病
            diseaseInfoMapper.updateDeleteFlagByIds(Collections.singletonList(dto.getId()));

        } else {
            //如果删除当前层级以及其所有子类
            //查询疾病树状结构
            List<TreeNode> root = this.getMasterData(new MasterDataQueryDTO());
            //根据当前节点id 得到当前节点以及所有子类id，全部做软删除
            List<String> idList = new ArrayList<>();
            for (TreeNode treeNode : root) {
                TreeNode.getAllNodeValue(idList, treeNode, dto.getId());
            }
            if(CollectionUtil.isNotEmpty(idList)) {
                diseaseInfoMapper.updateDeleteFlagByIds(idList);
            }
        }
        //删除之后同步到disease_warning表
        this.syncMasterData(null);
    }

    /**
     * 定时执行，每10分钟执行一次
     * */
    @Override
    public void syncMasterData() {
        //重载传染病主数据同步方法
        diseaseWarningMapper.deleteAll(WarningTypeProEnum.INFECTED.getCode());
        this.syncMasterData(null);
    }

    /**
     * 将传染病主数据同步到disease_warning表
     * */
    @Transactional
    public void syncMasterData(TbCdcmrInfectedDiseaseInfo diseaseInfo) {

        List<TbCdcmrDiseaseWarning> res = new ArrayList<>();
        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());

        //传参为空则查询主数据表中所有传染病 否则更新指定传染病的信息
        List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = diseaseInfo == null ? diseaseInfoMapper.listAll() : Collections.singletonList(diseaseInfo);

        diseaseInfoList.forEach(e -> {
            TbCdcmrDiseaseWarning diseaseWarning = new TbCdcmrDiseaseWarning();
            String id = String.valueOf(batchUidService.getUid(TB_CDCMR_DISEASE_WARNING));
            diseaseWarning.setId(id);
            diseaseWarning.setDiseaseCode(e.getId());
            diseaseWarning.setDiseaseName(e.getDiseaseName());
            diseaseWarning.setDiseaseInfoId(e.getId());
            diseaseWarning.setWarningType(WarningTypeProEnum.INFECTED.getCode());
            diseaseWarning.setUpdateTime(new Date());
            diseaseWarning.setUpdaterId(uapUserPo.getId());
            diseaseWarning.setUpdater(uapUserPo.getName());
            diseaseWarning.setDeleteFlag(e.getDeleteFlag());
            diseaseWarning.setStatus(e.getStatus());

            res.add(diseaseWarning);
        });

        //批量插入或者更新disease_warning表
        if (CollectionUtils.isNotEmpty(res)) {
            diseaseWarningMapper.insertOrUpdateBatch(res);
        }
    }

    @Override
    @Transactional
    public void uploadFile(MultipartFile file) {

        //清空传染病主数据维表
        diseaseInfoMapper.deleteAll();
        //解析表格
        String fileName = file.getOriginalFilename();
        try (InputStream in = file.getInputStream()) {

            List<UploadInfectedDiseaseInfo> uploadInfoList = ExcelUtils.readExcel(UploadInfectedDiseaseInfo.class, fileName, in);
            List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = this.dealUploadDiseaseInfo(uploadInfoList);
            diseaseInfoMapper.insertBatch(diseaseInfoList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理上传文件疾病的code问题
     * */
    private List<TbCdcmrInfectedDiseaseInfo> dealUploadDiseaseInfo(List<UploadInfectedDiseaseInfo> uploadInfoList) {

        List<TbCdcmrInfectedDiseaseInfo> diseaseInfoList = new ArrayList<>();
        uploadInfoList.forEach(e -> {
            //处理疾病code小数点问题
            e.setDiseaseCode(e.getDiseaseCode() == null ? null : String.format("%.0f", Double.parseDouble(e.getDiseaseCode())));
            e.setParentDiseaseCode(e.getParentDiseaseCode() == null ? null : String.format("%.0f", Double.parseDouble(e.getParentDiseaseCode())));
            TbCdcmrInfectedDiseaseInfo info = new TbCdcmrInfectedDiseaseInfo();
            BeanUtils.copyProperties(e, info);
            //利用redis缓存实现自增
            long key = RedisHelperUtil.getIncrement(redisTemplate, "infectedMasterDataId");
            String id = String.format("%05d", key);
            info.setId(id);
            info.setStatus(Constants.STATUS_ON);
            info.setDeleteFlag(Constants.STR_ZERO);
            info.setCreator(ADMIN);
            info.setUpdater(ADMIN);
            info.setCreateTime(new Date());
            info.setUpdateTime(new Date());
            diseaseInfoList.add(info);
        });
        //疾病name和code映射关系
        Map<String, TbCdcmrInfectedDiseaseInfo> diseaseNameToCodeMap = diseaseInfoList.stream()
                                                                                      .collect(Collectors.toMap(TbCdcmrInfectedDiseaseInfo::getDiseaseName,
                                                                                                                Function.identity(),
                                                                                                                (e1, e2) -> e2));
        //处理父类疾病name非空，但是父类code为空的疾病
        diseaseInfoList.forEach(e -> {
            if(StringUtils.isNotBlank(e.getParentDiseaseName())) {
                TbCdcmrInfectedDiseaseInfo info = diseaseNameToCodeMap.get(e.getParentDiseaseName());
                e.setParentDiseaseId(info == null ? null : info.getId());
            }
        });
        return diseaseInfoList;
    }

    @Override
    public CommonMasterData getDiseaseInfoByCode(String masterDataCode) {

        return diseaseInfoMapper.getDiseaseInfoByCode(masterDataCode);
    }

}
