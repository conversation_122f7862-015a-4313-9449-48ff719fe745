package com.iflytek.cdc.admin.controller.province;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.service.ExportPermissionConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "导出审核配置")
public class ExportConfigController {

    @Resource
    ExportPermissionConfigService exportPermissionConfigService;

    @PostMapping("/pt/{version}/exportConfig/query")
    @ApiOperation("查询审核配置")
    public ResponseEntity<PageInfo<ExportConfigDTO>> queryExportConfigs(@RequestBody ExportConfigQueryDTO queryDTO,@RequestParam String loginUserId) {
        PageInfo<ExportConfigDTO> configs = exportPermissionConfigService.queryExportConfigs(queryDTO,loginUserId);
        return ResponseEntity.ok().body(configs);
    }

    @PutMapping("/pt/{version}/exportConfig/updateApprovalRequired/{id}")
    @ApiOperation("更新审核权限")
    public ResponseEntity<Boolean> updateApprovalRequired(
            @RequestParam String loginUserId,
            @PathVariable String id,
            @RequestBody UpdateApprovalRequiredDTO updateDTO) {
        boolean isUpdated = exportPermissionConfigService.updateApprovalRequired(loginUserId,id, updateDTO.isApprovalRequired());
        return ResponseEntity.ok().body(isUpdated);
    }

    @GetMapping("/pt/{version}/exportConfig/exportPermission/{idOrCode}")
    @ApiOperation("查询导出权限配置")
    public ResponseEntity<ExportPermissionConfigDTO> getExportPermissionConfig(@PathVariable String idOrCode) {
        ExportPermissionConfigDTO config = exportPermissionConfigService.getExportPermissionConfig(idOrCode);
        return ResponseEntity.ok().body(config);
    }

    @GetMapping("/pt/{version}/exportConfig/approvalPermission/{id}")
    @ApiOperation("查询审核权限配置")
    public ResponseEntity<ApprovalPermissionConfigDTO> getApprovalPermissionConfig(
            @PathVariable String id) {
        ApprovalPermissionConfigDTO config = exportPermissionConfigService.getApprovalPermissionConfig(id);
        return ResponseEntity.ok().body(config);
    }

    @PutMapping("/pt/{version}/exportConfig/exportPermission/{id}")
    @ApiOperation("更新导出权限配置")
    public ResponseEntity<Boolean> updateExportPermissionConfig(
            @RequestParam String loginUserId,
            @PathVariable String id,
            @RequestBody ExportPermissionConfigDTO exportPermissionConfig) {
        boolean isUpdated = exportPermissionConfigService.updateExportPermissionConfig(id, exportPermissionConfig, loginUserId);
        return ResponseEntity.ok().body(isUpdated);
    }

    @PutMapping("/pt/{version}/exportConfig/approvalPermission/{id}")
    @ApiOperation("更新审核权限配置")
    public ResponseEntity<Boolean> updateApprovalPermissionConfig(
            @RequestParam String loginUserId,
            @PathVariable String id,
            @RequestBody ApprovalPermissionConfigDTO approvalPermissionConfig) {
        boolean isUpdated = exportPermissionConfigService.updateApprovalPermissionConfig(id, approvalPermissionConfig, loginUserId);
        return ResponseEntity.ok().body(isUpdated);
    }

    @DeleteMapping("/pt/{version}/exportConfig/{id}")
    @ApiOperation("删除导出权限配置")
    public ResponseEntity<Boolean> deleteExportPermissionConfig(
            @PathVariable String id) {
        boolean isDeleted = exportPermissionConfigService.deleteExportPermissionConfig(id);
        return ResponseEntity.ok().body(isDeleted);
    }


}
