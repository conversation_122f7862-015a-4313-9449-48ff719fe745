package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.ClientUpgradeParamDto;
import com.iflytek.cdc.admin.dto.ClientUpgradeUrlDto;
import com.iflytek.cdc.admin.dto.PageData;


/**
 * @ClassName ClientUpgradeUrlService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/30 14:27
 * @Version 1.0
 */
public interface ClientUpgradeUrlService {

    /**
     * 分页查询机构升级服务地址
     * @param clientUpgradeParamDto
     * @param loginUserId
     * @return
     */
    PageData<ClientUpgradeUrlDto> queryClientUpgradeUrlDto(ClientUpgradeParamDto clientUpgradeParamDto,String loginUserId);

    /**
     * 更新机构升级服务地址
     * @param clientUpgradeUrlDto
     * @return
     */
    int updateClientUpgradeUrlDto(ClientUpgradeUrlDto clientUpgradeUrlDto,String loginUserId);

    /**
     * 新增机构升级服务地址
     * @param clientUpgradeUrlDto
     * @return
     */
    int addClientUpgradeUrlDto(ClientUpgradeUrlDto clientUpgradeUrlDto,String loginUserId);
}
