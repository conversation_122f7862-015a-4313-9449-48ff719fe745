package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.entity.UserManual;
import com.iflytek.cdc.admin.mapper.UserManualMapper;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.UserManualService;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.util.StorageClientUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.util.StringUtils;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;

@Slf4j
@Service("userManualService")
public class UserManualServiceImpl extends AbstractSyncMdmDataService implements UserManualService {

    @Autowired
    private UserManualMapper userManualMapper;

    @Autowired
    private StorageClientUtil storageClientUtil;

    @Value(value = "${user.value.prefix}")
    private String userValuePrefix;

    public UserManualServiceImpl(UapUserApi uapUserApi, MdmDataSyncService mdmDataSyncService, BatchUidService batchUidService) {
        super(uapUserApi, mdmDataSyncService, batchUidService);
    }

    @Override
    @Transactional
    public ResponseResult userManualUpload(MultipartFile file,String versionCode) {
        if (file == null || StringUtils.isBlank(versionCode)){
            return new ResponseResult("参数异常，请重新上传");
        }
        //判断文件后缀是否满足上传条件 -- 避免直接调用后端接口出现能上传任意文件类型
        if(!FileUtils.isSatisfyRule(file, Constants.UPLOAD_PDF_FILE_TYPE)){
            log.error("文件格式不正确, 请上传pdf文件");
            throw new MedicalBusinessException("文件格式不正确, 请上传pdf文件");
        }
        String originalFileName = null;
        String fileType = null;
        String path = null;
        try {
            // 获取上传的文件信息
            InputStream inputStream = file.getInputStream();
            originalFileName = file.getOriginalFilename();

            String fileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));

            // 上传文件
            HashMap<String, String> map = new HashMap<>();
            map.put("Content-Type",file.getContentType());
            path = storageClientUtil.putObject(inputStream, originalFileName, map);
        } catch (Exception e) {
            log.error("上传文件失败",e);
        }
        if (null == path){
            return new ResponseResult(Constants.ERROR_CODE,"用户手册上传失败");
        }
        log.info("用户手册文件上传成功  文件名为" + originalFileName);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("version_code", versionCode);
        queryWrapper.eq("delete_flag", "0");
        // 判断是新建还是修改
        Integer count = userManualMapper.selectCount(queryWrapper);

        if (count > 0) {
            // 修改: 将db中的对应版本号的记录的delete_flag状态改为删除状态 1
            userManualMapper.updateDeteleFlag(versionCode);
        }
        // 用户手册信息保存到db
        UserManual userManual = UserManual.builder()
                .id(String.valueOf(batchUidService.getUid(TableName.USER_MANUAL)))
                .versionCode(versionCode)
                .fileName(originalFileName)
                .fileType(fileType)
                .fileUrl(path)
                .createDatetime(new Date())
                .updateDatetime(new Date())
                .deleteFlag("0")
                .build();
        userManualMapper.insert(userManual);
        log.info("新增用户手册记录成功,版本号为"+versionCode);
        return new ResponseResult(Constants.SUCCESS_CODE,"用户手册上传成功");
    }

    @Override
    public ResponseResult userManualQuery(String versionCode) {
        // 构造查询条件
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("version_code", versionCode);
        wrapper.eq("delete_flag", "0");
        wrapper.orderByDesc("create_datetime");
        wrapper.last("limit 1");
        // 查询
        UserManual userManual = userManualMapper.selectOne(wrapper);
        if (null != userManual){
            userManual.setFileUrl(userValuePrefix + userManual.getFileUrl());
        }
        return new ResponseResult<UserManual>(userManual);
    }
}
