package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.enums.MasterDataEnum;
import com.iflytek.cdc.admin.util.GetBeanUtil;
import org.springframework.util.Assert;

public class MasterDataFactory {

    public static IMasterDataService getMedicalService(String code) {
        final IMasterDataService masterDataService = GetBeanUtil.getBean(IMasterDataService.class, MasterDataEnum.getBeanNameByCode(code));
        Assert.isTrue(masterDataService != null, "未获取到处理类型:" + code);
        return masterDataService;
    }

    private MasterDataFactory() {
        //不让实例化该类
    }
}
