package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermDictInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermDictInfoFilter;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
;

/**
 * @ClassName SyncMdmDataServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:52
 * @Version 1.0
 */

@Service
@Slf4j
@AllArgsConstructor
public abstract class AbstractSyncMdmDataService {

    private final UapUserApi uapUserApi;

    public  MdmDataSyncService mdmDataSyncService;

    public  BatchUidService batchUidService;


    /**
     * 将一组数据固定分组，每组n个元素
     *
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> fixedGrouping2(List<T> source, int n) {

        if (null == source || source.size() == 0 || n <= 0){ return null;}

        List<List<T>> result = new ArrayList<List<T>>();
        int remainder = source.size() % n;
        int size = (source.size() / n);
        for (int i = 0; i < size; i++) {
            List<T> subset = null;
            subset = source.subList(i * n, (i + 1) * n);
            result.add(subset);
        }
        if (remainder > 0) {
            List<T> subset = null;
            subset = source.subList(size * n, size * n + remainder);
            result.add(subset);
        }
        return result;
    }


    public TUapUser getUapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (userCache == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }

    public boolean isCanUse(String originCode) {
        //查询字典目录
        MdmPageData<TermDictInfo, TermDictInfoFilter> mdmPageData = mdmDataSyncService.getMdmTermDictInfo(originCode);
        List<TermDictInfo> dictInfos = mdmPageData.getEntities();
        //判断为空
        if(CollUtil.isEmpty(dictInfos)){
            return true;
        }
        //如果被删除
        if(MdmDataSyncConstants.MDM_YES_DELETE.equals(dictInfos.get(0).getDeleted().toString())){
            return true;
        }
        //判断是否被禁用
        return dictInfos.get(0).getEnabled().toString().equals(MdmDataSyncConstants.NO_USE);
    }

    public boolean getMdmTermStatus(String originCode) {
        //查询字典目录
        MdmPageData<TermDictInfo, TermDictInfoFilter> mdmPageData = mdmDataSyncService.getMdmTermDictInfo(originCode);
        List<TermDictInfo> dictInfos = mdmPageData.getEntities();
        //判断为空
        if(CollUtil.isEmpty(dictInfos)){
            return false;
        }
        //如果被删除
        if(MdmDataSyncConstants.MDM_YES_DELETE.equals(dictInfos.get(0).getDeleted().toString())){
            return false;
        }
        //判断是否被禁用
        return !dictInfos.get(0).getEnabled().toString().equals(MdmDataSyncConstants.NO_USE);
    }
}
