package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.LogExportReqDto;

import com.iflytek.cdc.admin.entity.TbCdcmrLogExport;
import com.iflytek.cdc.admin.service.LogExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date :2023/8/3 11:13
 * @description:LogExportController
 */
@RestController
@Api(tags = "导出日志")
public class LogExportController {
    @Resource
    private LogExportService logExportService;


    @PostMapping("/v1/pt/logExport/save")
    @ApiOperation("保存导出文件记录")
    public String saveLogExport(@RequestBody LogExportReqDto reqDto,
                                @RequestParam String loginUserId) {
        reqDto.setLoginUserId(loginUserId);
        logExportService.insert(reqDto);
        return reqDto.getId();
    }

    @PostMapping("/v1/pt/logExport/update")
    @ApiOperation("更新导出文件记录")
    public String updateLogExport(@RequestBody LogExportReqDto reqDto,
                                @RequestParam String loginUserId) {
        TbCdcmrLogExport update = new TbCdcmrLogExport();
        Assert.isTrue(logExportService.selectByPrimaryKey(reqDto.getId())!=null,"不存在此记录");

        update.setId(reqDto.getId());
        update.setFileSize(reqDto.getFileSize() );
        update.setStatus(Constants.STATUS_ON);
        logExportService.updateByPrimaryKeySelective(update);
        return reqDto.getId();
    }
}
