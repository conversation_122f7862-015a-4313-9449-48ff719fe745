package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.DisLexicon;
import com.iflytek.cdc.admin.entity.DiseaseDiagnosisLexicon;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.entity.LexiconAssociationDirectory;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.QueryAssociationPageVO;


public interface DisLexiconMaintenanceService {
    ApiResult addDiseaseLexicon(DiseaseLexiconDTO dto);

    void updateDiseaseLexicon(DiseaseLexiconDTO dto);

    void deleteDiseaseLexicon(DiseaseLexiconDTO dto);

    PageData<DisLexicon> getDiseaseLexiconPage(SearchDiseaseLexiconDTO dto);

    PageData<DiseaseDiagnosisLexicon> getDiagnosisLexiconPage(SearchDiagnosisLexiconDTO dto);

    void updateDiagnosisLexicon(DiagnosisLexiconDTO dto);

    ApiResult associateLexicon(LexiconAssociationDTO dto);

    PageData<InfectiousDiseases> queryInfecPageInfo(SearchInfecInfoDTO sd);

    ApiResult addAssociationDirectory(LexiconAssociationDirectory dto);

    ApiResult exportAssociationDirectory(AssociationExportDTO dto) throws Exception;

    PageData<QueryAssociationPageVO> getDiseaseLexiconAssociationPage(QueryAssociationPageDTO dto);

    PageData<QueryAssociationPageVO> getDiseaseDiagnosisAssociationPage(QueryAssociationPageDTO dto);

    PageData<QueryAssociationPageVO> getInfectiousAssociationPage(QueryAssociationPageDTO dto);
}
