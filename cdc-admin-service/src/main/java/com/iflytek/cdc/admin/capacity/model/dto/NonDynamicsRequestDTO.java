package com.iflytek.cdc.admin.capacity.model.dto;

import com.iflytek.cdc.admin.capacity.api.algorithm.request.NonDynamicsRequest;
import com.iflytek.fpva.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 非传播动力学请求参数
 */
@Data
public class NonDynamicsRequestDTO {
    @ApiModelProperty("历史数据")
    private List<HistoryData> historyDataList;

    @ApiModelProperty("预测步长")
    private Integer step;

    @Data
    @ApiModel("历史数据")
    public static class HistoryData{
        @ApiModelProperty("日期")
        private Date date;

        @ApiModelProperty("数量")
        private Integer quantity;
    }

    public NonDynamicsRequest request(){
        NonDynamicsRequest nonDynamicsRequest  = new NonDynamicsRequest();
        Map<String, Integer> recordCount = this.getHistoryDataList().stream().collect(Collectors.toMap(d -> DateUtil.convertDateToString(d.getDate()), NonDynamicsRequestDTO.HistoryData::getQuantity, (o, n) -> n, LinkedHashMap::new));
        nonDynamicsRequest.setRecordsCount(recordCount);
        nonDynamicsRequest.setFutureDays(getStep());
        return nonDynamicsRequest;
    }
}
