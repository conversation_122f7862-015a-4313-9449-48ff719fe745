package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.mapper.InspectionWarnMapper;
import com.iflytek.cdc.admin.service.InspectionWarnService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class InspectionWarnServiceImpl implements InspectionWarnService {
    @Resource
    InspectionWarnMapper inspectionWarnMapper;
    @Override
    public List<CascadeVO> getInspectionInfectedList() {
        return inspectionWarnMapper.getInspectionInfectedList();
    }
}
