package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.outbound.entity.OutboundBatchExeResult;
import com.iflytek.cdc.admin.outbound.mapper.OutboundBatchExeResultMapper;
import com.iflytek.cdc.admin.outbound.service.OutboundBatchExeResultService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OutboundBatchExeResultServiceImpl extends CdcServiceBaseImpl<OutboundBatchExeResultMapper, OutboundBatchExeResult> implements OutboundBatchExeResultService {

    @Override
    public void save(List<OutboundBatchExeResult> resultList) {
        List<String> batchNos = resultList.stream().map(OutboundBatchExeResult::getBatch).collect(Collectors.toList());
        List<OutboundBatchExeResult> outboundBatchExeResults = listByBatch(batchNos);
        List<OutboundBatchExeResult> inserts = new ArrayList<>();
        List<OutboundBatchExeResult> updates = new ArrayList<>();
        Map<String, OutboundBatchExeResult> existedMap = outboundBatchExeResults.stream().collect(Collectors.toMap(OutboundBatchExeResult::getBatch, b -> b));
        resultList.forEach(r -> {
            if (existedMap.containsKey(r.getBatch())){
                OutboundBatchExeResult existed = existedMap.get(r.getBatch());
                r.setId(existed.getId());
                updates.add(r);
            }else {
                inserts.add(r);
            }
        });
        if (updates.size() > 0){
            updateBatchById(updates);
        }
        if (inserts.size() > 0){
            saveBatch(inserts);
        }
    }
    
    private List<OutboundBatchExeResult> listByBatch(List<String> batchNos){
        LambdaQueryWrapper<OutboundBatchExeResult> queryWrapper = lambdaQueryWrapper();
        queryWrapper.in(OutboundBatchExeResult::getBatch, batchNos);
        return list(queryWrapper);
    }
}
