package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.SympWarnDto;
import com.iflytek.cdc.admin.dto.SympQueryDTO;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface SchoolSymptomService {
    PageInfo<TbCdcmrSympSymptom> getSchoolSymptomPageList(SympQueryDTO sympQueryDTO);

    void addSchoolSymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId);

    void deleteSchoolSymptom(String id);

    void updateSchoolSymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId);

    PageInfo<SympWarnDto> getSchoolSymptomWarnPageList(SympQueryDTO sympQueryDTO);

    void updateSchoolSymptomWarn(SympWarnDto sympWarnDto, String loginUserId);

    SympWarnDto getWarnById(String warnId);

    void updateSchoolSymptomWarnStatus(SympWarnDto sympWarnDto, String loginUserId);

    List<SympWarnDto> getSchoolSymptomWarnAllList(String symptomCode);

    void exportSchoolSymptomWarnRule(HttpServletResponse response);

    List<CascadeVO> getSymptoms();

    List<CascadeVO> getSymptoms(String loginUserId);

}
