package com.iflytek.cdc.admin.controller.brief;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.TableWidthType;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.brief.OverviewSearchDto;
import com.iflytek.cdc.admin.entity.brief.BriefIndicatorsEntity;
import com.iflytek.cdc.admin.entity.brief.BriefInfoEntity;
import com.iflytek.cdc.admin.entity.brief.PushRecordEntity;
import com.iflytek.cdc.admin.job.BriefReportGenerateJob;
import com.iflytek.cdc.admin.service.brief.OverviewService;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.BriefInfoVo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 简要报告概述控制器
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Api(tags = "简报概况")
@RestController
@RequestMapping("/pt/{version}/brief/view")
@RequiredArgsConstructor
@Slf4j
public class BriefReportOverviewController {

    private final OverviewService overviewService;
    
    private final BriefReportGenerateJob job;

    /**
     * 获取简报概况
     */
    @PostMapping("/list")
    public ApiResult<PageInfo<BriefInfoEntity>> list(@RequestParam String loginUserId,
                                                     @RequestBody OverviewSearchDto searchDto) {
        searchDto.setLoginUserId(loginUserId);
        return overviewService.list(searchDto);
    }

    /**
     * 根据简报id获取简报信息
     */
    @GetMapping("/info/{id}")
    public ApiResult<BriefInfoVo> info(@PathVariable String id) {
        BriefInfoVo briefInfoVo = overviewService.getById(id);
        return ApiResult.ok(briefInfoVo);
    }


    @DeleteMapping("/deleteInfoById")
    public ApiResult<Object> deleteInfoById(@RequestParam String id) {
        overviewService.deleteInfoById(id);
        return ApiResult.ok();
    }

    /**
     * 获取推送记录；
     * 
     * 推送列表通过页面权限控制，不限制当前用户查看数据范围。
     */
    @PostMapping("/pushList")
    public ApiResult<PageInfo<PushRecordEntity>> pushList(@RequestBody OverviewSearchDto searchDto) {
        return overviewService.pushList(searchDto);
    }

    /**
     * 执行简报生成任务
     */
    @PostMapping("/execute")
    public void execute(@RequestParam String param) {
        job.execute(param);
    }

    /**
     * 下载word文档
     *
     * @param briefId 简短id
     * @return {@link ResponseEntity }<{@link byte[] }>
     */
    @GetMapping("/downloadWord/{briefId}")
    public ResponseEntity<byte[]> downloadWord(@PathVariable String briefId) {
        try (XWPFDocument document = new XWPFDocument(); ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {

            BriefInfoVo briefInfo = overviewService.getById(briefId);
            if (briefInfo == null) {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }

            // 动态标题
            createTitle(document, briefInfo.getTitle());

            // 动态正文
            createBody(document, briefInfo.getContent());
            // 附件表格
            if (StrUtil.isNotBlank(briefInfo.getAttachmentTitle())) {
                // 表格标题
                createTableTitle(document, briefInfo.getAttachmentTitle());

                List<BriefIndicatorsEntity> attachmentIndicators = briefInfo.getAttachmentIndicators();

                List<BriefIndicatorsEntity> headers = prepareHeaders(attachmentIndicators);

                List<Map<String, Map<String, String>>> rows = prepareRows(attachmentIndicators);

                int numberOfColumns = headers.size();
                int numberOfRows = rows.size();

                // 创建表格
                XWPFTable table = createTable(document, numberOfColumns, numberOfRows);
                setTableHeaders(table, headers);
                setTableContent(table, headers, rows);
            }

            // 将文档写入字节数组输出流
            document.write(byteArrayOutputStream);
            byte[] bytes = byteArrayOutputStream.toByteArray();

            // 设置响应头
            HttpHeaders headersResponse = new HttpHeaders();
            headersResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String fileName = briefInfo.getId() + "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".docx";
            headersResponse.setContentDispositionFormData("attachment", fileName);

            // 返回响应实体
            return new ResponseEntity<>(bytes, headersResponse, HttpStatus.OK);

        } catch (IOException e) {
            log.error("下载word文档出现异常", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 创建标题
     *
     * @param document 文件
     * @param title    标题
     */
    private void createTitle(XWPFDocument document, String title) {
        XWPFParagraph titleParagraph = document.createParagraph();
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
    }

    /**
     * 创建主体
     *
     * @param document 文件
     * @param content  内容
     */
    private void createBody(XWPFDocument document, String content) {
        // 按换行符分割内容
        String[] lines = content.split("\\n");

        for (String line : lines) {
            // 创建新段落
            XWPFParagraph bodyParagraph = document.createParagraph();
            XWPFRun bodyRun = bodyParagraph.createRun();
            bodyRun.setText(line);
            bodyRun.setFontSize(12);
            bodyParagraph.setIndentationFirstLine(720);
        }
    }

    /**
     * 创建表格标题
     *
     * @param document        文件
     * @param attachmentTitle 附件标题
     */
    private void createTableTitle(XWPFDocument document, String attachmentTitle) {
        XWPFParagraph tableTitleParagraph = document.createParagraph();
        XWPFRun tableTitleRun = tableTitleParagraph.createRun();
        tableTitleRun.setText(attachmentTitle);
        tableTitleRun.setBold(true);
        tableTitleRun.setFontSize(14);
        tableTitleParagraph.setAlignment(ParagraphAlignment.CENTER);
    }

    /**
     * 准备第一列
     *
     * @param attachmentIndicators 附件指示器
     * @return {@link List }<{@link BriefIndicatorsEntity }>
     */
    private List<BriefIndicatorsEntity> prepareHeaders(List<BriefIndicatorsEntity> attachmentIndicators) {
        List<BriefIndicatorsEntity> indicatorHeads = attachmentIndicators.stream().filter(e -> Constants.BriefReport.LOCATION_TYPE_HEAD.equals(e.getLocation())).collect(Collectors.toList());
        if (indicatorHeads.isEmpty()) {
            return Collections.emptyList();
        }
        List<BriefIndicatorsEntity> headers = new ArrayList<>();
        
        BriefIndicatorsEntity entity = new BriefIndicatorsEntity();
        entity.setDimensionName(indicatorHeads.get(0).getDimensionName());
        headers.add(entity);
        headers.addAll(indicatorHeads);
        
        return headers;
    }

    /**
     * 准备行
     *
     * @param attachmentIndicators 附件指示器
     * @return {@link List }<{@link Map }<{@link String }, {@link Map }<{@link String }, {@link String }>>>
     */
    private List<Map<String, Map<String, String>>> prepareRows(List<BriefIndicatorsEntity> attachmentIndicators) {
        if (attachmentIndicators == null) {
            return Collections.emptyList();
        }
        return attachmentIndicators.stream()
                .filter(e -> Constants.BriefReport.LOCATION_TYPE_TEXT.equals(e.getLocation()))
                .collect(Collectors.groupingBy(
                        BriefIndicatorsEntity::getLabelName,
                        Collectors.toMap(
                                BriefIndicatorsEntity::getIndexId,
                                BriefIndicatorsEntity::getIndexValue,
                                (existing, replacement) -> existing)
                ))
                .entrySet().stream()
                .map(entry -> {
                    Map<String, Map<String, String>> row = new HashMap<>();
                    row.put(entry.getKey(), entry.getValue());
                    return row;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建表格
     *
     * @param document        文件
     * @param numberOfColumns 列数
     * @param numberOfRows    行数
     * @return {@link XWPFTable }
     */
    private XWPFTable createTable(XWPFDocument document, int numberOfColumns, int numberOfRows) {
        XWPFTable table = document.createTable(numberOfRows + 1, numberOfColumns);
        table.setWidthType(TableWidthType.DXA);
        table.setWidth("100%");
        return table;
    }

    /**
     * 设置表头
     *
     * @param table   表格
     * @param headers 标题
     */
    private void setTableHeaders(XWPFTable table, List<BriefIndicatorsEntity> headers) {
        XWPFTableRow headerRow = table.getRow(0);
        for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
            XWPFTableCell cell = headerRow.getCell(colIndex);
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph.createRun();
            run.setText(colIndex == 0 ? headers.get(colIndex).getDimensionName() : headers.get(colIndex).getIndexName());
            run.setBold(true);
            run.setFontSize(12);
        }
    }

    /**
     * 设置表格内容
     *
     * @param table   表
     * @param headers 标题
     * @param rows    排
     */
    private void setTableContent(XWPFTable table, List<BriefIndicatorsEntity> headers, List<Map<String, Map<String, String>>> rows) {
        for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
            XWPFTableRow row = table.getRow(rowIndex + 1);
            Map<String, Map<String, String>> rowDataMap = rows.get(rowIndex);
            for (Map.Entry<String, Map<String, String>> entry : rowDataMap.entrySet()) {
                Map<String, String> rowData = entry.getValue();
                for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                    XWPFTableCell cell = row.getCell(colIndex);
                    XWPFParagraph paragraph = cell.addParagraph();
                    if (colIndex > 0) {
                        paragraph.setAlignment(ParagraphAlignment.RIGHT);
                    }
                    XWPFRun run = paragraph.createRun();
                    run.setText(colIndex == 0 ? entry.getKey() : rowData.getOrDefault(headers.get(colIndex).getIndexId(), ""));
                    run.setFontSize(12);
                }
            }
        }
    }
}
