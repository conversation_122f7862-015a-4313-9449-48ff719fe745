package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.admin.constant.UapOrgConstants;
import com.iflytek.cdc.admin.enums.OrgNodeTypeEnum;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.uap.usercenter.entity.TUapOrganization;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.pojo.UapMdmDataOrgDetailDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapMdmDataOrgDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgDto;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgTreeNode;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/6 10:37
 **/
@Service
@Slf4j
public class UapOrgServiceImpl implements UapOrgService {
    @Resource
    RestTemplate restTemplate;

    @Value("${userCenter.version:3.0.0}")
    private String userCenterVersion;

    private final UapUserApi uapUserApi;

    private final UapOrgApi uapOrgApi;

    public UapOrgServiceImpl(UapUserApi uapUserApi, UapOrgApi uapOrgApi) {
        this.uapUserApi = uapUserApi;
        this.uapOrgApi = uapOrgApi;
    }

    /**
     * 根据登录人员信息获取本机构及其下级机构所有机构的id
     *
     * @param loginUserId
     * @return
     **/
    @Override
    public List<String> queryDownOrgId(String loginUserId) {
        List<String> downOrgIds = new ArrayList<>();
        try {
            //获取登录用户所在机构的id
            TUapUser tUapUser = uapUserApi.getUserDetail(loginUserId).getUapUser();
            if (tUapUser == null) {
                log.error("获取登录用户信息异常：{}", loginUserId);
                throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
            }
            //根据机构id获取机构信息
            UapOrgDto uapOrgDto = uapOrgApi.getExt(tUapUser.getOrgId());
            if (uapOrgDto != null) {
                //获取当前机构及其下级机构的机构树
                UapOrgTreeNode uapOrgTreeNode = uapOrgApi.orgTreeByCode(uapOrgDto.getUapOrganization().getCode(), loginUserId, UapOrgConstants.IS_USER_TREE);
                getOrgIds(downOrgIds, uapOrgTreeNode);
            }
            downOrgIds.add(tUapUser.getOrgId());
        } catch (Exception e) {
            log.error("获取下级机构编号集合异常：{}", loginUserId);
            throw new MedicalBusinessException("获取下级机构异常");
        }
        return downOrgIds;
    }

    private List<String> getOrgIds(List<String> downOrgIds, UapOrgTreeNode uapOrgTreeNode) {
        if (uapOrgTreeNode != null) {
            //获取子机构列表
            List<UapOrgTreeNode> childNodes = uapOrgTreeNode.getChildNodes();
            //如果子机构列表不为空
            if (CollUtil.isNotEmpty(childNodes)) {
                //递归调用
                childNodes.forEach(e -> {
                    //添加到集合
                    downOrgIds.add(e.getId());
                    getOrgIds(downOrgIds, e);
                });
            }
        }
        return downOrgIds;
    }

    @Override
    public List<String> queryDownOrgCode(String loginUserId) {
        List<String> downOrgCodes = new ArrayList<>();
        try {
            //获取登录用户所在机构的id
            TUapUser tUapUser = uapUserApi.getUserDetail(loginUserId).getUapUser();
            if (tUapUser == null) {
                log.warn("获取登录用户信息异常：{}", loginUserId);
                throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
            }
            //根据机构id获取机构信息
            UapOrgDto uapOrgDto = uapOrgApi.getExt(tUapUser.getOrgId());
            if (uapOrgDto != null) {
                //获取当前机构及其下级机构的机构树
                UapOrgTreeNode uapOrgTreeNode = uapOrgApi.orgTreeByCode(uapOrgDto.getUapOrganization().getCode(), loginUserId, UapOrgConstants.IS_USER_TREE);
                getOrgCodes(downOrgCodes, uapOrgTreeNode);
                downOrgCodes.add(uapOrgDto.getUapOrganization().getCode());
            }
        } catch (Exception e) {
            log.error("获取下级机构编号集合异常,用户ID:{}", loginUserId, e);
            throw new MedicalBusinessException("获取下级机构异常");
        }
        return downOrgCodes;
    }

    @Override
    public UapOrgTreeNode getOrgTree(String loginUserId) {
        TUapUser tUapUser = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (tUapUser == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        UapOrgDto uapOrgDto = uapOrgApi.getExt(tUapUser.getOrgId());
        if (uapOrgDto != null) {
            //获取当前机构及其下级机构的机构树
            UapOrgTreeNode uapOrgTreeNode = uapOrgApi.orgTreeByCode(uapOrgDto.getUapOrganization().getCode(), loginUserId, UapOrgConstants.IS_USER_TREE);
            return uapOrgTreeNode;
        }
        return null;
    }

    /**
     * 业务人员维护的机构配置
     */
    public UapOrgTreeNode getOrgForBusinessPerson(String loginUserId){
        if (userCenterVersion.equals(UapOrgConstants.UAP_VERSION_V3)){
            return getOrgTree(loginUserId);
        }else {
            return getCdcAndDeptOrg(loginUserId);
        }
    }

    /**
     * 只保留疾控中心跟部门的数据
     */
    public UapOrgTreeNode getCdcAndDeptOrg(String loginUserId){
        UapOrgTreeNode uapOrgTreeNode = getOrgTree(loginUserId);
        if (uapOrgTreeNode == null || (!OrgNodeTypeEnum.CDC.getCode().equals(uapOrgTreeNode.getNodeType())
                && !OrgNodeTypeEnum.DEPARTMENT.getCode().equals(uapOrgTreeNode.getNodeType()))){
            return null;
        }

        if (uapOrgTreeNode.getChildNodes() != null){
            uapOrgTreeNode.setChildNodes(filterAndGetChildNodes(uapOrgTreeNode.getChildNodes()));
        }
        return uapOrgTreeNode;
    }

    /**
     * 过滤掉非疾控中心和科室的数据
     */
    private List<UapOrgTreeNode> filterAndGetChildNodes(List<UapOrgTreeNode> childNodes){
        if (childNodes == null){
            return null;
        }
        List<UapOrgTreeNode>  nodes = childNodes.stream().filter(u ->
                OrgNodeTypeEnum.CDC.getCode().equals(u.getNodeType())
                        || OrgNodeTypeEnum.DEPARTMENT.getCode().equals(u.getNodeType())).collect(Collectors.toList());
        if (nodes.size() > 0){
            for (UapOrgTreeNode node : nodes){
                if (node.getChildNodes() != null && node.getChildNodes().size() > 0){
                    node.setChildNodes(filterAndGetChildNodes(node.getChildNodes()));
                }
            }
        }
        return nodes.size() == 0 ? null : nodes;
    }

    @Override
    public UapMdmDataOrgDetailDto getByOrgId(String orgId) {
        String url = "http://uap-service-ext-service/v1/pt/ImaOrganization/" + orgId;
        return restTemplate.getForObject(url, UapMdmDataOrgDetailDto.class);
    }

    private List<String> getOrgCodes(List<String> downOrgCodes, UapOrgTreeNode uapOrgTreeNode) {
        if (uapOrgTreeNode != null) {
            //获取子机构列表
            List<UapOrgTreeNode> childNodes = uapOrgTreeNode.getChildNodes();
            if (!CollectionUtils.isEmpty(childNodes)) {
                childNodes.forEach(e -> {
                    //添加到集合
                    downOrgCodes.add(e.getCode());
                    getOrgCodes(downOrgCodes, e);
                });
            }

        }
        return downOrgCodes;
    }

    @Override
    public List<UapOrgTreeNode> getOrgTreeV5(String loginUserId) {
        String url = "http://uap-service-ext-service/v1/pt/ImaDept/adminTree?loginUserId="+loginUserId;
        Map<String, Object> requestMap = new HashMap<>();
        return Objects.requireNonNull(restTemplate.postForObject(url, requestMap, UapOrgTreeNode.class)).getChildNodes();
    }
}
