package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.AnalysisDimQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueQueryDto;
import com.iflytek.cdc.admin.dto.AnalysisDimValueRollupRelDto;
import com.iflytek.cdc.admin.vo.AnalysisDimDetailInfoVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimRollupValueRelVO;
import com.iflytek.cdc.admin.vo.AnalysisDimValueVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionEditVO;
import com.iflytek.cdc.admin.vo.AnalysisDimensionVO;

import java.util.List;

public interface AnalysisDimensionService {


    void addDim(AnalysisDimensionEditVO dimension);

    AnalysisDimensionVO getDimById(String id);

    void updateDimById(AnalysisDimensionEditVO dimension);

    void deleteDimById(String id);

    PageInfo<AnalysisDimensionVO> pageList(AnalysisDimQueryDto input);

    List<AnalysisDimRollupRelVO> getRollupDims(String dimId);

    AnalysisDimDetailInfoVO getDimDetailInfo(AnalysisDimValueQueryDto queryDto);

    /******************/

    void addRollupDim(AnalysisDimRollupRelVO rollupRelVOs);

    void updateRollupDim(AnalysisDimRollupRelVO rollupRelVOs);

    void deleteRollupDim(String id);


    void editAnalysisFlag(AnalysisDimValueVO dimValueVO);

    List<AnalysisDimRollupValueRelVO> getRollupValueRelBy(String analysisDimId, String dataAttrValueId);

    void editRollupDimValue(String analysisDimId, String dataAttrValueId, List<AnalysisDimValueRollupRelDto> rollupValueRelVOs);
}
