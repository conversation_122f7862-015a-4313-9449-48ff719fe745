package com.iflytek.cdc.admin.outbound.service;


import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.model.vo.ManualCallResultVo;

public interface PhoneCallService {

    String getAccount(String name, String telephone, String relationId);

    String updateTime(String recordId);

    String updateResult(String recordId, String relationId, ManualCallResultVo vo);

    String getRecordUrl(String audioName, String relationId);

    AudioRecord getRecord(String relationId);

    void saveAudio(String tempText, String audioName, String relationId);
}
