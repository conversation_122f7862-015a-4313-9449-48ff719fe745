package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientTypeMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrOutpatientWarnRuleMapper;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.OutpatientWarnService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OutpatientWarnServiceImpl implements OutpatientWarnService {
    @Resource
    TbCdcmrOutpatientTypeMapper tbCdcmrOutpatientTypeMapper;
    @Resource
    TbCdcmrOutpatientWarnMapper tbCdcmrOutpatientWarnMapper;
    @Resource
    TbCdcmrOutpatientWarnRuleMapper tbCdcmrOutpatientWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;

    @Resource
    DataAuthService dataAuthService;

    @Resource
    private ParamConfigService paramConfigService;
    @Override
    public PageInfo<TbCdcmrOutpatientType> getOutpatientPageList(OutpatientQueryDTO outpatientQueryDTO) {
        PageHelper.startPage(outpatientQueryDTO.getPageIndex());
        return new PageInfo<>(tbCdcmrOutpatientTypeMapper.getList());    }

    @Override
    public void addOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId) {

        if (tbCdcmrOutpatientTypeMapper.getByOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode()) != null) {
            throw new MedicalBusinessException("已存在相同门诊类型编码!");
        }
        if (tbCdcmrOutpatientTypeMapper.getByOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName()) != null) {
            throw new MedicalBusinessException("已存在相同门诊类型名称!");
        }
        tbCdcmrOutpatient.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_outpatient_type")));
        tbCdcmrOutpatient.setUpdater(loginUserId);
        tbCdcmrOutpatient.setUpdateTime(new Date());
        tbCdcmrOutpatient.setIsDeleted(0);
        tbCdcmrOutpatientTypeMapper.insert(tbCdcmrOutpatient);
        addOutpatientWarnByOutpatient(tbCdcmrOutpatient, loginUserId);
    }

    @Override
    public void deleteOutpatient(String id) {
        TbCdcmrOutpatientType outpatient = tbCdcmrOutpatientTypeMapper.selectByPrimaryKey(id);
        TbCdcmrOutpatientWarn outpatientWarn = tbCdcmrOutpatientWarnMapper.getByOutpatientTypeCode(outpatient.getOutpatientTypeCode());
        if (!tbCdcmrOutpatientWarnRuleMapper.getRuleListByWarnId(outpatientWarn.getId()).isEmpty()) {
            throw new MedicalBusinessException("还有在使用的规则，请先删除规则后再进行删除！");
        }
        tbCdcmrOutpatientWarnMapper.deleteByPrimaryKey(outpatient.getId());
        tbCdcmrOutpatientTypeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void updateOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId) {
        TbCdcmrOutpatientType existOutpatient = tbCdcmrOutpatientTypeMapper.getByOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());

        if (existOutpatient != null && !Objects.equals(existOutpatient.getId(), tbCdcmrOutpatient.getId())) {
            throw new MedicalBusinessException("已存在相同中毒编码!");
        }
        existOutpatient = tbCdcmrOutpatientTypeMapper.getByOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        if (existOutpatient != null && !Objects.equals(existOutpatient.getId(), tbCdcmrOutpatient.getId())) {
            throw new MedicalBusinessException("已存在相同中毒名称!");
        }
        TbCdcmrOutpatientType updateOutpatient = new TbCdcmrOutpatientType();
        updateOutpatient.setId(tbCdcmrOutpatient.getId());
        updateOutpatient.setUpdater(loginUserId);
        updateOutpatient.setUpdateTime(new Date());
        updateOutpatient.setOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        updateOutpatient.setRemark(tbCdcmrOutpatient.getRemark());
        updateOutpatient.setStatus(tbCdcmrOutpatient.getStatus());
        updateOutpatient.setOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        updateOutpatient.setOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        updateOutpatient.setOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        updateOutpatientWarnByOutpatient(updateOutpatient, loginUserId);
        tbCdcmrOutpatientTypeMapper.updateByPrimaryKeySelective(updateOutpatient);
    }

    @Override
    public PageInfo<OutpatientWarnDto> getOutpatientWarnPageList(OutpatientQueryDTO outpatientQueryDTO) {
        PageHelper.startPage(outpatientQueryDTO.getPageIndex(), outpatientQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrOutpatientWarnMapper.getList(outpatientQueryDTO));    }

    @Override
    public void updateOutpatientWarn(OutpatientWarnDto outpatientWarnDto, String loginUserId) {
        TbCdcmrOutpatientWarn tbCdcmrOutpatientWarn = new TbCdcmrOutpatientWarn();
        tbCdcmrOutpatientWarn.setId(outpatientWarnDto.getId());
        tbCdcmrOutpatientWarn.setStatus(outpatientWarnDto.getStatus());
        tbCdcmrOutpatientWarn.setOutpatientTypeCode(outpatientWarnDto.getOutpatientTypeCode());
        tbCdcmrOutpatientWarn.setOutpatientTypeName(outpatientWarnDto.getOutpatientTypeName());
        tbCdcmrOutpatientWarn.setRemark(outpatientWarnDto.getRemark());
        tbCdcmrOutpatientWarn.setUpdater(loginUserId);
        tbCdcmrOutpatientWarn.setIsDeleted(0);
        tbCdcmrOutpatientWarn.setUpdateTime(new Date());
        tbCdcmrOutpatientWarn.setMaxLifeCycle(outpatientWarnDto.getMaxLifeCycle());
        tbCdcmrOutpatientWarn.setSmsSendTypeCode(outpatientWarnDto.getSmsSendTypeCode());
        tbCdcmrOutpatientWarn.setSmsSendTypeDesc(outpatientWarnDto.getSmsSendTypeDesc());
        tbCdcmrOutpatientWarn.setObservation(outpatientWarnDto.getObservation());
        tbCdcmrOutpatientWarn.setGrowthRate(outpatientWarnDto.getGrowthRate());
        tbCdcmrOutpatientWarnMapper.updateByPrimaryKeySelective(tbCdcmrOutpatientWarn);
        updateRules(tbCdcmrOutpatientWarn.getId(), outpatientWarnDto.getRuleList(), loginUserId);
    }

    @Override
    public OutpatientWarnDto getWarnById(String warnId) {
        OutpatientWarnDto dto = new OutpatientWarnDto();
        TbCdcmrOutpatientWarn outpatientWarn = tbCdcmrOutpatientWarnMapper.selectByPrimaryKey(warnId);
        dto.setId(outpatientWarn.getId());
        dto.setStatus(outpatientWarn.getStatus());
        dto.setRemark(outpatientWarn.getRemark());
        dto.setOutpatientTypeCode(outpatientWarn.getOutpatientTypeCode());
        dto.setOutpatientTypeName(outpatientWarn.getOutpatientTypeName());
        dto.setMaxLifeCycle(outpatientWarn.getMaxLifeCycle());
        dto.setSmsSendTypeCode(outpatientWarn.getSmsSendTypeCode());
        dto.setSmsSendTypeDesc(outpatientWarn.getSmsSendTypeDesc());
        dto.setGrowthRate(outpatientWarn.getGrowthRate());
        dto.setObservation(outpatientWarn.getObservation());

        dto.setRuleList(tbCdcmrOutpatientWarnRuleMapper.getRuleListByWarnId(warnId));
        return dto;    }

    @Override
    public void updateOutpatientWarnStatus(OutpatientWarnDto outpatientWarnDto, String loginUserId) {
        TbCdcmrOutpatientWarn tbCdcmrOutpatientWarn = new TbCdcmrOutpatientWarn();
        tbCdcmrOutpatientWarn.setStatus(outpatientWarnDto.getStatus());
        tbCdcmrOutpatientWarn.setId(outpatientWarnDto.getId());
        tbCdcmrOutpatientWarnMapper.updateStatusByPrimaryKey(tbCdcmrOutpatientWarn);
    }

    @Override
    public List<OutpatientWarnDto> getOutpatientWarnAllList(String outpatientCode) {
        List<OutpatientWarnDto> warnList = tbCdcmrOutpatientWarnMapper.getAllList(outpatientCode);
        List<TbCdcmrOutpatientWarnRule> ruleList = tbCdcmrOutpatientWarnRuleMapper.getAllRuleList();
        warnList.forEach(outpatientWarnDto -> outpatientWarnDto.setRuleList(ruleList.stream().filter(tbCdcmrOutpatientWarnRule -> tbCdcmrOutpatientWarnRule.getWarnId().equals(outpatientWarnDto.getId())).collect(Collectors.toList())));
        return warnList;    }

    @Override
    public void exportOutpatientWarnRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.POISONING_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<OutpatientWarnRuleExportDataDto> dataList = tbCdcmrOutpatientWarnRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.POISONING_WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<CascadeVO> getOutpatientType() {
        List<TbCdcmrOutpatientType> result = tbCdcmrOutpatientTypeMapper.getList();
        List<CascadeVO> typeList = new ArrayList<>();
        result.forEach(e->{
            CascadeVO cascadeVO = new CascadeVO();
            cascadeVO.setLabel(e.getOutpatientTypeName());
            cascadeVO.setValue(e.getOutpatientTypeCode());
            typeList.add(cascadeVO);
        });
        return typeList;
    }

    @Override
    public List<CascadeVO> getOutpatientTypeNameList() {
        return getOutpatientType();
    }

    @Override
    public List<CascadeVO> getOutpatientTypeNameList(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_OUTPATIENT);
//        List<CascadeVO> poisonTypeList = getOutpatientType();
//
//        Collections.sort(poisonTypeList);
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getOutpatientDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        List<CascadeVO> resultList =poisonTypeList.stream().filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
//        return resultList;
    }

    public void addOutpatientWarnByOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId) {
        TbCdcmrOutpatientWarn tbCdcmrOutpatientWarn = new TbCdcmrOutpatientWarn();
        tbCdcmrOutpatientWarn.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_outpatient_warn")));
        tbCdcmrOutpatientWarn.setStatus(0);
        tbCdcmrOutpatientWarn.setOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        tbCdcmrOutpatientWarn.setOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        tbCdcmrOutpatientWarn.setOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        tbCdcmrOutpatientWarn.setOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        tbCdcmrOutpatientWarn.setRemark(null);
        tbCdcmrOutpatientWarn.setUpdater(loginUserId);
        tbCdcmrOutpatientWarn.setIsDeleted(0);
        tbCdcmrOutpatientWarn.setUpdateTime(new Date());
        tbCdcmrOutpatientWarn.setMaxLifeCycle(null);
        tbCdcmrOutpatientWarnMapper.insert(tbCdcmrOutpatientWarn);
    }

    public void updateOutpatientWarnByOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId) {
        TbCdcmrOutpatientWarn tbCdcmrOutpatientWarn = tbCdcmrOutpatientWarnMapper.getByOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        tbCdcmrOutpatientWarn.setOutpatientTypeCode(tbCdcmrOutpatient.getOutpatientTypeCode());
        tbCdcmrOutpatientWarn.setOutpatientTypeName(tbCdcmrOutpatient.getOutpatientTypeName());
        tbCdcmrOutpatientWarn.setUpdater(loginUserId);
        tbCdcmrOutpatientWarn.setUpdateTime(new Date());
        tbCdcmrOutpatientWarnMapper.updateByPrimaryKeySelective(tbCdcmrOutpatientWarn);
    }




    private void updateRules(String warnId, List<TbCdcmrOutpatientWarnRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_outpatient_warn_rule")));
            }
            rule.setWarnId(warnId);
            rule.setStatus(1);
            rule.setIsDeleted(0);
            rule.setUpdater(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrOutpatientWarnRule::getId).collect(Collectors.toList());
        tbCdcmrOutpatientWarnRuleMapper.deleteOtherByIds(idList, warnId);
        tbCdcmrOutpatientWarnRuleMapper.upsertRules(ruleList);
    }
}
