package com.iflytek.cdc.admin.datamodel.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.customizedapp.model.dto.EntityErQueryDTO;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.datamodel.service.EntityErModelService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/pt/{version}/entityErModel")
@RestController
public class EntityErModelController {

    @Resource
    private EntityErModelService entityErModelService;

    @GetMapping("/loadById")
    @ApiOperation("根据id查询er模型")
    public TbCdcdmEntityErModel loadById(@RequestParam String id){
        return entityErModelService.load(id);
    }

    /**
     * 查询所有er模型
     * @return er模型实例
     */
    @PostMapping
    @ApiOperation("查询所有er模型")
    public List<TbCdcdmEntityErModel> list( EntityErQueryDTO queryDTO){
        return entityErModelService.list(queryDTO);
    }

    /**
     * 新增数据
     *
     * @param input 实例对象
     * @return 实例对象
     */
    @PostMapping("/create")
    @ApiOperation("新增数据")
    public TbCdcdmEntityErModel create(@RequestBody TbCdcdmEntityErModel input){
        return entityErModelService.create(input);
    }

    /**
     * 更新数据
     *
     * @param input 实例对象
     * @return 实例对象
     */
    @PostMapping("/update")
    @ApiOperation("新增数据")
    public TbCdcdmEntityErModel update(@RequestBody TbCdcdmEntityErModel input){
        return entityErModelService.update(input);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     */
    @PostMapping("/deleteById")
    @ApiOperation("通过主键删除数据")
    public void deleteById(@RequestParam String id){
        entityErModelService.deleteById(id);
    }

    /**
     * 分页查询
     * @param queryDTO 查询条件
     * @return 分页数据
     */
    @PostMapping("/pageList")
    @ApiOperation("分页查询")
    public PageInfo<TbCdcdmEntityErModel> pageList(@RequestBody EntityErQueryDTO queryDTO){
        return entityErModelService.pageList(queryDTO);
    }

    /**
     * 根据编码进行更新
     *
     * @param input 实例对象
     * @return 实例对象
     */
    @PostMapping("/saveByCode")
    @ApiOperation("根据编码进行更新")
    public TbCdcdmEntityErModel saveByCode(@RequestBody TbCdcdmEntityErModel input){
        return entityErModelService.saveByCode(input);
    }
}
