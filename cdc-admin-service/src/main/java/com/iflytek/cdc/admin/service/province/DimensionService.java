package com.iflytek.cdc.admin.service.province;

import com.iflytek.cdc.admin.model.mr.vo.TreeNode;

import java.util.List;

public interface DimensionService {

    /**
     * 获取传染病级联关系
     * */
    List<TreeNode> getInfectedInfo(String infectedClassCode, String infectedTypeCode ,Boolean flag);

    /**
     * 获取机构级联关系
     * */
    List<TreeNode> getOrgInfo();

    /**
     * 获取症候群级联关系
     * */
    List<TreeNode> getSyndromeInfo();

    /**
     * 获取新发突发传染病级联
     * */
    List<TreeNode> getEmergingInfo();

    /**
     * 获取地方病级联
     * */
    List<TreeNode> getEndemicInfo();

    /**
     * 获取病原级联关系
     * */
    List<TreeNode> getPathogenInfo();

    /**
     *传染病父级诊断+病毒性肝炎子级
     * @param infectedClassCode
     * @param infectedTypeCode
     * @return
     */
    List<TreeNode> getInfectedParentInfo(String infectedClassCode, String infectedTypeCode);
}
