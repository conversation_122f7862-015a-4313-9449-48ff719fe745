package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.entity.AddressStandard;

import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
public interface AddressStandardService extends IService<AddressStandard> {

    /**
     * 存在就更新 不存在就新增
     *
     * @param addressStandard 标准地址表实体类
     */
    void saveOrUpdateAddressStandard(AddressStandard addressStandard);

    Set<String> getUnStdWords();
}
