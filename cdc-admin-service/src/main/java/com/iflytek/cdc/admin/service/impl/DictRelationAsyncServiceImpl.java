package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.entity.DictRelationDirectory;
import com.iflytek.cdc.admin.mapper.DictRelationMapper;
import com.iflytek.cdc.admin.service.DictRelationAsyncService;
import com.iflytek.cdc.admin.service.DictRelationTaskService;
import com.iflytek.cdc.admin.thread.Status;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageRequest;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;

import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2021/9/22 15:00
 **/
@Service
@Slf4j
@EnableAsync
public class DictRelationAsyncServiceImpl implements DictRelationAsyncService {

    @Resource
    RedisTemplate<String,Object> redisTemplate;

    @Resource
    DictRelationTaskService taskService;

    @Resource
    public DictRelationMapper dMapper;

    @Resource
    public TermDictApi termDictApi;


    @Override
    @Async("asyncExecutor")
    public void createTask(String batchId,String loginUserId) {
        try {
            //设置状态
            redisTemplate.opsForValue().set(batchId, Status.TODO.toString(), 5L, TimeUnit.MINUTES);
            //数据同步
            Future<String> future = taskService.asyncRelationByMdmAsync(loginUserId);
            //超时5分钟
            String result = future.get(5L, TimeUnit.MINUTES);
            if (StringUtils.isNotBlank(result)) {
                //设置状态已完成
                redisTemplate.opsForValue().set(batchId, Status.DONE.toString(), 5L, TimeUnit.MINUTES);
            }
        } catch (TimeoutException e) {
            log.error("数据同步超时异常:{}", e);
            redisTemplate.opsForValue().set(batchId, Status.ERROR.toString(), 1L, TimeUnit.MINUTES);
            throw new MedicalBusinessException("数据同步超时");
        } catch (Exception e) {
            log.error("字典关联数据同步异常：{}", e);
            throw new MedicalBusinessException("字典关联数据同步异常");
        }
    }


    @Override
    @Async("asyncExecutor")
    public void syncMdmData(String dictCode, String dictValueCode, String dictValueName, DictRelationDirectory relationDirectory, String flag) {
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        TermCodedValueInfoFilter mdmFilter = new TermCodedValueInfoFilter();
        mdmFilter.setDictCode(dictCode);
        if (StrUtil.isNotBlank(dictValueCode)) {
            mdmFilter.setCodedValue(dictValueCode);
            dictValueName = null;
        } else {
            mdmFilter.setCodedValueDesc(dictValueName);
        }
        pageRequest.setFilter(mdmFilter);
        //获取mdm数据
        List<TermCodedValueInfo> valueInfoList = termDictApi.getTermCodedValueList(pageRequest).getEntities();
        //如果为空
        if (CollUtil.isEmpty(valueInfoList)) {
            ///直接删除本地库中的数据
            dMapper.deleteRelationByMdm(relationDirectory.getId(), dictValueCode, dictValueName);
//            if ("origin".equals(flag)) {
//                throw new MedicalBusinessException("参数字典值在mdm中已被删除:" + dictValueCode);
//            } else {
//                throw new MedicalBusinessException("关联字典值在mdm中已被删除");
//            }
        } else {

            if ("origin".equals(flag)) {
                if (StrUtil.isNotBlank(dictValueCode)) {
                    //新增判断逻辑，查询后管库中当前值
                    List<String> originDictValueName = dMapper.findOriginDictValueName(relationDirectory.getId(), dictValueCode);
                    if (CollectionUtil.isNotEmpty(originDictValueName)) {
                        boolean match = originDictValueName.stream().anyMatch(origin -> !origin.equals(valueInfoList.get(0).getDescription()));
                        if (match) {
                            dMapper.updateOriginNameByMdm(dictValueCode, relationDirectory.getId(), valueInfoList.get(0).getDescription());
                        }
                    }
                }
            } else if ("target".equals(flag)) {
                if (StrUtil.isNotBlank(dictValueCode)) {
                    //新增判断逻辑，查询后管库中当前值
                    List<String> targetDictValueName = dMapper.findTargetDictValueName(relationDirectory.getId(), dictValueCode);
                    if (CollectionUtil.isNotEmpty(targetDictValueName)) {
                        boolean match = targetDictValueName.stream().anyMatch(origin -> !origin.equals(valueInfoList.get(0).getDescription()));
                        if (match) {
                            dMapper.updateTargetNameByMdm(dictValueCode, relationDirectory.getId(), valueInfoList.get(0).getDescription());
                        }
                    }
                }
            }
        }
    }
}
