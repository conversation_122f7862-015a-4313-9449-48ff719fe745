package com.iflytek.cdc.admin.vo.brief;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.dto.brief.ApplicableDiseaseDto;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模板vo
 *
 * <AUTHOR>
 * @date 2024-12-30 14:52:02
 */
@Data
@TableName("tb_cdcbr_template")
public class TemplateVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * $column.comments
	 */
	@TableId
	private String id;
	/**
	 * 统计周期
	 */
	private String statisticsCycle;
	/**
	 * 模板标题
	 */
	private String title;
	/**
	 * 分析对象
	 */
	private String analysisObject;

	/**
	 * 内容
	 */
	private String content;
	/**
	 * 业务类型：传染病；XX症候群
	 */
	private String businessType;
	/**
	 * 分析对象名称
	 */
	private String analysisObjectName;
	/**
	 * 修改者ID
	 */
	private String creatorId;
	/**
	 * 修改者
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	/**
	 * 修改者ID
	 */
	private String updatorId;
	/**
	 * 修改者
	 */
	private String updatorName;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;
	/**
	 * 删除标识
	 */
	private String deleteFlag = DeleteFlagEnum.NO.getCode();

	/**
	 * 规则状态
	 */
	private String ruleStatus;

	/**
	 * 适用疾病对象
	 */
	private ApplicableDiseaseDto applicableDisease;

	private String briefReportType;
}
