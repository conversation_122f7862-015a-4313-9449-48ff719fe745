package com.iflytek.cdc.admin.service.province;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.dto.CommonParamPageDTO;
import com.iflytek.cdc.admin.dto.CommonParamListDTO;
import com.iflytek.cdc.admin.dto.CommonParamSimpleDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrCommonParam;

import java.util.List;

public interface TbCdcmrCommonParamService extends IService<TbCdcmrCommonParam> {
    /**
     * 新增配置参数
     * @param param 配置参数
     * @return 新增的配置ID
     */
    String insert(TbCdcmrCommonParam param, String loginUserName);

    /**
     * 根据ID删除配置参数（逻辑删除）
     * @param id 配置ID
     * @return 是否删除成功
     */
    Boolean deleteById(String id);

    Boolean updateBy(TbCdcmrCommonParam param, String loginUserName);

    /**
     * 查询配置参数列表
     * @return 配置参数列表
     */
    List<TbCdcmrCommonParam> getCommonParams(CommonParamPageDTO commonParamPageDTO);

    /**
     * 根据ID获取配置参数
     * @param id 配置ID
     * @return 配置参数
     */
    TbCdcmrCommonParam getById(String id);

    /**
     * 根据配置分类和配置类型查询简化的配置参数列表
     * @param dto 查询参数
     * @return 简化的配置参数列表
     */
    List<CommonParamSimpleDTO> getCommonParamSimpleList(CommonParamListDTO dto);

    /**
     * 根据paramKey获取配置参数
     * @param paramKey 配置KEY
     * @return 配置参数
     */
    TbCdcmrCommonParam getByParamKey(String paramKey);
}