package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.admin.enums.ConditionTypeEnum;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrCheckAuthorityConfigMapper;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrSyndromeMonitorConfigMapper;
import com.iflytek.cdc.admin.model.processCheck.dto.ProcessCheckQueryDTO;
import com.iflytek.cdc.admin.model.processCheck.vo.CheckPersonInfo;
import com.iflytek.cdc.admin.model.processCheck.vo.SubgroupInfo;
import com.iflytek.cdc.admin.service.province.ProcessCheckConfigService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class ProcessCheckConfigServiceImpl extends ServiceImpl<TbCdcmrCheckAuthorityConfigMapper, TbCdcmrCheckAuthorityConfig> implements ProcessCheckConfigService {

    private final static String TB_CDCMR_CHECK_AUTHORITY_CONFIG = "tb_cdcmr_check_authority_config";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrSyndromeMonitorConfigMapper syndromeMonitorConfigMapper;

    @Override
    public PageInfo<TbCdcmrCheckAuthorityConfig> getProcessCheckConfig(ProcessCheckQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(baseMapper.getCheckConfigBy(dto));
    }

    @Override
    public List<TbCdcmrCheckAuthorityConfig> getCheckConfigByLoginUser(String loginUserId, String diseaseType) {

        List<TbCdcmrCheckAuthorityConfig> result = new ArrayList<>();

        QueryWrapper<TbCdcmrCheckAuthorityConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("delete_flag", "0");
        wrapper.eq("status", 1);
        wrapper.eq("type", diseaseType);
        //查询改疾病类型配置的审核权限
        List<TbCdcmrCheckAuthorityConfig> configList = list(wrapper);

        if (CollUtil.isNotEmpty(configList)) {
            Gson gson = new Gson();
            configList.forEach(e -> {
                if (StringUtils.isNotBlank(e.getCheckPerson())) {
                    //解析单个疾病配置的审核权限情况
                    List<CheckPersonInfo> personInfoList = Arrays.asList(gson.fromJson(e.getCheckPerson(), CheckPersonInfo[].class));
                    List<String> userIdList = personInfoList.stream().map(CheckPersonInfo::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    //若存在，添加该疾病的权限配置
                    if (userIdList.contains(loginUserId)) {
                        result.add(e);
                    }
                }
            });
        }

        return result;
    }

    @Override
    public void editCheckConfig(TbCdcmrCheckAuthorityConfig config) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        if (StringUtils.isBlank(config.getId())) {
            config.setId(String.valueOf(batchUidService.getUid(TB_CDCMR_CHECK_AUTHORITY_CONFIG)));
            config.setCreator(uapUserPo.getName());
            config.setCreatorId(uapUserPo.getId());
            config.setCreateTime(new Date());
            config.setStatus(Integer.valueOf(StatusEnum.STATUS_ON.getCode()));
            config.setDeleteFlag(DeleteFlagEnum.NO.getCode());
        }
        config.setUpdater(uapUserPo.getName());
        config.setUpdaterId(uapUserPo.getId());
        config.setUpdateTime(new Date());

        //编辑配置
        baseMapper.upsert(config);
    }

    @Override
    public List<String> getSyndromeSubgroup(String id) {

        //查询该症候群亚组规则
        String ruleList = syndromeMonitorConfigMapper.getSyndromeSubgroupBy(id, ConditionTypeEnum.SUBGROUP.getCode());
        if(StringUtils.isBlank(ruleList)) {
            return new ArrayList<>();
        }
        Gson gson = new Gson();
        List<SubgroupInfo> subgroupInfoList = Arrays.asList(gson.fromJson(ruleList, SubgroupInfo[].class));
        return subgroupInfoList.stream().map(SubgroupInfo::getSubgroupName).collect(Collectors.toList());
    }
}
