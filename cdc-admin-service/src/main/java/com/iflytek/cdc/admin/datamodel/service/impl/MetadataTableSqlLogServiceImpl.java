package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableSqlLog;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableSqlLogMapper;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableSqlLogService;
import com.iflytek.cdc.admin.enums.TableColumnTypeEnum;
import com.iflytek.cdc.admin.enums.TableSqlStatusEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MetadataTableSqlLogServiceImpl extends CdcServiceBaseImpl<TbCdcdmMetadataTableSqlLogMapper, TbCdcdmMetadataTableSqlLog> implements MetadataTableSqlLogService {

    private static final String CREATE_TABLE_SQL = "CREATE TABLE %s.%s (\n" +
                                                        "%s,\n" +
                                                        "\tCONSTRAINT %s_pk PRIMARY KEY (id)\n" +
                                                        ");";

    private static final String ALERT_TABLE_SQL = "ALTER TABLE %s.%s RENAME TO %s;";
    private static final String COMMENT_TABLE_SQL = "COMMENT ON TABLE %s.%s IS '%s';";
    private static final String RENAME_COLUMN_SQL = "ALTER TABLE %s.%s RENAME COLUMN %s TO %s;";

    private static final String ADD_COLUMN_SQL = "ALTER TABLE %s.%s ADD %s %s %s;";
    private static final String ALERT_COLUMN_SQL = "ALTER TABLE %s.%s ALTER COLUMN %s TYPE %s USING %s::%s;";

    private static final String COMMENT_COLUMN_SQL = "COMMENT ON COLUMN %s.%s.%s IS '%s';";

    /**
     * 生成创建表的SQL语句。
     * @param tbCdcdmMetadataTableInfo 包含表信息的元数据对象。
     * @return 返回一个字符串，该字符串是用于创建数据库表的SQL语句，包括创建表、添加注释以及为每个字段添加注释的SQL语句。
     */
    private String generateCreateTableSql(TbCdcdmMetadataTableInfo tbCdcdmMetadataTableInfo){
        List<String> results = new ArrayList<>();
        results.add(String.format(CREATE_TABLE_SQL,
                tbCdcdmMetadataTableInfo.getSchema(),
                tbCdcdmMetadataTableInfo.getTableName(),
                getCreateTableColumnSql(tbCdcdmMetadataTableInfo.getColumnInfoList()),
                tbCdcdmMetadataTableInfo.getTableName()));
        results.add(String.format(COMMENT_TABLE_SQL, 
                tbCdcdmMetadataTableInfo.getSchema(),
                tbCdcdmMetadataTableInfo.getTableName(), 
                tbCdcdmMetadataTableInfo.getTableDesc()));
        tbCdcdmMetadataTableInfo.getColumnInfoList().forEach(columnInfo -> {
            results.add(getCommentColumnSql(tbCdcdmMetadataTableInfo, columnInfo));
        });
        return String.join("\n", results);
    }

    /**
     * 根据列信息生成修改表相关的SQL。
     * @param tbCdcdmMetadataTableInfo 包含旧表和新表信息的键值。
     * @param updates 需要更新的列信息列表，每个元素是一个包含旧列和新列信息的键值对。
     * @param inserts 需要插入的新列信息列表。
     * @return 返回生成的警报SQL字符串，如果updates或inserts为空，则只生成重命名表和注释表的SQL。
     */
    public String generateAlertSqlByColumnInfo(Pair<TbCdcdmMetadataTableInfo, TbCdcdmMetadataTableInfo> tbCdcdmMetadataTableInfo,
                                               List<Pair<TbCdcdmMetadataTableColumnInfo, TbCdcdmMetadataTableColumnInfo>> updates,
                                               List<TbCdcdmMetadataTableColumnInfo> inserts) {
        TbCdcdmMetadataTableInfo oldTable = tbCdcdmMetadataTableInfo.getKey();
        TbCdcdmMetadataTableInfo newTable = tbCdcdmMetadataTableInfo.getValue();

        List<String> results = new ArrayList<>();
        if (!StringUtils.equals(oldTable.getTableName(), newTable.getTableName())){
            results.add(String.format(ALERT_TABLE_SQL, oldTable.getSchema(),
                    oldTable.getTableName(), newTable.getTableName()));
        }
        
        if (StringUtils.isNotEmpty(newTable.getTableDesc())){
            if (!StringUtils.equals(oldTable.getTableDesc(), newTable.getTableDesc())){
                results.add(String.format(COMMENT_TABLE_SQL, oldTable.getSchema(),
                        oldTable.getTableName(), newTable.getTableDesc()));
            }
        }

        if(!CollectionUtils.isEmpty(updates)){
            for(Pair<TbCdcdmMetadataTableColumnInfo, TbCdcdmMetadataTableColumnInfo> update : updates){
                TbCdcdmMetadataTableColumnInfo old = update.getKey();
                TbCdcdmMetadataTableColumnInfo newData = update.getValue();
                if (!old.getColumnName().equals(newData.getColumnName())){
                    results.add(String.format(RENAME_COLUMN_SQL, newTable.getSchema(),
                            newTable.getTableName(), old.getColumnName(), newData.getColumnName()));
                }
                if (!old.getDataType().equals(newData.getDataType())){
                    results.add(String.format(ALERT_COLUMN_SQL,
                            newTable.getSchema(),
                            newTable.getTableName(),
                            old.getColumnName(),
                            getColumnType(newData),
                            newData.getColumnName(),
                            newData.getDataType()));
                }
                if (StringUtils.isNotEmpty(newData.getColumnDesc())){
                    if (!StringUtils.equals(old.getColumnDesc(), newData.getColumnDesc())){
                        results.add(getCommentColumnSql(newTable, newData));
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(inserts)){
            inserts.forEach(i -> {
                results.add(String.format(ADD_COLUMN_SQL,
                        newTable.getSchema(),
                        newTable.getTableName(),
                        i.getColumnName(),
                        getColumnType(i),
                        i.getColumnRequired()));
                results.add(getCommentColumnSql(newTable, i));
            });
        }
        return String.join("\n", results);
    }
    
    /**
     * 获取列的类型。
     * @param columnInfo 需要获取类型的列的信息。
     * @return 如果列的长度或目标不为空，返回列的类型和长度；否则只返回列的类型。
     */
    private String getColumnType(TbCdcdmMetadataTableColumnInfo columnInfo){
        List<String> lengths = new ArrayList<>();
        
        if(columnInfo.getColumnLength() != null && TableColumnTypeEnum.VARCHAR.getCode().equals(columnInfo.getDataType())) {
            lengths.add(String.valueOf(columnInfo.getColumnLength()));
        }
        if(TableColumnTypeEnum.NUMERIC.getCode().equals(columnInfo.getDataType()) &&
                columnInfo.getColumnPrecision() != null){
            lengths.add(String.valueOf(columnInfo.getColumnPrecision()));
        }
        if (lengths.size() > 0){
            return columnInfo.getDataType() + "(" + String.join(",", lengths) + ")";
        }
        return columnInfo.getDataType();
    }
    
    /**
     * 获取指定表的列注释SQL语句。
     * @param tbCdcdmMetadataTableInfo 需要获取注释的表的信息。
     * @param columnInfo 需要获取注释的列的信息。
     * @return 返回一个格式化后的SQL语句，用于查询指定表的指定列的注释信息。
     */
    private String getCommentColumnSql(TbCdcdmMetadataTableInfo tbCdcdmMetadataTableInfo, TbCdcdmMetadataTableColumnInfo columnInfo){
        return String.format(COMMENT_COLUMN_SQL, tbCdcdmMetadataTableInfo.getSchema(),
                tbCdcdmMetadataTableInfo.getTableName(), columnInfo.getColumnName(), columnInfo.getColumnDesc());
    }

    /**
     * 获取创建表的列SQL语句。
     * @param columnInfoList 包含表列信息的列表。
     * @return 返回一个字符串，该字符串是创建表的列SQL语句，每个列的信息由列名、列类型和列是否必填组成，各列之间用逗号分隔。
     */
    private String getCreateTableColumnSql(List<TbCdcdmMetadataTableColumnInfo> columnInfoList){
        return columnInfoList.stream().map(c -> "\t" +  c.getColumnName() + " " + getColumnType(c) + " " + c.getColumnRequired()).collect(Collectors.joining(",\n"));
    }

    /**
     * 创建表的SQL语句。
     * @param tableInfo 需要创建的表的信息。
     */
    @Override
    public void createTableSql(TbCdcdmMetadataTableInfo tableInfo) {
        TbCdcdmMetadataTableSqlLog sqlLog = new TbCdcdmMetadataTableSqlLog();
        sqlLog.setSql(generateCreateTableSql(tableInfo));
        sqlLog.setTableId(tableInfo.getId());
        sqlLog.setStatus(TableSqlStatusEnum.WAITING_EXECUTE.getCode());
        create(sqlLog);
    }

    /**
     * 创建修改语句的SQL。
     * @param tbCdcdmMetadataTableInfo 需要生成警报SQL的元数据表信息对。
     * @param updates 需要更新的列信息列表。
     * @param inserts 需要插入的列信息列表。
     */
    @Override
    public void createAlertSql(Pair<TbCdcdmMetadataTableInfo, TbCdcdmMetadataTableInfo> tbCdcdmMetadataTableInfo, List<Pair<TbCdcdmMetadataTableColumnInfo, TbCdcdmMetadataTableColumnInfo>> updates, List<TbCdcdmMetadataTableColumnInfo> inserts) {
        TbCdcdmMetadataTableSqlLog sqlLog = new TbCdcdmMetadataTableSqlLog();
        sqlLog.setSql(generateAlertSqlByColumnInfo(tbCdcdmMetadataTableInfo, updates, inserts));
        sqlLog.setTableId(tbCdcdmMetadataTableInfo.getKey().getId());
        sqlLog.setStatus(TableSqlStatusEnum.WAITING_START.getCode());
        create(sqlLog);
    }

    @Override
    public void startToExecute(TbCdcdmMetadataTableInfo tbCdcdmMetadataTableInfo) {
        LambdaUpdateChainWrapper<TbCdcdmMetadataTableSqlLog> sqlLogLambdaUpdateChainWrapper = lambdaUpdate();
        sqlLogLambdaUpdateChainWrapper.set(TbCdcdmMetadataTableSqlLog::getStatus, TableSqlStatusEnum.WAITING_EXECUTE.getCode())
                .eq(TbCdcdmMetadataTableSqlLog::getTableId, tbCdcdmMetadataTableInfo.getId())
                .eq(TbCdcdmMetadataTableSqlLog::getStatus, TableSqlStatusEnum.WAITING_START.getCode())
                .update();
    }

    @Override
    public List<TbCdcdmMetadataTableSqlLog> listToExecute() {
        return list(lambdaQueryWrapper().in(TbCdcdmMetadataTableSqlLog::getStatus
                , TableSqlStatusEnum.WAITING_EXECUTE.getCode()
                , TableSqlStatusEnum.EXECUTE_FAILED.getCode()));
    }

    @Override
    public void update(List<TbCdcdmMetadataTableSqlLog> logs){
        updateBatchById(logs);
    }
}
