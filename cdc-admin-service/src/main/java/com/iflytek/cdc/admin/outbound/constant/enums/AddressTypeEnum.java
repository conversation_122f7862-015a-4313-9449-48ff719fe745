package com.iflytek.cdc.admin.outbound.constant.enums;

import lombok.Getter;

@Getter
public enum AddressTypeEnum {
    /**
     * APP
     */
    APP("app", "APP"),
    /**
     * 语音
     */
    AUDIO("audio", "语音"),
    /**
     * 上传
     */
    UPLOAD("upload", "上传"),
    /**
     * 电话
     */
    CALL("call", "电话");

    private String code;

    private String desc;

    AddressTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AddressTypeEnum getByCode(String code) {

        for (AddressTypeEnum addressTypeEnum : AddressTypeEnum.values()) {
            if (addressTypeEnum.getCode().equals(code)) {
                return addressTypeEnum;
            }
        }

        return null;
    }

    public static String getByName(String code) {
        for (AddressTypeEnum addressTypeEnum : AddressTypeEnum.values()) {
            if (addressTypeEnum.getCode().equals(code)) {
                return addressTypeEnum.getDesc();
            }
        }
        return AddressTypeEnum.APP.name();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
