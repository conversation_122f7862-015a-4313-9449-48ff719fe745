package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleExportDataDto;
import com.iflytek.cdc.admin.dto.CustomizedWarningRuleVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarningRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningRuleItemMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrCustomizedWarningRuleMapper;
import com.iflytek.cdc.admin.service.CustomizedWarningRuleService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;

@Service
public class CustomizedWarningRuleServiceImpl implements CustomizedWarningRuleService {
    @Resource
    TbCdcmrCustomizedWarningRuleMapper tbCdcmrCustomizedWarningRuleMapper;
    @Resource
    TbCdcmrCustomizedWarningRuleItemMapper tbCdcmrCustomizedWarningRuleItemMapper;
    @Resource
    BatchUidService batchUidService;

    @Resource
    private ParamConfigService paramConfigService;

    @Override
    public void addCustomizedWarningRule(TbCdcmrCustomizedWarningRule tbCdcmrCustomizedWarningRule) {
        tbCdcmrCustomizedWarningRuleMapper.insertSelective(tbCdcmrCustomizedWarningRule);
    }

    @Override
    @Transactional
    public void updateCustomizedWarningRule(CustomizedWarningRuleVO customizedWarningRuleVO, String loginUserId) {
        customizedWarningRuleVO.setCreateTime(new Date());
        customizedWarningRuleVO.setCreator(loginUserId);
        customizedWarningRuleVO.setUpdater(loginUserId);
        customizedWarningRuleVO.setUpdateTime(new Date());
        customizedWarningRuleVO.getItemList().forEach(e -> {
            e.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_customized_warning_rule")));
            e.setWarningId(customizedWarningRuleVO.getId());
            e.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
            e.setStatus(Constants.STATUS_ON);
            e.setUpdater(loginUserId);
            e.setCreator(loginUserId);
            e.setUpdateTime(new Date());
            e.setCreateTime(new Date());
        });
        tbCdcmrCustomizedWarningRuleMapper.updateByPrimaryKeySelective(customizedWarningRuleVO);
        tbCdcmrCustomizedWarningRuleItemMapper.deleteByRuleId(customizedWarningRuleVO.getId());
        tbCdcmrCustomizedWarningRuleItemMapper.batchInsert(customizedWarningRuleVO.getItemList());
    }

    @Override
    public void deleteCustomizedWarningRule(String warnId) {
        tbCdcmrCustomizedWarningRuleMapper.deleteByWarnId(warnId);
    }

    @Override
    public PageInfo<CustomizedWarningRuleVO> getCustomizedWarningRuleList(CustomizedWarnQueryDTO customizedWarnQueryDTO) {
        PageHelper.startPage(customizedWarnQueryDTO.getPageIndex(), customizedWarnQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrCustomizedWarningRuleMapper.getList(customizedWarnQueryDTO));
    }

    @Override
    public CustomizedWarningRuleVO getCustomizedWarningRuleById(String id) {
        CustomizedWarningRuleVO result = tbCdcmrCustomizedWarningRuleMapper.selectByPrimaryKey(id);
        result.setItemList(tbCdcmrCustomizedWarningRuleItemMapper.selectByRuleId(id));
        return result;
    }

    @Override
    public List<CustomizedWarningRuleVO> getAllEnabledWarningRule(String warningType) {
        List<CustomizedWarningRuleVO> customizedWarningRuleVOList = tbCdcmrCustomizedWarningRuleMapper.getEnabledWarn(warningType);
        customizedWarningRuleVOList.forEach(e -> {
            e.setItemList(tbCdcmrCustomizedWarningRuleItemMapper.selectByRuleId(e.getId()));
        });
        return customizedWarningRuleVOList;
    }

    @Override
    public List<CustomizedWarningRuleVO> getAllEnabledWarningRuleWithoutItem() {
        return tbCdcmrCustomizedWarningRuleMapper.getEnabledWarn(null);
    }

    @Override
    public void updateCustomizedWarningRuleStatus(Integer status, String id, String loginUserId) {
        CustomizedWarningRuleVO customizedWarningRuleVO = new CustomizedWarningRuleVO();
        customizedWarningRuleVO.setId(id);
        customizedWarningRuleVO.setStatus(status);
        customizedWarningRuleVO.setUpdater(loginUserId);
        customizedWarningRuleVO.setUpdateTime(new Date());
        tbCdcmrCustomizedWarningRuleMapper.updateByPrimaryKeySelective(customizedWarningRuleVO);
    }

    @Override
    public void exportCustomizedWarningRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.CUSTOMIZED_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<CustomizedWarningRuleExportDataDto> dataList = tbCdcmrCustomizedWarningRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        dataList.forEach(this::getIncubation);
        dataList.forEach(this::getMaxLifeCycle);
        dataList.forEach(this::getMedicalCount);
        dataList.forEach(this::getMonitorObject);
        dataList.forEach(this::getMedicalAttribute);
        dataList.forEach(this::getTimeScope);
        dataList.forEach(this::getWarnType);

        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.CUSTOMIZED_WRAN_RULE_EXCEL_TITLE);
    }


    private void getIncubation(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(customizedWarningRuleExportDataDto.getIncubation())) {
            customizedWarningRuleExportDataDto.setIncubation(customizedWarningRuleExportDataDto.getIncubation() + "天");
        } else {
            customizedWarningRuleExportDataDto.setIncubation("/");
        }
    }

    private void getMaxLifeCycle(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if (customizedWarningRuleExportDataDto.getMaxLifeCycle() != null) {
            customizedWarningRuleExportDataDto.setMaxLifeCycle(customizedWarningRuleExportDataDto.getMaxLifeCycle() + "天");

        } else {
            customizedWarningRuleExportDataDto.setMaxLifeCycle("/");
        }
    }

    private void getMonitorObject(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if ("1".equals(customizedWarningRuleExportDataDto.getMonitorObject())) {
            customizedWarningRuleExportDataDto.setMonitorObject("等级医院");
        } else if ("2".equals(customizedWarningRuleExportDataDto.getMonitorObject())) {
            customizedWarningRuleExportDataDto.setMonitorObject("基层医疗");
        } else if ("3".equals(customizedWarningRuleExportDataDto.getMonitorObject())) {
            customizedWarningRuleExportDataDto.setMonitorObject("学校/单位");
        } else if ("4".equals(customizedWarningRuleExportDataDto.getMonitorObject())) {
            customizedWarningRuleExportDataDto.setMonitorObject("街道区域");
        } else {
            customizedWarningRuleExportDataDto.setMonitorObject("/");
        }
    }

    private void getTimeScope(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(customizedWarningRuleExportDataDto.getTimeRange())) {
            if ("1".equals(customizedWarningRuleExportDataDto.getTimeRangeUnit())) {
                customizedWarningRuleExportDataDto.setTimeRange("≤" + customizedWarningRuleExportDataDto.getTimeRange() + "天");
            } else if ("2".equals(customizedWarningRuleExportDataDto.getTimeRangeUnit())) {
                customizedWarningRuleExportDataDto.setTimeRange("≤" + customizedWarningRuleExportDataDto.getTimeRange() + "周");
            } else if ("3".equals(customizedWarningRuleExportDataDto.getTimeRangeUnit())) {
                customizedWarningRuleExportDataDto.setTimeRange("≤" + customizedWarningRuleExportDataDto.getTimeRange() + "月");
            }
        } else {
            customizedWarningRuleExportDataDto.setTimeRange("/");
        }
    }

    private void getMedicalCount(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if (customizedWarningRuleExportDataDto.getMedicalCount() != null) {
            customizedWarningRuleExportDataDto.setMedicalCount("≥" + customizedWarningRuleExportDataDto.getMedicalCount());
        } else {
            customizedWarningRuleExportDataDto.setMedicalCount("/");
        }
    }

    private void getMedicalAttribute(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if ("1".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("疑似病例");
        } else if ("2".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("临床诊断病历");
        } else if ("3".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("确诊病例");
        } else if ("4".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("病原携带者");
        } else if ("5".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("阳性检测");
        } else if ("99".equals(customizedWarningRuleExportDataDto.getMedicalAttribute())) {
            customizedWarningRuleExportDataDto.setMedicalAttribute("全部");
        } else {
            customizedWarningRuleExportDataDto.setMedicalAttribute("/");
        }
    }

    private void getWarnType(CustomizedWarningRuleExportDataDto customizedWarningRuleExportDataDto) {
        if ("1".equals(customizedWarningRuleExportDataDto.getWarnType())) {
            customizedWarningRuleExportDataDto.setWarnType("预警信号");
        } else if ("2".equals(customizedWarningRuleExportDataDto.getWarnType())) {
            customizedWarningRuleExportDataDto.setWarnType("上报提示");
        } else {
            customizedWarningRuleExportDataDto.setWarnType("/");
        }
    }
}
