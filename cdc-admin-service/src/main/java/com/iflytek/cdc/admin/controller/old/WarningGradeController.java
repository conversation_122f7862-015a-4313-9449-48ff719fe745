package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.WarningGradeQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade;
import com.iflytek.cdc.admin.service.WarningGradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "预警分级维护")
public class WarningGradeController {

    @Resource
    WarningGradeService warningGradeService;

    @ApiOperation("预警分级维护-预警分级列表查询")
    @PostMapping("/{version}/pt/warningGrade/pageList")
    public PageInfo<TbCdcmrWarningGrade> warningGradePageList(@RequestBody WarningGradeQueryDTO warningGradeQueryDTO) {
        return warningGradeService.getWarningGradePageList(warningGradeQueryDTO);
    }

    @ApiOperation("预警分级维护-新增预警分级")
    @PostMapping("/{version}/pt/warningGrade/add")
    public void addWarningGrade(@RequestBody TbCdcmrWarningGrade tbCdcmrWarningGrade, @RequestParam String loginUserId) {
        warningGradeService.addWarningGrade(tbCdcmrWarningGrade, loginUserId);
    }

    @ApiOperation("预警分级维护-删除预警分级")
    @PostMapping("/{version}/pt/warningGrade/delete")
    public void deleteWarningGrade(@RequestParam String gradeCode) {
        warningGradeService.deleteWarningGrade(gradeCode);
    }

    @ApiOperation("预警分级维护-编辑预警分级")
    @PostMapping("/{version}/pt/warningGrade/update")
    public void updateWarningGrade(@RequestBody TbCdcmrWarningGrade tbCdcmrWarningGrade, @RequestParam String loginUserId) {
        warningGradeService.updateWarningGrade(tbCdcmrWarningGrade, loginUserId);
    }

    @ApiOperation("预警分级维护-预警分级列表查询(不分页)")
    @PostMapping("/{version}/pt/warningGrade/list")
    public List<TbCdcmrWarningGrade> warningGradeList() {
        return warningGradeService.getWarningGradeList();
    }

    @ApiOperation("预警分级配置维护-根据分类和编码查询分级配置内容")
    @GetMapping("/{version}/pt/warningGrade/getGradeByTypeAndCode")
    public List<TbCdcmrWarningGrade> getGradeByTypeAndCode(@RequestParam String configType, @RequestParam String diseaseCode) {
        return warningGradeService.getConfigByTypeAndCode(configType, diseaseCode);
    }
}
