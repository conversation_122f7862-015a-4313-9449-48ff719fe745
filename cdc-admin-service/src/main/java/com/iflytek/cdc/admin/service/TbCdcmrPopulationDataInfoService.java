package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.PopulationDataHistoricalInfoQueryDTO;
import com.iflytek.cdc.admin.dto.TbCdcmrPopulationDataInfoQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo;
import com.iflytek.cdc.admin.vo.PopulationDataInfoVO;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

public interface TbCdcmrPopulationDataInfoService {
    /**
     * 通过文件导入
     */
    void importByFile(InputStream inputStream, String loginUserId, String loginUserNmae);

    /**
     * 分页查询
     */
    PageInfo<TbCdcmrPopulationDataInfo> pageList(TbCdcmrPopulationDataInfoQueryDTO queryDTO, String loginUserName);

    /**
     * 查询
     */
    TbCdcmrPopulationDataInfo load(String id);

    /**
     * 更新
     */
    void update(TbCdcmrPopulationDataInfo input, String loginUserId);

    /**
     * 模板下载
     */
    byte[] downloadTemplate(String loginUserName);

    /**
     * 根据区域编码查询
     **/
    List<PopulationDataInfoVO> listByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    /**
     * 根据区域编码分组查询
     */
    Map<String, PopulationDataInfoVO> groupByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    /**
     * 统计
     */
    PopulationDataInfoVO statByAreaCodes(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    List<PopulationDataInfoVO> listByAreaCodesAndTime(TbCdcmrPopulationDataInfoQueryDTO queryDTO);

    List<TbCdcmrPopulationDataInfo> searchHistoricalStatisticsDate(PopulationDataHistoricalInfoQueryDTO queryDTO, String loginUserName);

    void updateHistoricalStatisticsDate(List<TbCdcmrPopulationDataInfo> input, String loginUserName);

    List<PopulationDataInfoVO> listByEachAreaCode(TbCdcmrPopulationDataInfoQueryDTO queryDTO);
}
