package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.MultichannelWarningRuleConstants;
import com.iflytek.cdc.admin.constant.SyndromeWarningRuleConstants;
import com.iflytek.cdc.admin.entity.TbCdcmrSignalPushConfiguration;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopic;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicConfig;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrMultichannelTopicWarningRule;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrPathogenInfo;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.model.mr.dto.TopicConfigEditDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicQueryDTO;
import com.iflytek.cdc.admin.model.mr.dto.TopicWarningRuleEditDTO;
import com.iflytek.cdc.admin.model.mr.vo.MultichannelTopicInfoVO;
import com.iflytek.cdc.admin.model.mr.vo.TopicConfigInfoVO;
import com.iflytek.cdc.admin.service.province.MultichannelTopicService;
import com.iflytek.cdc.admin.service.province.WarningChargePersonService;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelDetailService;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import com.iflytek.cdc.admin.vo.SignalPushConfigurationVO;
import com.iflytek.cdc.admin.vo.WarningChargePersonVO;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 多渠道专题
 * */

@RestController
@RequestMapping("/pt/{version}/multichannelTopic")
@Api(tags = "多渠道专题配置")
public class MultichannelTopicController {

    @Resource
    private MultichannelTopicService topicService;

    @Resource
    private WarningRiskLevelDetailService warningRiskLevelDetailService;

    @Resource
    private WarningChargePersonService warningChargePersonService;

    @PostMapping("/getTopicList")
    @ApiOperation("获取多渠道专题列表")
    public PageInfo<MultichannelTopicInfoVO> getTopicList(@RequestBody TopicQueryDTO dto){

        return topicService.getTopicList(dto);
    }

    @PostMapping("/editTopic")
    @ApiOperation("编辑专题（新增、编辑、删除）")
    @OperationLogAnnotation(operationName = "编辑专题（新增、编辑、删除）")
    public String editTopic(@RequestBody TbCdcmrMultichannelTopic topic){

        return topicService.editTopic(topic);
    }

    @PostMapping("/getTopicConfig")
    @ApiOperation("查询专题配置")
    public TopicConfigInfoVO getTopicConfig(@RequestParam(required = false) String topicId,
                                            @RequestParam(required = false) String topicName){

        return topicService.getTopicConfig(topicId, topicName);
    }

    @PostMapping("/editTopicConfig")
    @ApiOperation("编辑专题配置")
    @OperationLogAnnotation(operationName = "编辑专题配置")
    public void editTopicConfig(@RequestBody TopicConfigEditDTO dto){

        topicService.editTopicConfig(dto);
    }

    @PostMapping("/getTopicWarningRule")
    @ApiOperation("查看多渠道专题预警规则")
    public TbCdcmrMultichannelTopicWarningRule getTopicWarningRule(@RequestParam String topicId){

        return topicService.getTopicWarningRule(topicId);
    }

    @PostMapping("/editTopicWarningRule")
    @ApiOperation("编辑多渠道专题预警规则")
    @OperationLogAnnotation(operationName = "编辑多渠道专题预警规则")
    public void editTopicWarningRule(@RequestBody TopicWarningRuleEditDTO dto){

        topicService.editTopicWarningRule(dto);
    }

    @PostMapping("/getTimeLimitConfigBy")
    @ApiOperation("查询多渠道专题的时限配置")
    public RiskLevelDetailVO getTimeLimitConfigBy(@RequestParam String topicId){

        return warningRiskLevelDetailService.loadByDiseaseId(topicId,
                                                             null,
                                                             WarningTypeProEnum.MULTICHANNEL.getCode());
    }

    @PostMapping("/getChargePersonBy")
    @ApiOperation("查看多渠道专题的处理责任人配置")
    public WarningChargePersonVO getChargePersonBy(@RequestParam String topicId){

        return warningChargePersonService.loadByDiseaseCode(topicId, WarningTypeProEnum.MULTICHANNEL.getCode());
    }

    /**
     * 查看多渠道专题的信号推送配置
     */
    @PostMapping("/getSignPushConfigBy")
    @ApiOperation("查看多渠道专题的信号推送配置")
    public SignalPushConfigurationVO getSignPushConfigBy(@RequestParam String topicId){
        return warningChargePersonService.loadSignPushConfigByDiseaseCode(topicId);
    }

    @PostMapping("/listPathogenByTopicId")
    @ApiOperation("根据专题id查询该专题下配置的病原")
    public List<TbCdcmrMultichannelTopicConfig> listPathogenByTopicId(@RequestParam String topicId){

        return topicService.listPathogenByTopicId(topicId, MultichannelWarningRuleConstants.MultichannelType.PATHOGEN.getCode());
    }

    @GetMapping("/getPathogenInfo")
    @ApiOperation("查询病原信息")
    public List<TbCdcmrPathogenInfo> getPathogenInfo(){

        return topicService.getPathogenInfo();
    }

    @GetMapping("/getConstants")
    @ApiOperation("多渠道常量获取")
    public Object getConstants(){

        return MultichannelWarningRuleConstants.getInstance();
    }
}
