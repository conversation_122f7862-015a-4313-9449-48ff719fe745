package com.iflytek.cdc.admin.datamodel.mapper;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormValue;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModel;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelForm;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelVersion;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelFormQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelValueDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelFormConfigVO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelListVO;
import com.iflytek.cdc.admin.datamodel.model.vo.DataModelVersionHistoryVO;
import com.iflytek.cdc.admin.datamodel.model.vo.ModelFormListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DataModelConfigMapper {

    /**
     * 根据模型name查询模型id
     * */
    String getModelIdByModelName(@Param("modelName") String modelName);

    /**
     * 根据模型id查询模型信息
     * */
    List<TbCdcdmDataModel> getModelInfoByModelId(@Param("modelId") String modelId,
                                                 @Param("status") String status);

    /**
     * 根据数据模型版本id 查询该版本的发布状态
     * */
    String getVersionStatusByVersionId(@Param("modelVersionId") String modelVersionId);

    /**
     * 向数据模型表 新增or更新数据模型
     * */
    void insertIntoDataModel(TbCdcdmDataModel tbCdcdmDataModel);

    /**
     * 向数据模型版本表 新增or更新数据模型
     * */
    void insertIntoDataModelVersion(TbCdcdmDataModelVersion tbCdcdmDataModelVersion);

    /**
     * 获取数据模型列表
     * */
    List<DataModelListVO> getDataModelList(@Param("status") String status,
                                           @Param("keyWord") String keyWord,
                                           @Param("modelId") String modelId);

    List<DataModelListVO> getDataModelList(DataModelValueDTO dto);

    /**
     * 发布数据模型（即修改版本状态）
     * */
    void publishDataModel(@Param("modelId") String modelId,
                          @Param("modelVersionId") String modelVersionId,
                          @Param("status") String status,
                          @Param("loginUserName") String loginUserName);

    /**
     * 获取数据模型最新的版本状态
     * */
    List<TbCdcdmDataModelVersion> getDataModelVersion(@Param("statusList") List<String> statusList, @Param("modelId") String modelId);

    /**
     * 根据更新数据模型 版本状态
     * */
    void updateDataModel(@Param("modelId") String modelId,
                         @Param("modelVersionId") String modelVersionId,
                         @Param("status") String status,
                         @Param("currStatus") String currStatus,
                         @Param("loginUserName") String loginUserName);

    /**
     * 删除数据模型版本
     * */
    void deleteDataModel(@Param("modelVersionId") String modelVersionId,
                         @Param("loginUserName") String loginUserName);

    /**
     * 查询表单列表
     * */
    List<ModelFormListVO> getModelFormList(DataModelFormQueryDTO dto);

    /**
     * 查询某个数据模型的历史版本
     * */
    List<DataModelVersionHistoryVO> getDataModelVersionHistory(@Param("modelId") String modelId);

    /**
     * 编辑表单
     * */
    void editModelForm(TbCdcdmDataModelForm tbCdcdmDataModelForm);

    /**
     * 根据formId查询表单信息
     * */
    TbCdcdmDataModelForm getFormInfoByFormId(@Param("modelFormId") String modelFormId);

    /**
     * 保存数据模型填写的数据
     * */
    void saveModelFormData(TbCdcdmDataFormValue tbCdcdmDataFormValue);

    /**
     * 根据模型id查询表单列表
     * */
    List<TbCdcdmDataModelForm> getFormListByModelId(@Param("modelId") String modelId, @Param("status") String status);

    TbCdcdmDataFormValue getModelFormData(@Param("id") String id);

    /**
     * 更新当前模型的最新版本
     * */
    void updateDataModelLatestVersion(@Param("modelId") String modelId, @Param("version") String version);

    /**
     * 查询当前版本的内容
     * */
    TbCdcdmDataModelVersion getVersionInfoByVersionId(@Param("modelVersionId") String modelVersionId);

    /**
     * 查询当前版本的表单列表配置
     * */
    List<TbCdcdmDataModelForm> getFormListByVersionId(@Param("modelVersionIds") List<String> modelVersionIds);

    /**
     * 批量插入表单数据
     * */
    void batchInsertFormList(List<TbCdcdmDataModelForm> tbCdcdmDataModelForms);

    /**
     * 批量插入表单数据
     * */
    void editFormSequence(List<TbCdcdmDataModelForm> tbCdcdmDataModelForms);

    /**
     * 查询数据模型已发布版本表单配置
     * */
    List<DataModelFormConfigVO> getModelFormListByVersion(@Param("versionStatus") String versionStatus);
    
    TbCdcdmDataModel getDataModelInfoById(@Param("modelId") String modelId);

    /**
     * 切换数据模型发布状态
     * */
    void updatePublishDataModel(@Param("modelId") String modelId,
                                @Param("modelVersionId") String modelVersionId,
                                @Param("status") String status);

}
