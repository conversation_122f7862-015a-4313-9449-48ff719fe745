package com.iflytek.cdc.admin.customizedapp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.Gson;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用;
 * <AUTHOR> fengwang35
 * @date : 2024-9-3
 */
@ApiModel(value = "应用")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_cdccs_app")
public class TbCdccsApp extends BaseEntity implements Serializable{
    /** 名称 */
    @ApiModelProperty(value = "名称")
    private String appName ;
    /** 编码 */
    @ApiModelProperty(value = "编码")
    private String appCode ;
    /** 分类id */
    @ApiModelProperty(value = "分类id")
    private String categoryId ;

    @ApiModelProperty(value = "图标路径")
    private String logoPath;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 用户中心应用id
     */
    @ApiModelProperty(value = "用户中心应用id")
    private String uapAppId;

    /**
     * 用户中心应用权限id
     */
    @ApiModelProperty(value = "用户中心应用权限id")
    private String uapAppAuthorityId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序字段")
    private Integer orderSeq;

    @ApiModelProperty(value = "扩展属性json")
    private String extendJson;


    public static void main(String[] args) {
        Class<?> tbCdccsAppClass = TbCdccsMenu.class;
        Field[] fields = tbCdccsAppClass.getDeclaredFields();
        Map<String, Object> map = new HashMap<>();
        for (Field field: fields){
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null){
                System.out.println(field.getName() + "为" +annotation.value() + ";");
                map.put(field.getName(), "");
            }
        }
        Gson gson = new Gson();
        System.out.println(gson.toJson(map));


        Field[] fields2 = tbCdccsAppClass.getSuperclass().getDeclaredFields();
        for (Field field: fields2){
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null){
                System.out.println(field.getName() + "为" +annotation.value() + ";");
                map.put(field.getName(), "");
            }
        }
        for (Field field: fields){
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null){
                System.out.println(field.getName() + "为" +annotation.value());
                map.put(field.getName(), "");
            }
        }
        System.out.println(gson.toJson(map));

    }


}