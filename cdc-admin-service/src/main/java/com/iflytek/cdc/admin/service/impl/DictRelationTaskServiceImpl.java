package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.constant.DictRelationConstants;
import com.iflytek.cdc.admin.dto.AsyncRelationDTO;
import com.iflytek.cdc.admin.dto.SearchDictValueDTO;
import com.iflytek.cdc.admin.mapper.DictRelationMapper;
import com.iflytek.cdc.admin.service.DictRelationTaskService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientResponseException;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/22 16:07
 **/
@Service
@Slf4j
@EnableAsync
public class DictRelationTaskServiceImpl implements DictRelationTaskService {

    private final DictRelationMapper dMapper;

    private final TermDictApi termDictApi;


    public DictRelationTaskServiceImpl(DictRelationMapper dMapper, TermDictApi termDictApi) {
        this.dMapper = dMapper;
        this.termDictApi = termDictApi;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Future<String> asyncRelationByMdmAsync(String loginUserId)  {
        log.info("字典关联同步mdm数据任务开始");
        long startTime = System.currentTimeMillis();
        //先查询表里面所有的关联数据
        Set<AsyncRelationDTO> allRelation = new HashSet<>(dMapper.queryOriginAllRelation(DictRelationConstants.NO_DELETE));
        Set<AsyncRelationDTO> targetRelation = new HashSet<>(dMapper.queryTargetAllRelation(DictRelationConstants.NO_DELETE));
        //获取全部关联数据并去重
        allRelation.addAll(targetRelation);
        Iterator<AsyncRelationDTO> relationDtoIterator = allRelation.iterator();
        //按照字典编号分組放入map中
        Set<String> dictCodeSet = new HashSet<>();
        //获取所有的字典编号
        while (relationDtoIterator.hasNext()) {
            AsyncRelationDTO relation = relationDtoIterator.next();
            dictCodeSet.add(relation.getDictCode());
        }
        //按照编号进行分组
        Map<String, List<AsyncRelationDTO>> relationMap = allRelation.stream().collect(Collectors.groupingBy(AsyncRelationDTO::getDictCode));
        //获取mdm的字典目录集合
        List<TermDictInfo> mdmAllDict = getDictInfo(DictRelationConstants.PAGE_INDEX, DictRelationConstants.PAGE_SIZE);
        List<String> mdmTermDict = new ArrayList<>();
        List<String> deleteDictCode = new ArrayList<>();
        for (TermDictInfo termDict : mdmAllDict) {
            mdmTermDict.add(termDict.getCode());
        }
        Set<String> allExistCode = new HashSet<>();
        Set<String> localExistCode = new HashSet<>();
        //迭代本地,取出mdm和本地都存在的编码
        for (String code : dictCodeSet) {
            if (mdmTermDict.contains(code)) {
                //如果mdm和本地都存在
                allExistCode.add(code);
            } else {
                localExistCode.add(code);
            }
        }
        Iterator<String> localExistIter = localExistCode.iterator();
        List<AsyncRelationDTO> deleteAllList = new ArrayList<>();
        List<AsyncRelationDTO> updateAllList = new ArrayList<>();
        //判断本地库存在的是被删除了还是被禁用了
        judgeEnableOrDelete(deleteDictCode, allExistCode, localExistIter);
        //如果存在需要已被删除的字典目录
        if(CollUtil.isNotEmpty(deleteDictCode)){
            //则将该字典目录所有的映射关系全部删除
            dMapper.deleteRelationByDirectoryId(dMapper.queryDeleteIdByCode(deleteDictCode));
        }
        Iterator<String> allExistIter = allExistCode.iterator();
        deleteAndUpdateDict(relationMap, allExistIter, deleteAllList, updateAllList);
        if (CollUtil.isNotEmpty(deleteAllList)) {
            dMapper.deleteRelationByIds(dMapper.queryDirectoryIdsByAsync(deleteAllList));
        }
        if (CollUtil.isNotEmpty(updateAllList)) {
            for (AsyncRelationDTO ralation : updateAllList) {
                List<String> originIds = dMapper.queryOriginIdByAsync(ralation);
                List<String> targetIds = dMapper.queryTargetIdByAsync(ralation);
                if (CollUtil.isNotEmpty(originIds)) {
                    dMapper.updateOriginRelationNameByAsync(ralation, originIds, loginUserId);
                }
                if (CollUtil.isNotEmpty(targetIds)) {
                    dMapper.updateTargetRelationNameByAsync(ralation, targetIds, loginUserId);
                }
            }

        }
        log.info("字典关联同步mdm数据任务结束,共用时:{}", System.currentTimeMillis() - startTime);
        return new AsyncResult<>("ok");

    }

    private void deleteAndUpdateDict(Map<String, List<AsyncRelationDTO>> relationMap, Iterator<String> allExistIter, List<AsyncRelationDTO> deleteAllList, List<AsyncRelationDTO> updateAllList) {
        while (allExistIter.hasNext()) {
            String allDictCode = allExistIter.next();
            SearchDictValueDTO sd = new SearchDictValueDTO();
            sd.setDictCode(allDictCode);
            //查询该字典目录下所有的字典值
            List<TermCodedValueInfo> valueInfos = queryTermCodeValueInfo(sd, DictRelationConstants.PAGE_INDEX, DictRelationConstants.TERM_DICT_PAGE_SIZE);
            //获取分组后的字典值
            List<AsyncRelationDTO> asyncRelationDtos = relationMap.get(allDictCode);
            if (CollUtil.isNotEmpty(asyncRelationDtos) && CollUtil.isNotEmpty(valueInfos)) {
                Map<String, String> localRelationMap = new HashMap<>();
                //遍历字典值
                for (TermCodedValueInfo param : valueInfos) {
                    localRelationMap.put(param.getCodedValue(), param.getDescription());
                }
                List<AsyncRelationDTO> deleteList = new ArrayList<>();
                List<AsyncRelationDTO> updateList = new ArrayList<>();
                for (AsyncRelationDTO value : asyncRelationDtos) {
                    //如果mdm没有该字典值，则本地应该删除
                    if (!localRelationMap.containsKey(value.getDictValueCode())) {
                        deleteList.add(value);
                    } else {
                        //如果mdm存在该字典值，判断名称是否跟数据库一致，如果不一致，加入修改集合
                        if (!localRelationMap.get(value.getDictValueCode()).equals(value.getDictValueName())) {
                            //参数设置为新的字典名称
                            value.setDictValueName(localRelationMap.get(value.getDictValueCode()));
                            updateList.add(value);
                        }
                    }
                }
                //加入删除集合
                deleteAllList.addAll(deleteList);
                //加入修改集合
                updateAllList.addAll(updateList);
            }
        }
    }

    private void judgeEnableOrDelete(List<String> deleteDictCode, Set<String> allExistCode, Iterator<String> localExistIter) {
        MdmPageRequest<TermDictInfoFilter> pageRequest = new MdmPageRequest<>();
        TermDictInfoFilter filter = new TermDictInfoFilter();
        //判断是删除了还是禁用了
        while (localExistIter.hasNext()) {
            //定义mdm查询条件
            String localCode = localExistIter.next();
            filter.setCode(localCode);
            filter.setEnabled(Integer.valueOf(DictRelationConstants.NO_DELETE));
            pageRequest.setFilter(filter);
            MdmPageData<TermDictInfo, TermDictInfoFilter> mdmData = termDictApi.getTermDictList(pageRequest);
            List<TermDictInfo> dictInfos = mdmData.getEntities();
            //如果可以获取到值，mdm只禁用没有被删除
            if (CollUtil.isNotEmpty(dictInfos)) {
                allExistCode.add(localCode);
            } else {
                deleteDictCode.add(localCode);
            }
        }
    }

    public List<TermDictInfo> getDictInfo(Integer pageNumber, Integer pageSize) {
        Long total = 1L;
        //获取得数目没有达到总条数时
        List<TermDictInfo> allCodeValues = new ArrayList<>();
        while (allCodeValues.size() < total) {
            //查询值域信息
            MdmPageData<TermDictInfo, TermDictInfoFilter> mdmData = getTermDictInfo(pageNumber, pageSize);
            //获取值域值
            List<TermDictInfo> codedValueInfos = mdmData.getEntities();
            //获取目标信息
            MdmPageRequest<TermDictInfoFilter> nextData = mdmData.getNext();
            //设置总条数
            total = nextData.getTotal();
            //添加数据到总数据
            allCodeValues.addAll(codedValueInfos);
            pageNumber++;
        }
        return allCodeValues;
    }

    public MdmPageData<TermDictInfo, TermDictInfoFilter> getTermDictInfo(Integer pageNumber, Integer pageSize) {
        //定义mdm查询条件
        MdmPageRequest<TermDictInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(pageNumber);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermDictInfoFilter filter = new TermDictInfoFilter();
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            return termDictApi.getTermDictList(pageRequest);
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }


    public List<TermCodedValueInfo> queryTermCodeValueInfo(SearchDictValueDTO sd, Integer pageNumber, Integer pageSize) {
        long startTime = System.currentTimeMillis();
        try {
            Long total = 1L;
            LinkedList<TermCodedValueInfo> allCodeValues = new LinkedList<>();
            //获取得数目没有达到总条数时
            while (allCodeValues.size() < total) {
                //查询值域信息
                MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = getMdmCodeInfo(sd, pageNumber, pageSize);
                //获取值域值
                List<TermCodedValueInfo> codedValueInfos = mdmData.getEntities();
                //获取目标信息
                MdmPageRequest<TermCodedValueInfoFilter> nextData = mdmData.getNext();
                //设置总条数
                total = nextData.getTotal();
                //添加数据到总数据
                allCodeValues.addAll(codedValueInfos);
                pageNumber++;
            }
            log.info("查询mdm数据共需要时间：{}", System.currentTimeMillis() - startTime);
            return allCodeValues;
        } catch (Exception e) {
            log.error("查询主数据平台字典编码信息异常：{}", e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    public MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> getMdmCodeInfo(SearchDictValueDTO sd, Integer pageIndex, Integer pageSize) {
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(pageIndex);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode(sd.getDictCode());
        filter.setCodedValue(sd.getDictValueCode());
        filter.setCodedValueDesc(sd.getDictValueName());
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            return termDictApi.getTermCodedValueList(pageRequest);
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }

}
