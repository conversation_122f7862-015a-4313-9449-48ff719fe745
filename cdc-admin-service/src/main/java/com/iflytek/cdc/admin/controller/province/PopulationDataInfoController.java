package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.PopulationDataHistoricalInfoQueryDTO;
import com.iflytek.cdc.admin.dto.TbCdcmrPopulationDataInfoQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrPopulationDataInfo;
import com.iflytek.cdc.admin.service.TbCdcmrPopulationDataInfoService;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.vo.PopulationDataInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "人口数据信息管理")
public class PopulationDataInfoController {

    @Resource
    private TbCdcmrPopulationDataInfoService tbCdcmrPopulationDataInfoService;

    @GetMapping("/{version}/pt/populationDataInfo/downloadTemplate")
    @ApiOperation("人口数据信息模板下载")
    public ResponseEntity<byte[]> downloadTemplate(@RequestParam String loginUserName){
        byte[] bytes = tbCdcmrPopulationDataInfoService.downloadTemplate(loginUserName);
        String fileName = Constants.POPULATION_DATA_INFO_FILENAME;
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileName);
        return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.CREATED);

    }

    @PostMapping("/{version}/pt/populationDataInfo/importByFile")
    @ApiOperation("人口数据信息导入")
    @Transactional(rollbackFor = Exception.class)
    public void importByFile(@RequestParam MultipartFile file, @RequestParam String loginUserId, @RequestParam String loginUserName) throws IOException {
        tbCdcmrPopulationDataInfoService.importByFile(file.getInputStream(), loginUserId, loginUserName);
    }

    @PostMapping("/{version}/pt/populationDataInfo/update")
    @ApiOperation("编辑")
    @Transactional(rollbackFor = Exception.class)
    public void update(@RequestBody TbCdcmrPopulationDataInfo input, @RequestParam String loginUserId){
        tbCdcmrPopulationDataInfoService.update(input, loginUserId);
    }

    @PostMapping("/{version}/pt/populationDataInfo/load")
    @ApiOperation("单个查询")
    public TbCdcmrPopulationDataInfo load(@RequestParam String id)  {
        return tbCdcmrPopulationDataInfoService.load(id);
    }

    @PostMapping("/{version}/pt/populationDataInfo/pageList")
    @ApiOperation("列表查询")
    public PageInfo<TbCdcmrPopulationDataInfo> pageList(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO, @RequestParam String loginUserName) throws IOException {
        return  tbCdcmrPopulationDataInfoService.pageList(queryDTO, loginUserName);
    }

    @PostMapping("/{version}/pt/populationDataInfo/listByAreaCodes")
    @ApiOperation("根据区域编码查询")
    public List<PopulationDataInfoVO> listByAreaCodes(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO)  {
        return  tbCdcmrPopulationDataInfoService.listByAreaCodes(queryDTO);
    }

    /**
     * 根据区域编码列表查询每个对应区域的数据
     */
    @PostMapping("/{version}/pt/populationDataInfo/listByEachAreaCode")
    @ApiOperation("根据区域编码列表查询每个对应区域的数据")
    public List<PopulationDataInfoVO> listByEachAreaCode(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO) {
        return tbCdcmrPopulationDataInfoService.listByEachAreaCode(queryDTO);
    }

    @PostMapping("/{version}/pt/populationDataInfo/groupByAreaCodes")
    @ApiOperation("根据区域编码查询分组")
    public Map<String, PopulationDataInfoVO> groupByAreaCodes(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO)  {
        return  tbCdcmrPopulationDataInfoService.groupByAreaCodes(queryDTO);
    }
    @PostMapping("/{version}/pt/populationDataInfo/statByAreaCodes")
    @ApiOperation("根据区域编码统计")
    public  PopulationDataInfoVO statByAreaCodes(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO)  {
        return  tbCdcmrPopulationDataInfoService.statByAreaCodes(queryDTO);
    }

    @PostMapping("/{version}/pt/populationDataInfo/listByAreaCodesAndTime")
    @ApiOperation("根据区域编码-时间段查询")
    public List<PopulationDataInfoVO> listByAreaCodesAndTime(@RequestBody TbCdcmrPopulationDataInfoQueryDTO queryDTO)  {
        return  tbCdcmrPopulationDataInfoService.listByAreaCodesAndTime(queryDTO);
    }

    /**
     * 查看历史统计日期的信息
     */
    @PostMapping("/{version}/pt/populationDataInfo/historicalStatisticsDate")
    @ApiOperation("查看历史统计日期的信息")
    public List<TbCdcmrPopulationDataInfo> searchHistoricalStatisticsDate(@RequestBody PopulationDataHistoricalInfoQueryDTO queryDTO, @RequestParam String loginUserName)  {
        return  tbCdcmrPopulationDataInfoService.searchHistoricalStatisticsDate(queryDTO, loginUserName);
    }
    /**
     * 编辑历史统计日期的信息
     */
    @PostMapping("/{version}/pt/populationDataInfo/editHistoricalStatisticsDate")
    @ApiOperation("编辑历史统计日期的信息")
    public void editHistoricalStatisticsDate(@RequestBody List<TbCdcmrPopulationDataInfo> inputs,  @RequestParam String loginUserId){
        tbCdcmrPopulationDataInfoService.updateHistoricalStatisticsDate(inputs, loginUserId);
    }
}
