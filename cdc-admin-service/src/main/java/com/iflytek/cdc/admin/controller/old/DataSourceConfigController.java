package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.DataSourceConfigVO;
import com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig;
import com.iflytek.cdc.admin.enums.DataSourceValueEnum;
import com.iflytek.cdc.admin.service.DataSourceConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "数据来源配置")
public class DataSourceConfigController {
    @Resource
    DataSourceConfigService dataSourceConfigService;

    @GetMapping("/pt/v1/dataSourceConfig/getByBusinessTypeAndSignalType")
    @ApiOperation("通过分类查询")
    TbCdcmrDataSourceConfig getByBusinessTypeAndSignalType(@RequestParam String businessType, @RequestParam(required = false) String signalType) {
        return dataSourceConfigService.getByBusinessTypeAndSignalType(businessType, signalType);
    }

    @GetMapping("/pt/v1/dataSourceConfig/listByBusinessType")
    @ApiOperation("通过业务分类查询")
    List<TbCdcmrDataSourceConfig> listByBusinessType(@RequestParam String businessType) {
        return dataSourceConfigService.listByBusinessType(businessType);
    }

    @GetMapping("/pt/v1/dataSourceConfig/getList")
    @ApiOperation("获取列表")
    List<DataSourceConfigVO> getList() {
        return dataSourceConfigService.getList();
    }


    @PostMapping("/pt/v1/dataSourceConfig/updateConfig")
    @ApiOperation("更新配置")
    void updateConfig(@RequestBody TbCdcmrDataSourceConfig record, @RequestParam String loginUserId) {
        record.setUpdateId(loginUserId);
        dataSourceConfigService.updateConfig(record);
    }

    @GetMapping("/pt/v1/dataSourceConfig/getDataSourceDict")
    @ApiOperation("查询dataSource字典")
    List<Map<String, String>> getDataSourceDict() {
        return DataSourceValueEnum.getEnumContent();
    }
}
