package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.mapper.DictMappingMapper;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientResponseException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/27 16:30
 **/
@Service
@Slf4j
public class MdmDataSyncServiceImpl implements MdmDataSyncService {

    private final TermDictApi termDictApi;

    private final DictMappingMapper dMapper;

    public MdmDataSyncServiceImpl(TermDictApi termDictApi, DictMappingMapper dMapper) {
        this.termDictApi = termDictApi;
        this.dMapper = dMapper;
    }


    @Override
    public List<TermCodedValueInfo> mdmDictCodeSync(String originCode, Integer pageNumber, Integer pageSize) {
        try {
            DictMappingInfo di = getMappingInfo(originCode);
            Long total = 1L;
            List<TermCodedValueInfo> allCodeValues = new ArrayList<>();
            //获取得数目没有达到总条数时
            while (allCodeValues.size() < total) {
                //查询值域信息
                MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = getMdmCodeInfo(di, pageNumber, pageSize);
                //获取值域值
                List<TermCodedValueInfo> codedValueInfos = mdmData.getEntities();
                //获取目标信息
                MdmPageRequest<TermCodedValueInfoFilter> nextData = mdmData.getNext();
                //设置总条数
                total = nextData.getTotal();
                //添加数据到总数据
                allCodeValues.addAll(codedValueInfos);
                pageNumber++;
            }
            return allCodeValues;
        } catch (Exception e) {
            log.error("查询主数据平台字典编码信息异常：{}", e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    /**
     * 获取映射编码
     *
     * @param originCode
     * @return DictMappingInfo
     **/
    @Override
    public DictMappingInfo getMappingInfo(String originCode) {
        //先根据定义编码查询映射的字典目录编码
        DictMappingInfo di = dMapper.queryTargetInfo(originCode);
        if (di == null) {
            log.error("未找到对应的编码映射信息：{}", originCode);
            throw new MedicalBusinessException("未找到编码映射信息,请先添加");
        }
        return di;
    }

    @Override
    public List<CascadeVO> getMdmDictResult(String dictCode, String keyword) {
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        int pageSize = 10;
        //定义查询页码
        pageRequest.setPageNumber(1);
        pageRequest.setPageSize(pageSize);
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode(dictCode);
        pageRequest.setFilter(filter);
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> tempResult = termDictApi.getTermCodedValueList(pageRequest);
        if (tempResult.getNext().getTotal() > pageSize) {
            pageRequest.setPageSize(tempResult.getNext().getTotal().intValue());
            tempResult = termDictApi.getTermCodedValueList(pageRequest);
        }
        return tempResult.getEntities().stream().filter(e ->
                e.getDescription().contains(keyword)
        ).map(e -> {
            CascadeVO vo = new CascadeVO();
            vo.setLabel(e.getDescription());
            vo.setValue(e.getCodedValue());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取mdm字典值域信息
     *
     * @param di
     * @param pageNumber
     * @param pageSize
     * @return
     **/
    @Override
    public MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> getMdmCodeInfo(DictMappingInfo di, Integer pageNumber, Integer pageSize, String... codeValue) {
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(pageNumber);
        pageRequest.setPageSize(pageSize);
        //定义过滤条件
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode(di.getTargetCode());
        //设置字典值域编码
        if (ArrayUtil.isNotEmpty(codeValue)) {
            filter.setCodedValue(codeValue[0]);
        }
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = termDictApi.getTermCodedValueList(pageRequest);
            if (mdmData == null || mdmData.getNext() == null || mdmData.getEntities() == null) {
                log.error("未获取mdm字典编码信息：{}", di.getTargetCode());
                throw new MedicalBusinessException("未查询到主数据平台字典编码信息");
            }
            return mdmData;
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }

    /**
     * 获取mdm字典值域信息
     *
     * @param di
     * @param codeValue
     * @return
     **/
    @Override
    public MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> getMdmCodeInfoByCode(DictMappingInfo di, String codeValue) {
        //定义mdm查询条件
        MdmPageRequest<TermCodedValueInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义查询页码
        pageRequest.setPageNumber(1);
        pageRequest.setPageSize(10);
        //定义过滤条件
        TermCodedValueInfoFilter filter = new TermCodedValueInfoFilter();
        filter.setDictCode(di.getTargetCode());
        //设置字典值域编码
        filter.setCodedValue(codeValue);
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = termDictApi.getTermCodedValueList(pageRequest);
            if (mdmData == null || mdmData.getNext() == null || mdmData.getEntities() == null) {
                log.error("未获取mdm字典编码信息：{}", di.getTargetCode());
                throw new MedicalBusinessException("未查询到主数据平台字典编码信息");
            }
            return mdmData;
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }

    @Override
    public MdmPageData<TermDictInfo, TermDictInfoFilter> getMdmTermDictInfo(String originCode) {
        DictMappingInfo di = getMappingInfo(originCode);
        //定义mdm查询条件
        MdmPageRequest<TermDictInfoFilter> pageRequest = new MdmPageRequest<>();
        //定义过滤条件
        TermDictInfoFilter filter = new TermDictInfoFilter();
        //定义字典编码
        filter.setCode(di.getTargetCode());
        pageRequest.setFilter(filter);
        //获取查询结果
        try {
            MdmPageData<TermDictInfo, TermDictInfoFilter> mdmData = termDictApi.getTermDictList(pageRequest);
            if (mdmData == null || mdmData.getNext() == null || mdmData.getEntities() == null) {
                log.error("未获取mdm字典编码信息：{}", di.getTargetCode());
                throw new MedicalBusinessException("未查询到主数据平台字典编码信息");
            }
            return mdmData;
        } catch (RestClientResponseException e) {
            log.error(e.getResponseBodyAsString(), e);
            throw new MedicalBusinessException("查询主数据平台字典数据异常");
        }
    }
}
