package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.constant.InfectedWarningRuleConstants;
import com.iflytek.cdc.admin.constant.MedicalStatisticConstants;
import com.iflytek.cdc.admin.model.mr.vo.ConstantsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "常量配置")
@RestController
public class ConstantController {

    @PostMapping("/pt/{version}/getConstants")
    @ApiOperation("常量获取")
    public ConstantsVO getConstants(){

        return ConstantsVO.instance;
    }

    @PostMapping("/pt/{version}/getMedicalStatisticConstants")
    @ApiOperation("病例统计常量获取")
    public Map<String, Object> getMedicalStatisticConstants(){
        Map<String, Object> constants = new HashMap<>();
        constants.put("addressType", MedicalStatisticConstants.AddressTypeEnum.values());
        constants.put("timeType", MedicalStatisticConstants.TimeTypeEnum.values());
        constants.put("infectClass", MedicalStatisticConstants.InfectClassEnum.values());
        constants.put("infectType", MedicalStatisticConstants.InfectTypeEnum.values());
        constants.put("infectTransmitType", MedicalStatisticConstants.InfectTransmitTypeEnum.values());
        constants.put("orgClass", MedicalStatisticConstants.OrgClassEnum.values());
        return constants;
    }
}
