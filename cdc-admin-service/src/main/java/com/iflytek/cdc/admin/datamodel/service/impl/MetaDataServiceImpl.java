package com.iflytek.cdc.admin.datamodel.service.impl;

import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableInfoMapper;
import com.iflytek.cdc.admin.datamodel.service.MetaDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class MetaDataServiceImpl implements MetaDataService {

    @Resource
    private TbCdcdmMetadataTableInfoMapper tbCdcdmMetadataTableInfoMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    @Override
    public List<TbCdcdmMetadataTableInfo> getAllTable(String tableName) {

        return tbCdcdmMetadataTableInfoMapper.getTableInfoByTableName(tableName);
    }

    @Override
    public List<TbCdcdmMetadataTableInfo> listByTableNames(List<String> tableNames) {
        return tbCdcdmMetadataTableInfoMapper.getTableInfoByTableNames(tableNames);
    }

    @Override
    public List<TbCdcdmMetadataTableColumnInfo> getFieldByTableName(String tableName, String columnName) {

        return tbCdcdmMetadataTableColumnInfoMapper.getFieldByTableName(tableName, columnName);
    }

    @Override
    public List<TbCdcdmMetadataTableColumnInfo> getFieldByTableNames(List<String> tableNames) {
        return tbCdcdmMetadataTableColumnInfoMapper.getFieldByTableNames(tableNames);
    }
}
