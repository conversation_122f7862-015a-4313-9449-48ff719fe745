package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.enums.SyndromeCaseScopeEnum;
import com.iflytek.cdc.admin.enums.SyndromeWarningMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

@ApiModel("新发突发传染病预警规则常量")
@Data
public class EmergingWarningRuleConstants {

    private static final EmergingWarningRuleConstants instance = new EmergingWarningRuleConstants();

    @ApiModelProperty("监测病原规则类型")
    private Object ruleType = PathogenRuleTypeEnum.values();

    public static EmergingWarningRuleConstants getInstance(){
        return instance;
    }

    /**
     * 监测病原规则类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum PathogenRuleTypeEnum {
        /**
         * 核酸检测结果
         */
        NUCLEIC_ACID_RESULT("0", "核酸检测结果"),
        /**
         * 流感病毒检测结果
         */
        FLU_VIRUS_RESULT("1", "流感病毒检测结果"),
        /**
         * 冠状病毒检测结果
         */
        COR_VIRUS_RESULT("2", "冠状病毒检测结果"),
        /**
         * 腺病毒检测结果
         */
        ADEN_VIRUS_RESULT("3", "腺病毒检测结果"),
        /**
         * 流感嗜血杆菌检测结果
         */
        FLU_OBLIGATORY_SPORANGIA("4", "流感嗜血杆菌检测结果"),
        /**
         * 百日咳杆菌检测结果
         */
        COX_1BACTERIA("5", "百日咳杆菌检测结果"),

        ;
        private String value;
        private String label;

        PathogenRuleTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }

    }

}
