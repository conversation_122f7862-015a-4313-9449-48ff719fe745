package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.UploadExcelColumn;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.UapOrgConstants;
import com.iflytek.cdc.admin.constant.UidTableName;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.enums.FzMrOrgTypeMappingEnum;
import com.iflytek.cdc.admin.mapper.OrgMappingMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrFzUapOrganizationMapper;
import com.iflytek.cdc.admin.sdk.entity.HisInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.sdk.pojo.UapOrgInfo;
import com.iflytek.cdc.admin.service.OrgMappingService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.cdc.admin.util.BeanCombineUtil;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 机构映射实体类
 *
 * <AUTHOR>
 * @date 2021/6/30 10:56
 **/

@Slf4j
@Service
public class OrgMappingServiceImpl implements OrgMappingService {

    private final OrgMappingMapper oMapper;

    private final BatchUidService batchUidService;

    private final UapUserApi uapUserApi;

    private final UapOrgService uapOrgService;

    private final RedisTemplate redisTemplate;

    @Resource
    private ParamConfigService paramConfigService;

    public OrgMappingServiceImpl(OrgMappingMapper oMapper, BatchUidService batchUidService, UapUserApi uapUserApi, UapOrgService uapOrgService, RedisTemplate redisTemplate) {
        this.oMapper = oMapper;
        this.batchUidService = batchUidService;
        this.uapUserApi = uapUserApi;
        this.uapOrgService = uapOrgService;
        this.redisTemplate = redisTemplate;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ExcelUploadResult readUploadHisOrg(MultipartFile file, String loginUserId) {
        //判断文件大小
        if (file.getSize() > 2 * 1024 * 1024) {
            log.error("上传的his机构文件太大");
            throw new MedicalBusinessException("上传的文件大小不能超过2M");
        }
        //判断文件后缀是否满足上传条件 -- 避免直接调用后端接口出现能上传任意文件类型
        if(!FileUtils.isSatisfyRule(file, Constants.UPLOAD_FORM_FILE_TYPE)){
            log.error("上传文件类型不符");
            throw new MedicalBusinessException("上传文件类型不符");
        }
        //验证通过的数据
        List<HisOrg> hisOrgSuccessList = new ArrayList<>();
        //验证不通过的数据
        List<HisOrg> hisOrgFailList = new ArrayList<>();
        ExcelUploadResult uploadResult = new ExcelUploadResult();
        try {
            //获取excel
            XSSFWorkbook wb = XSSFWorkbookFactory.createWorkbook(file.getInputStream());
            //默认读取第一个sheet页
            XSSFSheet sheet = wb.getSheetAt(0);
            //获取sheet行数
            int rowNum = sheet.getPhysicalNumberOfRows();
            if (rowNum <= 1) {
                log.error("his机构数据导入文件为空");
                throw new MedicalBusinessException("上传的文件为空");
            }
            //读取excel内容
            List<HisOrg> hisOrgAllList = readExcelContent(new HisOrg(), sheet, rowNum);
            //验证excel内容
            for (HisOrg hisBean : hisOrgAllList) {
                //如果验证通过
                if (validExcelContent(hisBean)) {
                    hisOrgSuccessList.add(hisBean);
                } else {
                    //验证失败
                    hisOrgFailList.add(hisBean);
                }
            }
            //插入或修改数据
            insertHisOrg(hisOrgSuccessList, hisOrgFailList, loginUserId);
            //设置上传总条数
            uploadResult.setAllNum(hisOrgAllList.size());
            //上传成功总条数
            uploadResult.setSuccessNum(hisOrgSuccessList.size());
            //失败总条数
            uploadResult.setFailNum(hisOrgFailList.size());
            //失败案例下载url
            uploadResult.setHisOrgFailList(hisOrgFailList);
        } catch (Exception e) {
            log.error("读取上传的excel内容异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
        return uploadResult;
    }

    @Override
    public void deleteHisOrg(String hisOrgId) {
        try {
            oMapper.deleteByOrgId(hisOrgId);
            log.info("his机构数据删除成功：{}", hisOrgId);
        } catch (Exception e) {
            log.error("his机构数据删除异常：{}{}", hisOrgId, e);
            throw new MedicalBusinessException("his机构删除失败");
        }

    }

    /**
     * 插入数据
     *
     * @param hisOrgSuccessList, hisOrgFailList
     **/
    public void insertHisOrg(List<HisOrg> hisOrgSuccessList, List<HisOrg> hisOrgFailList, String loginUserId) {
        try {
            TUapUser userCache = getUapUser(loginUserId);
            //数据分组，根据来源厂商和机构编码去除重复数据
            Map<String, List<HisOrg>> hisOrgMap = hisOrgSuccessList.stream().collect(Collectors.groupingBy(OrgMappingServiceImpl::getGroupCode));
            for (Map.Entry<String, List<HisOrg>> org : hisOrgMap.entrySet()) {
                if (org.getValue().size() > 1) {
                    List<HisOrg> repeatList = org.getValue();
                    hisOrgSuccessList.removeAll(repeatList);
                    hisOrgSuccessList.add(repeatList.get(0));
                    org.getValue().remove(repeatList.get(0));
                    hisOrgFailList.addAll(repeatList);
                }
            }
            for (HisOrg hisOrg : hisOrgSuccessList) {
                //分批插入数据
                List<HisOrg> orgList = oMapper.findHisOrgByCode(hisOrg.getHisOrgCode(), hisOrg.getSourceId());
                //设置为未匹配
                hisOrg.setIsMatch(UapOrgConstants.NO_MATCH);
                //设置创建人和修改人
                if (StringUtils.isBlank(hisOrg.getCreator())) {
                    hisOrg.setCreator(userCache.getId());
                }
                hisOrg.setUpdator(userCache.getId());
                //判断his机构数据是否存在
                if (CollectionUtils.isEmpty(orgList)) {
                    //如果不存在则新增
                    hisOrg.setId(String.valueOf(batchUidService.getUid(UidTableName.HIS_ORG_TABLE)));
                    oMapper.insertHisOrg(hisOrg);
                } else {
                    //如果存在则修改
                    hisOrg.setId(orgList.get(0).getId());
                    oMapper.updateHisOrg(hisOrg);
                }

            }
        } catch (Exception e) {
            log.error("插入his机构数据异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }

    }

    private TUapUser getUapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        if (userCache == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return userCache;
    }

    public static String getGroupCode(HisOrg hisOrg) {
        return hisOrg.getHisOrgCode() + "#" + hisOrg.getSourceId();
    }

    @Override
    public PageInfo<UapOrganization> queryUapOrg(SearchHisOrgDTO searchOrgDTO, String loginUserId) {
        //根据his机构code查询映射表是否存在数据
        String uapOrgId = "";
        try {
            uapOrgId = oMapper.queryUapIdByHisCode(searchOrgDTO.getOrgCode(), searchOrgDTO.getSourceId());
        } catch (Exception e) {
            log.error("查询映射uap机构数据异常：{}{}", searchOrgDTO.getOrgCode(), e);
            throw new MedicalBusinessException("存在重复映射机构数据，请检查！");
        }
        //如果不存在，查询全部未匹配的机构数据
        if (StringUtils.isBlank(uapOrgId)) {
            List<String> downIds = uapOrgService.queryDownOrgId(loginUserId);
            searchOrgDTO.setDownIds(downIds);
            searchOrgDTO.setIsMatch(UapOrgConstants.NO_MATCH);
            PageHelper.startPage(searchOrgDTO.getPageIndex(), searchOrgDTO.getPageSize());
            return new PageInfo<>(oMapper.queryUapOrgList(searchOrgDTO));
        } else {
            //如果存在 查询与之匹配的uap机构数据
            return new PageInfo<>(oMapper.queryMatchUapOrg(uapOrgId));
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void unbindOrgMatch(MatchOrgDTO mo, String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = getUapUser(loginUserId);
        //his机构主键
        List<String> hisOrgIds = new ArrayList<>();
        hisOrgIds.add(mo.getHisOrgId());
        //uap主键机构
        List<String> uapOrgIds = new ArrayList<>();
        uapOrgIds.add(mo.getUapOrgId());
        UpdateOrgDTO ud = new UpdateOrgDTO();
        //设置状态为未匹配
        ud.setIsMatch(UapOrgConstants.NO_MATCH);
        ud.setUpdator(userCache.getId());
        try {
            //删除匹配数据
            oMapper.deleteByUapOrgId(mo.getUapOrgId());
            //删除redis中存在的映射关系数据
            //根据hisId查询his机构详细信息
            HisOrg hisOrg = oMapper.queryHisOrgInfoById(mo.getHisOrgId());
            redisTemplate.delete(Constants.REDIS_KEY + hisOrg.getSourceId() + "_" + hisOrg.getHisOrgCode());
            ud.setIds(hisOrgIds);
            //更改匹配的his机构数据的状态
            oMapper.updateHisMatchStatus(ud);
            //更改匹配的uap机构数据的状态
            ud.setIds(uapOrgIds);
            oMapper.updateUapMatchStatus(ud);
        } catch (Exception e) {
            log.error("机构解绑异常：{}{}", mo.getUapOrgId(), e);
            throw new MedicalBusinessException("机构解绑异常");
        }

    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public OrgMatchResultDTO matchOrg(MatchOrgDTO mo, String loginUserId) {
        OrgMatchResultDTO result = new OrgMatchResultDTO();
        //获取登录人员信息
        TUapUser userCache = getUapUser(loginUserId);
        //获取映射数据
        List<OrgMapping> orgList = getOrgMappingsList(mo, loginUserId);
        //匹配的his主键机构集合
        List<String> hisOrgIds = new ArrayList<>();
        //匹配的uap主键机构集合
        List<String> uapOrgIds = new ArrayList<>();
        UpdateOrgDTO ud = new UpdateOrgDTO();
        //设置状态为已匹配
        ud.setIsMatch(UapOrgConstants.YES_MATCH);
        if (CollUtil.isNotEmpty(orgList)) {
            for (OrgMapping org : orgList) {
                //设置主键
                org.setId(String.valueOf(batchUidService.getUid(UidTableName.ORG_MAPPING_TABLE)));
                org.setCreateUser(userCache.getId());
                org.setUpdateUser(userCache.getId());
                //添加匹配的id集合
                hisOrgIds.add(org.getHisId());
                uapOrgIds.add(org.getUapId());
            }
            ud.setUpdator(userCache.getId());
            //插入匹配数据
            oMapper.insertOrgMapper(orgList);
            //将映射数据放入到redis里面
            if (CollUtil.isNotEmpty(orgList)) {
                for (OrgMapping orgM : orgList) {
                    StringBuilder sb = new StringBuilder();
                    //组装key
                    sb.append(Constants.REDIS_KEY);
                    sb.append(orgM.getSourceCode()).append("_").append(orgM.getSourceOrgCode());
                    //数据放入
                    redisTemplate.opsForValue().set(sb.toString(), orgM);
                }
            }
            ud.setIds(hisOrgIds);
            //更改匹配的his机构数据的状态
            oMapper.updateHisMatchStatus(ud);
            //更改匹配的uap机构数据的状态
            ud.setIds(uapOrgIds);
            oMapper.updateUapMatchStatus(ud);
        }
        result.setMsg("共匹配到" + orgList.size() + "条数据");
        return result;
    }

    private List<OrgMapping> getOrgMappingsList(MatchOrgDTO mo, String loginUserId) {
        switch (mo.getMatchFlag()) {
            //如果是一键匹配
            case Constants.MATCH_ALL:
                //查询匹配的机构
                UapOrgMatchDTO ud = new UapOrgMatchDTO();
                ud.setIsMatch(UapOrgConstants.NO_MATCH);
                ud.setDownIds(uapOrgService.queryDownOrgId(loginUserId));
                List<OrgMapping> orgList = oMapper.queryOrgList(ud);
                formatMatchAllList(orgList);
                return orgList;
            //单个匹配
            case Constants.MATCH_SINGLE:
                //根据id查询机构信息
                List<OrgMapping> uapInfo = oMapper.queryOrgListById(mo.getUapOrgId());
                List<OrgMapping> hisInfo = oMapper.queryHisOrgListById(mo.getHisOrgId());
                //判断机构映射关系是否已经存在
                if (UapOrgConstants.YES_MATCH.equals(uapInfo.get(0).getIsMatch()) || UapOrgConstants.YES_MATCH.equals(hisInfo.get(0).getIsMatch())) {
                    log.error("机构映射关系已存在：{}", mo.getUapOrgId() + mo.getHisOrgId());
                    throw new MedicalBusinessException("机构已映射，请先解绑");
                }
                orgList = new ArrayList<>();
                orgList.add(BeanCombineUtil.combineCore(uapInfo.get(0), hisInfo.get(0)));
                return orgList;
            default:
                return new ArrayList<>();
        }
    }

    private void formatMatchAllList(List<OrgMapping> orgList) {
        if (CollUtil.isNotEmpty(orgList)) {
            //如果his机构表里面有重复名称的数据，根据uapId进行分组
            Map<String, List<OrgMapping>> orgMappingMap = orgList.stream().collect(Collectors.groupingBy(OrgMapping::getSysOrgId));
            for (Map.Entry<String, List<OrgMapping>> orgMapping : orgMappingMap.entrySet()) {
                //如果有重复数据
                if (orgMapping.getValue().size() > 1) {
                    //删除重复数据
                    List<OrgMapping> repeatList = orgMapping.getValue();
                    orgList.removeAll(repeatList);
                }
            }
            //如果uap机构存在重复机构名称的数据，根据hisId分组
            Map<String, List<OrgMapping>> orgHisMappingMap = orgList.stream().collect(Collectors.groupingBy(OrgMapping::getHisId));
            for (Map.Entry<String, List<OrgMapping>> orgHisMapping : orgHisMappingMap.entrySet()) {
                //如果有重复数据
                if (orgHisMapping.getValue().size() > 1) {
                    //删除重复数据
                    List<OrgMapping> repeatList = orgHisMapping.getValue();
                    orgList.removeAll(repeatList);
                }
            }
        }
    }

    @Override
    public void exportFail(List<HisOrg> hisOrgFailList, HttpServletResponse response) {
        XSSFWorkbook wb = null;
        OutputStream out = null;

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(hisOrgFailList);
        try {
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + Constants.FAIL_EXCEL_NAME);
            //获取excel模板存在的路径
            InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream(Constants.EXCEL_TEMPLATE_PATH);
            //生成工作簿
            if (is != null) {
                wb = new XSSFWorkbook(is);
                //内容map
                Map<String, Object> fileContentMap = new HashMap<>(20);
                List<String> uploadFileName = new ArrayList<>();
                //反射获取需要导出的字段名称
                extracted(new HisOrg(), fileContentMap, uploadFileName);
                XSSFSheet sheet = wb.getSheetAt(0);
                //填充excel内容
                for (int i = 1; i <= hisOrgFailList.size(); i++) {
                    XSSFRow row = sheet.createRow(i);
                    for (int j = 0; j < uploadFileName.size(); j++) {
                        //获取每一行的单元格
                        XSSFCell cell = row.createCell(j);
                        if ((int) fileContentMap.get(uploadFileName.get(j)) == j) {
                            StringBuilder sb = new StringBuilder();
                            //反射get方法获取字段的值
                            sb.append("get").append(uploadFileName.get(j).substring(0, 1).toUpperCase()).append(uploadFileName.get(j).substring(1));
                            Method method = hisOrgFailList.get(i - 1).getClass().getMethod(sb.toString());
                            Object fieldValue = method.invoke(hisOrgFailList.get(i - 1));
                            //填充内容
                            if (fieldValue != null) {
                                //设置单元的值
                                cell.setCellValue(fieldValue.toString());
                            }
                        }
                    }
                }
                out = response.getOutputStream();
                wb.write(out);
                out.flush();
            }
        } catch (Exception e) {
            log.error("生成失败案例excel错误", e);
            throw new MedicalBusinessException("失败案例下载异常");
        } finally {
            try {
                if (wb != null) {
                    wb.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 读取上传的excel的内容
     *
     * @param hisOrg, sheet, rowNum
     * @return List<HisOrg>
     **/
    public List<HisOrg> readExcelContent(HisOrg hisOrg, XSSFSheet sheet, int rowNum) {
        //反射获取字段数组
        Map<String, Object> fileContentMap = new HashMap<>(20);
        List<String> uploadFileName = new ArrayList<>();
        //获取导入的字段名称和顺序
        extracted(hisOrg, fileContentMap, uploadFileName);
        List<HisOrg> excelContList = new ArrayList<>();
        //将excel内容通过排序填充到map中
        for (int i = 1; i < rowNum; i++) {
            HisOrg hisContent = new HisOrg();
            Map<String, Object> map = new HashMap<>(20);
            //获取行
            XSSFRow row = sheet.getRow(i);
            for (int j = 0; j < uploadFileName.size(); j++) {
                //获取每一行的单元格
                XSSFCell cell = row.getCell(j);
                if (cell != null) {
                    if (cell.getCellType().equals(CellType.STRING)) {
                        cell.setCellType(CellType.STRING);
                        //如果单元格顺序和字段排序一样，填充map
                        if ((int) fileContentMap.get(uploadFileName.get(j)) == j) {
                            //填充字段名和单元格内容
                            map.put(uploadFileName.get(j), cell.getStringCellValue());
                        }
                    }
                }
            }
            //将map转换成bean
            BeanUtil.fillBeanWithMap(map, hisContent, false);
            excelContList.add(hisContent);
        }
        return excelContList;
    }

    /**
     * 判断字段是否有excel中需要导入的内容
     **/
    private void extracted(HisOrg hisOrg, Map<String, Object> fileContentMap, List<String> uploadFileName) {
        Field[] fields = hisOrg.getClass().getDeclaredFields();
        //获取上传的excel字段
        for (Field field : fields) {
            //判断是否有该注解
            boolean annotationPresent = field.isAnnotationPresent(UploadExcelColumn.class);
            if (annotationPresent) {
                //将字段的名称和排序放入到map中
                fileContentMap.put(field.getName(), field.getAnnotation(UploadExcelColumn.class).sort());
                uploadFileName.add(field.getName());
            }
        }
    }

    /**
     * 验证上传的机构数据
     *
     * @param hisOrg
     * @return boolean
     **/
    public Boolean validExcelContent(HisOrg hisOrg) {
        try {
            //生成验证工具类
            Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
            //字段验证获取结果
            Set<ConstraintViolation<HisOrg>> set = validator.validate(hisOrg, Default.class);
            //如果结果为空，则验证通过
            return CollectionUtils.isEmpty(set);
        } catch (Exception e) {
            log.error("验证数据格式内容异常", e);
            throw new MedicalBusinessException("数据导入异常");
        }
    }

    @Override
    public PageInfo<HisOrg> queryHisOrg(SearchHisOrgDTO hisOrgDTO) {
        PageHelper.startPage(hisOrgDTO.getPageIndex(), hisOrgDTO.getPageSize());
        return new PageInfo<>(oMapper.queryHisOrgList(hisOrgDTO));
    }

    @Override
    public UapOrgInfo queryUapOrgByHis(HisInfoFilter filter) {
        if (StringUtils.isBlank(filter.getHisOrgCode())) {
            throw new MedicalBusinessException("his机构编号不能为空");
        }
        if (StringUtils.isBlank(filter.getSourceId())) {
            throw new MedicalBusinessException("his厂商id不能为空");
        }
        UapOrgInfo uapOrgInfo = new UapOrgInfo();
        //先查询redis数据
        String configKey = Constants.REDIS_KEY + filter.getSourceId() + "_" + filter.getHisOrgCode();
        Object redisResult = redisTemplate.opsForValue().get(configKey);
        try {
            if (redisResult != null) {
                uapOrgInfo = JSONUtil.toBean(JSONUtil.toJsonStr(redisResult), UapOrgInfo.class);
            } else {
                //如果redis不存在，则查询数据库
                uapOrgInfo = oMapper.queryUapByHis(filter);
                if (uapOrgInfo != null) {
                    //将数据再放入redis里面
                    redisTemplate.opsForValue().set(Constants.REDIS_KEY + filter.getSourceId() + "_" + filter.getHisOrgCode(), uapOrgInfo);
                }
            }
        } catch (Exception e) {
            log.error("通过his查询uap信息异常:{}", e);
        }
        return uapOrgInfo == null ? new UapOrgInfo() : uapOrgInfo;
    }

    @Override
    public HisOrgInfo queryHisOrgByUap(String uapId) {
        if (StringUtils.isBlank(uapId)) {
            throw new MedicalBusinessException("uapId不能为空");
        }
        HisOrgInfo hisOrgInfos = new HisOrgInfo();
        try {
            hisOrgInfos = oMapper.queryHisByUap(uapId);
            if (hisOrgInfos == null) {
                return new HisOrgInfo();
            }
        } catch (Exception e) {
            log.error("通过uap查询His信息异常:{}", e);
        }
        return hisOrgInfos;
    }



}