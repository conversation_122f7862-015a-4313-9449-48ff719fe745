package com.iflytek.cdc.admin.apiservice;

import com.google.common.reflect.TypeToken;
import com.iflytek.cdc.admin.common.apiservice.ApiService;
import com.iflytek.cdc.admin.dto.epi.EmergencyPlanRequest;
import com.iflytek.cdc.admin.dto.epi.AreaDTO;
import com.iflytek.cdc.admin.util.CdcJsonUtils;
import com.iflytek.cdc.admin.vo.epi.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EpiServiceProApi {

    @Value("${cdc-epi-invest-service:http://cdc-ecd-platform-service/v1/pt}")
    private String epiServiceUrl;

    @Resource
    private ApiService apiService;

    private  final RestTemplate template = new RestTemplate();



    /**
     * 获取研判角色列表
     */
    public List<DictVO> getExpertTypeList(){

        String url =  epiServiceUrl + "/interfaceApi/getExpertTypeList";
        EpiResponseBaseVO<List<DictVO>> responseBaseVO = CdcJsonUtils.toBean(apiService.doGet(url, EpiResponseBaseVO.class),
                new TypeToken<EpiResponseBaseVO<List<DictVO>>>(){}.getType());
        return responseBaseVO.getDataOrThrow();
    }

    /**
     * 获取研判专家列表
     */
    public List<EcdOrgUserVO> getExpertList(List<String> expertCategories){
        if (expertCategories == null){
            expertCategories = new ArrayList<>();
        }
        String url = epiServiceUrl + "/interfaceApi/getExpertList?expertCategoryList=" + String.join(",", expertCategories);
        EpiResponseBaseVO<List<EcdOrgUserVO>> responseBaseVO = CdcJsonUtils.toBean(apiService.doGet(url, EpiResponseBaseVO.class),
                new TypeToken<EpiResponseBaseVO<List<EcdOrgUserVO>>>(){}.getType()) ;
        return responseBaseVO.getDataOrThrow();
    }

    /**
     * 获取应急值班人员
     */
    public List<DutyUserVO> getDutyUserList(List<AreaDTO> areaDTOS){

        Map<String , Object> requestBody = new HashMap<>();
        requestBody.put("list", areaDTOS);
        String url =  epiServiceUrl + "/interfaceApi/getDutyUserList";
        EpiResponseBaseVO<List<DutyUserVO>> responseBaseVO = CdcJsonUtils.toBean(apiService.doPost(url, requestBody, EpiResponseBaseVO.class),
                new TypeToken<EpiResponseBaseVO<List<DutyUserVO>>>(){}.getType() );
        return responseBaseVO.getDataOrThrow();

    }

    /**
     * 获取应急预案
     */
    public List<EmergencyPlanVO> getEmergencyPlan(EmergencyPlanRequest request){

        String url =  epiServiceUrl + "/interfaceApi/getEmergencyPlan";
        EpiResponseBaseVO<List<EmergencyPlanVO>> responseBaseVO = CdcJsonUtils.toBean(apiService.doPost(url, request, EpiResponseBaseVO.class),
                new TypeToken<EpiResponseBaseVO<List<EmergencyPlanVO>>>(){}.getType() );
        return responseBaseVO.getDataOrThrow();

    }

}
