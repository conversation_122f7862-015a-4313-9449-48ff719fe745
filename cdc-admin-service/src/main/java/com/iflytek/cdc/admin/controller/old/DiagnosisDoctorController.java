package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.service.DiagnosisDoctorService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@Api(tags = "诊疗医生维护")
public class DiagnosisDoctorController {


    @Resource
    DiagnosisDoctorService diagnosisDoctorService;

    @PostMapping("/pt/v1/diagnosisDoctor/add")
    @ApiOperation("新增")
    public void add(@RequestBody DiagnosisDoctorVO vo, @RequestParam String loginUserName) {
        diagnosisDoctorService.add(vo, loginUserName);

    }

    @PostMapping("/pt/v1/diagnosisDoctor/update")
    @ApiOperation("更新")
    public void update(@RequestBody DiagnosisDoctorVO vo, @RequestParam String loginUserName) {
        diagnosisDoctorService.update(vo, loginUserName);

    }

    @PostMapping("/pt/v1/diagnosisDoctor/delete")
    @ApiOperation("删除")
    public void delete(@RequestParam String id, @RequestParam String loginUserId, @RequestParam String loginUserName) {
        diagnosisDoctorService.delete(id, loginUserId, loginUserName);
    }

    @PostMapping("/pt/v1/diagnosisDoctor/updateStatus")
    @ApiOperation("启用/禁用")
    public void updateStatus(@RequestParam String id,
                             @RequestParam Integer status,
                             @RequestParam String loginUserId,
                             @RequestParam String loginUserName) {
        diagnosisDoctorService.updateStatus(id, status, loginUserId, loginUserName);
    }

    @PostMapping("/pt/v1/diagnosisDoctor/list")
    @ApiOperation("查询列表")
    public PageData<DiagnosisDoctorVO> list(@RequestParam String loginUserName, @RequestBody DiagnosisDoctorQueryVO queryVO) {
        return diagnosisDoctorService.list(loginUserName, queryVO);
    }

    @GetMapping(value = "pt/v1/diagnosisDoctor/subOrgList")
    @ApiOperation("下属机构列表")
    public List<TbCdcewOrganizationInfo> getSubOrgList(String loginUserName) {
        return diagnosisDoctorService.getSubOrgList(loginUserName);
    }


    @PostMapping("/pt/v1/diagnosisDoctor/downloadTemplate")
    @ApiOperation("下载导入模板")
    @LogExportAnnotation
    public ResponseEntity<byte[]> downloadTemplate(@RequestParam String loginUserName, @RequestParam String loginUserId) {
        byte[] bytes = diagnosisDoctorService.downloadTemplate(loginUserName,loginUserId);
        String fileName = "诊疗医生批量导入模板" + ".xlsx";
        HttpHeaders httpHeaders = new HttpHeaders();
        String attachment = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        httpHeaders.setContentDispositionFormData("attachment", attachment);
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(bytes, httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping(value = "/pt/v1/diagnosisDoctor/uploadFile")
    @ApiOperation("上传导入excel")
    public UploadResultVO uploadFile(@RequestParam MultipartFile file) {

        try {
            return diagnosisDoctorService.uploadFile(file);
        } catch (IOException e) {
            throw new MedicalBusinessException("上传文件错误");
        }
    }

    @PostMapping(value = "/pt/v1/diagnosisDoctor/batchAdd")
    @ApiOperation("将已上传的excel的内容导入到业务库")
    public UploadResultVO batchAdd(@RequestParam String attachmentId, @RequestParam String loginUserName) {
        return diagnosisDoctorService.batchAdd(attachmentId, loginUserName);
    }

    @GetMapping(value = "/pt/v1/org/getOrgListForBusinessPerson")
    @ApiOperation("查询人员信息维护所需的机构列表")
    public Object getOrgListForBusinessPerson(@RequestParam String loginUserName) {
        return diagnosisDoctorService.getOrgListForBusinessPerson(loginUserName);
    }

    @GetMapping("/pt/v1/file/download")
    @ApiOperation("下载文件")
    @LogExportAnnotation
    public ResponseEntity<byte[]> outCallDownload(@RequestParam(value = "id") String id,
                                                  @RequestParam(value = "loginUserId") String loginUserId) {

        return diagnosisDoctorService.downloadFile(id, loginUserId);
    }
}
