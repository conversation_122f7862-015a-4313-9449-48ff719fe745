package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.service.MedicalWarnService;
import com.iflytek.cdc.admin.service.mdm.MdmDataSyncExecute;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName SyncMdmDataMediaRule
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/18 14:35
 * @Version 1.0
 */

@Service("syncMdmDataMediaRule")
public  class SyncMdmDataMediaRuleImpl implements MdmDataSyncExecute {

    @Resource
    private  MedicalWarnService medicalWarnService;

    @Override
    public ResponseResult executeSyncMdmData(String code, String loginUserId, boolean flag) {
      return  medicalWarnService.syncMdmData(code,loginUserId);
    }


}
