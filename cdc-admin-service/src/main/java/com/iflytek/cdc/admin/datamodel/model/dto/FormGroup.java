package com.iflytek.cdc.admin.datamodel.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class FormGroup {
    private String type;
    private String field;
    private String title;
    private int repeat;
    private int max;
    private Props props;

    

    @Data
    public static class Props {
        private int max;
        private List<Rule> rules;

        
    }

    @Data
    public static class Rule {
        private String placeholder;
        private String field;
        private String title;
        private String type;
        //字段是否只读
        private boolean disabled;
        //字段是否必填
        private boolean required;
        private String maxLength;
        private List<String> databaseField;
        private DatabaseOption databaseOption;
        private List<String> databaseName;

        //字段值域选择
        private String optionField;

        //字段配置
        private String props;
        
    }

    @Data
    public static class DatabaseOption {
        private String value;
        private String label;

        
    }
}