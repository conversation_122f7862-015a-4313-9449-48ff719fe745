package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.WarningGradeEmergencyPlanVO;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeEmergencyPlan;

import java.util.List;

public interface WarningGradeEmergencyPlanService {
    List<WarningGradeEmergencyPlanVO> getByConfigId(String configId);

    void updateWarningGradeEmergencyPlan(List<TbCdcmrWarningGradeEmergencyPlan> planList, String loginUserId, String configId);

    List<TbCdcAttachment> getByTypeAndDisease(String configType, String diseaseCode);
}
