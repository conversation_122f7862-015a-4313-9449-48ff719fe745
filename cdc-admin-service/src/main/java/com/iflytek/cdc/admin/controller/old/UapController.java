package com.iflytek.cdc.admin.controller.old;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.SearchSyndromeInfoDTO;
import com.iflytek.cdc.admin.entity.SyndromeInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/2/28 9:26
 */
@RestController
@Api(tags = "uap调用服务")
@RefreshScope
@Slf4j
public class UapController {

    @Value("${iflyData.skipIp:http://*************:8670/uap-sso/login?uapService=http://*************:8181/dbcenter/index.do&token=}")
    String iflyDataIp;
    @GetMapping("/{version}/pb/uap/skipToIflyData")
    @ApiOperation("跳转")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void querySyndromeInfoPage(@PathVariable String version, @RequestParam String Authorization, HttpServletResponse resp) throws IOException {
        log.info("Authorization:"+Authorization);
        String url = iflyDataIp+ StrUtil.replace(Authorization,"ST ","") ;
        log.info("url:"+url);
        resp.sendRedirect(url);
    }

}
