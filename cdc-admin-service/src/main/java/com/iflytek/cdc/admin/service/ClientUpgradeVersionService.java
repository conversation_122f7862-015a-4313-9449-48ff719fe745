package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.sdk.entity.ClientUpgradeFilter;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionParam;
import com.iflytek.cdc.admin.sdk.pojo.PackageVersionVO;

import java.util.List;

/**
 * @ClassName ClientUpgradeVersionService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/2 15:24
 * @Version 1.0
 */
public interface ClientUpgradeVersionService {

    /**
     * 分页查询版本配置信息根据版本维度和机构维度
     * @param clientUpgradeParamDto
     * @param loginUserId
     * @return
     */
    PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDto(ClientUpgradeParamDto clientUpgradeParamDto,String loginUserId);

    /**
     * 查询历史版本信息
     * @param clientUpgradeParamDto
     * @param loginUserId
     * @return
     */
    PageData<ClientUpgradeVersionDto> queryClientUpgradeVersionDtoHis(ClientUpgradeParamDto clientUpgradeParamDto,String loginUserId);

    /**
     * 根据版本查询机构信息
     * @param clientUpgradeParamDto
     * @param loginUserId
     * @return
     */
    List<ClientUpgradeVersionDto> queryClientUpgradeByVersionCode(ClientUpgradeParamDto clientUpgradeParamDto,String loginUserId);

    ClientUpgradeVersionDto queryOrgClientUpgradeById(ClientUpgradeParamDto clientUpgradeParamDto,String loginUserId);

    /**
     * 新增机构版本配置
     * @param clientUpgradeVersionDtoParam
     */
    void addClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam,String loginUserId);

    /**
     * 更新机构版本配置
     * @param clientUpgradeVersionDtoParam
     */
    void updateClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam,String loginUserId);

    /**
     * 删除机构版本配置
     * @param clientUpgradeVersionDtoParam
     */
    void deleteClientUpgradeVersionDto(ClientUpgradeVersionDto clientUpgradeVersionDtoParam,String loginUserId);

    /**
     * 升级接口
     * @param clientUpgradeFilter
     * @return
     */
    List<PackageVersionVO>  queryClientUpgradeVersion(ClientUpgradeFilter clientUpgradeFilter);
}
