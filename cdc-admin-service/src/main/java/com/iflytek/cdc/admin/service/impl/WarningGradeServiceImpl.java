package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.WarningGradeQueryDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGrade;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfigRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeConfigMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeConfigRuleMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrWarningGradeMapper;
import com.iflytek.cdc.admin.service.WarningGradeConfigRuleService;
import com.iflytek.cdc.admin.service.WarningGradeService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarningGradeServiceImpl implements WarningGradeService {
    @Resource
    TbCdcmrWarningGradeMapper tbCdcmrWarningGradeMapper;
    @Resource
    TbCdcmrWarningGradeConfigRuleMapper tbCdcmrWarningGradeConfigRuleMapper;
    @Resource
    TbCdcmrWarningGradeConfigMapper tbCdcmrWarningGradeConfigMapper;
    @Resource
    WarningGradeConfigRuleService warningGradeConfigRuleService;

    @Override
    public PageInfo<TbCdcmrWarningGrade> getWarningGradePageList(WarningGradeQueryDTO warningGradeQueryDTO) {
        PageHelper.startPage(warningGradeQueryDTO.getPageIndex(), warningGradeQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrWarningGradeMapper.getPageList(warningGradeQueryDTO));
    }

    @Override
    public void addWarningGrade(TbCdcmrWarningGrade tbCdcmrWarningGrade, String loginUserId) {
        if (tbCdcmrWarningGradeMapper.getByGradeName(tbCdcmrWarningGrade.getGradeName()) != null) {
            throw new MedicalBusinessException("已存在相同名称!");
        }
        tbCdcmrWarningGrade.setUpdateUser(loginUserId);
        tbCdcmrWarningGrade.setUpdateTime(new Date());
        tbCdcmrWarningGrade.setIsDeleted(0);
        tbCdcmrWarningGradeMapper.updateByPrimaryKeySelective(tbCdcmrWarningGrade);
    }

    @Override
    @Transactional
    public void deleteWarningGrade(String gradeCode) {
        tbCdcmrWarningGradeMapper.deleteByPrimaryKey(gradeCode);
        tbCdcmrWarningGradeConfigRuleMapper.deleteByGradeCode(gradeCode);
    }

    @Override
    @Transactional
    public void updateWarningGrade(TbCdcmrWarningGrade tbCdcmrWarningGrade, String loginUserId) {
        TbCdcmrWarningGrade existWarningGrade = tbCdcmrWarningGradeMapper.getByGradeName(tbCdcmrWarningGrade.getGradeName());
        if (existWarningGrade != null && !existWarningGrade.getGradeCode().equals(tbCdcmrWarningGrade.getGradeCode())) {
            throw new MedicalBusinessException("已存在相同名称!");
        }

        if (Constants.STATUS_OFF.equals(tbCdcmrWarningGrade.getStatus())) {
            tbCdcmrWarningGradeConfigRuleMapper.deleteByGradeCode(tbCdcmrWarningGrade.getGradeCode());
        }
        TbCdcmrWarningGrade updateGrade = new TbCdcmrWarningGrade();
        updateGrade.setUpdateUser(loginUserId);
        updateGrade.setUpdateTime(new Date());
        updateGrade.setGradeName(tbCdcmrWarningGrade.getGradeName());
        updateGrade.setRemark(tbCdcmrWarningGrade.getRemark());
        updateGrade.setStatus(tbCdcmrWarningGrade.getStatus());
        updateGrade.setGradeCode(tbCdcmrWarningGrade.getGradeCode());
        updateGrade.setGradeName(tbCdcmrWarningGrade.getGradeName());
        tbCdcmrWarningGradeMapper.updateByPrimaryKeySelective(updateGrade);
    }

    @Override
    public List<TbCdcmrWarningGrade> getWarningGradeList() {

        return tbCdcmrWarningGradeMapper.getList();
    }

    @Override
    public List<TbCdcmrWarningGrade> getConfigByTypeAndCode(String configType, String diseaseCode) {
        TbCdcmrWarningGradeConfig config = tbCdcmrWarningGradeConfigMapper.getListByConfigTypeAndCode(configType, diseaseCode);
        if (config != null) {
            List<TbCdcmrWarningGradeConfigRule> ruleList = warningGradeConfigRuleService.getByConfigId(config.getId());
            if (!CollectionUtils.isEmpty(ruleList)) {
                List<String> gradeCodeList = ruleList.stream().map(TbCdcmrWarningGradeConfigRule::getGradeCode).collect(Collectors.toList());
                return getWarningGradeList().stream().filter(e -> gradeCodeList.contains(e.getGradeCode())).collect(Collectors.toList());
            } else {
                return new ArrayList<>();
            }
        } else {
            return new ArrayList<>();
        }
    }
}
