package com.iflytek.cdc.admin.capacity.api.algorithm.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeBaseSearchResponse {

    private String code;

    private String message;

    private String version;

    private List<KnowledgeBaseSearchResponse.Result> result;

    @Data
    public static class Result {
        @JsonProperty("传染病")
        public String 传染病;
        @JsonProperty("风险")
        public String 风险;
        @JsonProperty("事件类型")
        public String 事件类型;
        @JsonProperty("事件模板")
        public String 事件模板;
        @JsonProperty("病种类型")
        public String 病种类型;
        @JsonProperty("地域")
        public String 地域;
        @JsonProperty("时间范围")
        private String 时间范围;
        @JsonProperty("病例数")
        private String 病例数;
        @JsonProperty("死亡数")
        private String 死亡数;
        @JsonProperty("措施")
        private String 措施;
        @JsonProperty("专家指导")
        private String 专家指导;
        @JsonProperty("报告及发布")
        public String 报告及发布;
        @JsonProperty("监测及救治")
        public String 监测及救治;
        @JsonProperty("流行病学调查")
        public String 流行病学调查;
        @JsonProperty("实验室检测")
        public String 实验室检测;
        @JsonProperty("现场处置")
        public String 现场处置;
        @JsonProperty("预防措施")
        public String 预防措施;
        @JsonProperty("地方管控")
        private String 地方管控;
        @JsonProperty("应急物资")
        private String 应急物资;
        @JsonProperty("适用地域")
        private String 适用地域;
        @JsonProperty("适用人口")
        private String 适用人口;
        @JsonProperty("智能风险评估-疫情季节性统计")
        private String 智能风险评估疫情季节性统计;
        @JsonProperty("社会层面措施建议-限制或停止人群聚集活动")
        private String 社会层面措施建议限制或停止人群聚集活动;
        @JsonProperty("社会层面措施建议-停产、停业、停课等")
        private String 社会层面措施建议停产停业停课等;
        @JsonProperty("社会层面措施建议-流动人口交通检疫")
        private String 社会层面措施建议流动人口交通检疫;
        @JsonProperty("社会层面措施建议-出入境检疫")
        private String 社会层面措施建议出入境检疫;
        @JsonProperty("症候群种类")
        private String 症候群种类;
    }
}
