package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.ParamConfigConstants;
import com.iflytek.cdc.admin.constant.UidTableName;
import com.iflytek.cdc.admin.dto.ParamConfigDTO;
import com.iflytek.cdc.admin.dto.RedisConfigDTO;
import com.iflytek.cdc.admin.dto.SearchParamDTO;
import com.iflytek.cdc.admin.dto.UpdateOrgInfoDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.ParamOrg;
import com.iflytek.cdc.admin.entity.UapUserExtend;
import com.iflytek.cdc.admin.entity.UpdateOrgParam;
import com.iflytek.cdc.admin.mapper.ParamConfigMapper;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.ParamInfo;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.UapOrgService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/7/6 13:50
 **/
@Service
@Slf4j
public class ParamConfigServiceImpl implements ParamConfigService {

    private final BatchUidService batchUidService;

    private final UapOrgService orgService;

    private final ParamConfigMapper pMapper;

    private final UapUserService userService;

    private final RedisTemplate<String, Object> redisTemplate;

    public ParamConfigServiceImpl(BatchUidService batchUidService, UapOrgService orgService, ParamConfigMapper pMapper, UapUserService userService, RedisTemplate redisTemplate) {
        this.batchUidService = batchUidService;
        this.orgService = orgService;
        this.pMapper = pMapper;
        this.userService = userService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public PageInfo<ParamConfig> queryParamConfig(SearchParamDTO sd, String loginUserId) {
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        //查询未删除的参数数据
        sd.setIsDelete(ParamConfigConstants.NO_DELETE);
        return new PageInfo<>(pMapper.queryConfigInfo(sd));

    }

    @Override
    public PageInfo<ParamOrg> queryParamOrg(SearchParamDTO sd, String loginUserId) {
        //查询机构权限集合
        List<String> orgIds = orgService.queryDownOrgId(loginUserId);
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        sd.setOrgIds(orgIds);
        sd.setIsDelete(ParamConfigConstants.NO_DELETE);
        return new PageInfo<>(pMapper.queryConfigOrgInfo(sd));
    }

    @Override
    public PageInfo<ParamConfig> queryParamConfigById(String isDelete, SearchParamDTO sd) {
        PageHelper.startPage(sd.getPageIndex(), sd.getPageSize());
        return new PageInfo<>(pMapper.queryConfigInfoByOrg(isDelete, sd.getOrgId()));
    }

    @Override
    public List<ParamOrg> queryParamOrgByCode(SearchParamDTO sd, String loginUserId) {
        //查询机构权限集合
        List<String> orgIds = orgService.queryDownOrgId(loginUserId);
        sd.setOrgIds(orgIds);
        sd.setIsDelete(ParamConfigConstants.NO_DELETE);
        return pMapper.queryConfigOrgByCode(sd);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertParamConfig(ParamConfigDTO pc, String loginUserId) {
        try {
            //获取登录人员信息
            UapUserExtend uapUser = getUapUser(loginUserId);
            //检查参数编号是否存在
            if (CollUtil.isNotEmpty(pMapper.queryParamConfigByCode(pc.getConfigCode(), ParamConfigConstants.NO_DELETE))) {
                log.error("参数编号已存在,新增参数失败：{}", pc.getConfigCode());
                throw new MedicalBusinessException("该参数编号已存在");
            }
            //设置创建人和修改人
            pc.setCreateUser(uapUser.getId());
            pc.setUpdateUser(uapUser.getId());
            pc.setUpdateUserName(uapUser.getName());
            //设置未删除
            pc.setIsDelete(ParamConfigConstants.NO_DELETE);
            //设置主键
            pc.setId(String.valueOf(batchUidService.getUid(UidTableName.PARAM_CONFIG)));
            //插入参数数据
            pMapper.insertParamConfig(pc);
            //插入参数机构数据
            List<ParamOrg> orgList = insertParamOrg(pc, uapUser);
            //数据插入redis
            insertRedis(pc, orgList);
        } catch (Exception e) {
            log.error("新增参数数据异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void removeOrgParamConfig(String orgId, String configCode, String loginUserId) {
        try {
            //获取登陆人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            //根据机构id和configCode查询信息
            ParamOrg paramOrg = pMapper.queryOrgInfoByCodeAndId(configCode, orgId, ParamConfigConstants.NO_DELETE);
            ParamConfig paramConfig = pMapper.queryOneParamConfigByCode(configCode, ParamConfigConstants.NO_DELETE);
            if (paramOrg == null || paramConfig == null) {
                throw new MedicalBusinessException("机构配置数据不存在，无法移除");
            }
            pMapper.deleteByCodeAndId(configCode, orgId, ParamConfigConstants.YES_DELETE, uapUser.getId(), uapUser.getName());
            //删除redis的数据
            String key = Constants.REDIS_KEY + orgId + "-" + configCode;
            //删除redis中该机构特有的参数值
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("移除参数失败：{}", e);
            throw new MedicalBusinessException(e.getMessage());
        }

    }

    @Override
    public ParamConfigDTO queryParamConfigDetail(SearchParamDTO sd, String loginUserId) {
        ParamConfigDTO pd = new ParamConfigDTO();
        try {
            //查询参数信息
            ParamConfig pc = pMapper.queryOneParamConfigByCode(sd.getConfigCode(), ParamConfigConstants.NO_DELETE);
            BeanUtil.copyProperties(pc, pd);
            //查询机构权限集合
            sd.setOrgIds(orgService.queryDownOrgId(loginUserId));
            sd.setIsDelete(ParamConfigConstants.NO_DELETE);
            //查询关联机构信息
            pd.setOrgList(pMapper.queryConfigOrgByCode(sd));
        } catch (Exception e) {
            log.error("查询配置详细信息失败：{}{}", sd.getConfigCode(), e);
            throw new MedicalBusinessException("查询配置详细信息异常");
        }
        return pd;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateParamConfig(ParamConfigDTO pc, String loginUserId) {
        try {
            //获取登录人员信息
            UapUserExtend uapUser = getUapUser(loginUserId);
            //校验参数编号是否重复
            List<ParamConfig> oldConfigCode = pMapper.queryParamConfigByCode(pc.getConfigCode(), ParamConfigConstants.NO_DELETE);
            //如果长度大于0
            if (oldConfigCode.size() > 0) {
                if (!pc.getId().equals(oldConfigCode.get(0).getId())) {
                    log.error("参数编号已存在：{}", pc.getConfigCode());
                    throw new MedicalBusinessException("该参数编号已存在");
                }
            }
            //根据id查询原来的参数信息
            ParamConfig oldConfig = pMapper.queryById(pc.getId());
            List<String> orgIds = orgService.queryDownOrgId(loginUserId);
            //删除机构信息
            List<ParamOrg> oldOrgList = getParamOrg(loginUserId, oldConfig, orgIds);
            //设置修改人信息
            pc.setUpdateUser(uapUser.getId());
            pc.setUpdateUserName(uapUser.getName());
            pc.setIsDelete(ParamConfigConstants.NO_DELETE);
            pMapper.updateParamConfig(pc);
            //插入参数机构信息数据
            List<ParamOrg> orgList = insertParamOrg(pc, uapUser);
            UpdateOrgInfoDTO up = new UpdateOrgInfoDTO();
            //设置原来的参数编码
            up.setOldConfigCode(oldConfig.getConfigCode());
            up.setUpdateUser(uapUser.getId());
            //修改不是下级的
            up.setOrgIds(orgIds);
            //新的参数编码
            up.setConfigCode(pc.getConfigCode());
            pMapper.updateParamOrg(up);
            //删除redis数据
            deleteRedis(oldConfig, oldOrgList);
            //插入redis数据
            insertRedis(pc, orgList);
        } catch (Exception e) {
            log.error("更新参数信息异常：{}{}", pc.getId(), e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    private UpdateOrgParam getUpdateOrgParam(List<ParamOrg> orgList, TUapUser uapUser, ParamConfig oldConfig) {
        UpdateOrgParam uo = new UpdateOrgParam();
        uo.setConfigCode(oldConfig.getConfigCode());
        uo.setUpdateUser(uapUser.getId());
        uo.setDeleteFlag(ParamConfigConstants.YES_DELETE);
        uo.setOrgIds(orgList);
        return uo;
    }


    @Override
    public void deleteParamConfig(String id, String loginUserId) {
        try {
            //根据id获取删除配置的详细信息
            ParamConfig oldConfig = pMapper.queryById(id);
            pMapper.deleteConfigById(id, loginUserId, ParamConfigConstants.YES_DELETE);
            List<ParamOrg> oldOrgList = getParamOrg(loginUserId, oldConfig, null);
            //删除redis数据
            deleteRedis(oldConfig, oldOrgList);
        } catch (Exception e) {
            log.error("删除参数配置信息异常:{}{}", id, e);
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    private List<ParamOrg> getParamOrg(String loginUserId, ParamConfig oldConfig, List<String> orgIds) {
        SearchParamDTO sd = new SearchParamDTO();
        //设置参数编号
        sd.setConfigCode(oldConfig.getConfigCode());
        sd.setIsDelete(ParamConfigConstants.NO_DELETE);
        //获取下级id集合
        UapUserExtend uapUser = getUapUser(loginUserId);
        sd.setOrgIds(orgIds);
        sd.setIsDelete(ParamConfigConstants.NO_DELETE);
        //查询原有的有权限机构数据
        List<ParamOrg> oldOrgList = pMapper.queryConfigOrgByCode(sd);
        // 根据原来的id集合删除原有的机构配置
        if (CollUtil.isNotEmpty(oldOrgList)) {
            UpdateOrgParam uo = getUpdateOrgParam(oldOrgList, uapUser, oldConfig);
            pMapper.deleteOrgByCode(uo);
        }
        return oldOrgList;
    }

    private void deleteRedis(ParamConfig oldConfig, List<ParamOrg> oldOrgList) {
        //先删除通用配置
        redisTemplate.delete(Constants.REDIS_KEY + oldConfig.getConfigCode());
        //再删除机构原有的配置
        if (CollUtil.isNotEmpty(oldOrgList)) {
            oldOrgList.forEach(e -> {
                //拼接redis的key值
                StringBuilder sb = new StringBuilder();
                sb.append(Constants.REDIS_KEY);
                sb.append(e.getOrgId()).append("-").append(oldConfig.getConfigCode());
                redisTemplate.delete(sb.toString());
            });
        }
    }

    private List<ParamOrg> insertParamOrg(ParamConfigDTO pc, TUapUser uapUser) {
        //获取机构列表
        List<ParamOrg> orgList = pc.getOrgList();
        if (CollUtil.isNotEmpty(orgList)) {
            orgList.forEach(e -> {
                //设置主键
                e.setId(String.valueOf(batchUidService.getUid(UidTableName.PARAM_ORG)));
                //设置参数编号
                e.setConfigCode(pc.getConfigCode());
                //设置创建人和修改人
                e.setCreateUser(uapUser.getId());
                e.setUpdateUser(uapUser.getId());
                e.setUpdateUserName(uapUser.getName());
                //设置为未删除
                e.setIsDelete(ParamConfigConstants.NO_DELETE);
            });
            pMapper.insertParamOrg(orgList);
        }
        //插入参数关联机构信息
        return orgList;
    }

    private void insertRedis(ParamConfigDTO pc, List<ParamOrg> orgList) {
        //复制属性
        RedisConfigDTO rd = new RedisConfigDTO();
        BeanUtil.copyProperties(pc, rd);
        //先将全局参数插入到redis里面
        redisTemplate.opsForValue().set(Constants.REDIS_KEY + rd.getConfigCode(), JSONUtil.toJsonStr(rd));
        //如果特有参数配置不为空
        if (CollUtil.isNotEmpty(orgList)) {
            orgList.forEach(e -> {
                StringBuilder sb = new StringBuilder();
                //组装key
                sb.append(Constants.REDIS_KEY);
                sb.append(e.getOrgId()).append("-").append(rd.getConfigCode());
                //将机构特有的参数值插入到redis里面
                redisTemplate.opsForValue().set(sb.toString(), JSONUtil.toJsonStr(e));
            });
        }
    }

    private UapUserExtend getUapUser(String loginUserId) {
        //获取登录人员信息
        UapUserExtend uapUser = userService.getUserInfo(loginUserId);
        if (uapUser == null) {
            log.error("获取登录用户信息异常：{}", loginUserId);
            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
        }
        return uapUser;
    }

    @Override
    public ParamInfo paramInfoByCode(ParamInfoFilter filter) {
        if (StringUtils.isBlank(filter.getConfigCode())) {
            throw new MedicalBusinessException("参数编号不能为空");
        }
        //先组装redis的key，查询redis
        ParamInfo paramInfo = new ParamInfo();
        String configKey = Constants.REDIS_KEY + filter.getConfigCode();
        try {
            Object redisResult = redisTemplate.opsForValue().get(configKey);
            if (redisResult != null) {
                paramInfo = JSONUtil.toBean(JSONUtil.toJsonStr(redisResult), ParamInfo.class);
            }
            //如果没有查询出就查询数据库
            if (redisResult == null) {
                //去数据库查询配置信息
                ParamConfig paramConfig = pMapper.paramConfigInfo(filter.getConfigCode(), ParamConfigConstants.NO_DELETE, filter.getConfigGroup());
                if (paramConfig != null) {
                    BeanUtil.copyProperties(paramConfig, paramInfo);
                    //查询出的数据再放入redis里面
                    redisTemplate.opsForValue().set(configKey, paramInfo, 30, TimeUnit.MINUTES);
                }
            }
            if (StringUtils.isNotBlank(filter.getOrgId())) {
                StringBuilder sb = new StringBuilder();
                //组装key
                ParamOrg paramOrg;
                sb.append(Constants.REDIS_KEY);
                sb.append(filter.getOrgId()).append("-").append(filter.getConfigCode());
                Object orgResult = redisTemplate.opsForValue().get(sb.toString());
                //先查询redis，如果没有就查询数据库
                if (orgResult != null) {
                    Map orgMap = JSONUtil.toBean(JSONUtil.toJsonStr(orgResult), Map.class);
                    BeanUtil.fillBeanWithMap(orgMap, paramInfo, false);
                    if (orgMap.get("orgConfigValue") != null) {
                        paramInfo.setConfigValue(orgMap.get("orgConfigValue").toString());
                    }
                }
                //如果没有查询出就查询数据库
                if (orgResult == null) {
                    paramOrg = pMapper.orgInfo(filter.getConfigCode(), filter.getOrgId(), ParamConfigConstants.NO_DELETE);
                    if (paramOrg != null) {
                        formatOrgInfo(paramInfo, paramOrg);
                        //将数据再放入redis里面
                        redisTemplate.opsForValue().set(sb.toString(), paramInfo, 30, TimeUnit.MINUTES);
                    }
                }
            }
        } catch (Exception e) {
            log.info("通过参数编号查询参数信息异常:{}", e.getLocalizedMessage());
            ParamConfig paramConfig = pMapper.paramConfigInfo(filter.getConfigCode(), ParamConfigConstants.NO_DELETE, filter.getConfigGroup());
            BeanUtil.copyProperties(paramConfig, paramInfo);
        }
        return paramInfo;
    }

    @Override
    public List<ParamInfo> paramListByCodeList(List<ParamInfoFilter> filters) {
        //批量查询参数
        List<ParamInfo> paramInfos = new ArrayList<>();
        filters.forEach(filter -> {
            ParamInfo paramInfo = paramInfoByCode(filter);
            paramInfos.add(paramInfo);
        });
        return paramInfos;
    }

    @Override
    public List<ParamInfo> paramInfoByGroup(String configGroup) {
        if (StringUtils.isBlank(configGroup)) {
            throw new MedicalBusinessException("参数分组不能为空");

        }
        return pMapper.queryParamByGroup(configGroup, ParamConfigConstants.NO_DELETE);
    }

    @Override
    public List<ParamConfig> queryParamConfigByKeyword(String keyword) {
        return pMapper.queryParamByCode(keyword);
    }

    private void formatOrgInfo(ParamInfo paramInfo, ParamOrg paramOrg) {
        paramInfo.setOrgCode(paramOrg.getOrgCode());
        paramInfo.setOrgName(paramOrg.getOrgName());
        paramInfo.setOrgId(paramOrg.getOrgId());
        paramInfo.setDeptId(paramOrg.getDeptId());
        paramInfo.setConfigValue(paramOrg.getOrgConfigValue());

    }

    @Override
    public int getExportMax() {
        ParamConfig paramConfig = pMapper.queryOneParamConfigByCode(ParamConfigConstants.EXPORT_MAX_CODE, ParamConfigConstants.NO_DELETE);
        if (paramConfig != null){
            try {
                return  Integer.valueOf(paramConfig.getConfigValue()) ;
            }catch (Exception e){
                log.error("标记录值录入有误code#{},value#{}",ParamConfigConstants.EXPORT_MAX_CODE,paramConfig.getConfigValue(),e);
            }
        }
        return ParamConfigConstants.EXPORT_MAX_VALUE;
    }

    @Override
    public void checkExportMax(Collection collection) {
        int exportMax = getExportMax();
        if (collection != null && collection.size() > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }
    @Override
    public String getTrendForecastStatus(String configCode, String configGroup) {
        return pMapper.getStatusByCodeAndGroup(configCode, configGroup);
    }

    @Override
    public String getStatusByCodeAndGroup(String configCode, String configGroup) {
        return pMapper.getStatusByCodeAndGroup(configCode, configGroup);
    }

    @Override
    public void refreshRedisBy(String configCode, String configGroup) {

        String configKey = Constants.REDIS_KEY + configCode;
        ParamInfo paramInfo = new ParamInfo();
        ParamConfig paramConfig = pMapper.paramConfigInfo(configCode, ParamConfigConstants.NO_DELETE, configGroup);
        if (paramConfig != null) {
            BeanUtil.copyProperties(paramConfig, paramInfo);
            //更新redis缓存对应key的值
            redisTemplate.opsForValue().set(configKey, paramInfo, 30, TimeUnit.MINUTES);
        }
    }

}
