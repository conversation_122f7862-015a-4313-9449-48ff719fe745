package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

@Data
public class InfectedDataConstants {

    @ApiModelProperty(value = "传染病类型")
    private final Object infectedClass = InfectedDataConstants.InfectedClassTypeEnum.values();

    @ApiModelProperty(value = "法定报告传染病分类")
    private final Object infectedType = InfectedDataConstants.InfectedTypeEnum.values();

    @ApiModelProperty(value = "法定传染病管理分类")
    private final Object managementType = InfectedDataConstants.ManagementTypeEnum.values();

    @ApiModelProperty(value = "法定传染病传播途径分类")
    private final Object transmissionType = InfectedDataConstants.TransmissionTypeEnum.values();

    @ApiModelProperty(value = "诊断状态")
    private final Object diagnoseStatus = InfectedDataConstants.DiagnoseStatusEnum.values();

    @ApiModelProperty(value = "暴发疫情分级")
    private final Object epidemicLevel = InfectedDataConstants.EpidemicLevelEnum.values();

    /**
     * 传染病类型
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum InfectedClassTypeEnum{

        STATUTORY("01", "法定传染病"),

        OTHER("09", "其他传染病"),

        ;
        private final String value;
        private final String label;

        InfectedClassTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 法定报告传染病分类
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum InfectedTypeEnum{

        CLASS_A_INFECTIOUS_DISEASES("1", "甲类传染病"),

        CLASS_B_INFECTIOUS_DISEASES("2", "乙类传染病"),

        CLASS_C_INFECTIOUS_DISEASES("3", "丙类传染病"),

        OTHER("4", "其他法定管理及重点监测传染病"),

        ;
        private final String value;
        private final String label;

        InfectedTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 法定传染病管理分类
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum ManagementTypeEnum{

        CLASS_A_MANAGEMENT("1", "甲类传染病"),

        CLASS_B_MANAGEMENT("2", "乙类传染病"),

        CLASS_C_MANAGEMENT("3", "丙类传染病"),

        OTHER_MANAGEMENT("4", "非法定管理传染病"),

        ;
        private final String value;
        private final String label;

        ManagementTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 传播途径分类
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum TransmissionTypeEnum{

        RESPIRATORY_INFECTIOUS_DISEASES("1", "呼吸道传染病"),

        GASTROINTESTINAL_INFECTIOUS_DISEASES("2", "肠道传染病"),

        ZOONOTIC_INFECTIOUS_DISEASES("3", "自然疫源及虫媒传染病"),

        BLOOD_INFECTIOUS_DISEASES("4", "血源及性传播传染病"),

        OTHER("5", "其他"),

        NEONATAL_TETANUS_DISEASES("6", "新生儿破伤风传染病"),

        SEXUAL_DISEASES("7", "性传播传染病"),

        ;
        private final String value;
        private final String label;

        TransmissionTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 诊断状态枚举
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum DiagnoseStatusEnum {

        SUSPECT_PROCESS("1", "疑似病例"),

        CLINICALLY_DIAGNOSED_PROCESS("2", "临床诊断病例"),

        CONFIRMED_PROCESS("3", "确诊病例"),

        CARRIER("4", "无症状感染者/带菌者/隐性感染/携带者"),

        ;

        private final String value;
        private final String label;

        DiagnoseStatusEnum(String value, String label) {

            this.value = value;
            this.label = label;
        }

        public static String getValueByLabel(String label) {

            DiagnoseStatusEnum[] values = DiagnoseStatusEnum.values();
            for (DiagnoseStatusEnum value : values) {
                if (Objects.equal(label, value.getLabel())){
                    return value.getValue();
                }
            }
            return null;
        }
    }

    /**
     * 暴发疫情分级枚举
     * */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum EpidemicLevelEnum {

        I("I", "I级特别重大"),

        II("II", "II级重大"),

        III("III", "III级较大"),

        IV("IV", "IV级一般"),

        FOLLOW("FOLLOW", "关注"),

        ;

        private final String value;
        private final String label;

        EpidemicLevelEnum(String value, String label) {

            this.value = value;
            this.label = label;
        }
    }
}
