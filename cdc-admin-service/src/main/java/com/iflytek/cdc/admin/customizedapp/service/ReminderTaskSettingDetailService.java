package com.iflytek.cdc.admin.customizedapp.service;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsReminderTaskSettingDetail;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface ReminderTaskSettingDetailService extends ICdcService<TbCdccsReminderTaskSettingDetail> {

    /**
     * 通过任务配置新增或修改
     */
    void saveByTaskSetting(TbCdccsTaskSetting taskSetting, List<TbCdccsReminderTaskSettingDetail> details);

    /**
     * 通过taskSettingId 查询
     */
    List<TbCdccsReminderTaskSettingDetail> listByTaskSettingId(String taskSettingId);

    /**
     * 通过配置id删除
     */
    void deleteByTaskSettingId(String taskSettingId);

    /**
     * 根据配置id集合查询
     */
    List<TbCdccsReminderTaskSettingDetail> listByTaskSettingIds(List<String> taskIds);

}
