package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.InspectionStandardQueryParam;
import com.iflytek.cdc.admin.dto.InspectionStandardVO;
import com.iflytek.cdc.admin.entity.TbCdcmrInspectionStandard;

import java.util.List;

public interface InspectionStandardService {

    void syncInspectionStandard();

    void updateInspectionStandard(TbCdcmrInspectionStandard tbCdcmrInspectionStandard);

    PageInfo<InspectionStandardVO> getInspectionStandardList(InspectionStandardQueryParam param);

    List<TbCdcmrInspectionStandard> getInspectionStandardList(String keyword);

}
