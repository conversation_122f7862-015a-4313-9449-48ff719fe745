package com.iflytek.cdc.admin.service;


import com.iflytek.cdc.admin.dto.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DiagnosisDoctorService {
    byte[] downloadTemplate(String loginUserName,String loginUserId);

    UploadResultVO uploadFile(MultipartFile file) throws IOException;

    UploadResultVO batchAdd(String attachmentId, String loginUserName);

    void add(DiagnosisDoctorVO vo, String loginUserName);

    void update(DiagnosisDoctorVO vo, String loginUserName);

    void delete(String id, String loginUserId, String loginUserName);

    void updateStatus(String id, Integer status, String loginUserId, String loginUserName);

    PageData<DiagnosisDoctorVO> list(String loginUserName, DiagnosisDoctorQueryVO queryVO);

    List<TbCdcewOrganizationInfo> getSubOrgList(String loginUserName);

    Object getOrgListForBusinessPerson(String loginUserName);

    ResponseEntity<byte[]> downloadFile(String id, String loginUserId);
}
