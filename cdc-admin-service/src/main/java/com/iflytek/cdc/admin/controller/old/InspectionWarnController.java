package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.service.InspectionWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "检查检验规则接口")
public class InspectionWarnController {
    @Resource
    InspectionWarnService inspectionWarnService;

    @ApiOperation("查询检查检验传染病列表")
    @GetMapping("/{version}/pt/inspectionWarn/getInspectionInfectedList")
    public List<CascadeVO> getInspectionInfectedList() {
        return inspectionWarnService.getInspectionInfectedList();
    }
}
