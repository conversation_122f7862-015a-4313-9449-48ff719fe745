package com.iflytek.cdc.admin.capacity.api.algorithm.request;

import lombok.Data;

import java.util.List;

@Data
public class DiseaseTrendRequest {
    private List<Integer> historyData;//历史数据
    private Integer predictStep;//预测的步数
    private String disease;//传染病类型，如果为空则采用通用模型
    private List<Integer> historyDataAges;//历史数据的年龄分布,分别表示【年龄段0_3、年龄段3_6、年龄段6_18、年龄段18_60、年龄段60以上】
    private List<Integer> historyDataGenders;//历史数据的性别分布，分别表示【男，女】
    private Integer historyDataMonth;//历史数据的发病月份，取占的多数就行，比如7月2天且8月5天，则填8月
}
