package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseWarning;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrDiseaseWarningMapper;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseWarningRuleDTO;
import com.iflytek.cdc.admin.service.province.DiseaseWarningRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
@Slf4j
public class DiseaseWarningRuleServiceImpl extends ServiceImpl<TbCdcmrDiseaseWarningMapper, TbCdcmrDiseaseWarning> implements DiseaseWarningRuleService {

    @Override
    public void updateWarningRulePriority(DiseaseWarningRuleDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(USER_INFO.get()).orElse(new UapUserPo());
        TbCdcmrDiseaseWarning warning = new TbCdcmrDiseaseWarning();
        warning.setWarningPriority(dto.getWarningPriority());
        warning.setDiseaseInfoId(dto.getDiseaseInfoId());
        warning.setWarningType(dto.getWarningType());
        warning.setUpdater(uapUserPo.getName());
        warning.setUpdaterId(uapUserPo.getId());
        baseMapper.updateWarningRulePriority(warning);
    }

    @Override
    public Map<String, String> getWarningRulePriority(DiseaseWarningRuleDTO dto) {

        Map<String, String> resultMap = baseMapper.getWarningRulePriority(dto);
        if (MapUtil.isNotEmpty(resultMap)) {
            resultMap = MapUtil.toCamelCaseMap(resultMap);
        }
        return resultMap;
    }
}
