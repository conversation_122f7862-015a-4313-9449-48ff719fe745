package com.iflytek.cdc.admin.outbound.service;

import com.iflytek.cdc.admin.outbound.entity.OutboundRecord;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundRecordQueryDTO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface OutboundRecordService extends ICdcService<OutboundRecord> {

    List<OutboundRecord> listRecords(OutboundRecordQueryDTO queryDTO);

    /**
     * 状态变更
     */
    void saveStatus(List<OutboundRecord> outboundRecords);
}
