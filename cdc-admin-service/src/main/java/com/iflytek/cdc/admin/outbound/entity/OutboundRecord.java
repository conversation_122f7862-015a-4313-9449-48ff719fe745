package com.iflytek.cdc.admin.outbound.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_cdcmr_outbound_record")
public class OutboundRecord extends BaseEntity {

    @ApiModelProperty(value = "话术或短信id")
    private String speechOrSmsId;
    
    @ApiModelProperty(value = "外呼类型")
    private String type;
    
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "请求内容")
    private String requestContent;

//    @ApiModelProperty("音频地址")
//    private String audioUrl;

    @ApiModelProperty("响应内容")
    private String responseContent;
    
    @Getter
    public enum CallStatus{
        CALLING("1", "进行中"),
        CALLED("2", "已完成"),
        FAILED("3", "失败")
        ;
        
        private final String code;
        private final String desc;
        
        CallStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        
    }

    @Getter
    public enum Type{
        PHONE("1", "电话"),
        SMS("2", "短信")
        ;

        private final String code;
        private final String desc;

        Type(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }


}
