package com.iflytek.cdc.admin.outbound.service;


import com.iflytek.cdc.admin.outbound.model.dto.input.BatchList;
import com.iflytek.cdc.admin.outbound.model.dto.output.BatchDetailRs;
import com.iflytek.cdc.admin.outbound.model.dto.output.BatchListRs;
import com.iflytek.cdc.admin.outbound.model.dto.output.RecordDetailRs;
import com.iflytek.cdc.admin.outbound.model.dto.output.SpeechVariable;
import com.iflytek.cdc.admin.outbound.util.Response;

/**
 * 外呼结果、信息查询服务
 * <AUTHOR>
 */
public interface QueryOutBoundService {

    /**
     * 查询话术自定义变量
     * @param speechId
     * @return
     */
    Response<SpeechVariable> querySpeechVariable(String speechId);

    /**
     * 查询电话批次状态
     * @param batchId
     * @return
     */
    Response<BatchDetailRs> queryBatchDetail(String batchId);

    /**
     * 查询批次汇总数据
     * @param batchList
     * @return
     */
    Response<BatchListRs> queryBatchList(BatchList batchList);

    /**
     * 查询电话记录详情
     * @param recordId
     * @return
     */
    Response<RecordDetailRs> queryRecordDetail(String recordId);
}
