package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.bigmodel.model.BigModelUrlAndParamVo;
import com.iflytek.cdc.admin.bigmodel.service.BigModelService;
import lombok.Data;
import lombok.Getter;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/pt/v1/bigModel")
public class BigModelController {
    @Resource
    private BigModelService bigModelService;


    @PostMapping("/getBigModelFormQuestion")
    public BigModelUrlAndParamVo getBigModelFormQuestion(@RequestBody Input input) {
        return bigModelService.getBigModelFormQuestion(input.templateQuestion);
    }

    @Data
    private static final class Input{
        private String templateQuestion;
    }

}
