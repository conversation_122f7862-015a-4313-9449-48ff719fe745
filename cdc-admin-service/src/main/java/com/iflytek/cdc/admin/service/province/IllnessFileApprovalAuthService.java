package com.iflytek.cdc.admin.service.province;


import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.dto.IllnessFileApprovalAuthDto;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrIllnessFileApprovalAuth;

import java.util.List;

public interface IllnessFileApprovalAuthService extends IService<TbCdcmrIllnessFileApprovalAuth> {

    void saveBatchApprovalAuth(String loginUserId,List<TbCdcmrIllnessFileApprovalAuth> list);

    List<TbCdcmrIllnessFileApprovalAuth> getApprovalAuthByOrgId(String orgId);

    void updateApprovalAuth(String loginUserId,IllnessFileApprovalAuthDto authDto);

}
