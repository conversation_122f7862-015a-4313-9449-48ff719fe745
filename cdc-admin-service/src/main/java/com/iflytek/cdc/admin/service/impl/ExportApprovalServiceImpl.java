package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.ExportApplicationQueryDTO;
import com.iflytek.cdc.admin.dto.ExportApprovalDTO;
import com.iflytek.cdc.admin.dto.ExportApprovalListDTO;
import com.iflytek.cdc.admin.dto.ExportConfigDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrExportApproval;
import com.iflytek.cdc.admin.entity.TbCdcmrExportPermissionConfig;
import com.iflytek.cdc.admin.enums.ApprovalStatusEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportApprovalMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportPermissionConfigMapper;
import com.iflytek.cdc.admin.service.ExportApprovalService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ExportApprovalServiceImpl implements ExportApprovalService {

    @Resource
    private TbCdcmrExportApprovalMapper exportApprovalMapper;

    @Resource
    private TbCdcmrExportPermissionConfigMapper tbCdcmrExportPermissionConfigMapper;

    @Resource
    private UapServiceApi uapServiceApi;

    /**
     * 获取人工审核列表
     *
     * @param queryDTO 查询条件
     * @return 人工审核列表
     */
    @Override
    public PageInfo<ExportApprovalListDTO> queryManualApprovalList(ExportApplicationQueryDTO queryDTO) {
        queryDTO.setAppCode(queryDTO.getModuleType());
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return PageInfo.of(exportApprovalMapper.queryManualApprovalList(queryDTO));
    }

    /**
     * 提交审核申请
     */
    @Override
    public boolean submitApproval(String loginUserId, ExportApprovalDTO dto) {
        // 目标审批状态，后续需要根据审批阶段调整
        ApprovalStatusEnum targetApprovalStatus = ApprovalStatusEnum.getByCode(dto.getApprovalStatus());
        // 申请驳回必须提供驳回原因
        if (ApprovalStatusEnum.REJECTED.equals(targetApprovalStatus) && StrUtil.isBlank(dto.getRejectReason())) {
            throw new MedicalBusinessException("驳回原因不能为空");
        }

        // 查询审批记录
        TbCdcmrExportApproval oldApproval = exportApprovalMapper.selectById(dto.getId());
        if (oldApproval == null) {
            throw new MedicalBusinessException("审批记录不存在");
        }
        // 获取该用户所属的机构id
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String orgId = user.getOrgId();
        // 查询权限配置，获取审核级别和审核人信息

        ExportConfigDTO exportConfigDTO = tbCdcmrExportPermissionConfigMapper.selectByExportCodeAndOrgId(oldApproval.getExportCode(),orgId);
        if (exportConfigDTO == null) {
            throw new MedicalBusinessException("未找到对应的权限配置");
        }
        int approvalLevels = exportConfigDTO.getApprovalLevels();
        ApprovalStatusEnum previousApprovalStatus = ApprovalStatusEnum.getByCode(oldApproval.getApprovalStatus());
        // 动态计算下个实际状态
        ApprovalStatusEnum nextApprovalStatus = this.calculateNextStep(targetApprovalStatus, previousApprovalStatus, approvalLevels);

        // 检查当前用户是否有权限提交审核
        if (hasNoPermissionToApprove(loginUserId, nextApprovalStatus, oldApproval.getLevel1Approver(),
                oldApproval.getLevel2Approver(), approvalLevels)) {
            throw new MedicalBusinessException("当前用户无权提交审核");
        }

        // 更新审批记录
        oldApproval.setApprovalStatus(nextApprovalStatus.getCode());
        oldApproval.setApprovalTime(new Date());
        oldApproval.setRejectReason(dto.getRejectReason());
        String userName = user.getName();
        oldApproval.setApprover(userName); // 设置最后审批人为当前登录用户
        oldApproval.setUpdaterId(loginUserId);
        oldApproval.setUpdater(userName);
        oldApproval.setUpdaterTime(new Date());

        int rowsUpdated = exportApprovalMapper.updateById(oldApproval);

        // 返回操作是否成功
        return rowsUpdated > 0;

    }

    private ApprovalStatusEnum calculateNextStep(ApprovalStatusEnum targetApprovalStatus,
                                                 ApprovalStatusEnum previousApprovalStatus,
                                                 int approvalLevels) {
        if (approvalLevels == 2 && ApprovalStatusEnum.APPROVED.equals(targetApprovalStatus)
                && ApprovalStatusEnum.PENDING_APPROVAL.equals(previousApprovalStatus)) {
            return ApprovalStatusEnum.PENDING_SECOND_APPROVAL;
        }
        return targetApprovalStatus;
    }

    private boolean hasNoPermissionToApprove(String loginUserId, ApprovalStatusEnum nextApprovalStatus,
                                             String level1ApproverConfig, String level2ApproverConfig, int approvalLevels) {
        // 解析一级审批人
        List<String> level1Approvers = parseApprovers(level1ApproverConfig);
        boolean level1Miss = !level1Approvers.contains(loginUserId);
        // 解析二级审批人
        List<String> level2Approvers = parseApprovers(level2ApproverConfig);
        boolean level2Miss = !level2Approvers.contains(loginUserId);

        switch (nextApprovalStatus) {
            case REJECTED:
                return level1Miss && level2Miss;
            case PENDING_SECOND_APPROVAL:
                return level1Miss;
            case APPROVED:
                return (approvalLevels == 1 && level1Miss) || (approvalLevels == 2 && level2Miss);
            default:
                return true;
        }
    }

    private List<String> parseApprovers(String approversJson) {
        List<String> approvers = new ArrayList<>();
        if (approversJson != null && !approversJson.isEmpty()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                approvers = objectMapper.readValue(approversJson, new TypeReference<List<String>>() {
                });
            } catch (Exception e) {
                log.error("解析审批人信息失败: {}", approversJson, e);
                throw new MedicalBusinessException("解析审批人信息失败");
            }
        }
        return approvers;
    }
}
