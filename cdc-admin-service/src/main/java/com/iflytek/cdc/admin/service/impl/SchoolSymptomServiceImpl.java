package com.iflytek.cdc.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.SympWarnDto;
import com.iflytek.cdc.admin.dto.SympQueryDTO;
import com.iflytek.cdc.admin.dto.SympWarnRuleExportDataDto;
import com.iflytek.cdc.admin.dto.TbCdcmrUserDataAuth;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrSympSymptom;
import com.iflytek.cdc.admin.entity.TbCdcmrSympWarn;
import com.iflytek.cdc.admin.entity.TbCdcmrSympWarnRule;
import com.iflytek.cdc.admin.mapper.TbCdcmrSympSymptomMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrSympWarnMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrSympWarnRuleMapper;
import com.iflytek.cdc.admin.service.DataAuthService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.SchoolSymptomService;
import com.iflytek.cdc.admin.util.ExportWarnRuleUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SchoolSymptomServiceImpl implements SchoolSymptomService {

    @Resource
    TbCdcmrSympSymptomMapper tbCdcmrSympSymptomMapper;

    @Resource
    TbCdcmrSympWarnMapper tbCdcmrSympWarnMapper;

    @Resource
    TbCdcmrSympWarnRuleMapper tbCdcmrSympWarnRuleMapper;

    @Resource
    BatchUidService batchUidService;

    @Resource
    DataAuthService dataAuthService;

    @Resource
    private ParamConfigService paramConfigService;
    @Override
    public PageInfo<TbCdcmrSympSymptom> getSchoolSymptomPageList(SympQueryDTO sympQueryDTO) {
        PageHelper.startPage(sympQueryDTO.getPageIndex(), sympQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrSympSymptomMapper.getList(sympQueryDTO));
    }

    @Override
    @Transactional
    public void addSchoolSymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        if (tbCdcmrSympSymptomMapper.getBySymptomCode(tbCdcmrSympSymptom.getSymptomCode()) != null) {
            throw new MedicalBusinessException("已存在相同症状编码!");
        }
        if (tbCdcmrSympSymptomMapper.getBySymptomName(tbCdcmrSympSymptom.getSymptomName()) != null) {
            throw new MedicalBusinessException("已存在相同症状名称!");
        }
        tbCdcmrSympSymptom.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_symp_symptom")));
        tbCdcmrSympSymptom.setUpdater(loginUserId);
        tbCdcmrSympSymptom.setUpdateTime(new Date());
        tbCdcmrSympSymptom.setIsDeleted(0);
        tbCdcmrSympSymptomMapper.insert(tbCdcmrSympSymptom);
        addSchoolSymptomWarnBySymptom(tbCdcmrSympSymptom, loginUserId);
    }

    @Override
    @Transactional
    public void deleteSchoolSymptom(String id) {
        TbCdcmrSympSymptom scSymptom = tbCdcmrSympSymptomMapper.selectByPrimaryKey(id);
        TbCdcmrSympWarn scWarn = tbCdcmrSympWarnMapper.getBySymptomCode(scSymptom.getSymptomCode());
        if (!tbCdcmrSympWarnRuleMapper.getRuleListByWarnId(scWarn.getId()).isEmpty()) {
            throw new MedicalBusinessException("还有在使用的规则，请删除后再进行症状的删除！");
        }
        tbCdcmrSympWarnMapper.deleteByPrimaryKey(scWarn.getId());
        tbCdcmrSympSymptomMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional
    public void updateSchoolSymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        TbCdcmrSympSymptom existSymptom = tbCdcmrSympSymptomMapper.getBySymptomCode(tbCdcmrSympSymptom.getSymptomCode());

        if (existSymptom != null && !Objects.equals(existSymptom.getId(), tbCdcmrSympSymptom.getId())) {
            throw new MedicalBusinessException("已存在相同症状编码!");
        }
        existSymptom = tbCdcmrSympSymptomMapper.getBySymptomName(tbCdcmrSympSymptom.getSymptomName());
        if (existSymptom != null && !Objects.equals(existSymptom.getId(), tbCdcmrSympSymptom.getId())) {
            throw new MedicalBusinessException("已存在相同症状名称!");
        }
        TbCdcmrSympSymptom updateSymptom = new TbCdcmrSympSymptom();
        updateSymptom.setId(tbCdcmrSympSymptom.getId());
        updateSymptom.setUpdater(loginUserId);
        updateSymptom.setUpdateTime(new Date());
        updateSymptom.setSymptomName(tbCdcmrSympSymptom.getSymptomName());
        updateSymptom.setRemark(tbCdcmrSympSymptom.getRemark());
        updateSymptom.setStatus(tbCdcmrSympSymptom.getStatus());
        updateSymptom.setSymptomCode(tbCdcmrSympSymptom.getSymptomCode());
        updateSchoolSymptomWarnBySymptom(updateSymptom, loginUserId);
        tbCdcmrSympSymptomMapper.updateByPrimaryKeySelective(updateSymptom);
    }

    @Override
    public PageInfo<SympWarnDto> getSchoolSymptomWarnPageList(SympQueryDTO sympQueryDTO) {
        PageHelper.startPage(sympQueryDTO.getPageIndex(), sympQueryDTO.getPageSize());
        return new PageInfo<>(tbCdcmrSympWarnMapper.getList(sympQueryDTO));
    }


    public void addSchoolSymptomWarnBySymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        TbCdcmrSympWarn tbCdcmrSympWarn = new TbCdcmrSympWarn();
        tbCdcmrSympWarn.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_symp_warn")));
        tbCdcmrSympWarn.setStatus(0);
        tbCdcmrSympWarn.setSymptomCode(tbCdcmrSympSymptom.getSymptomCode());
        tbCdcmrSympWarn.setSymptomName(tbCdcmrSympSymptom.getSymptomName());
        tbCdcmrSympWarn.setIncubation(null);
        tbCdcmrSympWarn.setRemark(null);
        tbCdcmrSympWarn.setUpdater(loginUserId);
        tbCdcmrSympWarn.setIsDeleted(0);
        tbCdcmrSympWarn.setUpdateTime(new Date());
        tbCdcmrSympWarn.setMaxLifeCycle(null);
        tbCdcmrSympWarnMapper.insert(tbCdcmrSympWarn);
    }

    public void updateSchoolSymptomWarnBySymptom(TbCdcmrSympSymptom tbCdcmrSympSymptom, String loginUserId) {
        TbCdcmrSympWarn tbCdcmrSympWarn = tbCdcmrSympWarnMapper.getBySymptomCode(tbCdcmrSympSymptom.getSymptomCode());
        tbCdcmrSympWarn.setSymptomCode(tbCdcmrSympSymptom.getSymptomCode());
        tbCdcmrSympWarn.setSymptomName(tbCdcmrSympSymptom.getSymptomName());
        tbCdcmrSympWarn.setUpdater(loginUserId);
        tbCdcmrSympWarn.setUpdateTime(new Date());
        tbCdcmrSympWarnMapper.updateByWarn(tbCdcmrSympWarn);
    }


    @Override
    @Transactional
    public void updateSchoolSymptomWarn(SympWarnDto sympWarnDto, String loginUserId) {
        TbCdcmrSympWarn tbCdcmrSympWarn = new TbCdcmrSympWarn();
        tbCdcmrSympWarn.setId(sympWarnDto.getId());
        tbCdcmrSympWarn.setStatus(sympWarnDto.getStatus());
        tbCdcmrSympWarn.setSymptomCode(sympWarnDto.getSymptomCode());
        tbCdcmrSympWarn.setSymptomName(sympWarnDto.getSymptomName());
        tbCdcmrSympWarn.setIncubation(sympWarnDto.getIncubation());
        tbCdcmrSympWarn.setRemark(sympWarnDto.getRemark());
        tbCdcmrSympWarn.setUpdater(loginUserId);
        tbCdcmrSympWarn.setIsDeleted(0);
        tbCdcmrSympWarn.setUpdateTime(new Date());
        tbCdcmrSympWarn.setMaxLifeCycle(sympWarnDto.getMaxLifeCycle());
        tbCdcmrSympWarn.setSmsSendTypeCode(sympWarnDto.getSmsSendTypeCode());
        tbCdcmrSympWarn.setSmsSendTypeDesc(sympWarnDto.getSmsSendTypeDesc());
        tbCdcmrSympWarnMapper.updateByPrimaryKeySelective(tbCdcmrSympWarn);
        updateRules(tbCdcmrSympWarn.getId(), sympWarnDto.getRuleList(), loginUserId);
    }


    @Override
    public SympWarnDto getWarnById(String warnId) {
        SympWarnDto dto = new SympWarnDto();
        TbCdcmrSympWarn scWarn = tbCdcmrSympWarnMapper.selectByPrimaryKey(warnId);
        dto.setId(scWarn.getId());
        dto.setStatus(scWarn.getStatus());
        dto.setRemark(scWarn.getRemark());
        dto.setSymptomCode(scWarn.getSymptomCode());
        dto.setIncubation(scWarn.getIncubation());
        dto.setSymptomName(scWarn.getSymptomName());
        dto.setMaxLifeCycle(scWarn.getMaxLifeCycle());
        dto.setSmsSendTypeCode(scWarn.getSmsSendTypeCode());
        dto.setSmsSendTypeDesc(scWarn.getSmsSendTypeDesc());
        dto.setRuleList(tbCdcmrSympWarnRuleMapper.getRuleListByWarnId(warnId));
        return dto;
    }

    @Override
    public void updateSchoolSymptomWarnStatus(SympWarnDto sympWarnDto, String loginUserId) {
        TbCdcmrSympWarn tbCdcmrSympWarn = new TbCdcmrSympWarn();
        tbCdcmrSympWarn.setStatus(sympWarnDto.getStatus());
        tbCdcmrSympWarn.setId(sympWarnDto.getId());
        tbCdcmrSympWarnMapper.updateStatusByPrimaryKey(tbCdcmrSympWarn);
    }

    @Override
    public List<SympWarnDto> getSchoolSymptomWarnAllList(String symptomCode) {
        List<SympWarnDto> warnList = tbCdcmrSympWarnMapper.getAllList(symptomCode);
        List<TbCdcmrSympWarnRule> ruleList = tbCdcmrSympWarnRuleMapper.getAllRuleList();
        warnList.forEach(sympWarnDto -> sympWarnDto.setRuleList(ruleList.stream().filter(tbCdcmrSympWarnRule -> tbCdcmrSympWarnRule.getWarnId().equals(sympWarnDto.getId())).collect(Collectors.toList())));
        return warnList;
    }

    @Override
    public void exportSchoolSymptomWarnRule(HttpServletResponse response) {
        response.setContentType("multipart/form-data");
        response.setCharacterEncoding("utf-8");
        // 文件名中文乱码设置
        String fileName = null;
        try {
            fileName = new String(Constants.SC_WRAN_RULE_FILENAME.getBytes(), "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        List<SympWarnRuleExportDataDto> dataList = tbCdcmrSympWarnRuleMapper.getExportData();

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(dataList);
        // 修改状态名称
        dataList.forEach(SchoolSymptomServiceImpl::getIncubation);
        dataList.forEach(SchoolSymptomServiceImpl::getMonitorObject);
        dataList.forEach(SchoolSymptomServiceImpl::getMaxLifeCycle);
        dataList.forEach(SchoolSymptomServiceImpl::getFormula);
        dataList.forEach(SchoolSymptomServiceImpl::getTimeScope);

        // 导出excel
        ExportWarnRuleUtil.excelExport(dataList, response, null, Constants.SC_WRAN_RULE_EXCEL_TITLE);
    }

    @Override
    public List<CascadeVO> getSymptoms() {
        List<TbCdcmrSympSymptom> scSymptomList = tbCdcmrSympSymptomMapper.findAll();
        return scSymptomList.stream()
                .collect(Collectors.toMap(TbCdcmrSympSymptom::getSymptomCode, e -> e, (k1, k2) -> k1))
                .values().stream().map(e -> {
                    CascadeVO vo = new CascadeVO();
                    vo.setLabel(e.getSymptomName());
                    vo.setValue(e.getSymptomCode());
                    return vo;
                }).collect(Collectors.toList());

    }

    @Override
    public List<CascadeVO> getSymptoms(String loginUserId) {
        return dataAuthService.getConfiguredDiseaseCodesByAuth(loginUserId,Constants.DATA_AUTH_SCHOOL_SYMPTOM);
//        List<TbCdcmrSympSymptom> scSymptomList = tbCdcmrSympSymptomMapper.findAll();
//
//        List<TbCdcmrUserDataAuth> dataAuths = dataAuthService.getSchoolSymptomDataAuthByLoginUserId(loginUserId);
//        List<String> collect = dataAuths.stream().map(TbCdcmrUserDataAuth::getAuthId).collect(Collectors.toList());
//        return scSymptomList.stream()
//                .collect(Collectors.toMap(TbCdcmrSympSymptom::getSymptomCode, e -> e, (k1, k2) -> k1))
//                .values().stream().map(e -> {
//                    CascadeVO vo = new CascadeVO();
//                    vo.setLabel(e.getSymptomName());
//                    vo.setValue(e.getSymptomCode());
//                    return vo;
//                }).filter(cascadeVO -> collect.contains(cascadeVO.getValue())).collect(Collectors.toList());
    }

    private static void getIncubation(SympWarnRuleExportDataDto sympWarnRuleExportDataDto) {
        if (sympWarnRuleExportDataDto.getIncubation() != null) {
            sympWarnRuleExportDataDto.setIncubation(sympWarnRuleExportDataDto.getIncubation() + "天");

        } else {
            sympWarnRuleExportDataDto.setIncubation("/");
        }
    }

    private static void getMaxLifeCycle(SympWarnRuleExportDataDto sympWarnRuleExportDataDto) {
        if (sympWarnRuleExportDataDto.getMaxLifeCycle() != null) {
            sympWarnRuleExportDataDto.setMaxLifeCycle(sympWarnRuleExportDataDto.getMaxLifeCycle() + "天");
        } else {
            sympWarnRuleExportDataDto.setMaxLifeCycle("/");
        }
    }

    private static void getMonitorObject(SympWarnRuleExportDataDto sympWarnRuleExportDataDto) {
        if ("1".equals(sympWarnRuleExportDataDto.getMonitorObject())) {
            sympWarnRuleExportDataDto.setMonitorObject("宿舍");
        } else if ("2".equals(sympWarnRuleExportDataDto.getMonitorObject())) {
            sympWarnRuleExportDataDto.setMonitorObject("班级");
        } else if ("3".equals(sympWarnRuleExportDataDto.getMonitorObject())) {
            sympWarnRuleExportDataDto.setMonitorObject("年级");
        } else if ("4".equals(sympWarnRuleExportDataDto.getMonitorObject())) {
            sympWarnRuleExportDataDto.setMonitorObject("校区");
        } else {
            sympWarnRuleExportDataDto.setMonitorObject("/");
        }
    }

    private static void getFormula(SympWarnRuleExportDataDto sympWarnRuleExportDataDto) {
        if ("0".equals(sympWarnRuleExportDataDto.getFormula())) {
            sympWarnRuleExportDataDto.setFormula("数值");
            sympWarnRuleExportDataDto.setMedicalCount("≥" + sympWarnRuleExportDataDto.getParam1());
        } else if ("1".equals(sympWarnRuleExportDataDto.getFormula())) {
            sympWarnRuleExportDataDto.setFormula("公式");
            sympWarnRuleExportDataDto.setMedicalCount("≥" + sympWarnRuleExportDataDto.getParam1() + "+ ( T -" + sympWarnRuleExportDataDto.getParam2() + ") ×" + sympWarnRuleExportDataDto.getParam3() + "%");
        } else {
            sympWarnRuleExportDataDto.setFormula("/");
        }
    }

    private static void getTimeScope(SympWarnRuleExportDataDto sympWarnRuleExportDataDto) {
        if (!org.springframework.util.StringUtils.isEmpty(sympWarnRuleExportDataDto.getTimeRange())) {
            if ("1".equals(sympWarnRuleExportDataDto.getTimeRangeUnit())) {
                sympWarnRuleExportDataDto.setTimeRange("≤" + sympWarnRuleExportDataDto.getTimeRange() + "天");
            } else if ("2".equals(sympWarnRuleExportDataDto.getTimeRangeUnit())) {
                sympWarnRuleExportDataDto.setTimeRange("≤" + sympWarnRuleExportDataDto.getTimeRange() + "周");
            } else if ("3".equals(sympWarnRuleExportDataDto.getTimeRangeUnit())) {
                sympWarnRuleExportDataDto.setTimeRange("≤" + sympWarnRuleExportDataDto.getTimeRange() + "月");
            }
        } else {
            sympWarnRuleExportDataDto.setTimeRange("/");
        }
    }

    private void updateRules(String warnId, List<TbCdcmrSympWarnRule> ruleList, String loginUserId) {
        ruleList.forEach(rule -> {
            if (rule.getId() == null) {
                rule.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_symp_warn_rule")));
            }
            rule.setWarnId(warnId);
            rule.setStatus(1);
            rule.setIsDeleted(0);
            rule.setUpdater(loginUserId);
            rule.setUpdateTime(new Date());
        });

        List<String> idList = ruleList.stream().map(TbCdcmrSympWarnRule::getId).collect(Collectors.toList());
        tbCdcmrSympWarnRuleMapper.deleteOtherByIds(idList, warnId);
        tbCdcmrSympWarnRuleMapper.upsertRules(ruleList);
    }
}
