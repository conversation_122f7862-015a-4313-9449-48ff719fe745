package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.SearchSyndromeInfoDTO;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.SyndromeInfo;
import com.iflytek.cdc.admin.sdk.entity.SyndromeInfoFilter;
import com.iflytek.cdc.admin.sdk.pojo.SyndInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/26 15:38
 **/
public interface SyndromeService {

    /**
     * 分页查询症候群标准信息维护
     * @param sd
     * @return
     **/
    PageInfo<SyndromeInfo>  querySyndromeInfoPage(SearchSyndromeInfoDTO sd);

    void insertSyndromeInfo(String loginUserId, SyndromeInfo syndromeInfo);
    /**
     * 编辑症候群标准信息
     * @param loginUserId
     * @param si
     * @return
     **/
    void  updateSyndromeInfo(String loginUserId,SyndromeInfo  si);

    /**
     * 症候群主数据同步
     * @param loginUserId
     * @return
     **/
    void syndromeDataSync(String loginUserId);

    /**
     * 判断是否有权限可以启用
     * @param id
     * @return
     **/
    boolean judgeIsUse(String  id);

    PageInfo<SyndInfo>  syndromeInfoPage(SyndromeInfoFilter filter);

    /**
     * 根据症候群编号查询信息
     * @param code
     * @return
     **/
    SyndInfo  syndInfoByCode(String  code);

    List<CascadeVO> getSyndromeList();

    List<CascadeVO> getSyndromeList(String loginUserId);


}
