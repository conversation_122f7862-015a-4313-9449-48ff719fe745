package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.entity.TbCdcmrDiseaseMonitorRule;
import com.iflytek.cdc.admin.service.MonitorConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "监测配置维护")
public class MonitorConfigController {

    @Resource
    private MonitorConfigService monitorConfigService;

    @PostMapping("/{version}/pt/monitor/config/getMonitorConfigMsg")
    @ApiOperation("查询监测配置维护信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<TbCdcmrDiseaseMonitorRule> getMonitorConfigMsg(@PathVariable String version,
                                                               @RequestParam(required = false) String diseaseCode,
                                                               @RequestParam(required = false) String diseaseName,
                                                               @RequestParam String configType) {

        return monitorConfigService.getMonitorConfigMsg(diseaseCode,diseaseName, configType);
    }

    @PostMapping("/{version}/pt/monitor/config/getMonitorConfigByIds")
    @ApiOperation("通过id查询监测配置维护信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<TbCdcmrDiseaseMonitorRule> getMonitorConfigByIds(@PathVariable String version,
                                                                @RequestBody List<String> ids) {

        return monitorConfigService.getMonitorConfigByIds(ids);
    }

    @PostMapping("/{version}/pt/monitor/config/updateMonitorConfig")
    @ApiOperation("更新监测配置维护信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateMonitorConfig(@PathVariable String version,
                                    @RequestParam String loginUserId,
                                    @RequestBody TbCdcmrDiseaseMonitorRule tbCdcmrDiseaseMonitorRule) {

        monitorConfigService.updateMonitorConfig(loginUserId, tbCdcmrDiseaseMonitorRule);
    }
}
