package com.iflytek.cdc.admin.outbound.service;


import com.iflytek.cdc.admin.outbound.model.dto.SignDTO;
import com.iflytek.cdc.admin.outbound.model.dto.input.CallTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.PlanTask;
import com.iflytek.cdc.admin.outbound.model.dto.input.SmsTask;
import com.iflytek.cdc.admin.outbound.model.dto.output.CreateCallRs;
import com.iflytek.cdc.admin.outbound.util.Response;

/**
 * 执行外呼服务
 * <AUTHOR>
 */
public interface CreateOutboundService {

    /**
     * 执行外呼（用方案）
     * @param planTask
     * @return
     */
    Response<CreateCallRs> execPlanTask(PlanTask planTask);

    /**
     * 执行外呼（用话术）
     * @param callTask
     * @return
     */
    Response<CreateCallRs> execCallTask(CallTask callTask);

    /**
     * 执行短信
     * @param smsTask
     * @return
     */
    Response<CreateCallRs> execSmsTask(SmsTask smsTask);

    /**
     * 获取鉴权信息
     * @return
     */
    SignDTO getSign();

}
