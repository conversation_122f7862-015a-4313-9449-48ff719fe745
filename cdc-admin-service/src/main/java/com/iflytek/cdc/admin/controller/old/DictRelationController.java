package com.iflytek.cdc.admin.controller.old;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.DictRelationConstants;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.DictRelation;
import com.iflytek.cdc.admin.entity.DictRelationDirectory;
import com.iflytek.cdc.admin.sdk.entity.DictRelationFilter;
import com.iflytek.cdc.admin.sdk.pojo.DictValueInfo;
import com.iflytek.cdc.admin.sdk.pojo.InfectedValueInfo;
import com.iflytek.cdc.admin.service.DictRelationService;
import com.iflytek.cdc.admin.service.DictRelationTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/25 11:16
 **/
@RestController
@Api(tags = "业务字典关联接口")
public class DictRelationController {

    private final DictRelationService  dService;

    public DictRelationController(DictRelationService dService) {
        this.dService = dService;
    }


    @GetMapping("/{version}/pt/dict/relation/queryDirectory")
    @ApiOperation("查询mdm字典目录")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<DictRelationGroupDTO> queryDirectory(@PathVariable String version){
        return dService.queryDirectory(DictRelationConstants.PAGE_INDEX,DictRelationConstants.PAGE_SIZE);
    }

    @PostMapping("/{version}/pt/dict/relation/insertDirectory")
    @ApiOperation("新增字典关联目录")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void insertDirectory(@PathVariable String version, @RequestParam String loginUserId, @RequestBody DictRelationDirectory directory){
         dService.insertDirectory(directory,loginUserId);
    }

    @GetMapping("/{version}/pt/dict/relation/queryDirectoryList")
    @ApiOperation("查询字典关联目录列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<DictRelationDirectory> queryDirectoryList(@PathVariable String version, @RequestParam String directoryName){
       return  dService.queryDirectoryList(directoryName);
    }

    @GetMapping("/{version}/pt/dict/relation/queryDirectoryById")
    @ApiOperation("根据id查询字典关联目录详细信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public DictRelationDirectory queryDirectoryById(@PathVariable String version, @RequestParam String directoryId){
        return  dService.queryDirectoryById(directoryId);
    }

    @GetMapping("/{version}/pt/dict/relation/deleteDirectory")
    @ApiOperation("删除字典目录")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteDirectory(@PathVariable String version, @RequestParam String  id,@RequestParam String loginUserId){
          dService.deleteDirectory(id,loginUserId);
    }

    @PostMapping("/{version}/pt/dict/relation/queryDictValuePage")
    @ApiOperation("分页查询字典值域列表")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo  queryDictValuePage(@PathVariable String version, @RequestBody SearchDictValueDTO sd){
        return dService.queryOriginDictValueList(sd);
    }

    @PostMapping("/{version}/pt/dict/relation/insertRelation")
    @ApiOperation("建立关联关系")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void insertRelation(@PathVariable String version, @RequestBody DictRelation  dr,@RequestParam String  loginUserId){
       dService.insertRelation(dr,loginUserId);
    }

    @PostMapping("/{version}/pt/dict/relation/deleteRelation")
    @ApiOperation("解除关联关系")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteRelation(@PathVariable String version,@RequestBody DeleteRelationDTO relationDTO){
        dService.deleteRelation(relationDTO);
    }

    @PostMapping("/{version}/pt/dict/relation/exportDictRelation")
    @ApiOperation("导出字典映射关联关系")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void exportDictRelation(@PathVariable String version, HttpServletResponse response,@RequestBody SearchExportRelationDTO sd, @RequestParam("loginUserId") String loginUserId) {
         dService.exportRelation(sd,response);
    }

    @PostMapping("/{version}/pt/dict/relation/updateRelationName")
    @ApiOperation("修改关联字典目录名称")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void updateRelationName(@PathVariable String version, @RequestBody UpdateRelationDTO ud,@RequestParam String loginUserId){
        dService.updateRelationName(ud,loginUserId);
    }

    @PostMapping("/{version}/pt/dict/relation/relationDictValueInfo")
    @ApiOperation("查询映射字典信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<DictValueInfo>  relationDictValueInfo(@PathVariable String version, @RequestBody DictRelationFilter  filter){
       return  dService.queryDictValueInfo(filter);
    }

    @PostMapping("/{version}/pt/dict/relation/infectedValueDetail")
    @ApiOperation("查询病种详细信息")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<InfectedValueInfo>  relationInfectedValueDetail(@PathVariable String version, @RequestBody DictRelationFilter  filter){
        return  dService.queryInfectedValueDetail(filter);
    }
}
