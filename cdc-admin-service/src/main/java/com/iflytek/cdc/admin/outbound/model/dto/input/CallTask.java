package com.iflytek.cdc.admin.outbound.model.dto.input;


import com.iflytek.cdc.admin.outbound.model.dto.input.param.Dweller;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Recall;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Sms;
import com.iflytek.cdc.admin.outbound.model.dto.input.param.Var;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 外呼开放接口（用话术）
 * 应用场景：单次随访，只有电话随访，可以附带短信，可以自定义变量
 * <AUTHOR>
 */
@Data
public class CallTask {

    @ApiModelProperty(value = "要外呼的居民信息（居民数不超过500）", required = true)
    private List<Dweller> dwellers;

    @ApiModelProperty(value = "话术id（讯飞医疗提供）", required = true)
    private Long speechId;

    @ApiModelProperty(value = "任务名称（不填将默认生成一键随访 yyyyMMddHHmmss）")
    private String planName;

    @ApiModelProperty(value = "话术动态参数")
    private List<Var> vars;

    @ApiModelProperty(value = "时间类型(1：即呼，2：指定日期)")
    private Integer type;

    @ApiModelProperty(value = "指定日期(type = 2 时生效) 格式：yyyy-MM-dd HH:mm:ss")
    private String date;

    @ApiModelProperty(value = "是否启用重播提醒,不启用可不传")
    private Recall recall;

    @ApiModelProperty(value = "是否启用短信提醒,不启用可不传")
    private Sms sms;
}
