package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.OutpatientQueryDTO;
import com.iflytek.cdc.admin.dto.OutpatientWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientType;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface OutpatientWarnService {
    PageInfo<TbCdcmrOutpatientType> getOutpatientPageList(OutpatientQueryDTO poisoningQueryDTO);

    void addOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId);

    void deleteOutpatient(String id);

    void updateOutpatient(TbCdcmrOutpatientType tbCdcmrOutpatient, String loginUserId);

    PageInfo<OutpatientWarnDto> getOutpatientWarnPageList(OutpatientQueryDTO poisoningQueryDTO);

    void updateOutpatientWarn(OutpatientWarnDto poisoningWarnDto, String loginUserId);

    OutpatientWarnDto getWarnById(String warnId);

    void updateOutpatientWarnStatus(OutpatientWarnDto poisoningWarnDto, String loginUserId);

    List<OutpatientWarnDto> getOutpatientWarnAllList(String poisoningCode);

    void exportOutpatientWarnRule(HttpServletResponse response);

    List<CascadeVO> getOutpatientType();

    List<CascadeVO> getOutpatientTypeNameList();

    List<CascadeVO> getOutpatientTypeNameList(String loginUserId);
}
