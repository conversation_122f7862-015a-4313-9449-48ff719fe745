package com.iflytek.cdc.admin.outbound.controller;

import com.iflytek.cdc.admin.outbound.entity.OutboundTemplate;
import com.iflytek.cdc.admin.outbound.service.OutboundTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "外呼模板接口")
@RequestMapping("/pt/v1/outbound-template")
public class OutboundTemplateController {

    @Resource
    private OutboundTemplateService  outboundTemplateService;

    @GetMapping("/listByTypeAndCategory")
    @ApiOperation("根据类型和分组查询模板数据")
    public List<OutboundTemplate> listByTypeAndCategory(@RequestParam(required = false) String type, @RequestParam String category){
        return outboundTemplateService.listByTypeAndCategory(type, category);
    }

    @GetMapping("/loadById")
    @ApiOperation("根据id查询")
    public OutboundTemplate loadById(@RequestParam String id){
        return outboundTemplateService.loadCascade(id);
    }
}
