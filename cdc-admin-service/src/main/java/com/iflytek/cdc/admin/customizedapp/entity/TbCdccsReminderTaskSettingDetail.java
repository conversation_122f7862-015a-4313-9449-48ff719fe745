package com.iflytek.cdc.admin.customizedapp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 消息提醒任务配置详情表;
 * <AUTHOR> fengwang35
 * @date : 2024-10-29
 */
@ApiModel(value = "消息提醒任务配置详情表")
@TableName("tb_cdccs_reminder_task_setting_detail")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsReminderTaskSettingDetail extends BaseEntity implements Serializable{
    public static final String TABLE_NAME = "tb_cdccs_reminder_task_setting_detail";

    /** 页面id */
    @ApiModelProperty(value = "页面id")
    private String taskSettingId ;

    /** 起始日期所在表 */
    @ApiModelProperty(value = "起始日期所在表")
    private String startDateTableKey ;

    @ApiModelProperty(value = "开始日期的数据模型field")
    private String startDateModelField;

    /** 起始日期所在列 */
    @ApiModelProperty(value = "起始日期所在列")
    private String startDateTableColumn ;

    @ApiModelProperty(value = "提醒周期json字段配置")
    private String  reminderCycleSettingJson;

    /** 提醒方式 */
    @ApiModelProperty(value = "提醒方式")
    private String reminderMethod ;

    /** 提醒文案模板 */
    @ApiModelProperty(value = "提醒文案模板")
    private String reminderTemplate ;

    /** 提醒对象类型 */
    @ApiModelProperty(value = "提醒对象类型")
    private String reminderObjectType ;

    /** 提醒对象id集合 */
    @ApiModelProperty(value = "提醒对象id集合")
    private String reminderObjectIds ;

    /** 生成的任务数据模型id */
    @ApiModelProperty(value = "生成的任务数据模型id")
    private String dataModelId ;

    /** 是否开启AI随访 */
    @ApiModelProperty(value = "是否开启AI随访")
    private String enableAi ;

    @ApiModelProperty(value = "任务周期配置")
    @TableField(exist = false)
    private List<ReminderCycleSetting> reminderCycleSettingList;

    @Data
    public static class ReminderCycleSetting{
        /** 提醒周期次数 */
        @ApiModelProperty(value = "提醒周期次数")
        private Integer reminderCycleNum ;
        /** 提醒周期日期单位 */
        @ApiModelProperty(value = "提醒周期日期单位")
        private String reminderCycleUnit ;
        /** 提醒次数 */
        @ApiModelProperty(value = "提醒次数")
        private Integer reminderTotalNum ;
    }

}