package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.entity.mr.TbCdcmrUserPageConfig;
import com.iflytek.cdc.admin.service.province.UserPageConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "用户页面配置")
@RequestMapping("/pt/{version}/userPageConfig")
@RestController
public class UserPageConfigController {
    
    @Resource
    private UserPageConfigService userPageConfigService;
    
    /**
     * 保存配置
     */
    @PostMapping("/saveConfig")
    @ApiOperation("保存配置")
   public void saveConfig(@RequestBody TbCdcmrUserPageConfig pageConfig, @RequestParam String loginUserId){
        userPageConfigService.saveConfig(pageConfig);
   }

    /**
     * 根据页面编码及用户id查询
     */
    @GetMapping("/loadPageConfig")
    @ApiOperation("根据页面编码及用户id查询")
   public TbCdcmrUserPageConfig loadPageConfig(@RequestParam String loginUserId, @RequestParam String pageCode){
        return userPageConfigService.loadPageConfig(loginUserId, pageCode);
   }
   
   /**
     * 删除配置
     */
    @PostMapping("/deleteUserConfig")
    @ApiOperation("删除用户配置")
   public void deleteUserConfig(@RequestParam String userId, @RequestParam String pageCode){
        userPageConfigService.deleteUserConfig(userId, pageCode);
   }
}
