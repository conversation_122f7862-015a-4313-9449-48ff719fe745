package com.iflytek.cdc.admin.outbound.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.outbound.entity.OutboundPerson;
import com.iflytek.cdc.admin.outbound.model.vo.OutboundPersonVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OutboundPersonMapper extends BaseMapper<OutboundPerson> {

    /**
     * 根据结果记录id查询
     */
    List<OutboundPersonVO> listByRecordIds(@Param("recordIds") List<String> recordIds);
}
