package com.iflytek.cdc.admin.service.province;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.dto.RiskLevelDetailQueryDTO;
import com.iflytek.cdc.admin.dto.WarningRiskLevelDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevelDetail;
import com.iflytek.cdc.admin.sdk.entity.ParamInfoFilter;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import com.iflytek.cdc.admin.vo.epi.SignalProcessingLimitConfigVO;

import java.util.List;

/**
 * 传染病预警-风险等级配置表 服务类
 *
 */
public interface WarningRiskLevelDetailService extends IService<TbCdcmrWarningRiskLevelDetail> {

    /**
     * 查询风险等级详情
     */
    List<RiskLevelDetailVO> listRiskLevelDetail(RiskLevelDetailQueryDTO queryDTO);

    /**
     * 加载风险等级详情
     */
    RiskLevelDetailVO loadByDiseaseId(String diseaseId,
                                      String riskLevelCode,
                                      String warningType);

    /**
     * 变更风险等级
     */
    TbCdcmrWarningRiskLevelDetail saveRiskLevelDetail(WarningRiskLevelDTO dto);

    /**
     * 保存风险等级
     */
    boolean save(TbCdcmrWarningRiskLevelDetail detail);

    /**
     * 查询风险等级
     */
    List<TbCdcmrWarningRiskLevelDetail> getRiskLevelDetailBy(List<String> idList);

    List<SignalProcessingLimitConfigVO> getSignalProcessingLimitConfig(List<String> signalNames);

    SignalProcessingLimitConfigVO getSignalProcessingLimitConfigVO(String signalName);
}
