package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonDisease;

public interface UnknownReasonDiseaseService {
    PageInfo<TbCdcmrUnknownReasonDisease> getPageList(UnknownReasonQueryDto dto);

    void add(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId);

    void delete(String id);

    void update(TbCdcmrUnknownReasonDisease tbCdcmrUnknownReasonDisease, String loginUserId);
}
