package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.iflytek.cdc.admin.constant.UapOrgConstants;
import com.iflytek.cdc.admin.dto.PlunOrgChangeDto;
import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.enums.AreaEnums;
import com.iflytek.cdc.admin.mapper.*;
import com.iflytek.cdc.admin.sdk.pojo.HisOrgInfo;
import com.iflytek.cdc.admin.service.PlunOrgUpdateService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.service.UapOrgApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/19
 **/
@Service
@Slf4j
public class PlunOrgUpdateServiceImpl implements PlunOrgUpdateService {

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private OrgMappingMapper orgMappingMapper;

    @Resource
    private UapOrgApi uapOrgApi;

    @Override
    public ResponseResult orgChangeMonitor(PlunOrgChangeDto orgChangeDto) {
        try {

            UapOrganization uapOrganization = new UapOrganization();
            BeanUtils.copyProperties(orgChangeDto, uapOrganization);

            //1、新增
            if (UapOrgConstants.CREATE.equals(orgChangeDto.getChangeType())) {
                insertOrg(uapOrganization);
            }
            //2、更新
            if (UapOrgConstants.UPDATE.equals(orgChangeDto.getChangeType())){
                updateOrg(uapOrganization);
            }

            //3、删除
            if (UapOrgConstants.DELETE.equals(orgChangeDto.getChangeType())){
                deleteOrg(orgChangeDto);
            }
        }catch (Exception e){
            log.error("同步插件机构成功异常：{}", e);
            throw new MedicalBusinessException("同步插件机构成功异常");
        }
        return new ResponseResult("同步插件机构成功");
    }

    private void updateOrg(UapOrganization uapOrganization) {
        orgMappingMapper.updateUapOrg(uapOrganization);
        extracted(uapOrganization);
    }

    private void insertOrg(UapOrganization uapOrganization) {
        //判断该机构在映射表是否有数据
        HisOrgInfo hisOrgInfo = orgMappingMapper.queryHisByUap(uapOrganization.getId());
        if (ObjectUtil.isEmpty(hisOrgInfo)) {
            uapOrganization.setIsMatch("0");
        } else {
            uapOrganization.setIsMatch("1");
        }
        orgMappingMapper.insertUapOrg(uapOrganization);
        extracted(uapOrganization);

    }

    /**
     * 新增或修改机构，重新写入卫生院映射和功能区映射
     * @param uapOrganization
     */
    private void extracted(UapOrganization uapOrganization) {
        //执行卫生院映射
        orgMappingMapper.healthCenter(uapOrganization.getId());
        //执行功能区映射
        AreaEnums areaEnum = AreaEnums.getAreaEnum(uapOrganization.getDistrictCode());
        if (areaEnum != null) {
            Map<String,String> params = new HashMap<>();
            params.put("districtCode",areaEnum.getDistrictCode());
            params.put("standardDistrictCode",areaEnum.getStandardDistrictCode());
            params.put("standardDistrictName",areaEnum.getStandardDistrictName());
            orgMappingMapper.functionalArea(params);
        }
    }


    private void deleteOrg(PlunOrgChangeDto orgChangeDto) {
        //采用逻辑删除
       orgMappingMapper.deleteUapOrgById(orgChangeDto.getId());
    }
}
