package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgPo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.RiskReportPushDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordQueryDto;
import com.iflytek.cdc.admin.dto.RiskReportPushUserInfoDto;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;
import com.iflytek.cdc.admin.entity.TbCdcmrRiskReportPushRecord;
import com.iflytek.cdc.admin.mapper.RiskReportPushMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportTaskMapper;
import com.iflytek.cdc.admin.service.RiskReportPushService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class RiskReportPushServiceImpl implements RiskReportPushService {

    private static final String TABLE_NAME = "tb_cdcmr_risk_report_push_record";

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private TbCdcmrExportTaskMapper exportTaskMapper;

    @Autowired
    public void setExportTaskMapper(TbCdcmrExportTaskMapper exportTaskMapper) {
        this.exportTaskMapper = exportTaskMapper;
    }

    private RiskReportPushMapper riskReportPushMapper;

    @Autowired
    public void setRiskReportPushMapper(RiskReportPushMapper riskReportPushMapper) {
        this.riskReportPushMapper = riskReportPushMapper;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    @Override
    public Boolean pushReport(String loginUserId, RiskReportPushDto dto) {
        String recipientId = dto.getRecipientId();
        if (StrUtil.isBlank(recipientId)) {
            log.error("缺少被推送人信息");
            return false;
        }
        String taskId = dto.getTaskId();
        // 查询该报告
        Boolean flag = checkReport(taskId);
        if (!flag) {
            return false;
        }
        UapUserPo recipientInfo = uapServiceApi.getUser(recipientId);
        UapUserPo senderInfo = uapServiceApi.getUser(loginUserId);
        if (recipientInfo == null) {
            log.error("未查询到被推送人信息");
            return false;
        }
        if (senderInfo == null) {
            log.error("未查询到推送人信息");
            return false;
        }

        RiskReportPushUserInfoDto recipient = new RiskReportPushUserInfoDto();
        recipient.setUserId(recipientInfo.getId());
        recipient.setUserName(recipientInfo.getName());
        recipient.setOrgId(recipientInfo.getOrgId());

        RiskReportPushUserInfoDto sender = new RiskReportPushUserInfoDto();
        sender.setUserId(senderInfo.getId());
        sender.setUserName(senderInfo.getName());
        sender.setOrgId(senderInfo.getOrgId());
        // 构造推送记录对象
        TbCdcmrRiskReportPushRecord tbCdcmrRiskReportPushRecord = new TbCdcmrRiskReportPushRecord();
        tbCdcmrRiskReportPushRecord.setId(String.valueOf(batchUidService.getUid(TABLE_NAME)));
        tbCdcmrRiskReportPushRecord.setPushTime(new Date());
        tbCdcmrRiskReportPushRecord.setRecipient(recipient);
        tbCdcmrRiskReportPushRecord.setSender(sender);
        tbCdcmrRiskReportPushRecord.setTaskId(taskId);

        UapOrgPo recipientOrg = uapServiceApi.getUserOrg(recipientInfo.getLoginName());
        String district = recipientOrg.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            tbCdcmrRiskReportPushRecord.setDistrictName(district);
            tbCdcmrRiskReportPushRecord.setDistrictCode(recipientOrg.getDistrictCode());
        }

        String city = recipientOrg.getCity();
        if (StrUtil.isNotBlank(city)) {
            tbCdcmrRiskReportPushRecord.setCityName(city);
            tbCdcmrRiskReportPushRecord.setCityCode(recipientOrg.getCityCode());
        }

        String province = recipientOrg.getProvince();
        if (StrUtil.isNotBlank(province)) {
            tbCdcmrRiskReportPushRecord.setProvinceName(province);
            tbCdcmrRiskReportPushRecord.setProvinceCode(recipientOrg.getProvinceCode());
        }

        tbCdcmrRiskReportPushRecord.setCreatorId(loginUserId);
        tbCdcmrRiskReportPushRecord.setCreator(senderInfo.getName());
        tbCdcmrRiskReportPushRecord.setCreateTime(new Date());

        tbCdcmrRiskReportPushRecord.setUpdatorId(loginUserId);
        tbCdcmrRiskReportPushRecord.setUpdator(senderInfo.getName());
        tbCdcmrRiskReportPushRecord.setUpdateTime(new Date());

        int i = this.riskReportPushMapper.insert(tbCdcmrRiskReportPushRecord);
        return i > 0;
    }

    private Boolean checkReport(String taskId) {
        TbCdcmrExportTask tbCdcmrExportTask = exportTaskMapper.selectById(taskId);
        if (tbCdcmrExportTask == null){
            log.error("未查询报告信息, taskid : {}", taskId);
            return false;
        }
        String deleteFlag = tbCdcmrExportTask.getDeleteFlag();
        if (!StrUtil.equals(deleteFlag, "0")){
            log.error("当前查询报告信息已删除, taskid : {}", taskId);
            return false;
        }
        Integer status = tbCdcmrExportTask.getStatus();
        if (status != 2){
            log.error("当前报告未完成, taskid : {}", taskId);
            return false;
        }
        return true;
    }

    @Override
    public PageInfo<RiskReportPushRecordDto> queryList(String loginUserId,RiskReportPushRecordQueryDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<RiskReportPushRecordDto> list = this.riskReportPushMapper.queryList(loginUserId,dto);
        return new PageInfo<>(list);
    }
}
