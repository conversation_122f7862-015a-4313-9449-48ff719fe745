package com.iflytek.cdc.admin.controller.province;


import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.SyndromeWarningRuleConstants;
import com.iflytek.cdc.admin.dto.RiskLevelDetailQueryDTO;
import com.iflytek.cdc.admin.dto.WarningRiskLevelDTO;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningRiskLevel;
import com.iflytek.cdc.admin.enums.WarningTypeProEnum;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelDetailService;
import com.iflytek.cdc.admin.service.province.WarningRiskLevelService;
import com.iflytek.cdc.admin.vo.RiskLevelDetailVO;
import com.iflytek.cdc.admin.vo.epi.SignalProcessingLimitConfigVO;
import com.iflytek.cdc.admin.workbench.annotation.OperationLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 传染病预警-风险等级配置表 前端控制器
 */
@RestController
@Api(tags = "预警风险等级维护")
public class WarningRiskLevelDetailController {
    @Resource
    private WarningRiskLevelDetailService warningRiskLevelDetailService;

    @Resource
    private WarningRiskLevelService warningRiskLevelService;

    @ApiOperation("预警风险等级列表查询")
    @GetMapping("/pt/{version}/warningRiskLevelDetail/listRiskLevel")
    public List<TbCdcmrWarningRiskLevel> listRiskLevel() {

        return warningRiskLevelService.listRiskLevelByWarningType();
    }

    @ApiOperation("预警风险等级列表查询")
    @GetMapping("/pt/{version}/warningRiskLevelDetail/listDiseaseRiskLevel")
    public List<RiskLevelDetailVO> listDiseaseRiskLevel(@RequestParam String warningType) {
        RiskLevelDetailQueryDTO queryDTO = new RiskLevelDetailQueryDTO();
        //使用公共处理时限的预警类型 统一转化
        queryDTO.setWarningType(WarningTypeProEnum.commonTimeLimitWarning.contains(warningType) ?
                Constants.COMMON_TIME_LIMIT : warningType);
        return warningRiskLevelDetailService.listRiskLevelDetail(queryDTO);
    }

    @ApiOperation("保存预警风险等级详情")
    @OperationLogAnnotation(operationName = "保存预警风险等级详情")
    @PostMapping("/pt/{version}/warningRiskLevelDetail/saveRiskLevelDetail")
    public void saveRiskLevelDetail(@RequestBody WarningRiskLevelDTO dto) {

        warningRiskLevelDetailService.saveRiskLevelDetail(dto);
    }

    @ApiOperation("获取信号处理时限配置")
    @PostMapping("/pt/{version}/warningRiskLevelDetail/getSignalProcessingLimitConfig")
    public List<SignalProcessingLimitConfigVO> getSignalProcessingLimitConfig(@RequestBody List<String> signalNames){
        return warningRiskLevelDetailService.getSignalProcessingLimitConfig(signalNames);
    }
}

