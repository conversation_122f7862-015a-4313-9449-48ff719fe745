package com.iflytek.cdc.admin.datamodel.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单页面配置表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-2
 */
@ApiModel(value = "表单页面配置表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcdmDataFormTemplate implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "主键ID")
    private String formTemplateDetailId ;

    /**
     * 页面模板代码
     */
    @ApiModelProperty(name = "页面模板代码")
    private String formTemplateCode ;

    /**
     * 页面模板名称
     */
    @ApiModelProperty(name = "页面模板名称")
    private String formTemplateName ;

    /**
     * 配置信息
     */
    @ApiModelProperty(name = "配置信息")
    private String configInfo ;

    /**
     * 配置json
     */
    @ApiModelProperty(name = "配置json")
    private String configJson ;

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型ID")
    private String modelId ;

    /**
     * 数据模型ID
     */
    @ApiModelProperty(name = "数据模型name")
    private String modelName ;

    /**
     * 数据模型版本ID
     */
    @ApiModelProperty(name = "数据模型版本ID")
    private String modelVersionId ;

    /**
     * 是否启用
     */
    @ApiModelProperty(name = "是否启用")
    private Integer isEnable ;

    /**
     * 是否删除
     */
    @ApiModelProperty(name = "是否删除")
    private Integer isDeleted ;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人")
    private String creator ;

    /**
     * 修改时间
     */
    @ApiModelProperty(name = "修改时间")
    private Date updateTime ;

    /**
     * 修改人
     */
    @ApiModelProperty(name = "修改人")
    private String updater ;

    /**
     * 模型使用关键字段，用于串联该模型下所有的表
     */
    @ApiModelProperty(value = "模型使用关键字段，用于串联该模型下所有的表")
    private String keyField ;

    /**
     * 数据搜索时主表id
     */
    @ApiModelProperty(value = "数据搜索时主表id")
    private String masterTableId ;

    /**
     * 数据搜索时主表
     */
    @ApiModelProperty(value = "数据搜索时主表")
    private String masterTable ;

    /**
     * 该数据模型展示字段(主表中不可变字段)
     */
    @ApiModelProperty(value = "该数据模型展示字段(主表中不可变字段)")
    private String fieldsConfig ;

    /**
     * 数据模型属于该页面的某一个组
     */
    @ApiModelProperty(value = "数据模型属于该页面的某一个组")
    private String modelGroup ;

    /**
     * 前端配置
     */
    @ApiModelProperty(value = "前端配置")
    private String webConfig ;

    @ApiModelProperty(value = "关联疾病")
    private String relatedDisease;

    @ApiModelProperty(value = "业务主键")
    private String businessKey;

    /**
     * 检索表
     */
    @ApiModelProperty(value = "检索表")
    private String retrieveTable ;

    @ApiModelProperty(value = "es 索引名")
    private String esIndexName;

    /**
     * es 索引模板；不加入 json
     */
    private String esIndexTemplate;
}
