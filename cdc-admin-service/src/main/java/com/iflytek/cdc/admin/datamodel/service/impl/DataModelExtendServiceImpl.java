package com.iflytek.cdc.admin.datamodel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataModelExtend;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmEntityErModel;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmDataModelExtendMapper;
import com.iflytek.cdc.admin.datamodel.service.DataModelExtendService;
import com.iflytek.cdc.admin.datamodel.service.EntityErModelService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DataModelExtendServiceImpl extends CdcServiceBaseImpl<TbCdcdmDataModelExtendMapper, TbCdcdmDataModelExtend> implements DataModelExtendService {

    @Resource
    private TbCdcdmDataModelExtendMapper tbCdcdmDataModelExtendMapper;

    @Resource
    private EntityErModelService entityErModelService;

    @Override
    public void saveDataModelExtend(String modelId, String modelVersionId, String entityErModelId) {
        TbCdcdmDataModelExtend existed = loadByModelIdAndVersionId(modelId, modelVersionId);
        if (existed != null){
            existed.setEntityErModelId(entityErModelId);
            update(existed);
        }else {
            TbCdcdmDataModelExtend tbCdcdmDataModelExtend = new TbCdcdmDataModelExtend();
            tbCdcdmDataModelExtend.setModelId(modelId);
            tbCdcdmDataModelExtend.setEntityErModelId(entityErModelId);
            tbCdcdmDataModelExtend.setModelVersionId(modelVersionId);
            create(tbCdcdmDataModelExtend);
        }
    }

    @Override
    public String loadErModelIdByModelId(String modelId) {
        return tbCdcdmDataModelExtendMapper.loadErModelIdByModelId(modelId);
    }

    @Override
    public TbCdcdmEntityErModel loadByModelId(String modelId) {
        String erModelId = loadErModelIdByModelId(modelId);
        if(StringUtils.isEmpty(erModelId)){
            throw new MedicalBusinessException("数据模型没有配置ER模型");
        }
        return entityErModelService.load(erModelId);
    }

    /**
     * 通过模型和版本id查询
     */
    public TbCdcdmDataModelExtend loadByModelIdAndVersionId(String modelId, String versionId){
        LambdaQueryWrapper<TbCdcdmDataModelExtend> queryWrapper = lambdaQueryWrapper();
        queryWrapper.eq(TbCdcdmDataModelExtend::getModelId, modelId).eq(TbCdcdmDataModelExtend::getModelVersionId, versionId);
        return getOne(queryWrapper);
    }
}
