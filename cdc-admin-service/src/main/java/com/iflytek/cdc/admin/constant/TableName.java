package com.iflytek.cdc.admin.constant;

/**
 * 数据库表操作常量类
 */
public class TableName {

    private TableName() {

    }
    public static final String CLIENT_UPGRADE_URL = "tb_cdcmr_client_upgrade_url";

    public static final String CLIENT_UPGRADE_VERSION = "tb_cdcmr_client_upgrade_version";
    public static final String INFECTIOUS_DISASES_LEXICON = "infected_disease_lexicon";
    public static final String LEXICON_ASSOCIATION = "tb_cdcmr_lexicon_association";
    public static final String CLIENT_UPGRADE_DISTRICT = "tb_cdcmr_client_upgrade_district";

    public static final String INFECTIOUS_DISASES = "tb_cdcmr_infectious_diseases";

    public static final String INFECTIOUS_DIAGSIS = "tb_cdcmr_infectious_diagnosis";

    public static final String ADDRESS_DETAIL_MAPPING = "tb_cdcmr_address_detail_mapping";

    public static final String USER_MANUAL = "tb_cdcmr_user_manual";

    public static final String MEDICAL_WARN = "tb_cdcmr_medical_warn";

    public static final String MEDICAL_WARN_RULE = "tb_cdcmr_medical_warn_rule";

    public static final String TB_CDCBR_TEMPLATE = "tb_cdcbr_template";
    public static final String TB_CDCBR_TEMPLATE_RULE = "tb_cdcbr_template_rule";
    public static final String TB_CDCBR_TEMPLATE_RULE_DETAIL = "tb_cdcbr_template_rule_detail";
    public static final String TB_CDCBR_PUSH_USER = "tb_cdcbr_push_user";
    public static final String TB_CDCBR_TEMPLATE_INDICATORS = "tb_cdcbr_template_indicators";
    public static final String TB_CDCBR_BRIEF_INFO = "tb_cdcbr_brief_info";
    public static final String TB_CDCBR_BRIEF_INDICATORS = "tb_cdcbr_brief_indicators";
    public static final String TB_CDCBR_PUSH_RECORD = "tb_cdcbr_push_record";
    
    public static final String LEXICON_ASSOCIATION_DIRECTORY = "tb_cdcmr_lexicon_association_directory";
}
