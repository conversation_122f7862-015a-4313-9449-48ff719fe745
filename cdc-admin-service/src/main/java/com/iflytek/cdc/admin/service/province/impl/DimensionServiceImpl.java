package com.iflytek.cdc.admin.service.province.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.cdc.admin.constant.InfectedDataConstants;
import com.iflytek.cdc.admin.entity.mr.*;
import com.iflytek.cdc.admin.mapper.province.*;
import com.iflytek.cdc.admin.model.mr.vo.InfectedDiseaseWarningVO;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.DimensionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DimensionServiceImpl implements DimensionService {

    @Resource
    private TbCdcmrSyndromeDiseaseInfoMapper syndromeDiseaseInfoMapper;

    @Resource
    private TbCdcmrInfectedDiseaseInfoMapper infectedDiseaseInfoMapper;

    @Resource
    private TbCdcmrEmergingDiseaseInfoMapper emergingDiseaseInfoMapper;

    @Resource
    private TbCdcmrEndemicDiseaseInfoMapper endemicDiseaseInfoMapper;

    @Resource
    private TbCdcmrPathogenInfoMapper tbCdcmrPathogenInfoMapper;

    private static final String MAX_NUM = "9999";

    /**
     * 传染病父级诊断+病毒性肝炎子级
     * @param infectedClassCode
     * @param infectedTypeCode
     * @return
     */
    @Override
    public List<TreeNode> getInfectedParentInfo(String infectedClassCode, String infectedTypeCode) {
        return getInfectedInfo(infectedClassCode,infectedTypeCode,true);
    }

    /**
     * 监测模块只查 法定传染病
     * */
    @Override
    public List<TreeNode> getInfectedInfo(String infectedClassCode, String infectedTypeCode, Boolean flag) {

        List<TbCdcmrInfectedDiseaseInfo> infectedDiseaseWarningList ;
        if (flag){
            //传染病父级诊断+病毒性肝炎子级
            infectedDiseaseWarningList = infectedDiseaseInfoMapper.getAllParentInfectedInfoBy(infectedClassCode, infectedTypeCode,"00011");
        } else {
            infectedDiseaseWarningList = infectedDiseaseInfoMapper.getAllInfectedInfoBy(infectedClassCode, infectedTypeCode);
        }
        

        //法定传染病 - 从疾病第二层构建树结构
        List<TreeNode> secondNode = TreeNode.buildTreeByNodeList(infectedDiseaseWarningList,
                                                                 TbCdcmrInfectedDiseaseInfo::getId,
                                                                 TbCdcmrInfectedDiseaseInfo::getParentDiseaseId,
                                                                 TbCdcmrInfectedDiseaseInfo::getDiseaseName,
                                                                 TbCdcmrInfectedDiseaseInfo::getId);
        //构建病种结构
        Map<Pair<String, String>, List<TbCdcmrInfectedDiseaseInfo>> infectedClassMap = infectedDiseaseWarningList.stream()
                                                                                                                 .filter(e -> StringUtils.isNotBlank(e.getDiseaseTypeCode()) && StringUtils.isNotBlank(e.getDiseaseTypeName()))
                                                                                                                 .collect(Collectors.groupingBy(TbCdcmrInfectedDiseaseInfo::getDiseaseClass));

        List<TreeNode> rootNode = new ArrayList<>();
        for(Map.Entry<Pair<String, String>, List<TbCdcmrInfectedDiseaseInfo>> entry : infectedClassMap.entrySet()){

            //该病种类型的code-value
            Pair<String, String> diseaseClassPair = entry.getKey();
            //该病种下的疾病
            List<TbCdcmrInfectedDiseaseInfo> diseaseWarnings = entry.getValue();
            TreeNode root = TreeNode.from(diseaseClassPair.getFirst(), diseaseClassPair.getSecond(), diseaseClassPair.getFirst());
            Set<TreeNode> childNodes = new HashSet<>();
            //用第二层的疾病 匹配该病种下的疾病
            secondNode.forEach(e -> {
                for (TbCdcmrInfectedDiseaseInfo disease : diseaseWarnings) {
                    if(Objects.equals(e.getValue(), disease.getId()) || Objects.equals(e.getValue(), disease.getParentDiseaseId())){
                        childNodes.add(e);
                    }
                }
            });
            root.setChildren(new ArrayList<>(childNodes));
            rootNode.add(root);
        }

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);

        rootNode = rootNode.stream().sorted(Comparator.nullsLast(Comparator.comparing(TreeNode::getId))).collect(Collectors.toList());
        return rootNode;
    }

    /**
     * 获取非法定传染病树形结构 非法定传染病只有两层
     * */
    private List<TreeNode> getOtherInfect(List<InfectedDiseaseWarningVO> infectInfoList){

        String value = InfectedDataConstants.InfectedClassTypeEnum.OTHER.getValue();
        String label = InfectedDataConstants.InfectedClassTypeEnum.OTHER.getLabel();
        //过滤非法定传染病
        infectInfoList = infectInfoList.stream()
                                       .filter(e -> Objects.equals(value, e.getInfectedClassCode()))
                                       .collect(Collectors.toList());
        if(CollectionUtil.isEmpty(infectInfoList)){
            return new ArrayList<>();
        }
        //非法定传染病部分种类，只有层级关系
        List<TreeNode> treeNodeList = TreeNode.buildTreeByNodeList(infectInfoList,
                                                                   InfectedDiseaseWarningVO::getDiseaseCode,
                                                                   InfectedDiseaseWarningVO::getParentDiseaseCode,
                                                                   InfectedDiseaseWarningVO::getDiseaseName,
                                                                   InfectedDiseaseWarningVO::getDiseaseCode);
        TreeNode root = TreeNode.from(MAX_NUM, label, value);
        root.setChildren(treeNodeList);
        return Collections.singletonList(root);
    }

    @Override
    public List<TreeNode> getOrgInfo() {
        return null;
    }

    @Override
    public List<TreeNode> getSyndromeInfo() {

        List<TbCdcmrSyndromeDiseaseInfo> diseaseInfoList = syndromeDiseaseInfoMapper.listAll();
        if(CollectionUtil.isEmpty(diseaseInfoList)){
            return new ArrayList<>();
        }
        List<TreeNode> root = TreeNode.buildTreeByNodeList(diseaseInfoList,
                                                           TbCdcmrSyndromeDiseaseInfo::getId,
                                                           TbCdcmrSyndromeDiseaseInfo::getDiseaseParentId,
                                                           TbCdcmrSyndromeDiseaseInfo::getDiseaseName,
                                                           TbCdcmrSyndromeDiseaseInfo::getDiseaseCode);
        Collections.sort(root);
        return root;
    }

    @Override
    public List<TreeNode> getEmergingInfo() {

        List<TbCdcmrEmergingDiseaseInfo> diseaseInfoList = emergingDiseaseInfoMapper.listAll();
        if(CollectionUtil.isEmpty(diseaseInfoList)){
            return new ArrayList<>();
        }
        List<TreeNode> root = TreeNode.buildTreeByNodeList(diseaseInfoList,
                                                           TbCdcmrEmergingDiseaseInfo::getId,
                                                           TbCdcmrEmergingDiseaseInfo::getDiseaseParentId,
                                                           TbCdcmrEmergingDiseaseInfo::getDiseaseName,
                                                           TbCdcmrEmergingDiseaseInfo::getDiseaseCode);
        Collections.sort(root);
        return root;
    }

    @Override
    public List<TreeNode> getEndemicInfo() {

        List<TbCdcmrEndemicDiseaseInfo> diseaseInfoList = endemicDiseaseInfoMapper.listAll();
        if(CollectionUtil.isEmpty(diseaseInfoList)){
            return new ArrayList<>();
        }
        List<TreeNode> root = TreeNode.buildTreeByNodeList(diseaseInfoList,
                                                           TbCdcmrEndemicDiseaseInfo::getId,
                                                           TbCdcmrEndemicDiseaseInfo::getDiseaseParentId,
                                                           TbCdcmrEndemicDiseaseInfo::getDiseaseName,
                                                           TbCdcmrEndemicDiseaseInfo::getDiseaseCode);
        Collections.sort(root);
        return root;
    }

    @Override
    public List<TreeNode> getPathogenInfo() {

        List<TbCdcmrPathogenInfo> pathogenInfoList = tbCdcmrPathogenInfoMapper.listAll();
        //病原 - 从疾病第二层构建树结构
        List<TreeNode> secondNode = TreeNode.buildTreeByNodeList(pathogenInfoList,
                                                                 TbCdcmrPathogenInfo::getId,
                                                                 TbCdcmrPathogenInfo::getParentId,
                                                                 TbCdcmrPathogenInfo::getPathogenName,
                                                                 TbCdcmrPathogenInfo::getId);
        //构建病种结构
        Map<Pair<String, String>, List<TbCdcmrPathogenInfo>> pathogenClassMap = pathogenInfoList.stream()
                                                                                                .filter(e -> StringUtils.isNotBlank(e.getPathogenClassCode()) && StringUtils.isNotBlank(e.getPathogenClassName()))
                                                                                                .collect(Collectors.groupingBy(TbCdcmrPathogenInfo::getDiseaseClass));

        List<TreeNode> rootNode = new ArrayList<>();
        for(Map.Entry<Pair<String, String>, List<TbCdcmrPathogenInfo>> entry : pathogenClassMap.entrySet()){

            //该病种类型的code-value
            Pair<String, String> diseaseClassPair = entry.getKey();
            //该病原分类种下的 病原
            List<TbCdcmrPathogenInfo> pathogenList = entry.getValue();
            TreeNode root = TreeNode.from(diseaseClassPair.getFirst(), diseaseClassPair.getSecond(), diseaseClassPair.getFirst());
            Set<TreeNode> childNodes = new HashSet<>();
            //用第二层的疾病 匹配该病种下的疾病
            secondNode.forEach(e -> {
                for (TbCdcmrPathogenInfo disease : pathogenList) {
                    if(Objects.equals(e.getValue(), disease.getId()) || Objects.equals(e.getValue(), disease.getParentId())){
                        childNodes.add(e);
                    }
                }
            });
            root.setChildren(new ArrayList<>(childNodes));
            rootNode.add(root);
        }

        //将所有叶子节点的children设置为null
        TreeNode.setChildrenToNull(rootNode);
        //树节点根据id排序
        TreeNode.sortTree(rootNode);
        return rootNode;
    }
}
