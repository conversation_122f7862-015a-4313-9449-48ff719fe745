package com.iflytek.cdc.admin.controller.province;


import com.iflytek.cdc.admin.capacity.api.algorithm.response.InfluenceFactorResponse;
import com.iflytek.cdc.admin.capacity.service.DiseaseInfluenceFactorService;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.InfluenceFactorRequest;
import com.iflytek.cdc.admin.util.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@RestController
@Api(tags = "疾病发病影响因素分析")
@RequestMapping("/pt/{version}/diseaseInfluenceFactor")
public class DiseaseInfluenceFactorController {

    @Resource
    private DiseaseInfluenceFactorService diseaseInfluenceFactorService;

    @PostMapping("/importDataByFile")
    @ApiOperation("疾病影响因素——样本数据上传")
    public void importDataByFile(@RequestParam MultipartFile file) throws IOException {
        diseaseInfluenceFactorService.importDataByFile(file.getInputStream());

    }

    @PostMapping("/influenceFactorAnalysis")
    @ApiOperation("疾病影响因素分析结果")
    public InfluenceFactorResponse.Result.InfluenceFactor influenceFactorAnalysis(@RequestBody InfluenceFactorRequest influence,
                                                                                  @RequestParam(required = false) String loginUserId){
        return diseaseInfluenceFactorService.influenceFactorAnalysis(influence,loginUserId);
    }


    @GetMapping("/downloadTemplate")
    @ApiOperation("样本数据模板下载")
    public ResponseEntity<byte[]> downloadTemplate(){
        String fileName = Constants.DISEASE_INFLUENCE_FACTOR;
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileName);
        return new ResponseEntity<>(diseaseInfluenceFactorService.downloadTemplate(), httpHeaders, HttpStatus.CREATED);
    }

    @GetMapping("/findDiseaseInfluenceFactor")
    @ApiOperation("疾病影响因素集合")
    public List<String> findDiseaseInfluenceFactor(){
        return diseaseInfluenceFactorService.findDiseaseInfluenceFactor();
    }



}
