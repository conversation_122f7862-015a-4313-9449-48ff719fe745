package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.reflect.TypeToken;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsReminderTaskSettingDetail;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsTaskSetting;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsTaskSettingMapper;
import com.iflytek.cdc.admin.customizedapp.model.dto.TaskSettingQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.ReminderTaskSettingDetailService;
import com.iflytek.cdc.admin.customizedapp.service.TaskSettingService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TaskSettingServiceImpl extends CdcServiceBaseImpl<TbCdccsTaskSettingMapper, TbCdccsTaskSetting>  implements TaskSettingService {
    @Resource
    private ReminderTaskSettingDetailService tbCdccsReminderTaskSettingDetailService;

    @Override
    public void beforeCreate(TbCdccsTaskSetting entity) {
        super.beforeCreate(entity);
        if (entity.getFilters() != null){
            entity.setFilterJson(gson.toJson(entity.getFilters()));
        }
    }

    @Override
    public void afterCreate(TbCdccsTaskSetting entity) {
        super.afterCreate(entity);
        if (!CollectionUtils.isEmpty(entity.getReminderTaskSettingDetails())){
            tbCdccsReminderTaskSettingDetailService.saveByTaskSetting(entity, entity.getReminderTaskSettingDetails());
        }
    }

    @Override
    public void afterDelete(String id) {
        super.afterDelete(id);
        tbCdccsReminderTaskSettingDetailService.deleteByTaskSettingId(id);
    }

    @Override
    public void beforeUpdate(TbCdccsTaskSetting entity) {
        super.beforeUpdate(entity);
        if (entity.getFilters() != null){
            entity.setFilterJson(gson.toJson(entity.getFilters()));
        }
        if (!CollectionUtils.isEmpty(entity.getReminderTaskSettingDetails())){
            tbCdccsReminderTaskSettingDetailService.saveByTaskSetting(entity, entity.getReminderTaskSettingDetails());
        }
    }


    @Override
    public List<TbCdccsTaskSetting> listCascade(TaskSettingQueryDTO queryDTO) {
        List<TbCdccsTaskSetting> all = list(buildLambdaQueryWrapper(queryDTO));
        Map<String, List<TbCdccsReminderTaskSettingDetail>> detailMap = tbCdccsReminderTaskSettingDetailService.listByTaskSettingIds(
                all.stream().map(TbCdccsTaskSetting::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(TbCdccsReminderTaskSettingDetail::getTaskSettingId));
        all.forEach(t -> {
            t.setReminderTaskSettingDetails(detailMap.getOrDefault(t.getId(), new ArrayList<>()));
            fulfillFilters(t);
        });
        return all;
    }

    @Override
    public PageInfo<TbCdccsTaskSetting> pageList(TaskSettingQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(list(buildLambdaQueryWrapper(queryDTO)));
    }

    @Override
    public void fulfillChildProperty(TbCdccsTaskSetting taskSetting) {
        super.fulfillChildProperty(taskSetting);
        taskSetting.setReminderTaskSettingDetails(tbCdccsReminderTaskSettingDetailService.listByTaskSettingId(taskSetting.getId()));
        fulfillFilters(taskSetting);
    }

    public LambdaQueryWrapper<TbCdccsTaskSetting> buildLambdaQueryWrapper(TaskSettingQueryDTO queryDTO){
        LambdaQueryWrapper<TbCdccsTaskSetting> tbCdccsTaskSettingLambdaQueryWrapper = lambdaQueryWrapper();
        if (StringUtils.isNotEmpty(queryDTO.getAppId())){
            tbCdccsTaskSettingLambdaQueryWrapper.eq(TbCdccsTaskSetting::getAppId, queryDTO.getAppId());
        }
        if(StringUtils.isNotEmpty(queryDTO.getTaskName())){
            tbCdccsTaskSettingLambdaQueryWrapper.like(TbCdccsTaskSetting::getTaskName, queryDTO.getTaskName());
        }
        return tbCdccsTaskSettingLambdaQueryWrapper;
    }

    private void fulfillFilters(TbCdccsTaskSetting taskSetting){
        if (StringUtils.isNotEmpty(taskSetting.getFilterJson())){
            taskSetting.setFilters(gson.fromJson(taskSetting.getFilterJson(), new TypeToken<List<TbCdccsTaskSetting.Filter>>(){}.getType()));
        }
    }

}
