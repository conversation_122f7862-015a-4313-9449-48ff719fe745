package com.iflytek.cdc.admin.datamodel.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.admin.datamodel.model.dto.DataFormTemplateQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.dto.DataModelTemplateDTO;
import com.iflytek.cdc.admin.datamodel.service.DataFormTemplateService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/pt/{version}/dataFormTemplate")
public class DataFormTemplateController {

    @Resource
    private DataFormTemplateService templateService;

    @GetMapping("/getTemplateById")
    @ApiOperation("根据配置表id查询配置详情")
    public TbCdcdmDataFormTemplate getDataFormTemplateById(@RequestParam String formTemplateDetailId){

        return templateService.getDataFormTemplateById(formTemplateDetailId);
    }

    @PostMapping("/addDataFormTemplate")
    @ApiOperation("新增一条配置")
    public void addDataFormTemplate(@RequestBody DataModelTemplateDTO dto){

        templateService.addDataFormTemplate(dto);
    }

    @PostMapping("/updateDataFormTemplate")
    @ApiOperation("更新该模型的配置")
    public void updateDataFormTemplate(@RequestBody DataModelTemplateDTO dto){

        templateService.updateDataFormTemplate(dto);
    }

    @PostMapping("/getAllModelTemplate")
    @ApiOperation("获取所有的模型配置")
    public PageInfo<TbCdcdmDataFormTemplate> getAllModelTemplate(@RequestBody DataFormTemplateQueryDTO dto){

        return templateService.getAllModelTemplate(dto);
    }

    @GetMapping("/getConfigInfoByDiseaseCode")
    @ApiOperation("根据疾病code获取配置的数据模型configInfo")
    public String getConfigInfoByDiseaseCode(@RequestParam String diseaseCode){

        return templateService.getConfigInfoByDiseaseCode(diseaseCode);
    }
}
