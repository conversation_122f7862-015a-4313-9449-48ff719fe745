package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.EndemicWaringRuleQueryDTO;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrEndemicDiseaseWarningRule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;

import java.util.List;

public interface EndemicDiseaseWarningRuleService extends IService<TbCdcmrEndemicDiseaseWarningRule>{


    PageInfo<TbCdcmrEndemicDiseaseWarningRule> getRuleDetail(String diseaseInfoId, Integer pageIndex, Integer pageSize, String riskLevel, String warningMethod);

    List<TreeNode> getEndemicTree(EndemicWaringRuleQueryDTO dto);

    void savaOrUpdateRule(TbCdcmrEndemicDiseaseWarningRule rule);
}
