package com.iflytek.cdc.admin.outbound.model.dto.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 查询批次汇总数据-输入参数
 * <AUTHOR>
 */
@Data
@Builder
public class BatchList {

    @ApiModelProperty(value = "批次id", required = true)
    private String batch;

    @ApiModelProperty("手机号")
    private String telephone;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("分页页数")
    private Integer pageIndex;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

}
