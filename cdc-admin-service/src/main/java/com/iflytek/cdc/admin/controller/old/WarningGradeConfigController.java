package com.iflytek.cdc.admin.controller.old;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.TbCdcmrWarningGradeConfig;
import com.iflytek.cdc.admin.service.WarningGradeConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "预警分级配置维护")
public class WarningGradeConfigController {
    @Resource
    WarningGradeConfigService warningGradeConfigService;

    @ApiOperation("预警分级配置维护-症候群预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/syndrome/pageList")
    public PageInfo<SyndromeGradeConfigVO> syndromeWarningGradeConfigPageList(@RequestBody SyndromeGradeConfigParam param) {
        return warningGradeConfigService.syndromeWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-传染病预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/infected/pageList")
    public PageInfo<InfectedGradeConfigVO> infectedWarningGradeConfigPageList(@RequestBody InfectedGradeConfigParam param) {
        return warningGradeConfigService.infectedWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-症状预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/symptom/pageList")
    public PageInfo<SymptomGradeConfigVO> symptomWarningGradeConfigPageList(@RequestBody SymptomGradeConfigParam param) {
        return warningGradeConfigService.symptomWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-中毒预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/poison/pageList")
    public PageInfo<PoisonGradeConfigVO> poisonWarningGradeConfigPageList(@RequestBody PoisonGradeConfigParam param) {
        return warningGradeConfigService.poisonWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-不明原因预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/unknownReason/pageList")
    public PageInfo<UnknownReasonGradeConfigVO> unknownReasonWarningGradeConfigPageList(@RequestBody UnknownReasonGradeConfigParam param) {
        return warningGradeConfigService.unknownReasonWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-联防联控预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/preventionControl/pageList")
    public PageInfo<PreventionControlGradeConfigVO> preventionControlWarningGradeConfigPageList(@RequestBody PreventionControlGradeConfigParam param) {
        return warningGradeConfigService.preventionControlWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-门诊预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/outpatient/pageList")
    public PageInfo<OutpatientGradeConfigVO> outpatientWarningGradeConfigPageList(@RequestBody OutpatientGradeConfigParam param) {
        return warningGradeConfigService.outpatientWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-自定义预警分级列表查询")
    @PostMapping("/{version}/pt/warningGradeConfig/customized/pageList")
    public PageInfo<CustomizedGradeConfigVO> customizedWarningGradeConfigPageList(@RequestBody CustomizedGradeConfigParam param) {
        return warningGradeConfigService.customizedWarningGradeConfigPageList(param);
    }

    @ApiOperation("预警分级配置维护-新增/更新预警分级配置状态")
    @PostMapping("/{version}/pt/warningGradeConfig/updateStatus")
    public void updateWarningGradeConfigStatus(@RequestBody TbCdcmrWarningGradeConfig config, @RequestParam String loginUserId) {
        warningGradeConfigService.updateWarningGradeConfigStatus(config, loginUserId);
    }


    @ApiOperation("预警分级配置维护-新增/更新预警分级配置")
    @PostMapping("/{version}/pt/warningGradeConfig/update")
    public void updateWarningGradeConfig(@RequestBody WarningGradeConfigVO configVO, @RequestParam String loginUserId) {
        warningGradeConfigService.updateWarningGradeConfig(configVO, loginUserId);
    }

    @ApiOperation("预警分级配置维护-根据分类查询分级配置内容")
    @GetMapping("/{version}/pt/warningGradeConfig/getConfigByType")
    public List<WarningGradeConfigVO> getConfigByType(@RequestParam String configType) {
        return warningGradeConfigService.getConfigByType(configType);
    }


}
