package com.iflytek.cdc.admin.service.brief;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.dto.brief.TemplateSettingDto;
import com.iflytek.cdc.admin.entity.brief.IndicatorsEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateEntity;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.enums.StatisticalPeriodEnum;
import com.iflytek.cdc.admin.mapper.brief.BriefTemplateMapper;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.TemplateExcelVo;
import com.iflytek.cdc.admin.vo.brief.TemplateSettingVo;
import com.iflytek.cdc.admin.vo.brief.TemplateVo;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 模板服务impl
 *
 * <AUTHOR>
 * @date 2024/12/30
 */
@Service
@RequiredArgsConstructor
public class BriefTemplateServiceImpl extends ServiceImpl<BriefTemplateMapper, TemplateEntity> implements BriefTemplateService {

    private final BriefTemplateMapper briefTemplateMapper;
    private final BatchUidService batchUidService;
    private final TemplateRuleService templateRuleService;

    @Override
    public ApiResult<PageInfo<TemplateVo>> queryPage(TemplateSearchDto searchDto) {
        PageHelper.startPage(searchDto.getPageIndex(), searchDto.getPageSize());
        List<TemplateVo> templateEntities = briefTemplateMapper.listBySearch(searchDto);
        PageHelper.clearPage();
        return ApiResult.ok(new PageInfo<>(templateEntities));
    }

    @Override
    public ApiResult<?> deleteById(String loginUserId, String loginUserName, String id) {
        UpdateWrapper<TemplateEntity> qw = new UpdateWrapper<>();
        qw.set("delete_flag", DeleteFlagEnum.YES.getCode());
        qw.set("updator_id", loginUserId);
        qw.set("updator_name", loginUserName);
        qw.set("update_time", new Date());
        qw.eq("id", id);
        briefTemplateMapper.update(null, qw);
        
        // 级联删除模板生成规则
        templateRuleService.deleteCascadeByTemplateId(id);
        return ApiResult.ok();
    }

    @Override
    public ApiResult<?> saveOrUpdateTemplate(TemplateEntity template, String loginUserId) {
        // 判断是否存在，统计周期+标题+分析对象
        QueryWrapper<TemplateEntity> qw = new QueryWrapper<>();
        qw.eq("statistics_cycle", template.getStatisticsCycle());
        qw.eq("analysis_object", template.getAnalysisObject());
        qw.eq("title", template.getTitle());
        qw.eq("delete_flag", DeleteFlagEnum.NO.getCode());
        TemplateEntity templateEntity = briefTemplateMapper.selectOne(qw);
        if (templateEntity != null && !templateEntity.getId().equals(template.getId())) {
            return ApiResult.failed("该模板已存在！");
        }
        if (StrUtil.isBlank(template.getId())) {
            template.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_TEMPLATE)));
            template.setCreatorId(loginUserId);
            template.setCreateTime(new Date());
        }
        template.setUpdateTime(new Date());
        template.setUpdatorId(loginUserId);
        this.saveOrUpdate(template);
        return ApiResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<?> setting(TemplateSettingDto settingDto) {
        String templateId = settingDto.getTemplateId();
        // 更新模板配置信息
        UpdateWrapper<TemplateEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("content", settingDto.getContent());
        updateWrapper.set("content_title", settingDto.getContentTitle());
        updateWrapper.set("attachment_title", settingDto.getAttachmentTitle());
        updateWrapper.set("original_content", settingDto.getOriginalContent());
        updateWrapper.set("updator_id", settingDto.getUpdatorId());
        updateWrapper.set("updator_name", settingDto.getUpdatorName());
        updateWrapper.set("update_time", new Date());
        updateWrapper.set("attachment_flag", settingDto.getAttachmentFlag());
        updateWrapper.eq("id", templateId);
        briefTemplateMapper.update(null, updateWrapper);
        // 删除指标
        briefTemplateMapper.deleteIndicatorsByTemplateId(templateId, settingDto.getAttachmentFlag());
        // 插入指标
        List<IndicatorsEntity> indicators = settingDto.getIndicators();
        if (CollUtil.isNotEmpty(indicators)) {
            indicators.forEach(e -> {
                e.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_TEMPLATE_INDICATORS)));
                e.setTemplateId(templateId);
                e.setIndexScope(Constants.BriefReport.INDEX_SCOPE_ZW);
            });
            // 批量插入指标信息
            briefTemplateMapper.insertBatchIndicators(indicators);
        }
        List<IndicatorsEntity> attachmentIndicators = settingDto.getAttachmentIndicators();
        if (CollUtil.isNotEmpty(attachmentIndicators)) {
            attachmentIndicators.forEach(e -> {
                e.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_TEMPLATE_INDICATORS)));
                e.setTemplateId(templateId);
                e.setIndexScope(Constants.BriefReport.INDEX_SCOPE_FJ);
            });
            briefTemplateMapper.insertBatchIndicators(attachmentIndicators);
        }
        return ApiResult.ok();
    }

    @Override
    public TemplateSettingVo getSetting(String id) {
        TemplateSettingVo templateSettingVo = briefTemplateMapper.getSettingDetail(id);
        // 查询指标
        List<IndicatorsEntity> allIndicators = briefTemplateMapper.listIndicators(id);
        templateSettingVo.setIndicators(allIndicators.stream().filter(e ->
                Constants.BriefReport.INDEX_SCOPE_ZW.equals(e.getIndexScope())).collect(Collectors.toList()));
        templateSettingVo.setAttachmentIndicators(allIndicators.stream().filter(e ->
                Constants.BriefReport.INDEX_SCOPE_FJ.equals(e.getIndexScope())).collect(Collectors.toList()));
        return templateSettingVo;
    }

    @Override
    public ResponseEntity<byte[]> exportTemplateList(TemplateSearchDto searchDto) {
        if (searchDto.isAllFlag()) {
            searchDto.setIdList(null);
        }
        List<TemplateVo> templateEntities = briefTemplateMapper.listBySearch(searchDto);
        List<TemplateExcelVo> excelRes = new ArrayList<>();
        templateEntities.forEach(entity -> {
            TemplateExcelVo excelVO = new TemplateExcelVo();
            BeanUtils.copyProperties(entity, excelVO);
            excelVO.setStatisticsCycleDesc(StatisticalPeriodEnum.getValueByCode(entity.getStatisticsCycle()));
            excelRes.add(excelVO);
        });
        ByteArrayOutputStream excelOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(excelOutputStream, TemplateExcelVo.class)
                .sheet("Sheet1")
                .doWrite(excelRes);
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("简报生成规则列表.xlsx");
        return new ResponseEntity<>(excelOutputStream.toByteArray(), httpHeaders, HttpStatus.CREATED);
    }
}