package com.iflytek.cdc.admin.controller.old;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.ExportAddressDTO;
import com.iflytek.cdc.admin.dto.SearchAddressDTO;
import com.iflytek.cdc.admin.dto.amap.search.PoisV2;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.RelationAddress;
import com.iflytek.cdc.admin.entity.StandardAddress;
import com.iflytek.cdc.admin.service.AddressDetailMappingService;
import com.iflytek.cdc.admin.service.AddressParsService;
import com.iflytek.cdc.admin.service.AddressStandardService;
import com.iflytek.cdc.admin.service.AmapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/27 14:22
 **/
@RestController
@Api(tags = "地址解析接口")
public class AddressParsController {

    private final AddressParsService aService;

    @Resource
    AmapService amapService;

    @Resource
    AddressParsService addressParsService;
    
    @Resource
    AddressDetailMappingService addressDetailMappingService;

    @Resource
    AddressStandardService addressStandardService;


    public AddressParsController(AddressParsService aService) {
        this.aService = aService;
    }

    @PostMapping("/{version}/pt/address/pars/queryStandardAddressList")
    @ApiOperation("分页查询标准地址数据")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<StandardAddress> queryStandardAddressList(@RequestBody SearchAddressDTO sd, @PathVariable String version) {
        return aService.queryStandardAddressList(sd);
    }

    @PostMapping("/{version}/pt/address/pars/queryRelationAddressList")
    @ApiOperation("分页查询映射地址数据")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public PageInfo<RelationAddress> queryRelationAddressList(@PathVariable String version, @RequestBody SearchAddressDTO sd) {
        return aService.queryRelationAddressList(sd);
    }

    @GetMapping("/{version}/pt/address/pars/deleteRelationAddress")
    @ApiOperation("删除映射地址数据")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void deleteRelationAddress(@RequestParam String id, @PathVariable String version) {
        aService.deleteRelationAddress(id);
    }

    @PostMapping("/{version}/pt/address/pars/exportAddress")
    @ApiOperation("导出标准地址数据")
    @LogExportAnnotation
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void exportAddress(@RequestBody ExportAddressDTO ed, HttpServletResponse response, @PathVariable String version, @RequestParam("loginUserId") String loginUserId) {
        aService.exportAddress(ed, response);

    }

    @GetMapping("/{version}/pt/address/pars/inputTips")
    @ApiOperation("输入提示")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<PoisV2> inputTips(String keyWords, String areaCode) {
        return amapService.inputTips(keyWords, areaCode);
    }

    @PostMapping("/{version}/pt/address/pars/manualMapping")
    @ApiOperation("手动匹配地址数据")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public void manualMapping(String loginUserId,@RequestBody PoisV2 poisV2) {
         amapService.manualMapping(loginUserId, poisV2);
    }

    @GetMapping("/{version}/pt/address/pars/getAllRegion")
    @ApiOperation("获取所有区划")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public List<CascadeVO> getAllRegion() {
        return addressParsService.getAllRegion();
    }

    @PostMapping("/{version}/pt/address/pars/manualSimilarity")
    @ApiOperation("地址映射相似度计算")
    @ApiImplicitParams({@ApiImplicitParam(name = "similarity", value = "", defaultValue = "0.0"),
                        @ApiImplicitParam(name = "initial", value = "1 初始化刷新：强制重新计算时间范围内所有匹配相似度(无论是否手动匹配)", allowableValues = "0,1", defaultValue = "0", required = true)})
    public void manualSimilarity(String startDateStr,
                                 String endDateStr,
                                 @RequestParam(required = false) Integer status,
                                 @RequestParam(required = false) Double similarity,
                                 @RequestParam Integer initial) {
        DateTime startDate = DateUtil.parse(startDateStr);
        DateTime endDate = DateUtil.parse(endDateStr);
        status = Objects.equals(1, initial) ? null : status;
        similarity = Objects.equals(1, initial) ? null : similarity;  // 如果不是初始化强制刷新，则自动过滤非空

        addressDetailMappingService.manualSimilarity(startDate, endDate, status, similarity);
    }

    @GetMapping("/{version}/pt/address/output/downloadNotStdAddress")
    @ApiOperation("获取未能标化的地址")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public Set<String> downloadNotStdAddress() {
        return addressStandardService.getUnStdWords();
    }
}
