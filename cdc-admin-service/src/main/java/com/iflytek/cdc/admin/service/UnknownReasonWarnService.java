package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.UnknownReasonQueryDto;
import com.iflytek.cdc.admin.dto.UnknownReasonWarnDto;
import com.iflytek.cdc.admin.entity.CascadeVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface UnknownReasonWarnService {
    PageInfo<UnknownReasonWarnDto> getUnknownReasonWarnPageList(UnknownReasonQueryDto dto);

    void updateUnknownReasonWarn(UnknownReasonWarnDto unknownReasonWarnDto, String loginUserId);

    void updateUnknownReasonWarnStatus(UnknownReasonWarnDto unknownReasonWarnDto, String loginUserId);

    UnknownReasonWarnDto getWarnById(String warnId);

    List<UnknownReasonWarnDto> getUnknownReasonWarnAllList(String diseaseCode);

    void exportUnknownReasonWarnRule(HttpServletResponse response);

    List<CascadeVO> getUnknownReasonNameList();

    List<CascadeVO> getUnknownReasonNameList(String loginUserId);


    public List<CascadeVO> getUnknownReason() ;


    }
