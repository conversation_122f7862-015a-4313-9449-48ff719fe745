package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.ParamConfigConstants;
import com.iflytek.cdc.admin.dto.ExportApplicationListDTO;
import com.iflytek.cdc.admin.dto.ExportTaskDTO;
import com.iflytek.cdc.admin.dto.ExportTaskQueryDTO;
import com.iflytek.cdc.admin.entity.ParamConfig;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.entity.TbCdcmrExportTask;
import com.iflytek.cdc.admin.mapper.ParamConfigMapper;
import com.iflytek.cdc.admin.mapper.TbCdcAttachmentMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrExportTaskMapper;
import com.iflytek.cdc.admin.service.ExportTaskService;
import com.iflytek.cdc.admin.service.FileService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 导出记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Service
public class ExportTaskServiceImpl extends ServiceImpl<TbCdcmrExportTaskMapper, TbCdcmrExportTask> implements ExportTaskService {
    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcmrExportTaskMapper exportTaskMapper;

    @Resource
    private UapServiceApi uapServiceApi;
    @Resource
    private ParamConfigMapper pMapper;

    @Resource
    private TbCdcAttachmentMapper tbCdcAttachmentMapper;

    @Value("${storage.switch:swift}")
    private String storageSwitch;
    @Value("${swift.prefix:/file-obj}")
    private String swiftPrefix;

    @Value("${minio.prefix:/minIoFile}")
    private String minioPrefix;

    @Resource
    private FileService fileService;

    private static final String CSV_TASK_MAX_CODE = "cdc-platform-service:csv_task_max";

    public static final String TABLE_TB_CDCMR_EXPORT_TASK = "tb_cdcmr_export_task";
    @Override
    public TbCdcmrExportTask add(ExportTaskDTO exportTask, String loginUserId) {
        TbCdcmrExportTask insert = new TbCdcmrExportTask();
        BeanUtils.copyProperties(exportTask,insert);

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String userName = user.getName();

        insert.setId(String.valueOf(batchUidService.getUid(TABLE_TB_CDCMR_EXPORT_TASK)));
        //启用状态 1进行中;2已完成;3任务中断
        insert.setStatus(Constants.TASK_STATUS_START);
        insert.setDeleteFlag(Constants.DELETE_FLAG_STR_0);
        insert.setCreateTime(new Date());
        insert.setUpdateTime(new Date());
        insert.setCreator(userName);
        insert.setCreatorId(loginUserId);
        exportTaskMapper.insert(insert);
        return insert;
    }

    @Override
    public int getProceedCount() {
        return exportTaskMapper.getProceedCount();
    }

    @Override
    public TbCdcmrExportTask getProceedDoneTask(String taskParams, String taskUrl, String userId) {
        return exportTaskMapper.getProceedDoneTask(taskParams,taskUrl,userId);
    }

    @Override
    public TbCdcmrExportTask checkExistTask(ExportTaskDTO exportTask, String loginUserId) {
        if (StrUtil.isNotBlank(exportTask.getId())){
            return exportTaskMapper.selectById(exportTask.getId());
        }
        TbCdcmrExportTask exitTask = this.getProceedDoneTask(exportTask.getTaskParam(),exportTask.getTaskUrl(),loginUserId);
        if (null != exitTask){
            return exitTask;
        }
        return null;
    }

    @Override
    public Boolean checkExportTaskCount() {
        int proceedCount = exportTaskMapper.getProceedCount();
        ParamConfig paramConfig = pMapper.paramConfigInfo(CSV_TASK_MAX_CODE, ParamConfigConstants.NO_DELETE, null);
        if (paramConfig != null && Integer.valueOf(paramConfig.getConfigValue()) >= proceedCount) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public PageInfo<TbCdcmrExportTask> queryList(ExportTaskQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(),queryDTO.getPageSize());
        List<TbCdcmrExportTask> list = exportTaskMapper.queryList(queryDTO);
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<ExportApplicationListDTO> queryExportRecordList(ExportTaskQueryDTO queryDTO) {
        queryDTO.setAppCode(queryDTO.getModuleType());
        // 联表分页查询
        try (Page<ExportApplicationListDTO> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            return PageInfo.of(exportTaskMapper.queryExportRecordList(queryDTO));
        }
    }

    @Override
    public void updateTask(ExportTaskDTO taskDTO) {
        TbCdcmrExportTask tbCdcmrExportTask = exportTaskMapper.selectById(taskDTO.getId());
        if (tbCdcmrExportTask == null){
            throw new MedicalBusinessException("导出任务不存在");
        }
        tbCdcmrExportTask.setStatus(taskDTO.getStatus());
        if (StringUtils.isNotEmpty(taskDTO.getAttachmentId())){
            TbCdcAttachment tbCdcAttachment = tbCdcAttachmentMapper.selectByPrimaryKey(taskDTO.getAttachmentId());
            if (tbCdcAttachment != null){
                tbCdcmrExportTask.setAttachmentId(tbCdcAttachment.getId());
                tbCdcmrExportTask.setAttachmentUrl(fulfillAttachmentUrl(tbCdcAttachment.getAttachmentPath()));
                tbCdcmrExportTask.setAttachmentSize(String.valueOf(taskDTO.getSize()));
            }
        }
        exportTaskMapper.updateById(tbCdcmrExportTask);
    }

    private String fulfillAttachmentUrl(String attachmentPath){
        if ("swift".equals(storageSwitch)){
            return swiftPrefix + attachmentPath;
        } else {
            return attachmentPath.contains(minioPrefix) ? attachmentPath : minioPrefix + attachmentPath;
        }
    }
}
