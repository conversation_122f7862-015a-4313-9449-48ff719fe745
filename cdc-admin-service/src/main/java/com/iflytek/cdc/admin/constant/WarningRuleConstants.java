package com.iflytek.cdc.admin.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

public class WarningRuleConstants {


    /**
     * 指标
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum WarningIndicatorEnum{
        MEDICAL_CASE("MEDICAL_CASE", "发病数"),
        DEATH_CASE("DEATH_CASE", "死亡数"),
        ;
        private final String value;
        private final String label;

        WarningIndicatorEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    /**
     * 空间维度类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum AreaTypeEnum{
        LIVING_ADDR("LIVING_ADDR", "住址"),
        SCHOOL("SCHOOL", "学校"),
        COMPANY("COMPANY", "单位"),
        ;
        private String value;
        private String label;

        AreaTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }
    /**
     * 空间维度层级
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum AreaLevelEnum{
        PROVINCE(1, "省级行政区", ""),
        CITY(2, "地级行政区", ""),
        DISTRICT(3, "县级行政区", ""),
        STREET(4, "乡级行政区", ""),
        VILLAGE(5, "社区/村", ""),
        TEAM(6, "小区/组", ""),
        KEY_PLACE(7, "重点场所", "学校或学前教育机构/养老机构/社会福利机构/酒店民宿/医疗机构"),
        SCHOOL(8, "学校",""),
        COMPANY(9, "工作单位",""),
        ORGANIZATION(10, "医疗机构",""),
//        ADDR(99, "地址"),
        ;
        private final Integer value;
        private final String label;
        private final String tip;

        AreaLevelEnum(Integer value, String label, String tip) {
            this.value = value;
            this.label = label;
            this.tip = tip;
        }
    }

    /**
     * 地址维度类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public  enum AddressTypeEnum {
        /**
         * 病例地址（病例所有在本省的地址）
         */
        CASE_ADDRESS("CASE_ADDRESS", "病例地址（病例所有在本省的地址）"),
        /**
         * 病例现住址
         */
        LIVING_ADDRESS("LIVING_ADDRESS", "病例现住址"),

        SCHOOL("SCHOOL", "学校"),
        COMPANY("COMPANY", "工作单位"),
        ORGANIZATION("ORGANIZATION", "医疗机构"),
        ;
        private String value;
        private String label;

        AddressTypeEnum(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

}
