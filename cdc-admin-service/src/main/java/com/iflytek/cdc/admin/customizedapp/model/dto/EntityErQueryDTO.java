package com.iflytek.cdc.admin.customizedapp.model.dto;

import com.iflytek.cdc.admin.dto.PageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("er 模型查询")
public class EntityErQueryDTO extends PageInfoDTO {
    @ApiModelProperty("关键词查询")
    private String queryKey;
}
