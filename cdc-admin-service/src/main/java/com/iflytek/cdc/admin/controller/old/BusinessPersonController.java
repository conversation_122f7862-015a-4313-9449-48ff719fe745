package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.BusinessPersonQueryVo;
import com.iflytek.cdc.admin.dto.DataAuthVO;
import com.iflytek.cdc.admin.dto.outcall.BusinessPersonResult;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.service.*;
import com.iflytek.zhyl.uap.usercenter.pojo.UapOrgTreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "业务人员管理")
@RestController
public class BusinessPersonController {

    @Resource
    DataAuthService dataAuthService;

    @Resource
    UapUserService uapUserService;

    @Resource
    SyndromeService syndromeService;

    @Resource
    InfectiousDiseasesApiService infectiousDiseasesApiService;

    @Resource
    SchoolSymptomService schoolSymptomService;

    @Resource
    PoisoningWarnService poisoningWarnService;

    @Resource
    OutpatientWarnService outpatientWarnService;

    @Resource
    UnknownReasonWarnService unknownReasonWarnService;

    @Resource
    PreventionControlWarnService preventionControlWarnService;

    @Resource
    CustomizedWarningTaskService customizedWarningTaskService;

    @Resource
    UapOrgService uapOrgService;

    @PostMapping("/pt/v1/dataAuth/add")
    @ApiOperation("新增")
    public void addDataAuth(@RequestBody DataAuthVO vo) {
        dataAuthService.add(vo);
    }

    @GetMapping("/pt/v1/uapOrg/getOrgTree")
    @ApiOperation("获取机构列表")
    public UapOrgTreeNode getOrgTree(@RequestParam String loginUserId) {
        return uapOrgService.getOrgTree(loginUserId);
    }

    @GetMapping("/pt/v1/uapOrg/getOrgForBusinessPerson")
    @ApiOperation("获取机构列表，5.0.0中只显示疾控中心及部门")
    public UapOrgTreeNode getOrgForBusinessPerson(@RequestParam String loginUserId) {
        return uapOrgService.getOrgTree(loginUserId);
    }

    @PostMapping("/pt/v1/uapUser/search")
    @ApiOperation("查询用户中心人员列表")
    public BusinessPersonResult getUapUserList(@RequestParam String loginUserId, @RequestParam String loginOrgId, @RequestBody BusinessPersonQueryVo queryVo) {
        return uapUserService.getUapUserList(loginUserId, loginOrgId, queryVo);
    }

    @GetMapping("/pt/v1/dataAuth/getSyndromeNameList")
    @ApiOperation("症候群列表")
    public List<CascadeVO> getSyndromeList() {
        return syndromeService.getSyndromeList();
    }

    @GetMapping("/pt/v1/dataAuth/getInfectedNameList")
    @ApiOperation("查询传染病列表")
    public List<CascadeVO> getInfectedNameList() {
        return infectiousDiseasesApiService.getInfectedNameListWithoutChildren();
    }
    @GetMapping("/pt/v1/dataAuth/getInfectedNameListTree")
    @ApiOperation("查询传染病列表")
    public List<CascadeVO> getInfectedNameListTree() {
        return infectiousDiseasesApiService.getInfectedNameList();
    }

    @GetMapping("/pt/v1/dataAuth/getScSymptomNameList")
    @ApiOperation("学校症状列表")
    public List<CascadeVO> getScSymptomList() {
        return schoolSymptomService.getSymptoms();
    }

    @GetMapping("/pt/v1/dataAuth/getPoisonNameList")
    @ApiOperation("中毒列表")
    public List<CascadeVO> getPoisonList() {
        return poisoningWarnService.getPoisonNameList();
    }

    @GetMapping("/pt/v1/dataAuth/getOutpatientNameList")
    @ApiOperation("门诊列表")
    public List<CascadeVO> getOutpatientList() {
        return outpatientWarnService.getOutpatientTypeNameList();
    }

    @GetMapping("/pt/v1/dataAuth/getUnknownReasonNameList")
    @ApiOperation("不明原因列表")
    public List<CascadeVO> getUnknownReasonList() {
        return unknownReasonWarnService.getUnknownReasonNameList();
    }
    @GetMapping("/pt/v1/dataAuth/getCustomizedNameList")
    @ApiOperation("自定义列表")
    public List<CascadeVO> getCustomizedNameList() {
        return customizedWarningTaskService.getCustomizedNameList();
    }

    @GetMapping("/pt/v1/dataAuth/getPreventionControlNameList")
    @ApiOperation("联防联控列表")
    public List<CascadeVO> getPreventionControlList() {

        return preventionControlWarnService.getPreventionControlNameList();
    }
}
