package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.iflytek.cdc.admin.constant.*;
import com.iflytek.cdc.admin.dto.InfectiousDiseasesDto;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.dto.UpdateInfecDTO;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.entity.InfectiousDiseases;
import com.iflytek.cdc.admin.mapper.InfectiousDiseasesMapper;
import com.iflytek.cdc.admin.service.DictRelationService;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.MedicalWarnService;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InfectiousDiseasesServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/7/29 10:47
 * @Version 1.0
 */
@Service("infecDisService")
@Slf4j
public class InfectiousDiseasesServiceImpl extends AbstractSyncMdmDataService implements SyncMdmDataService<InfectiousDiseases> {

    @Resource
    public InfectiousDiseasesMapper infectiousDiseasesMapper;
    @Resource
    private MedicalWarnService medicalWarnService;
    @Resource
    private DictRelationService dictRelationService;

    public InfectiousDiseasesServiceImpl(UapUserApi uapUserApi, MdmDataSyncService mdmDataSyncService, BatchUidService batchUidService) {
        super(uapUserApi, mdmDataSyncService, batchUidService);
    }

    @Override
    public void addInfectiousDiseases(String loginUserId, InfectiousDiseasesDto record) {
        if (StringUtils.isEmpty(record.getDiseasesCode())){
            throw new MedicalBusinessException("病种编码不能为空，请输入病种编码！");
        }
        if (StringUtils.isEmpty(record.getDiseasesName())){
            throw new MedicalBusinessException("病种名称不能为空，请输入病种名称！");
        }
        if (infectiousDiseasesMapper.selectByDiseasesCode(record.getDiseasesCode()) != null){
            throw new MedicalBusinessException("病种编码重复，请更换病种编码！");
        }
        if (infectiousDiseasesMapper.selectByDiseasesName(record.getDiseasesName()) != null){
            throw new MedicalBusinessException("病种名称重复，请更换病种名称！");
        }
        TUapUser uapUser = getUapUser(loginUserId);
        InfectiousDiseases entity = new InfectiousDiseases();
        BeanUtils.copyProperties(record, entity);
        entity.setId(String.valueOf(batchUidService.getUid(TableName.INFECTIOUS_DISASES)));
        entity.setCreateUser(uapUser.getId());
        entity.setCreateTime(new Date());
        entity.setIsDelete(Constants.COMMON_STRNUM_ZERO);
        if (StringUtils.isNotEmpty(record.getDiseasesType())){
            entity.setDiseasesTypeName(dictRelationService.getTargetName(MdmDataSyncConstants.INFECTYPE_CODE, record.getDiseasesType()));
        }
        if (StringUtils.isNotEmpty(record.getDiseasesClassify())){
            entity.setDiseasesClassifyName(dictRelationService.getTargetName(MdmDataSyncConstants.INFECCLFY_CODE, record.getDiseasesClassify()));
        }
        infectiousDiseasesMapper.insert(entity);
        medicalWarnService.addByInfectiousDisease(entity);

    }
    @Override
    public void syncMdmData(String batchId,String loginUserId) {
        //todo 等待mdm修改字典目录接口，获取字典目录状态，如果被删除或被禁用，则直接将所有本地数据状态修改为禁用。
        //        //先查询本地数据表所有的编码数据
        if (isCanUse(MdmDataSyncConstants.INFECDIS_CODE)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        executeSyncMdmData(loginUserId);
    }

    public void executeSyncMdmData( String loginUserId) {

        //获取本地数据
        List<InfectiousDiseases> infectiousDiseases = queryInfecInfo(new SearchInfecInfoDTO());
        //获取mdm主数据平台数据
        long startTime=System.currentTimeMillis();
        List<TermCodedValueInfo> mdmCodeValues = getMdmDataSync(MdmDataSyncConstants.INFECDIS_CODE);
        if(CollectionUtils.isEmpty(mdmCodeValues)){
            log.warn("同步MDM传染病病种数据为空");
            return;
        }
        long endTime=System.currentTimeMillis();
        log.warn("同步病种数据运行时间： "+(endTime-startTime)+"ms");
        insertSyncMdmData(loginUserId, infectiousDiseases, mdmCodeValues);
        updateSyncMdmData(loginUserId, infectiousDiseases, mdmCodeValues);
        updateCodeNme(loginUserId, infectiousDiseases, mdmCodeValues);
    }

    public void  updateCodeNme(String loginUserId,List<InfectiousDiseases> infectiousDiseases, List<TermCodedValueInfo> mdmCodeValues){
        TUapUser uapUser = getUapUser(loginUserId);
        //取出本地库中存在而mdm也存在的数据
        List<TermCodedValueInfo> allExistData=mdmCodeValues.stream().filter(mdm->infectiousDiseases.stream().map(InfectiousDiseases::getDiseasesCode).anyMatch(
                code->Objects.equals(mdm.getCodedValue(),code))).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allExistData)){
            allExistData.forEach(allData->
                    infectiousDiseasesMapper.updateCodeName(allData.getCodedValue(),allData.getDescription(),uapUser.getId()));
        }
    }

    @Override
    public void insertSyncMdmData(String  loginUserId, List<InfectiousDiseases> infectiousDiseases, List<TermCodedValueInfo> mdmCodeValues) {
        //获取登录人员信息
        TUapUser uapUser = getUapUser(loginUserId);
        //取出mdm库中存在而本地库中不存在的数据
        List<TermCodedValueInfo> mdmExistData = mdmCodeValues.stream().filter(mdm -> infectiousDiseases.stream().map(InfectiousDiseases::getDiseasesCode).
                noneMatch(code -> Objects.equals(mdm.getCodedValue(), code))).collect(Collectors.toList());
        //如果mdm库中存在而本地库中不存在的数据，则直接插入到本地库中
        List<InfectiousDiseases> insertInfos = new ArrayList<InfectiousDiseases>();
        //格式化需要插入数据库中的数据
        mdmExistData.forEach(mdmData -> {
            InfectiousDiseases si = new InfectiousDiseases();
            si.setId(String.valueOf(batchUidService.getUid(TableName.INFECTIOUS_DISASES)));
            si.setDiseasesCode(mdmData.getCodedValue());
            si.setDiseasesName(mdmData.getDescription());
            si.setCreateUser(uapUser.getId());
            si.setIsEnable(MdmDataSyncConstants.YES_USE);
            insertInfos.add(si);
        });
        if (insertInfos.size() > 0) {
            infectiousDiseasesMapper.insertInfectiousDiseases(insertInfos);
        }
    }

    @Override
    public void updateSyncMdmData(String  loginUserId, List<InfectiousDiseases> infectiousDiseases, List<TermCodedValueInfo> mdmCodeValues) {
        //获取登录人员信息
        TUapUser uapUser = getUapUser(loginUserId);
        //取出本地库中存在而mdm不存在的数据
        List<InfectiousDiseases> localExistData = infectiousDiseases.stream().filter(s -> mdmCodeValues.stream().map(TermCodedValueInfo::getCodedValue).
                noneMatch(code -> Objects.equals(s.getDiseasesCode(), code))).collect(Collectors.toList());
        //本地库中存在而mdm不存在的数据
        UpdateInfecDTO ud = new UpdateInfecDTO();
        List<String> updateIds = new ArrayList<>();
        localExistData.forEach(localData -> {
            updateIds.add(localData.getId());
        });
        ud.setUpdateUser(uapUser.getId());
        ud.setIsEnable(MdmDataSyncConstants.NO_USE);
        ud.setIds(updateIds);
        //批量更新mdm已删除的数据状态
        if (updateIds.size() > 0) {
            infectiousDiseasesMapper.updateInfecInfo(ud);
        }
    }

    @Override
    public List<InfectiousDiseases> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        List<InfectiousDiseases> infectiousDiseases = infectiousDiseasesMapper.queryInfecInfo(searchInfecInfoDTO);
        return infectiousDiseases;
    }

    @Override
    public List<TermCodedValueInfo> getMdmDataSync(String code) {
        //判断字典目录是否存在
        if (isCanUse(code)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        List<TermCodedValueInfo> mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(code,
                MdmDataSyncConstants.SYNDROME_PAGENUMBER, MdmDataSyncConstants.SYNDROME_PAGESIZE);
        return mdmCodeValues;
    }

    @Override
    public PageData<InfectiousDiseases> queryInfecPageInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        PageMethod.startPage(searchInfecInfoDTO.getPageIndex(), searchInfecInfoDTO.getPageSize());
        List<InfectiousDiseases> infectiousDiseases = infectiousDiseasesMapper.queryInfecInfo(searchInfecInfoDTO);
        PageInfo<InfectiousDiseases> pageInfo = new PageInfo<>(infectiousDiseases);
        PageData<InfectiousDiseases> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public void updateInfecInfo(String loginUserId, InfectiousDiseases infecDis) {
        try {
            //获取登录人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            infecDis.setUpdateUser(uapUser.getId());
            if (StringUtils.isNotEmpty(infecDis.getDiseasesType())){
                infecDis.setDiseasesTypeName(dictRelationService.getTargetName(MdmDataSyncConstants.INFECTYPE_CODE, infecDis.getDiseasesType()));
            }
            if (StringUtils.isNotEmpty(infecDis.getDiseasesClassify())){
                infecDis.setDiseasesClassifyName(dictRelationService.getTargetName(MdmDataSyncConstants.INFECCLFY_CODE, infecDis.getDiseasesClassify()));
            }
            infectiousDiseasesMapper.updateInfectiousDiseases(infecDis);
        } catch (Exception e) {
            log.error("传染病病种信息维护异常：{}", e);
            throw new MedicalBusinessException("传染病病种信息维护异常");
        }
    }

    @Override
    public boolean judgeIsUse(String id) {
        //根据id查询该字典值域的详细
        InfectiousDiseases si = infectiousDiseasesMapper.queryInfoById(id);
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(MdmDataSyncConstants.INFECDIS_CODE);
        //判断字典目录是否存在
        if (isCanUse(MdmDataSyncConstants.INFECDIS_CODE)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            return false;
        }
        //查询字典值域
        String[] codeValue = {si.getDiseasesCode()};
        //获取字典值域数据
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfo(di, MdmDataSyncConstants.SYNDROME_PAGENUMBER,
                MdmDataSyncConstants.SYNDROME_PAGESIZE, codeValue);
        if (CollUtil.isEmpty(mdmData.getEntities())) {
            return false;
        }
        return !StringUtils.isBlank(mdmData.getEntities().get(0).getId().toString());
    }
}
