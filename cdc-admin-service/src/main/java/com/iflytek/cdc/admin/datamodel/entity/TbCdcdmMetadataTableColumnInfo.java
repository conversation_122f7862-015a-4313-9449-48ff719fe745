package com.iflytek.cdc.admin.datamodel.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "表字段元信息")
@Data
@TableName("tb_cdcdm_metadata_table_column_info")
public class TbCdcdmMetadataTableColumnInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表元信息ID
     */
    @ApiModelProperty(value = "表元信息ID")
    private String tableId ;

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    private String columnCode ;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String columnName ;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String columnType ;

    /**
     * 字段名称描述
     */
    @ApiModelProperty(value = "字段名称描述")
    private String columnDesc ;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType ;

    /**
     * 业务字段
     */
    @ApiModelProperty(value = "业务字段")
    private String businessColumn ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo ;


    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName ;

    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formName ;

    /**
     * 组名称
     */
    @ApiModelProperty(value = "组名称")
    private String groupName ;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName ;

    /**
     * 过滤条件
     */
    @ApiModelProperty(value = "过滤条件")
    private String filterCondition ;

    /**
     * 是否主键
     */
    @ApiModelProperty(value = "是否主键")
    private String isPrimaryKey ;

    /**
     * 表编码
     */
    @ApiModelProperty(value = "表编码")
    private String tableCode ;

    /**
     * 列的长度
     */
    private Integer columnLength;
    
    /**
     * 列的精度
     */
    private Integer columnPrecision;

    /**
     * 是否必填
     */
    private String isRequired;

    /**
     * 排序
     */
    private Integer sort;

    public String getColumnRequired(){
        if ("1".equals(isRequired)){
            return "NOT NULL";
        }
        return "NULL";
    }

    public static TbCdcdmMetadataTableColumnInfo of(String columnName, String columnDesc, String dataType, String isRequired){
        TbCdcdmMetadataTableColumnInfo columnInfo = new TbCdcdmMetadataTableColumnInfo();
        columnInfo.setColumnName(columnName);
        columnInfo.setColumnDesc(columnDesc);
        columnInfo.setDataType(dataType);
        columnInfo.setIsRequired(isRequired);
        return columnInfo;
    }
}