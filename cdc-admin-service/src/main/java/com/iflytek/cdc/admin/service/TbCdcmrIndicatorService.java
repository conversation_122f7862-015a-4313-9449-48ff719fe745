package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.IndicatorQueryDto;
import com.iflytek.cdc.admin.vo.CdcmrIndicatorVO;

import java.util.List;

public interface TbCdcmrIndicatorService {
    /**
     * 新增
     */
    CdcmrIndicatorVO create(CdcmrIndicatorVO input, String loginUserId);

    /**
     * 根据id查询
     */
    CdcmrIndicatorVO load(String id);

    /**
     * 修改
     */
    void update(CdcmrIndicatorVO input, String logingUserId);

    /**
     * 分页查询
     */
    PageInfo<CdcmrIndicatorVO> pageList(IndicatorQueryDto queryDto);

    /**
     * 根据标签查询
     */
    CdcmrIndicatorVO loadByLabel(String label);

    /**
     * 根据标签查询
     */
    List<CdcmrIndicatorVO> listByLabels(List<String> labels);

    /**
     * 删除
     */
    void delete(String id);
}
