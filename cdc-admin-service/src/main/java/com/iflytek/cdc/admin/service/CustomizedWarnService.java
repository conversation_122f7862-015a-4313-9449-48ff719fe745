package com.iflytek.cdc.admin.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.CustomizedLogicFieldVO;
import com.iflytek.cdc.admin.dto.CustomizedWarnQueryDTO;
import com.iflytek.cdc.admin.dto.CustomizedWarnVO;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedEventTypeConfig;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedWarn;

import java.util.List;

public interface CustomizedWarnService {
    PageInfo<TbCdcmrCustomizedWarn> getCustomizedWarnPageList(CustomizedWarnQueryDTO queryDTO, String loginUserId);

    void addCustomizedWarn(CustomizedWarnVO customizedWarnVO, String loginUserId);

    void deleteCustomizedWarn(String id);

    void updateCustomizedWarn(CustomizedWarnVO customizedWarnVO, String loginUserId);

    CustomizedWarnVO getCustomizedWarnById(String id);

    List<CustomizedWarnVO> getAllEnabledWarn(String warningType);

    void updateCustomizedWarnStatus(Integer status, String id, String loginUserId);

    Boolean checkCustomizedWarnByName(String name);

    /**
     * 根据类型查询自定义配置的逻辑判断字段
     */
    List<CustomizedLogicFieldVO> listLogicFieldByTypeAndSource(String warningType, String dataSource);

    /**
     * 根据类型查询自定义配置的聚集性分析字段
     */
    List<TbCdcmrCustomizedEventTypeConfig> listEventTypeConfigByTypeAndSource(String warningType, String dataSource);

    /**
     * 根据类型查询自定义配置的逻辑判断字段
     */
    List<CustomizedLogicFieldVO> listNeedConfigField(String warningType, String dataSource);

}
