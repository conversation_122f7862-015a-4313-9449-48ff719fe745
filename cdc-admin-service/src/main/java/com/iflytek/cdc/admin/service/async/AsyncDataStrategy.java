package com.iflytek.cdc.admin.service.async;




import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 获取数据转换bean的工厂类
 */
@Service("asyncDataStrategy")
@Slf4j
public class AsyncDataStrategy {


    public final AsyncMdmDataService getSyncBean(String code) {

        if (null == code) {
            log.warn("unknown deal type,  code = {}",code);
            return null;
        }

        String beanName = BeanRelationEnum.valueOf(code).getBeanName();
        if (StringUtils.isEmpty(beanName)) {
            log.warn("unknown bean name,  type = {}", beanName);
            return null;
        }
        return (AsyncMdmDataService) SpringContextUtil.getApplicationContext().getBean(beanName);
    }

    public enum BeanRelationEnum {
        //动态加载诊断异步操作类
        INFECDIA("asyncMdmDataInfecDia");

        private String beanName;

        BeanRelationEnum(String beanName) {
            this.beanName = beanName;
        }

        public String getBeanName() {
            return beanName;
        }
    }
}
