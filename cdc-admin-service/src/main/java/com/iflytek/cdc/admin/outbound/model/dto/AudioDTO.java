package com.iflytek.cdc.admin.outbound.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.admin.outbound.constant.enums.AddressTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("录音文件")
public class AudioDTO {

    @ApiModelProperty("分布式主键")
    private String id;

    @ApiModelProperty("文件名称")
    private String name;

    @ApiModelProperty("地址")
    private String url;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("录制时所在地")
    private String address;

    @ApiModelProperty("音频时长 秒")
    private int time;

    public String getAddress() {
        if(!StringUtils.isEmpty(address)){
            return AddressTypeEnum.getByName(address);
        }
        return address;
    }
}
