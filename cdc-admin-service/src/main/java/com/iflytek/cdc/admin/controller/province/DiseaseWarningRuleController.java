package com.iflytek.cdc.admin.controller.province;

import com.iflytek.cdc.admin.dto.InfectedDiseaseWarningRuleDTO;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseWarningRuleDTO;
import com.iflytek.cdc.admin.service.province.DiseaseWarningRuleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/pt/{version}/diseaseWarningRule")
public class DiseaseWarningRuleController {

    @Resource
    private DiseaseWarningRuleService diseaseWarningRuleService;

    @PostMapping("/updateWarningRulePriority")
    @ApiOperation("更新预警规则优先级")
    public void updateWarningRulePriority(@RequestBody DiseaseWarningRuleDTO dto){

        diseaseWarningRuleService.updateWarningRulePriority(dto);
    }

    @PostMapping("/getWarningRulePriority")
    @ApiOperation("查看预警规则优先级")
    public Map<String, String> getWarningRulePriority(@RequestBody DiseaseWarningRuleDTO dto){

        return diseaseWarningRuleService.getWarningRulePriority(dto);
    }

}
