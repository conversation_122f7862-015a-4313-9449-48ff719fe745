package com.iflytek.cdc.admin.constant;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/7/28 9:42
 **/
@Component
public class MdmDataSyncConstants {


    static Map<String,String> map = new HashMap<String,String>();

    static{
        map.put("MEDIARULE","cdcmr-infecdis-code");
    }

    public  static Map<String,String> getMap(){
        return map;
    }

    /**
     * 症候群编码
     **/
    public static  final  String SYNDROME_CODE="cdcmr-zhq-code";

    /**
     * 传染病病种编码
     **/
    public static  final  String INFECDIS_CODE="cdcmr-infecdis-code";

    /**
     * 传染病分类编码
     **/
    public static  final  String INFECCLFY_CODE="cdcmr-infecdisclfy-code";

    /**
     * 传染病类别编码
     **/
    public static  final  String INFECTYPE_CODE="cdcmr-infecdistype-code";

    /**
     * 传染病诊断编码
     **/
    public static  final  String INFECDIA_CODE="cdcmr-infecdia-code";

    /**
     * 传染病诊断类型编码
     **/
    public static  final  String INFECDIATYPE_CODE="cdcmr-infecdiatype-code";

    /**
     * mdm症候群值域查询使用页条数
     **/
    public static  final  Integer  SYNDROME_PAGESIZE=1000;

    /**
     * mdm症候群值域查询使用起始页数
     **/
    public static final  Integer  SYNDROME_PAGENUMBER=1;

    /**
     * 启用
     **/
    public static final  String   YES_USE="1";

    /**
     * 禁用
     **/
    public static final  String   NO_USE="0";

    /**
     * 已删除
     **/
    public static final  String  MDM_YES_DELETE="1";

    /**
     * 未删除
     **/
    public static final  String  MDM_NO_DELETE="0";

    /**
     * 传染病诊断编码
     **/
    public static  final  String INFECDIA_STRATEGY="INFECDIA";

    public static final  Integer  MAX_INSERT_MDM_SIZE=2000;

    public static final  Integer  MDM_UPDATE_ID_SIZE=200;

}
