package com.iflytek.cdc.admin.service.brief;

import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.EasyExcel;
import com.iflytek.cdc.admin.enums.StatisticalPeriodEnum;
import com.iflytek.cdc.admin.enums.StatusEnum;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.vo.brief.TemplateRuleExcelVo;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.apiservice.CdcDataServiceApi;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.customizedapp.enums.DateUnitEnum;
import com.iflytek.cdc.admin.dto.brief.TemplateSearchDto;
import com.iflytek.cdc.admin.entity.brief.PushUserEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateRuleDetailEntity;
import com.iflytek.cdc.admin.entity.brief.TemplateRuleEntity;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.mapper.brief.TemplateRuleMapper;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.util.brief.CycleTimeUtil;
import com.iflytek.cdc.admin.vo.brief.TemplatePushRuleVo;
import com.iflytek.cdc.admin.vo.brief.TemplateRuleVo;
import com.iflytek.cdc.admin.vo.region.DimRegionNation;
import com.iflytek.medicalboot.core.id.BatchUidService;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;


/**
 * 模板规则服务impl
 *
 * <AUTHOR>
 * @date 2024/12/31
 */
@Service("templateRuleService")
@RequiredArgsConstructor
public class TemplateRuleServiceImpl extends ServiceImpl<TemplateRuleMapper, TemplateRuleEntity> implements TemplateRuleService {

    private final TemplateRuleMapper templateRuleMapper;
    private final BatchUidService batchUidService;
    private final CdcDataServiceApi cdcDataServiceApi;

    @Override
    public ApiResult<PageInfo<TemplateRuleVo>> queryPage(TemplateSearchDto searchDto) {
        PageHelper.startPage(searchDto.getPageIndex(), searchDto.getPageSize());
        List<TemplateRuleVo> templateEntities = templateRuleMapper.listBySearch(searchDto);
        PageHelper.clearPage();
        return ApiResult.ok(new PageInfo<>(templateEntities));
    }

    @Override
    public ApiResult<?> changeStatus(String loginUserId, String updatorName, String id, String status) {
        UpdateWrapper<TemplateRuleEntity> uq = new UpdateWrapper<>();
        uq.set("status", status);
        uq.set("updator_id", loginUserId);
        uq.set("updator_name", updatorName);
        uq.set("update_time", new Date());
        uq.eq("id", id);
        templateRuleMapper.update(null, uq);
        return ApiResult.ok();
    }

    @Override
    public void generateRule(TemplateRuleEntity ruleEntity, String loginUserId) {
        if (StrUtil.isBlank(ruleEntity.getId())) {
            ruleEntity.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_TEMPLATE_RULE)));
            ruleEntity.setCreatorId(loginUserId);
            ruleEntity.setCreateTime(new Date());
        }
        // 填充区划信息
        try {
            fillRegionInfo(ruleEntity);
        } catch (Exception e) {
            log.error("填充区划信息失败", e);
        }
        ruleEntity.setUpdatorId(loginUserId);
        ruleEntity.setUpdateTime(new Date());
        this.saveOrUpdate(ruleEntity);
        // 按时间生成规则明细（先删后增）
        regenerateRuleDetail(ruleEntity);
    }

    /**
     * 填充区划信息
     * @param ruleEntity
     */
    private void fillRegionInfo(TemplateRuleEntity ruleEntity) {
        String regionCode;
        if (StrUtil.isNotBlank(ruleEntity.getDistrictCode())) {
            regionCode = ruleEntity.getDistrictCode();
        } else if (StrUtil.isNotBlank(ruleEntity.getCityCode())) {
            regionCode = ruleEntity.getCityCode();
        } else if (StrUtil.isNotBlank(ruleEntity.getProvinceCode())) {
            regionCode = ruleEntity.getProvinceCode();
        } else {
            // 没有 code 就直接退出
            return;
        }

        DimRegionNation region = cdcDataServiceApi.getRegionByCode(regionCode);
        if (region != null) {
            if (StrUtil.isNotBlank(region.getProvinceName())) {
                ruleEntity.setProvinceCode(region.getProvinceCode());
                ruleEntity.setProvinceName(region.getProvinceName());
            } else {
                ruleEntity.setProvinceCode(null);
                ruleEntity.setProvinceName(null);
            }

            if (StrUtil.isNotBlank(region.getCityName())) {
                ruleEntity.setCityCode(region.getCityCode());
                ruleEntity.setCityName(region.getCityName());
            } else {
                ruleEntity.setCityCode(null);
                ruleEntity.setCityName(null);
            }
            
            if (StrUtil.isNotBlank(region.getDistrictName())) {
                ruleEntity.setDistrictCode(region.getRegionCode());
                ruleEntity.setDistrictName(region.getDistrictName());
            } else {
                ruleEntity.setDistrictCode(null);
                ruleEntity.setDistrictName(null);
            }
        }
    }

    private void regenerateRuleDetail(TemplateRuleEntity ruleEntity) {
        // 删除规则明细
        templateRuleMapper.deleteDetailByRuleId(ruleEntity.getId());

        List<String> dateList;
        if (Constants.YES_USE.equals(ruleEntity.getRepeatFlag())) {
            dateList = CycleTimeUtil.getDatesBetween(ruleEntity.getStartTime(), ruleEntity.getEndTime(), ruleEntity.getStatisticsCycle());
        } else {
            dateList = Lists.newArrayList(ruleEntity.getCustomTime().split(","));
        }
        List<TemplateRuleDetailEntity> ruleDetails = Lists.newArrayList();
        dateList.forEach(date -> {
            TemplateRuleDetailEntity ruleDetail = new TemplateRuleDetailEntity();
            ruleDetail.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_TEMPLATE_RULE_DETAIL)));
            ruleDetail.setTemplateId(ruleEntity.getTemplateId());
            ruleDetail.setRuleId(ruleEntity.getId());
            ruleDetail.setStatisticsTime(date);
            ruleDetail.setStatisticsCycle(ruleEntity.getStatisticsCycle());
            DateUnitEnum unitEnum = DateUnitEnum.getByCode(ruleEntity.getStatisticsCycle());
            switch (unitEnum) {
                case YEAR:
                    LocalDate[] dates = CycleTimeUtil.getYearStartEnd(Integer.parseInt(date.substring(0, 4)));
                    ruleDetail.setStartDate(dates[0]);
                    ruleDetail.setEndDate(dates[1]);
                    break;
                case QUARTER:
                    // date 格式: yyyy年第N季
                    int qYear   = Integer.parseInt(date.substring(0, 4));
                    int quarter = Integer.parseInt(date.substring(date.indexOf("第") + 1, date.indexOf("季")));
                    int startMonth = (quarter - 1) * 3 + 1;
                    int endMonth   = startMonth + 2;
                    YearMonth startYM = YearMonth.of(qYear, startMonth);
                    YearMonth endYM   = YearMonth.of(qYear, endMonth);
                    ruleDetail.setStartDate(startYM.atDay(1));
                    ruleDetail.setEndDate(endYM.atEndOfMonth());
                    break;
                case MONTH:
                    LocalDate[] monthDates = CycleTimeUtil.getMonthStartEnd(Integer.parseInt(date.substring(0, 4)), Integer.parseInt(date.substring(5, date.indexOf("月"))));
                    ruleDetail.setStartDate(monthDates[0]);
                    ruleDetail.setEndDate(monthDates[1]);
                    break;
                case MEADOW:
                    // date 格式: yyyy年M月[上|中|下]旬
                    int dYear  = Integer.parseInt(date.substring(0, 4));
                    int dMonth = Integer.parseInt(date.substring(date.indexOf("年") + 1, date.indexOf("月")));
                    String section = date.substring(date.indexOf("月") + 1, date.indexOf("旬"));
                    int startDay;
                    int endDay;
                    switch (section) {
                        case "上":
                            startDay = 1;
                            endDay   = 10;
                            break;
                        case "中":
                            startDay = 11;
                            endDay   = 20;
                            break;
                        case "下":
                            startDay = 21;
                            endDay   = YearMonth.of(dYear, dMonth).lengthOfMonth();
                            break;
                        default:
                            throw new IllegalArgumentException("非法旬段：" + section);
                    }
                    ruleDetail.setStartDate(LocalDate.of(dYear, dMonth, startDay));
                    ruleDetail.setEndDate(LocalDate.of(dYear, dMonth, endDay));
                    break;
                case WEEK:
                    LocalDate[] weekDates = CycleTimeUtil.getWeekStartEnd(Integer.parseInt(date.substring(0, 4)), Integer.parseInt(date.substring(6, date.indexOf("周"))));
                    ruleDetail.setStartDate(weekDates[0]);
                    ruleDetail.setEndDate(weekDates[1]);
                    break;
                case DAY:
                    LocalDate[] dayDates = CycleTimeUtil.getDayStartEnd(LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy年M月d日")));
                    ruleDetail.setStartDate(dayDates[0]);
                    ruleDetail.setEndDate(dayDates[1]);
                    break;
                default:
                    break;
            }
            ruleDetails.add(ruleDetail);
        });
        Lists.partition(ruleDetails, 500).forEach(templateRuleMapper::batchSaveRuleDetail);
    }

    @Override
    public TemplateRuleEntity getByTemplateIdOrId(String templateId, String id) {

        QueryWrapper<TemplateRuleEntity> qw = new QueryWrapper<>();
        if (StrUtil.isNotBlank(id)) {
            qw.eq("id", id);
        }
        if (StrUtil.isNotBlank(templateId)) {
            qw.eq("template_id", templateId);
        }
        qw.eq("delete_flag", DeleteFlagEnum.NO.getCode());
        return templateRuleMapper.selectOne(qw);
    }

    @Override
    public ApiResult<PageInfo<TemplatePushRuleVo>> pushRuleList(TemplateSearchDto searchDto) {
        PageHelper.startPage(searchDto.getPageIndex(), searchDto.getPageSize());
        List<TemplatePushRuleVo> templateEntities = templateRuleMapper.pushRuleList(searchDto);
        PageHelper.clearPage();
        return ApiResult.ok(new PageInfo<>(templateEntities));
    }

    @Override
    public void saveAuthUser(List<PushUserEntity> userList, String loginUserId, String updatorName) {
        // 修改规则生效信息
        String ruleId = userList.get(0).getRuleId();
        // 获取机构Id
        String orgId = userList.get(0).getOrgId();
        UpdateWrapper<TemplateRuleEntity> uq = new UpdateWrapper<>();
        uq.set("auth_user_id", loginUserId);
        uq.set("auth_user_name", updatorName);
        uq.set("auth_time", new Date());
        uq.eq("id", ruleId);
        templateRuleMapper.update(null, uq);
        // 删除已配置的权限用户
        templateRuleMapper.deleteUserByRuleId(ruleId,orgId);
        // 维护用户权限
        userList.forEach(user -> user.setId(String.valueOf(batchUidService.getUid(TableName.TB_CDCBR_PUSH_USER))));
        // 分批插入权限用户
        Lists.partition(userList, 500).forEach(templateRuleMapper::saveAuthUser);
    }

    @Override
    public List<PushUserEntity> pushUserList(String ruleId,String orgId) {
        return templateRuleMapper.pushUserList(ruleId,orgId);
    }

    @Override
    public void deleteCascadeByTemplateId(String templateId) {
        // 删除规则即可，可以不删除明细
        templateRuleMapper.update(null, new UpdateWrapper<TemplateRuleEntity>()
                .eq("template_id", templateId)
                .set("delete_flag", DeleteFlagEnum.YES.getCode()));
    }

    @Override
    public ResponseEntity<byte[]> exportRuleList(TemplateSearchDto searchDto) {
        if (searchDto.isAllFlag()) {
            searchDto.setIdList(null);
        }
        List<TemplateRuleVo> templateEntities = templateRuleMapper.listBySearch(searchDto);
        List<TemplateRuleExcelVo> excelRes = new ArrayList<>();
        templateEntities.forEach(entity -> {
            TemplateRuleExcelVo excelVO = new TemplateRuleExcelVo();
            BeanUtils.copyProperties(entity, excelVO);
            excelVO.setStatisticsCycleDesc(StatisticalPeriodEnum.getValueByCode(entity.getStatisticsCycle()));
            String region = dealRegion(entity);
            excelVO.setRegion(region);
            excelVO.setStatus(StatusEnum.getValueByCode(entity.getStatus()));
            excelRes.add(excelVO);
        });
        ByteArrayOutputStream excelOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(excelOutputStream, TemplateRuleExcelVo.class)
                .sheet("Sheet1")
                .doWrite(excelRes);
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("简报模板列表.xlsx");
        return new ResponseEntity<>(excelOutputStream.toByteArray(), httpHeaders, HttpStatus.CREATED);
    }

    private String dealRegion(TemplateRuleVo entity) {
        StringBuilder region = new StringBuilder();
        if (StrUtil.isNotBlank(entity.getProvinceName())) {
            region.append(entity.getProvinceName());
        }
        if (StrUtil.isNotBlank(entity.getCityName())) {
            region.append(entity.getCityName());
        }
        if (StrUtil.isNotBlank(entity.getDistrictName())) {
            region.append(entity.getDistrictName());
        }
        return region.length() > 0 ? region.toString() : "";
    }
}
