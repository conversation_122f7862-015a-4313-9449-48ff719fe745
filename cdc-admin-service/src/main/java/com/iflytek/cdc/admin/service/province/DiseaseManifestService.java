package com.iflytek.cdc.admin.service.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestInfo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrDiseaseManifestRule;
import com.iflytek.cdc.admin.model.mr.dto.DiseaseManifestQueryDTO;
import com.iflytek.cdc.admin.model.mr.vo.DiseaseManifestRulesVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface DiseaseManifestService {

    /**
     * 查询症状监测列表
     * */
    PageInfo<TbCdcmrDiseaseManifestInfo> getDiseaseManifestInfoList(DiseaseManifestQueryDTO dto);

    /**
     * 新增症状监测信息
     * */
    void addDiseaseManifestInfo(TbCdcmrDiseaseManifestInfo manifestInfo);

    /**
     * 更新某条症状监测信息
     * */
    void updateDiseaseManifestInfo(TbCdcmrDiseaseManifestInfo manifestInfo);

    /**
     * 查询某个症状的规则组
     * */
    List<DiseaseManifestRulesVO> getDiseaseManifestRules(String diseaseManifestId);

    /**
     * 编辑规则组
     * */
    void editDiseaseManifestRules(String diseaseManifestId, List<TbCdcmrDiseaseManifestRule> manifestRules);
}
