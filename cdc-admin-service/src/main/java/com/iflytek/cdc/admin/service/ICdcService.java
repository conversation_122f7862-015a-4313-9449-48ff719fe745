package com.iflytek.cdc.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ICdcService<T> extends IService<T> {

    /**
     * 创建
     * @param entity 实体对象
     * @return 实体对象
     */
    T create(T entity);

    /**
     * 修改
     * @param entity 实体对象
     * @return 实体对象
     */
    T update(T entity);

    /**
     * 根据id删除
     * @param id id
     */
    void deleteById(String id);


    /**
     * 级联查询
     */
    T loadCascade(String id);

    /**
     * 批量根据id查询
     */
    List<T> listByIds(List<String> ids);

}
