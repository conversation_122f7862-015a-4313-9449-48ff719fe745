package com.iflytek.cdc.admin.customizedapp.entity;

import com.iflytek.cdc.admin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;

/**
 * 按钮配置;
 * <AUTHOR> fengwang35
 * @date : 2024-9-9
 */
@ApiModel(value = "按钮配置")
@TableName("tb_cdccs_page_button_setting")
@EqualsAndHashCode(callSuper = true)
@Data
public class TbCdccsPageButtonSetting extends BaseEntity implements Serializable{
    /** 主键 */
    @ApiModelProperty(value = "主键")
    private String id ;
    /** 页面id */
    @ApiModelProperty(value = "页面id")
    private String pageId ;
    /** 页面名称 */
    @ApiModelProperty(value = "页面名称")
    private String pageName ;
    /** 操作类型 */
    @ApiModelProperty(value = "操作类型")
    private String openType ;
    /** 按钮名称 */
    @ApiModelProperty(value = "按钮名称")
    private String buttonName ;
    /** 按钮类型 */
    @ApiModelProperty(value = "按钮类型")
    private String buttonType ;
    /** 关联类型 */
    @ApiModelProperty(value = "关联类型")
    private String relationType ;
    /** 关联code */
    @ApiModelProperty(value = "关联code")
    private String relationCode ;
    /** 出参配置 */
    @ApiModelProperty(value = "出参配置")
    private String outParameter ;
    /** 显示条件 */
    @ApiModelProperty(value = "显示条件")
    private String showCondition ;
    /** 扩展属性json */
    @ApiModelProperty(value = "扩展属性json")
    private String extendJson ;

    @ApiModelProperty(value = "是否APP显示")
    private String appShow;

}