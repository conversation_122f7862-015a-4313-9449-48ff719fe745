package com.iflytek.cdc.admin.customizedapp.controller;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsApp;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/customizedApp/app")
public class AppController {

    @Resource
    private AppService tbCdccsAppService;

    /**
     * 通过ID查询单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation("通过ID查询单条数据")
    public TbCdccsApp queryById(@RequestParam String id){
        return tbCdccsAppService.getById(id);
    }


    /**
     * 通过code查询单条数据
     */
    @GetMapping("/queryByCode")
    @ApiOperation("通过ID查询单条数据")
    public TbCdccsApp queryByCode(@RequestParam String code){
        return tbCdccsAppService.loadByCode(code);
    }

    /**
     * 查询所有应用
     */
    @PostMapping("/listAll")
    @ApiOperation("查询所有分类")
    public List<TbCdccsApp> listAll(@RequestBody AppQueryDTO queryDTO){
        return tbCdccsAppService.listAll(queryDTO);
    }

    /**
     * 新增数据
     */
    @PostMapping("/add")
    @ApiOperation("新增数据")
    public TbCdccsApp addApp(@RequestBody TbCdccsApp tbCdccsApp){
        return tbCdccsAppService.create(tbCdccsApp);
    }

    /**
     * 更新数据
     */
    @PostMapping("/update")
    @ApiOperation("更新数据")
    public TbCdccsApp update(@RequestBody TbCdccsApp tbCdccsApp){
        return tbCdccsAppService.update(tbCdccsApp);
    }

    /**
     * 通过主键删除数据
     */
    @PostMapping("/deleteById")
    @ApiOperation("通过主键删除数据")
    public void deleteById(@RequestParam String id){
        tbCdccsAppService.deleteById(id);
    }

    /**
     * 根据分类id查询
     */
    @GetMapping("/listByCategoryId")
    @ApiOperation("根据分类id查询")
    public List<TbCdccsApp> listByCategoryId(@RequestParam  String categoryId, @RequestParam(required = false) String appName){
        return tbCdccsAppService.listByCategoryId(categoryId, appName);
    }

    public static void main(String[] args) {
        String str = "调查目的、调查内容、调查范围";
        String[] split = str.split("[;；,，、]");
        //System.out.println(str.replace("、", "\n"));

        for (int i = 0; i < split.length; i++){
            System.out.println( i + 1 + "\t" + split[i] + "\t" + "" + split[i] + "");
        }

    }

}
