package com.iflytek.cdc.admin.outbound.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.outbound.entity.OutboundRecord;
import com.iflytek.cdc.admin.outbound.mapper.OutboundRecordMapper;
import com.iflytek.cdc.admin.outbound.model.dto.OutboundRecordQueryDTO;
import com.iflytek.cdc.admin.outbound.service.OutboundRecordService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class OutboundRecordServiceImpl extends CdcServiceBaseImpl<OutboundRecordMapper, OutboundRecord>implements OutboundRecordService {


    @Override
    public List<OutboundRecord> listRecords(OutboundRecordQueryDTO queryDTO){
        LambdaQueryWrapper<OutboundRecord> queryWrapper = lambdaQueryWrapper();
        if (StringUtils.isNotEmpty(queryDTO.getBatchNo())){
            queryWrapper.eq(OutboundRecord::getBatchNo, queryDTO.getBatchNo());
        }
        if (queryDTO.getStartDate() != null){
            queryWrapper.ge(OutboundRecord::getCreateTime, queryDTO.getStartDate());
        }
        if (queryDTO.getEndDate() != null){
            queryWrapper.le(OutboundRecord::getCreateTime, queryDTO.getEndDate());
        }
        if (!CollectionUtils.isEmpty(queryDTO.getStatuses())){
            queryWrapper.in(OutboundRecord::getStatus, queryDTO.getStatuses());
        }
        return list(queryWrapper);
    }

    @Override
    public void saveStatus(List<OutboundRecord> outboundRecords) {
        outboundRecords.forEach(o -> {
            lambdaUpdate().set(OutboundRecord::getStatus, o.getStatus()).eq(OutboundRecord::getId, o.getId()).update();
        });
    }
}
