package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.MdmDataSyncParamDTO;
import com.iflytek.cdc.admin.dto.ResponseResult;
import com.iflytek.cdc.admin.service.mdm.MdmDataSyncHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * @ClassName MdmDictDataSyncController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/18 13:59
 * @Version 1.0
 */

@RestController
@Api(tags = "MDM数据操作公共接口")
public class MdmDictDataSyncController {

    @Resource
    private MdmDataSyncHandler mdmDataSyncHandler;

    @PostMapping("/{version}/pt/common/MdmDictDataSync")
    @ApiOperation("mdm数据同步")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ResponseResult mdmDataSyncs(@PathVariable String version, @RequestParam String loginUserId, @RequestBody MdmDataSyncParamDTO  paramDTO) {
       return   mdmDataSyncHandler.executeSyncMdmData(paramDTO.getCode(), loginUserId, paramDTO.getFlag());
    }

    @PostMapping("/{version}/pt/common/mdmDataSyncsGetStatus")
    @ApiOperation("mdm数据同步获取异步状态")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public String  mdmDataSyncsGetStatus(@PathVariable String version,@RequestParam String loginUserId,@RequestBody MdmDataSyncParamDTO  paramDTO){
        return  mdmDataSyncHandler.syncMdmGetStatus(paramDTO.getCode(), paramDTO.getBatchId(), loginUserId);
    }

    @PostMapping("/{version}/pt/common/getMdmDictData")
    @ApiOperation("页面获取mdm下拉框数据")
    @ApiImplicitParam(name = "version", paramType = "path", allowableValues = Constants.CURRENT_VERSION, required = true)
    public ResponseResult getMdmDictData(String code){
        return mdmDataSyncHandler.getMdmDicData(code);
    }
}
