package com.iflytek.cdc.admin.outbound.service;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.mapper.CaseAudioMapper;
import com.iflytek.cdc.admin.outbound.model.dto.AudioDTO;
import com.iflytek.cdc.admin.service.FileService;
import com.iflytek.cdc.admin.util.FileUtils;
import com.iflytek.cdc.admin.util.PCMUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * 语音转写服务
 */
@Service
@Slf4j
@RefreshScope
public class AudioService {
    @Value("${sound.temp.path:/home/<USER>/audios/}")
    private String tempAudioPath;
    @Value("${sound.url.replace.old:}")
    private String replaceContentOld;
    @Value("${sound.url.replace.new:}")
    private String replaceContentNew;
    @Resource
    private CaseAudioMapper caseAudioMapper;
    @Autowired
    private BatchUidService batchUidService;
    @Resource
    private AudioTextService audioTextService;
    @Resource
    private FileService fileService;



    public static final ThreadLocal<String> audioDuration = new ThreadLocal<>();

    public void uploadAudio(String businessId,
                            String audioName,
                            String tempResult,
                            String address,
                            MultipartFile file,
                            String callPath) {
        String attachmentId = null;
        //如果是pcm文件需要转换再上传 否则直接上传
        String audioFileName = UUID.randomUUID() + ".wav";
        String tempAudio = tempAudioPath + audioFileName;
        FileUtils.mkdirFile(tempAudioPath);
        Integer index = caseAudioMapper.findMaxIndex(businessId);
        if (file.getOriginalFilename().endsWith(".pcm")) {
            try {
                PCMUtil.convertAudioFiles(file, tempAudio);
            } catch (Exception e) {
                throw new MedicalBusinessException("转换pcm失败");
            }
            try (FileInputStream inputStream = new FileInputStream(tempAudio)) {
                attachmentId = fileService.upload(inputStream, businessId,  audioFileName);
            } catch (Exception e) {
                throw new MedicalBusinessException("存储转换后wav失败");
            }
        } else if (StrUtil.equals(address, "call") || StrUtil.equals(address, "audio")) {
            try {
                String tempAudio2 = tempAudioPath + UUID.randomUUID() + ".wav";
                PCMUtil.convert16kAudioFiles(file, tempAudio2);
                PCMUtil.get16KUrl(tempAudio2, tempAudio);
                file = new MockMultipartFile("file", audioFileName, "audio/wav", new FileInputStream(tempAudio));
//                    swiftService.uploadFileAsync(file, caseId, audioFileName);
//                if (StrUtil.equals(address, "call")) {
//                    fileService.uploadFileAsync(file, businessId, audioFileName);
//                } else {
                attachmentId = fileService.upload(file.getInputStream() , businessId, audioFileName);
//                }
            } catch (Exception e) {
                log.error("8k转换16k失败",e);
                throw new MedicalBusinessException("8k转换16k失败");
            }
        } else if (StrUtil.equals(address, "upload")) {
            try {
//                if (!TaskAttachmentConstant.localAudioFormat.contains(cn.hutool.core.io.FileUtil.getSuffix(file.getOriginalFilename()))) {
//                    throw new MedicalBusinessException("上传的音频格式不正确，仅支持扩展名为wav,m4a,amr,mp3的音频文件");
//                }
                String tempAudio2 = tempAudioPath + UUID.randomUUID() + file.getOriginalFilename();
                PCMUtil.convert16kAudioFiles(file, tempAudio2);
                PCMUtil.convertToWav(tempAudio2, tempAudio);
                file = new MockMultipartFile("file", audioFileName, "audio/wav", new FileInputStream(tempAudio));
                attachmentId = fileService.upload(file.getInputStream(), businessId, audioFileName);
            } catch (Exception e) {
                log.error("格式转换失败",e);
                throw new MedicalBusinessException("格式转换失败");
            }
        } else {
            try (InputStream inputStream = file.getInputStream()) {
                attachmentId = fileService.upload(inputStream, businessId,  audioFileName);
            } catch (Exception e) {
                throw new MedicalBusinessException("存储失败");
            }
        }


        //2、新增记录
        File audioFile = new File(tempAudio);
        AudioRecord audioRecord = new AudioRecord();
        audioRecord.setAudioFinalText("");
        audioRecord.setAudioTempText(tempResult);
        audioRecord.setCallPath(callPath);
        audioRecord.setBusinessId(businessId);
        audioRecord.setAudioAddress(address);
        audioRecord.setAudioAddressType(address);
        audioRecord.setAudioIndex(index == null ? 1 : ++index);
        audioRecord.setId(String.valueOf(batchUidService.getUid(AudioRecord.TABLE_NAME)));
        audioRecord.setAudioPath(fileService.getPathByAttachmentId(attachmentId));
        audioRecord.setObjectName(audioFileName);
        audioRecord.setTransformFinishFlag(0);
        // 音频时长
        if (StrUtil.equals(address, "audio") || StrUtil.equals(address, "call")) {
            audioRecord.setIsCall("1");
        }

        if (StrUtil.equals(address, "audio")) {
            audioRecord.setAudioTime(Integer.valueOf(audioDuration.get()));
        } else {
            audioRecord.setAudioTime(PCMUtil.getDuration(audioFile).intValue());
        }
        audioRecord.setAudioName(audioName);
        caseAudioMapper.insertSelective(audioRecord);
        //删除
        audioFile.delete();
        //3、上传转写 如果是非pcm直接上传原始multipart 如果经过转换 需要上传新的临时文件
        audioTextService.getAudioText(file, audioRecord.getId());
    }

    public AudioRecord getOne(String id) {
        return caseAudioMapper.findById(id);
    }

    public AudioRecord getCallAudio(String caseId, String audioAddress) {
        return caseAudioMapper.getCallAudio(caseId, audioAddress);
    }

    public void deleteByPathAndCaseId(String url, String caseId) {
        caseAudioMapper.deleteByPathAndBusinessId(url, caseId);
    }

    public List<AudioDTO> getList(String caseId) {
        return caseAudioMapper.findByBusinessId(caseId);
    }

    public Integer updateFileName(String id, String name) {
        return caseAudioMapper.updatefileName(id, name);
    }

    /**
     * 下载音频文本压缩包
     *
     * @param id       id
     * @param response HttpServletResponse
     */
    public void downloadCaseAudio(String id, HttpServletResponse response) {
        // 1.查询音频及文本信息
        AudioRecord audioRecord = caseAudioMapper.findById(id);
        if (audioRecord == null) {
            throw new MedicalBusinessException("没有获取到音频文件");
        }
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
             BufferedInputStream inputStream = new BufferedInputStream(new URL(audioRecord.getAudioPath()
                     .replace(replaceContentOld, replaceContentNew)).openStream())) {
            String fileName = URLEncoder.encode(audioRecord.getAudioName(), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".zip");
            // 2.1下载音频
            zos.putNextEntry(new ZipEntry(audioRecord.getAudioName() + ".wav"));
            int byteContent;
            byte[] data = new byte[1024];
            while ((byteContent = inputStream.read(data, 0, 1024)) != -1) {
                zos.write(data, 0, byteContent);
            }
            // 2.1下载转写文本
            String audioFinalText = audioRecord.getAudioFinalText();
            String audioTempText = audioRecord.getAudioTempText();
            String audioText = formatAudioText(audioFinalText, audioTempText);
            zos.putNextEntry(new ZipEntry(audioRecord.getAudioName() + ".txt"));
            IoUtil.write(zos, true, audioText.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("下载病例音频失败!", e);
        }
    }

    /**
     * 格式化音频文本 优先取audioFinalText，其次audioTempText
     *
     * @param audioFinalText 最终转写文本
     * @param audioTempText  临时转写文本
     * @return 格式化之后的音频文本
     */
    public String formatAudioText(String audioFinalText, String audioTempText) {
        StringBuilder sb = new StringBuilder();
        // audioFinalText有值
        if (StrUtil.isNotBlank(audioFinalText)) {
            for (Object o : new JSONArray(audioFinalText)) {
                JSONObject jsonObject = (JSONObject) o;
                String person = jsonObject.getStr("speaker");
                String paragraph = jsonObject.getStr("paragraph");
                sb.append("发言人").append(person).append(":").append(paragraph).append("\n");
            }
            return sb.toString();
        }

        // audioTempText有值
        if (StrUtil.isNotBlank(audioTempText)) {
            for (Object o : new JSONArray(audioTempText)) {
                JSONObject jsonObject = (JSONObject) o;
                String person = jsonObject.getStr("person");
                String paragraph = jsonObject.getStr("paragraph");
                sb.append(person).append(":").append(paragraph).append("\n");
            }
            return sb.toString();
        }
        return StrUtil.EMPTY;
    }

    public int checkIsExistCaseAudio(String url, String relationId) {
        //判断是否已经存在
        return caseAudioMapper.checkIsExistCaseAudio(url, relationId);
    }

    public void saveAudio(String tempText, String audioName, String relationId) {
        AudioRecord audioRecord = new AudioRecord();
        audioRecord.setBusinessId(relationId);
        audioRecord.setId(String.valueOf(batchUidService.getUid(AudioRecord.TABLE_NAME)));
        audioRecord.setAudioTempText(tempText);
        audioRecord.setTransformFinishFlag(0);
        audioRecord.setIsCall("1");
        audioRecord.setAudioAddressType("call");
        audioRecord.setAudioName(audioName);
        caseAudioMapper.insertSelective(audioRecord);
    }

    public void updateAudioUrl(String url, String relationId) {
        caseAudioMapper.updateAudioUrl(url, relationId);
    }

    public void updateByPathAndCaseId(String url, String relationId) {
        caseAudioMapper.updateByPathAndBusinessId(url, relationId);
    }

    public String getTempTextByPathAndCaseId(String url, String relationId) {
        return caseAudioMapper.getTempTextByPathAndCaseId(url, relationId);
    }
}
