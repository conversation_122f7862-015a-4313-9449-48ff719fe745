package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.dto.GroupRegionDTO;
import com.iflytek.cdc.admin.dto.GroupRegionInsertDTO;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.UploadResultVO;
import com.iflytek.cdc.admin.vo.region.RegionCascadeVO;
import com.iflytek.cdc.admin.dto.addressstandardize.RegionQueryDTO;
import com.iflytek.cdc.admin.vo.region.GroupRegionVO;
import com.iflytek.cdc.admin.vo.LabelValueVO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 */
public interface RegionService {


    /**
     * 1. 增量同步UAP区划, 默认同步配置省份, 到区县Level; 未配置则同步全国区县
     * 2. 调用高德API解析区划数据
     */
    void syncUapNationByTime(Date date);

    void syncUapNation(String code, int targetLevel);

    void amapAnalysis(String areaCode, int targetLevel, Date startDate, Date endDate);

    PageData<GroupRegionVO> regionSearch(String loginUserId, RegionQueryDTO regionQueryDTO);

    void insertGroupRegion(String loginUserId, GroupRegionInsertDTO groupRegionInsertDTO);

    void updateGroupRegion(String loginUserId, GroupRegionDTO groupRegionDTO);

    void enableGroupRegion(String loginUserId, String id, String isEnable);

    void deleteGroupRegion(String loginUserId, String id);

    ResponseEntity<byte[]> exportGroupRegionBy(String loginUserId, RegionQueryDTO regionQueryDTO);

    RegionCascadeVO getUserRegionLabel(String loginUserId, int fromLevel, int toLevel);

    List<LabelValueVO> getChildLabelByUserCode(String loginUserId, String regionCode);

    UploadResultVO importGroupRegion(String loginUserId, MultipartFile file) throws IOException;
}
