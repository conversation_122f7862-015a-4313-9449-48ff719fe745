package com.iflytek.cdc.admin.service.async;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @ClassName SpringContextUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/10 14:20
 * @Version 1.0
 */

@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext ctu;

    public SpringContextUtil() {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        ctu = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return ctu;
    }
}
