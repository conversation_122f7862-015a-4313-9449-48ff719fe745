package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.addressstandardize.*;
import com.iflytek.cdc.admin.dto.amap.geocode.AmapGeocodeJsonRootBean;
import com.iflytek.cdc.admin.dto.amap.geocode.Regeocode;
import com.iflytek.cdc.admin.dto.amap.inputtips.InputTips;
import com.iflytek.cdc.admin.dto.amap.inputtips.Tips;
import com.iflytek.cdc.admin.dto.amap.search.AmapSearchJsonRootBean;
import com.iflytek.cdc.admin.dto.amap.search.AmapSearchJsonRootBeanV2;
import com.iflytek.cdc.admin.dto.amap.search.Pois;
import com.iflytek.cdc.admin.dto.amap.search.PoisV2;
import com.iflytek.cdc.admin.dto.amap.searchlatest.AmapSearchJsonRootBeanLatest;
import com.iflytek.cdc.admin.dto.amap.searchlatest.PoisLatest;
import com.iflytek.cdc.admin.entity.AddressDetailMapping;
import com.iflytek.cdc.admin.entity.AddressStandard;
import com.iflytek.cdc.admin.entity.AmapAddressDetailMapping;
import com.iflytek.cdc.admin.mapper.RegionMapper;
import com.iflytek.cdc.admin.service.AddressDetailMappingService;
import com.iflytek.cdc.admin.service.AddressStandardService;
import com.iflytek.cdc.admin.service.AmapService;
import com.iflytek.cdc.admin.service.StdTextPreProcessService;
import com.iflytek.cdc.admin.util.IdCardUtil;
import com.iflytek.cdc.admin.util.Level5AdministrativeCode;
import com.iflytek.cdc.admin.util.PhoneUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.simmetrics.StringMetric;
import org.simmetrics.metrics.StringMetrics;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName AmapService
 * @Description 高德地图 RestfulApi接口封装与解析
 * <AUTHOR>
 * @Date 2021/6/11 9:49
 * @Version 1.0
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class AmapServiceImpl implements AmapService {

    /**
     * OBJECT_MAPPER
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 手机号正则表达式
     */
    public static final Pattern MOBILE_PATTERN = Pattern.compile("^1[0-9]{10}$");

    /**
     * 高德开发者账号KEY
     */
    @Value("${amap.key}")
    private String amapKey;

    /**
     * 高德外网访问URL
     */
    @Value("${amap.url}")
    private String amapUrl;

    @Value("${cdc.std.invalidWord.cache.expireDays:30}")
    private int invalidWordExpireDays;

    @Value("${cdc.std.invalidWord.std.retryTimes:0}")
    private int invalidWordRetryTimes;

    @Value("${cdc.std.poiTypes:医疗保健服务;住宿服务;商务住宅;政府机构及社会团体;科教文化服务;公司企业;地名地址信息}")
    private String availablePoiType;
    /**
     * 注入的 Bean 无法调用 未注册 的高德服务
     */
    private RestTemplate restTemplate = new RestTemplate();

    /**
     * 模糊地址与标准地址映射表 服务类
     */
    @Autowired
    private AddressDetailMappingService addressDetailMappingService;

    /**
     * 标准地理信息表 服务类
     */
    @Autowired
    private AddressStandardService addressStandardService;

    @Autowired
    private BatchUidService batchUidService;

    @Resource
    private RegionMapper regionMapper;

    @Resource
    private StdTextPreProcessService stdTextPreProcessService;

    @Resource
    public RedisTemplate<String, Object> redisTemplate;

    private final UapUserApi uapUserApi;

    private Map<String, Set<String>> shortRegions = new HashMap<>();
    private Map<String, Set<String>> fullRegions = new HashMap<>();
    private Map<String, String> regionMapping = new HashMap<>();

    public AmapServiceImpl(UapUserApi uapUserApi) {
        this.uapUserApi = uapUserApi;
    }

    private TUapUser getUapUser(String loginUserId) {
        //获取登录人员信息
        TUapUser userCache = null;
        if("".equals(loginUserId.trim())) {
            return new TUapUser("");
        }
        try {
            userCache = uapUserApi.getUserDetail(loginUserId).getUapUser();
        } catch (Exception e) {
            log.error("获取登录用户信息异常：{}", loginUserId);
        }
        if (userCache == null) {
//            throw new MedicalBusinessException("获取登录用户信息异常，请重新登录");
            return new TUapUser("");
        }
        return userCache;
    }


    /**
     * 地址智能解析
     *
     * @param addressFuzzyNormalizationDTO
     * @param loginUserId
     * @return
     */
    @Override
    public AddressStandardVo fuzzyAddressNormalization(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId) {
        // 确定在哪个城市搜索
        String searchInWhichCity = "";

        // 模糊地址
        String addressDetail = addressFuzzyNormalizationDTO.getAddressDetail();

        // 医疗机构所在省 省名
        String provinceName = addressFuzzyNormalizationDTO.getProvinceName();
        // 医疗机构所在市 市名
        String cityName = addressFuzzyNormalizationDTO.getCityName();
        // 医疗机构所在区/县 区/县编码
        String districtCode = addressFuzzyNormalizationDTO.getDistrictCode();
        // 患者身份证号
        String idCard = addressFuzzyNormalizationDTO.getIdCard();
        // 患者手机号
        String telephone = addressFuzzyNormalizationDTO.getTelephone();


        // 1.2 解析出 身份证 手机号 就诊医疗机构 的 归属地
        String telephoneRegion = PhoneUtil.parseOutProvinceCityByTelephone(telephone);
        String idCardRegion = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardProvinceCode(idCard)).getAdministrative() +
                Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
        String orgRegion = provinceName + cityName;


        //  三个都没有 我们选择中国做为范围来搜
        if (!ObjectUtils.isEmpty(cityName)
                && !ObjectUtils.isEmpty(idCard)
                && !ObjectUtils.isEmpty(telephone)) {
            // 三个都来 谁最多最优先
            if (idCardRegion.equals(telephoneRegion) && (!"".equals(idCardRegion))) {
                searchInWhichCity = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
                idCardRegion = searchInWhichCity;
                telephoneRegion = searchInWhichCity;
                orgRegion = "";
            } else {
                searchInWhichCity = cityName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = searchInWhichCity;
            }
        } else if (!ObjectUtils.isEmpty(provinceName)
                || !ObjectUtils.isEmpty(cityName)
                || !ObjectUtils.isEmpty(districtCode)) {
            if (!ObjectUtils.isEmpty(districtCode)) {
                searchInWhichCity = districtCode;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = districtCode;

            } else if (!ObjectUtils.isEmpty(cityName)) {
                searchInWhichCity = cityName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = cityName;
            } else if (!ObjectUtils.isEmpty(provinceName)) {
                searchInWhichCity = provinceName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = provinceName;
            }
        } else if (!ObjectUtils.isEmpty(idCard)) {
            searchInWhichCity = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
            idCardRegion = searchInWhichCity;
            telephoneRegion = "";
            orgRegion = "";
        } else if (!ObjectUtils.isEmpty(telephone) && MOBILE_PATTERN.matcher(telephone).matches()) {

            searchInWhichCity = PhoneUtil.getAddressInfo(PhoneUtil.parseOutProvinceCityByTelephone(telephone)).get(0).get("city");
            idCardRegion = "";
            telephoneRegion = searchInWhichCity;
            orgRegion = "";
        } else {
            /* 调用高德接口 开始 */
            searchInWhichCity = "中国";
            idCardRegion = "";
            telephoneRegion = "";
            orgRegion = "";
        }


        if ("".equals(addressDetail)) {
            return AddressStandardVo.builder()
                    .addressAreaCode("unknown")
                    .addressAreaName("unknown")
                    .addressProvinceCode("unknown")
                    .addressProvinceName("unknown")
                    .addressCityCode("unknown")
                    .addressCityName("unknown")
                    .addressDistrictCode("unknown")
                    .addressDistrictName("unknown")
                    .addressTownCode("unknown")
                    .addressTownName("unknown")
                    .addressLatitude("unknown")
                    .addressLongitude("unknown")
                    .build();
        }

        /* 检索 模糊地址地址缓存检索表 是否有对应的地址 */
        List<AddressDetailMapping> addressDetailMappingList = addressDetailMappingService.list(
                new LambdaQueryWrapper<AddressDetailMapping>()
                        // 模糊地址
                        .eq(AddressDetailMapping::getAddressDetail, addressDetail)
                        // 患者手机号 解析到的标准行政区 市名
                        .eq(AddressDetailMapping::getTelephoneCityName, telephoneRegion)
                        // 患者身份证 解析到的标准行政区 市名
                        .eq(AddressDetailMapping::getIdCardCityName, idCardRegion)
                        // 医疗机构 所在的标准行政区 市名
                        .eq(AddressDetailMapping::getOrgCityName, orgRegion)
        );

        if (CollUtil.isNotEmpty(addressDetailMappingList)) {
            // 有对应的 模糊地址 与 标准地址 映射关系
            AddressDetailMapping addressDetailMapping = addressDetailMappingList.get(0);
            List<AddressStandard> addressStandardList = addressStandardService.list(
                    new LambdaQueryWrapper<AddressStandard>()
                            .eq(AddressStandard::getId, addressDetailMapping.getAddressStandardId())
            );
            if (CollUtil.isNotEmpty(addressStandardList)) {
                // 根据映射关系表 中的映射关系 在标准地址表中 查到了标准地址
                AddressStandard addressStandard = addressStandardList.get(0);
                AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
                // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
                BeanUtils.copyProperties(addressStandard, addressStandardVo);
                return addressStandardVo;
            } else {
                // 没有查到标准地址
                // 删除映射 返回再跑一轮的数据
                addressDetailMappingService.remove(
                        new LambdaQueryWrapper<AddressDetailMapping>()
                                // 模糊地址
                                .eq(AddressDetailMapping::getAddressDetail, addressDetail)
                                // 患者手机号 解析到的标准行政区 市名
                                .eq(AddressDetailMapping::getTelephoneCityName, telephoneRegion)
                                // 患者身份证 解析到的标准行政区 市名
                                .eq(AddressDetailMapping::getIdCardCityName, idCardRegion)
                                // 医疗机构 所在的标准行政区 市名
                                .eq(AddressDetailMapping::getOrgCityName, orgRegion)
                );
                return fuzzyAddressNormalization(addressFuzzyNormalizationDTO, loginUserId);
            }
        } else {

            String search = search(addressDetail, searchInWhichCity);
            AmapAddressDetailMapping amapAddressDetailMapping = geocode(search);

            /* 调用高德接口 结束 */
            amapAddressDetailMapping.setAddressDetail(addressDetail);
            amapAddressDetailMapping.setTelephoneCityName(telephoneRegion);
            amapAddressDetailMapping.setTelephoneCityCode(Level5AdministrativeCode.getLevel5AdministrativeCodeByAdministrative(telephoneRegion).getCode());
            amapAddressDetailMapping.setIdCardCityName(idCardRegion);
            amapAddressDetailMapping.setIdCardCityCode(Level5AdministrativeCode.getLevel5AdministrativeCodeByAdministrative(idCardRegion).getCode());
            amapAddressDetailMapping.setOrgCityName(orgRegion);
            amapAddressDetailMapping.setOrgCityCode(Level5AdministrativeCode.getLevel5AdministrativeCodeByAdministrative(orgRegion).getCode());

            // 更新映射表 和 标准地址表
            AddressDetailMapping addressDetailMapping = AddressDetailMapping.builder().build();
            AddressStandard addressStandard = AddressStandard.builder().build();
            BeanUtils.copyProperties(amapAddressDetailMapping, addressDetailMapping);
            BeanUtils.copyProperties(amapAddressDetailMapping, addressStandard);

            // 获取UAP用户开始
            //获取登录人员信息
            TUapUser userCache = getUapUser(loginUserId);
            // 获取UAP用户结束

            String addressStandardId = "AMAP_" + addressStandard.getAddressAreaCode();

            addressDetailMapping.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
            addressDetailMapping.setAddressStandardId(addressStandardId);
            addressDetailMapping.setCreator(userCache.getId());
            addressDetailMapping.setUpdator(userCache.getId());


            addressStandard.setId(addressStandardId);
            addressStandard.setCoordinatesType("AMAP_GCJ-02");
            addressStandard.setAddressDetailDesc(amapAddressDetailMapping.getAddressAreaName());
            addressStandard.setAddressName(
                    amapAddressDetailMapping.getAddressAreaName()
                            .replaceFirst(addressStandard.getAddressProvinceName(), "")
                            .replaceFirst(addressStandard.getAddressCityName(), "")
                            .replaceFirst(addressStandard.getAddressDistrictName(), "")
                            .replaceFirst(addressStandard.getAddressTownName(), "")
            );
            addressStandard.setCreator(userCache.getId());
            addressStandard.setUpdator(userCache.getId());

            addressDetailMappingService.mappingSimilarity(addressStandard, addressDetailMapping);

            addressStandardService.saveOrUpdateAddressStandard(addressStandard);
            addressDetailMappingService.saveOrUpdateAddressDetailMapping(addressDetailMapping);


            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
            BeanUtils.copyProperties(addressStandard, addressStandardVo);
            return addressStandardVo;
        }
    }

    @Override
    public AddressStandardVo fuzzyAddressNormalizationSimple(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId) {
        // 模糊地址
        String addressDetail = stdTextPreProcessService.textPreProcess(Constants.STD_CATEGORY_ADDRESS,
                addressFuzzyNormalizationDTO.getTypes(),
                addressFuzzyNormalizationDTO.getAddressDetail());
        if (StringUtils.isBlank(addressDetail)) {
            return AddressStandardVo.emptyValue();
        }
        // 确定在哪个城市搜索
        String searchInWhichCity = this.getCity(addressDetail, addressFuzzyNormalizationDTO.getDistrictCode());

        /* 检索 模糊地址地址缓存检索表 是否有对应的地址 */
        List<AddressDetailMapping> addressDetailMappingList = addressDetailMappingService.list(
                new LambdaQueryWrapper<AddressDetailMapping>()
                        // 模糊地址
                        .eq(AddressDetailMapping::getAddressDetail, addressDetail)
        );

        Boolean useCache = !Optional.ofNullable(addressFuzzyNormalizationDTO.getRefresh()).orElse(Boolean.FALSE);
        if (Boolean.TRUE.equals(useCache) && CollUtil.isNotEmpty(addressDetailMappingList)) {
            // 有对应的 模糊地址 与 标准地址 映射关系
            AddressDetailMapping addressDetailMapping = addressDetailMappingList.get(0);
            List<AddressStandard> addressStandardList = addressStandardService.list(
                    new LambdaQueryWrapper<AddressStandard>()
                            .eq(AddressStandard::getId, addressDetailMapping.getAddressStandardId())
            );
            if (CollUtil.isNotEmpty(addressStandardList)) {
                // 根据映射关系表 中的映射关系 在标准地址表中 查到了标准地址
                AddressStandard addressStandard = addressStandardList.get(0);
                AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
                // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
                BeanUtils.copyProperties(addressStandard, addressStandardVo);
                return addressStandardVo;
            } else {
                // 没有查到标准地址
                // 删除映射 返回再跑一轮的数据
                addressDetailMappingService.remove(
                        new LambdaQueryWrapper<AddressDetailMapping>()
                                // 模糊地址
                                .eq(AddressDetailMapping::getAddressDetail, addressDetail)
                );
                return fuzzyAddressNormalizationSimple(addressFuzzyNormalizationDTO, loginUserId);
            }
        } else {
            PoisV2 poisV2 = searchV2(addressDetail, searchInWhichCity, addressFuzzyNormalizationDTO.getTypes());
            if (null == poisV2 || StringUtils.isBlank(poisV2.getId())) {
                return AddressStandardVo.emptyValue();
            }
            AmapAddressDetailMapping amapAddressDetailMapping = geocode(poisV2.getLocation());
            /* 调用高德接口 结束 */

            // 获取UAP用户开始
            //获取登录人员信息
            TUapUser userCache = getUapUser(loginUserId);
            // 获取UAP用户结束
            AddressDetailMapping addressDetailMapping = AddressDetailMapping.builder().build();
            String addressStandardId = "AMAP_" + poisV2.getId();
            addressDetailMapping.setAddressDetail(addressDetail);
            addressDetailMapping.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
            addressDetailMapping.setAddressStandardId(addressStandardId);
            addressDetailMapping.setOrgCityName("");
            addressDetailMapping.setOrgCityCode("");
            addressDetailMapping.setIdCardCityCode("");
            addressDetailMapping.setIdCardCityName("");
            addressDetailMapping.setTelephoneCityCode("");
            addressDetailMapping.setTelephoneCityName("");
            addressDetailMapping.setPoiType(poisV2.getType());
            addressDetailMapping.setCreator(userCache.getId());
            addressDetailMapping.setUpdator(userCache.getId());

            AddressStandard addressStandard = AddressStandard.builder().build();
            addressStandard.setId(addressStandardId);
            addressStandard.setAddressAreaName(
                    StringUtils.isNotBlank(poisV2.getAdname()) ?
                            poisV2.getPname() + poisV2.getCityname() + poisV2.getAdname() + poisV2.getName()
                            : poisV2.getName()
            );
            addressStandard.setAddressAreaCode(poisV2.getId());
            addressStandard.setAddressLongitude(poisV2.getLocation().split(",")[0]);
            addressStandard.setAddressLatitude(poisV2.getLocation().split(",")[1]);
            addressStandard.setAddressProvinceName(poisV2.getPname());
            addressStandard.setAddressProvinceCode(poisV2.getPcode());
            addressStandard.setAddressCityName(poisV2.getCityname());
            addressStandard.setAddressCityCode(StringUtils.isEmpty(poisV2.getCitycode()) ? "" : poisV2.getAdcode().substring(0, 4) + "00");
            addressStandard.setAddressDistrictName(poisV2.getAdname());
            addressStandard.setAddressDistrictCode(poisV2.getAdcode());
            addressStandard.setAddressTownName(amapAddressDetailMapping.getAddressTownName());
            addressStandard.setAddressTownCode(amapAddressDetailMapping.getAddressTownCode());
            addressStandard.setPoiType(poisV2.getType());
            addressStandard.setCoordinatesType("AMAP_GCJ-02");
            addressStandard.setAddressName(poisV2.getName());
            addressStandard.setAddressDetailDesc(amapAddressDetailMapping.getAddressAreaName());
            addressStandard.setCreator(userCache.getId());
            addressStandard.setUpdator(userCache.getId());

            addressDetailMappingService.mappingSimilarity(addressStandard, addressDetailMapping);

            addressStandardService.saveOrUpdateAddressStandard(addressStandard);
            addressDetailMappingService.saveOrUpdateAddressDetailMapping(addressDetailMapping);

            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
            BeanUtils.copyProperties(addressStandard, addressStandardVo);
            return addressStandardVo;
        }
    }

    @Override
    public AddressStandardVo fuzzyCompanyNormalization(AddressFuzzyNormalizationDTO addressFuzzyNormalizationDTO, String loginUserId) {
        // 模糊地址
        String addressDetail = stdTextPreProcessService.textPreProcess(Constants.STD_CATEGORY_COMPANY,
                addressFuzzyNormalizationDTO.getTypes(),
                addressFuzzyNormalizationDTO.getAddressDetail());
        if (StringUtils.isBlank(addressDetail)) {
            return AddressStandardVo.emptyValue();
        }
        // 确定在哪个城市搜索
        String searchInWhichCity = this.getCity(addressDetail, addressFuzzyNormalizationDTO.getDistrictCode());

        /* 检索 模糊地址地址缓存检索表 是否有对应的地址 */
        List<AddressDetailMapping> addressDetailMappingList = addressDetailMappingService.list(
                new LambdaQueryWrapper<AddressDetailMapping>()
                        // 模糊地址
                        .eq(AddressDetailMapping::getAddressDetail, addressDetail)
        );

        Boolean useCache = !Optional.ofNullable(addressFuzzyNormalizationDTO.getRefresh()).orElse(Boolean.FALSE);
        if (Boolean.TRUE.equals(useCache) && CollUtil.isNotEmpty(addressDetailMappingList)) {
            // 有对应的 模糊地址 与 标准地址 映射关系
            AddressDetailMapping addressDetailMapping = addressDetailMappingList.get(0);
            List<AddressStandard> addressStandardList = addressStandardService.list(
                    new LambdaQueryWrapper<AddressStandard>()
                            .eq(AddressStandard::getId, addressDetailMapping.getAddressStandardId())
            );
            if (CollUtil.isNotEmpty(addressStandardList)) {
                // 根据映射关系表 中的映射关系 在标准地址表中 查到了标准地址
                AddressStandard addressStandard = addressStandardList.get(0);
                AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
                // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
                BeanUtils.copyProperties(addressStandard, addressStandardVo);
                return addressStandardVo;
            } else {
                // 没有查到标准地址
                // 删除映射 返回再跑一轮的数据
                addressDetailMappingService.remove(
                        new LambdaQueryWrapper<AddressDetailMapping>()
                                // 模糊地址
                                .eq(AddressDetailMapping::getAddressDetail, addressDetail)
                );
                return fuzzyCompanyNormalization(addressFuzzyNormalizationDTO, loginUserId);
            }
        } else {
            PoisV2 poisV2 = searchV2(addressDetail, searchInWhichCity, addressFuzzyNormalizationDTO.getTypes());
            if (null == poisV2 || StringUtils.isBlank(poisV2.getId())) {
                return AddressStandardVo.emptyValue();
            }
            AmapAddressDetailMapping amapAddressDetailMapping = geocode(poisV2.getLocation());
            /* 调用高德接口 结束 */
            //获取登录人员信息
            TUapUser userCache = getUapUser(loginUserId);
            // 更新映射表 和 标准地址表
            AddressDetailMapping addressDetailMapping = AddressDetailMapping.builder().build();
            String addressStandardId = "AMAP_" + poisV2.getId();
            addressDetailMapping.setAddressDetail(addressDetail);
            addressDetailMapping.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
            addressDetailMapping.setAddressStandardId(addressStandardId);
            addressDetailMapping.setOrgCityName("");
            addressDetailMapping.setOrgCityCode("");
            addressDetailMapping.setIdCardCityCode("");
            addressDetailMapping.setIdCardCityName("");
            addressDetailMapping.setTelephoneCityCode("");
            addressDetailMapping.setTelephoneCityName("");
            addressDetailMapping.setPoiType(poisV2.getType());
            addressDetailMapping.setCreator(userCache.getId());
            addressDetailMapping.setUpdator(userCache.getId());

            AddressStandard addressStandard = AddressStandard.builder().build();
            addressStandard.setId(addressStandardId);
            addressStandard.setAddressAreaName(poisV2.getName());
            addressStandard.setAddressAreaCode(poisV2.getId());
            addressStandard.setAddressLongitude(poisV2.getLocation().split(",")[0]);
            addressStandard.setAddressLatitude(poisV2.getLocation().split(",")[1]);
            addressStandard.setAddressProvinceName(poisV2.getPname());
            addressStandard.setAddressProvinceCode(poisV2.getPcode());
            addressStandard.setAddressCityName(poisV2.getCityname());
            addressStandard.setAddressCityCode(StringUtils.isEmpty(poisV2.getCitycode()) ? "" : poisV2.getAdcode().substring(0, 4) + "00");
            addressStandard.setAddressDistrictName(poisV2.getAdname());
            addressStandard.setAddressDistrictCode(poisV2.getAdcode());
            addressStandard.setAddressTownName(amapAddressDetailMapping.getAddressTownName());
            addressStandard.setAddressTownCode(amapAddressDetailMapping.getAddressTownCode());
            addressStandard.setPoiType(poisV2.getType());
            addressStandard.setCoordinatesType("AMAP_GCJ-02");
            addressStandard.setAddressName(poisV2.getName());
            addressStandard.setAddressDetailDesc(amapAddressDetailMapping.getAddressAreaName());
            addressStandard.setCreator(userCache.getId());
            addressStandard.setUpdator(userCache.getId());

            addressDetailMappingService.mappingSimilarity(addressStandard, addressDetailMapping);

            addressStandardService.saveOrUpdateAddressStandard(addressStandard);
            addressDetailMappingService.saveOrUpdateAddressDetailMapping(addressDetailMapping);

            AddressStandardVo addressStandardVo = AddressStandardVo.builder().build();
            // addressStandard 比 addressStandardVo 多一个 POI_TYPE(POI类型) 字段
            BeanUtils.copyProperties(addressStandard, addressStandardVo);
            return addressStandardVo;
        }
    }

    /**
     * 高德地图 搜索服务-关键字查询  搜索POI
     *
     * @param keywords 查询关键字
     * @param city     查询城市
     * @return
     */
    @Override
    public String search(String keywords, String city) {


        String url = amapUrl + "/v3/place/text?keywords=" + keywords.replace("#", "-") + "&city=" + city + "&citylimit=true&output=json&offset=20&page=1&key=" + amapKey + "&extensions=all";

        ResponseEntity<AmapSearchJsonRootBean> amapSearchJsonRootBean = null;
        try {
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI 接口,请求URL: {}", url);
            amapSearchJsonRootBean = restTemplate.postForEntity(url, "", AmapSearchJsonRootBean.class);
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapSearchJsonRootBean.getBody()));
        } catch (Exception e) {
            log.error("远程调用 高德地图 搜索服务-关键字查询 搜索POI 出现异常: {}", e.getMessage());
            return "";
        }
        List<Pois> poisList = amapSearchJsonRootBean.getBody().getPois();
        if (CollUtil.isNotEmpty(poisList)) {
            return poisList.get(0).getLocation();
        }
        return "";
    }

    @Override
    public PoisV2 searchV2(String keywords, String region, String poiType) {
        String url = amapUrl + "/v5/place/text?keywords=" + keywords + "&output=json&page_size=20&key=" + amapKey;
        if (StringUtils.isNotEmpty(region)) {
            url += "&region=" + region + "&city_limit=true";
        }
        if (StringUtils.isNotEmpty(poiType)) {
            url += "&types=" + poiType;
        } else {
            url += "&types=" + availablePoiType;
        }

        String redisKey = Constants.REDIS_KEY + Constants.TYPE_STD_WORD + keywords;
        try {
            Object redisData = redisTemplate.opsForValue().get(redisKey);
            if (!Objects.isNull(redisData) && Integer.parseInt(String.valueOf(redisData)) > invalidWordRetryTimes) {
                redisTemplate.opsForValue().set(redisKey, Integer.parseInt(String.valueOf(redisData)) + 1);
                return null;
            }
        } catch (Exception e) {
            log.error("Redis操作异常：{0}", e);
        }

        ResponseEntity<AmapSearchJsonRootBeanV2> amapSearchJsonRootBean = null;
        try {
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 接口,请求URL: {}", url);
            amapSearchJsonRootBean = restTemplate.postForEntity(url, "", AmapSearchJsonRootBeanV2.class);
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapSearchJsonRootBean.getBody()));
        } catch (Exception e) {
            log.error("远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 出现异常: {}", e.getMessage());
            return null;
        }

        List<PoisV2> poisList = Objects.requireNonNull(amapSearchJsonRootBean.getBody()).getPois();
        PoisV2 bestOne = getBestOne(keywords, poisList);
        try {
            if (null == bestOne) {
                // 若没有获取到值时，存储到redis，后续一段时间范围内不再调用高德。
                Object o = Optional.ofNullable(redisTemplate.opsForValue().get(redisKey)).orElse("0");
                redisTemplate.opsForValue().set(redisKey, Integer.parseInt(String.valueOf(o)) + 1);
                redisTemplate.expire(redisKey, invalidWordExpireDays, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("Redis操作异常：{0}", e);
        }
        return bestOne;
    }

    private PoisV2 getBestOne(String keywords, List<PoisV2> poisList){
        List<String> availablePoiTypes = new ArrayList<>(Arrays.asList(availablePoiType.split(";")));
        if (CollUtil.isNotEmpty(poisList)) {
            //1,过滤不符合条件的poiType
            List<PoisV2> availableList = poisList.stream().filter(e -> availablePoiTypes.contains(org.apache.commons.lang3.StringUtils.substringBefore(e.getType(), ";"))).collect(Collectors.toList());
//            //2,计算相似度
//            StringMetric smithWatermanGotoh = StringMetrics.smithWatermanGotoh();
//            AtomicInteger index = new AtomicInteger(0);
//            availableList.forEach(e -> {
//                e.setSimilarRate(StrUtil.similar(keywords, e.getName()));
//                e.setSimilarRateV2(smithWatermanGotoh.compare(keywords, e.getName()));
//                e.setOriOrder(index.addAndGet(1));
//            });
//            //3,过滤低相似度
//            List<PoisV2> finalList = availableList.stream().filter(e -> e.getSimilarRate() >= 0.5D)
//                                                           .sorted(Comparator.comparing(PoisV2::getSimilarRate, Comparator.reverseOrder()).thenComparing(PoisV2::getOriOrder))
//                                                           .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(availableList)) {
                return availableList.get(0);
            }
        }
        return null;
    }

    @Override
    public PoisV2 searchV2ById(String id) {
        String url = amapUrl + "/v5/place/detail?id=" + id +
                "&key=" + amapKey;
        ResponseEntity<AmapSearchJsonRootBeanV2> amapSearchJsonRootBean = null;
        try {
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 ById 接口,请求URL: {}", url);
            amapSearchJsonRootBean = restTemplate.postForEntity(url, "", AmapSearchJsonRootBeanV2.class);
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 ById 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapSearchJsonRootBean.getBody()));
        } catch (Exception e) {
            log.error("远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 ById  出现异常: {}", e.getMessage());
            return null;
        }
        List<PoisV2> poisList = amapSearchJsonRootBean.getBody().getPois();
        if (CollUtil.isNotEmpty(poisList)) {
            return poisList.get(0);
        }
        return null;
    }

    /**
     * 高德地图 逆地理编码
     *
     * @param latitudeAndLongitude 经纬度字符串(经度,纬度)
     * @return
     */
    @Override
    public AmapAddressDetailMapping geocode(String latitudeAndLongitude) {


        // 设置默认返回值
        AmapAddressDetailMapping amapAddressDetailMapping = AmapAddressDetailMapping.builder()
                /* 标准化地址 */
                .addressAreaCode("unknown")
                /* 标准化地址 编码 */
                .addressAreaName("unknown")
                /* 经度 */
                .addressLongitude("unknown")
                /* 纬度 */
                .addressLatitude("unknown")
                /* 省份名 */
                .addressProvinceName("unknown")
                /* 省份编码 */
                .addressProvinceCode("unknown")
                /* 城市名 */
                .addressCityName("unknown")
                /* 城市编码 */
                .addressCityCode("unknown")
                /* 区县/乡 名 */
                .addressDistrictName("unknown")
                /* 区县/乡 编码 */
                .addressDistrictCode("unknown")
                /* 街道/村 名 */
                .addressTownName("unknown")
                /* 街道/村 编码 */
                .addressTownCode("unknown")
                /* POI 类型 */
                .poiType("unknown")
                .build();
        if ("".equals(latitudeAndLongitude)) {
            return amapAddressDetailMapping;
        }

        // 请求高德接口对经纬度进行逆地理编码
        String url = amapUrl + "/v3/geocode/regeo?key=" + amapKey + "&location=" + latitudeAndLongitude + "&poitype=地名&radius=1000&extensions=all&batch=false&roadlevel=0";

        ResponseEntity<AmapGeocodeJsonRootBean> amapGeocodeJsonRootBeanResponseEntity = null;
        try {
            log.info("地址智能解析: 远程调用高德 逆地理编码 接口,请求URL: {}", url);
            amapGeocodeJsonRootBeanResponseEntity = restTemplate.postForEntity(url, "", AmapGeocodeJsonRootBean.class);
            log.info("地址智能解析: 远程调用高德 逆地理编码 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapGeocodeJsonRootBeanResponseEntity.getBody()));


            Regeocode regeocode = Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody()).getRegeocode();

            if (regeocode != null && CollUtil.isNotEmpty(regeocode.getPois())) {
                // 根据高德接口构建返回值
                amapAddressDetailMapping =
                        AmapAddressDetailMapping.builder()
                                /* 标准化地址 */
                                .addressAreaName(regeocode.getFormattedAddress())
                                /* 标准化地址 编码 */
                                .addressAreaCode(regeocode.getPois().get(0).getId())
                                /* 经度 */
                                .addressLongitude(regeocode.getPois().get(0).getLocation().split(",")[0])
                                /* 纬度 */
                                .addressLatitude(regeocode.getPois().get(0).getLocation().split(",")[1])
                                /* 省份名 */
                                .addressProvinceName(regeocode.getAddressComponent().getProvince())
                                /* 省份编码 */
                                .addressProvinceCode(regeocode.getAddressComponent().getAdcode().substring(0, 2) + "0000")
                                /* 城市名 */
                                .addressCityName(regeocode.getAddressComponent().getCity())
                                /* 城市编码 */
                                .addressCityCode(StringUtils.isEmpty(regeocode.getAddressComponent().getCity()) ? "" : regeocode.getAddressComponent().getAdcode().substring(0, 4) + "00")
                                /* 区县/乡 名 */
                                .addressDistrictName(regeocode.getAddressComponent().getDistrict())
                                /* 区县/乡 编码 */
                                .addressDistrictCode(regeocode.getAddressComponent().getAdcode())
                                /* 街道/村 名 */
                                .addressTownName(regeocode.getAddressComponent().getTownship())
                                /* 街道/村 编码 */
                                .addressTownCode(regeocode.getAddressComponent().getTowncode())
                                .poiType(regeocode.getPois().get(0).getType())
                                .build();
            }
        } catch (Exception e) {
            log.error("远程调用高德接口 逆地理编码 出现错误,经纬度: {}, 异常: {}", latitudeAndLongitude, e.getMessage());
        }

        return amapAddressDetailMapping;
    }

    /**
     * 地址智能解析 分页
     *
     * @param addressFuzzyNormalizationLatestDTO
     * @return
     */
    @Override
    public PageData<AddressStandardLatestVo> fuzzyAddressNormalizationLatest(AddressFuzzyNormalizationLatestDTO addressFuzzyNormalizationLatestDTO) {

        // 确定在哪个城市搜索
        String searchInWhichCity = "";

        // 模糊地址
        String addressDetail = addressFuzzyNormalizationLatestDTO.getAddressDetail();

        // 医疗机构所在省 省名
        String provinceName = addressFuzzyNormalizationLatestDTO.getProvinceName();
        // 医疗机构所在市 市名
        String cityName = addressFuzzyNormalizationLatestDTO.getCityName();
        // 医疗机构所在区/县 区/县编码
        String districtCode = addressFuzzyNormalizationLatestDTO.getDistrictCode();
        // 患者身份证号
        String idCard = addressFuzzyNormalizationLatestDTO.getIdCard();
        // 患者手机号
        String telephone = addressFuzzyNormalizationLatestDTO.getTelephone();


        // 1.2 解析出 身份证 手机号 就诊医疗机构 的 归属地
        String telephoneRegion = PhoneUtil.parseOutProvinceCityByTelephone(telephone);
        String idCardRegion = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardProvinceCode(idCard)).getAdministrative() +
                Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
        String orgRegion = provinceName + cityName;


        //  三个都没有 我们选择中国做为范围来搜
        if (!ObjectUtils.isEmpty(cityName)
                && !ObjectUtils.isEmpty(idCard)
                && !ObjectUtils.isEmpty(telephone)) {
            // 三个都来 谁最多最优先
            if (idCardRegion.equals(telephoneRegion) && (!"".equals(idCardRegion))) {
                searchInWhichCity = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
                idCardRegion = searchInWhichCity;
                telephoneRegion = searchInWhichCity;
                orgRegion = "";
            } else {
                searchInWhichCity = cityName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = searchInWhichCity;
            }
        } else if (!ObjectUtils.isEmpty(provinceName)
                || !ObjectUtils.isEmpty(cityName)
                || !ObjectUtils.isEmpty(districtCode)) {
            if (!ObjectUtils.isEmpty(districtCode)) {
                searchInWhichCity = districtCode;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = districtCode;

            } else if (!ObjectUtils.isEmpty(cityName)) {
                searchInWhichCity = cityName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = cityName;
            } else if (!ObjectUtils.isEmpty(provinceName)) {
                searchInWhichCity = provinceName;
                idCardRegion = "";
                telephoneRegion = "";
                orgRegion = provinceName;
            }
        } else if (!ObjectUtils.isEmpty(idCard)) {
            searchInWhichCity = Level5AdministrativeCode.getLevel5AdministrativeCodeByCode(IdCardUtil.getIdCardCityCode(idCard)).getAdministrative();
            idCardRegion = searchInWhichCity;
            telephoneRegion = "";
            orgRegion = "";
        } else if (!ObjectUtils.isEmpty(telephone) && MOBILE_PATTERN.matcher(telephone).matches()) {

            searchInWhichCity = PhoneUtil.getAddressInfo(PhoneUtil.parseOutProvinceCityByTelephone(telephone)).get(0).get("city");
            idCardRegion = "";
            telephoneRegion = searchInWhichCity;
            orgRegion = "";
        } else {
            /* 调用高德接口 开始 */
            searchInWhichCity = "中国";
            idCardRegion = "";
            telephoneRegion = "";
            orgRegion = "";
        }


        return searchLatestList(addressDetail, searchInWhichCity, addressFuzzyNormalizationLatestDTO.getPageIndex(), addressFuzzyNormalizationLatestDTO.getPageSize(), addressFuzzyNormalizationLatestDTO.getTypes());

    }

    /**
     * 根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码
     *
     * @param longitudeAndLatitudeDto
     * @return
     */
    @Override
    public AddressStandardLatestVo getAreaInfoByLongitudeAndLatitude(LongitudeAndLatitudeDto longitudeAndLatitudeDto) {
        AddressStandardLatestVo addressStandardLatestVo = new AddressStandardLatestVo();
        addressStandardLatestVo.setAddressName("");
        addressStandardLatestVo.setAddressDetailDesc("");
        addressStandardLatestVo.setAddressAreaCode("");
        addressStandardLatestVo.setAddressAreaName("");
        addressStandardLatestVo.setAddressProvinceCode("");
        addressStandardLatestVo.setAddressProvinceName("");
        addressStandardLatestVo.setAddressCityCode("");
        addressStandardLatestVo.setAddressCityName("");
        addressStandardLatestVo.setAddressDistrictCode("");
        addressStandardLatestVo.setAddressDistrictName("");
        addressStandardLatestVo.setAddressTownCode("");
        addressStandardLatestVo.setAddressTownName("");
        addressStandardLatestVo.setAddressLongitude("");
        addressStandardLatestVo.setAddressLatitude("");

        try {
            String latitudeAndLongitude = longitudeAndLatitudeDto.getLongitude() + "," + longitudeAndLatitudeDto.getLatitude();
            String geocodeUrl = amapUrl + "/v3/geocode/regeo?key=" + amapKey + "&location=" + latitudeAndLongitude + "&radius=10&extensions=all&batch=false&roadlevel=0";

            log.info("根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码: 远程调用高德 逆地理编码 接口,请求URL: {}", geocodeUrl);
            ResponseEntity<AmapGeocodeJsonRootBean> amapGeocodeJsonRootBeanResponseEntity = restTemplate.postForEntity(geocodeUrl, "", AmapGeocodeJsonRootBean.class);
            log.info("根据经纬度获取最近POI点的 省 市 区县 村街道 名称及编码: 远程调用高德 逆地理编码 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapGeocodeJsonRootBeanResponseEntity.getBody()));

            addressStandardLatestVo.setAddressProvinceCode(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody()).getRegeocode().getAddressComponent().getAdcode().substring(0, 2) + "0000");
            addressStandardLatestVo.setAddressProvinceName(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getProvince()));
            addressStandardLatestVo.setAddressCityCode(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody()).getRegeocode().getAddressComponent().getAdcode().substring(0, 4) + "00");
            addressStandardLatestVo.setAddressCityName("".equals(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getCity())) ? addressStandardLatestVo.getAddressProvinceName() : Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getCity()));
            addressStandardLatestVo.setAddressDistrictCode(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getAdcode()));
            addressStandardLatestVo.setAddressDistrictName(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getDistrict()));
            addressStandardLatestVo.setAddressTownCode(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getTowncode()));
            addressStandardLatestVo.setAddressTownName(Objects.requireNonNull(amapGeocodeJsonRootBeanResponseEntity.getBody().getRegeocode().getAddressComponent().getTownship()));
        } catch (Exception e) {
            log.info("远程调用高德接口异常: {}", e.getMessage());
        }

        return addressStandardLatestVo;
    }

    @Override
    public List<PoisV2> inputTips(String keyWords, String areaCode) {
        String url = amapUrl + "/v5/place/text?keywords=" + keyWords.replace("#", "-") +
                "&output=json&offset=20&page=1&key=" + amapKey + "&extensions=all";
        if (StringUtils.isNotEmpty(areaCode)) {
            url += "&region=" + areaCode + "&city_limit=true";
        }
        ResponseEntity<AmapSearchJsonRootBeanV2> amapSearchJsonRootBean = null;
        try {
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 接口,请求URL: {}", url);
            amapSearchJsonRootBean = restTemplate.postForEntity(url, "", AmapSearchJsonRootBeanV2.class);
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapSearchJsonRootBean.getBody()));
        } catch (Exception e) {
            log.error("远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 出现异常: {}", e.getMessage());
            return null;
        }
        List<PoisV2> poisList = amapSearchJsonRootBean.getBody().getPois();
        if (CollUtil.isNotEmpty(poisList)) {
            return poisList;
        }
        return null;
    }

    @Override
    public List<Tips> inputTipsV2(String keyWords, String areaCode) {
        String url = amapUrl + "/v3/assistant/inputtips?keywords=" + keyWords.replace("#", "-") +
                "&output=json&offset=20&page=1&key=" + amapKey + "&extensions=all";
        if (StringUtils.isNotEmpty(areaCode)) {
            url += "&region=" + areaCode + "&city_limit=true";
        }
        ResponseEntity<InputTips> inputTips = null;
        try {
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 接口,请求URL: {}", url);
            inputTips = restTemplate.postForEntity(url, "", InputTips.class);
            log.info("地址智能解析: 远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(inputTips.getBody()));
        } catch (Exception e) {
            log.error("远程调用 高德地图 搜索服务-关键字查询 搜索POI-2.0 出现异常: {}", e.getMessage());
            return null;
        }
        List<Tips> tips = inputTips.getBody().getTips();
        if (CollUtil.isNotEmpty(tips)) {
            return tips;
        }
        return null;
    }

    @Override
    public void manualMapping(String loginUserId, PoisV2 poisV2) {
        String addressDetail = poisV2.getAddress();
        poisV2 = this.searchV2ById(poisV2.getId());
        if (poisV2 == null){
            throw new MedicalBusinessException("未找到数据");
        }
        AmapAddressDetailMapping amapAddressDetailMapping = geocode(poisV2.getLocation());
        /* 调用高德接口 结束 */
        //获取登录人员信息
        TUapUser userCache = getUapUser(loginUserId);
        // 更新映射表 和 标准地址表
        AddressDetailMapping addressDetailMapping = AddressDetailMapping.builder().build();
        String addressStandardId = "AMAP_" + poisV2.getId();
        addressDetailMapping.setAddressDetail(addressDetail);
        addressDetailMapping.setId(String.valueOf(batchUidService.getUid(TableName.ADDRESS_DETAIL_MAPPING)));
        addressDetailMapping.setAddressStandardId(addressStandardId);
        addressDetailMapping.setOrgCityName("");
        addressDetailMapping.setOrgCityCode("");
        addressDetailMapping.setIdCardCityCode("");
        addressDetailMapping.setIdCardCityName("");
        addressDetailMapping.setTelephoneCityCode("");
        addressDetailMapping.setTelephoneCityName("");
        addressDetailMapping.setPoiType(poisV2.getType());
        addressDetailMapping.setCreator(userCache.getId());
        addressDetailMapping.setUpdator(userCache.getId());
        addressDetailMapping.setStatus(1);


        AddressStandard addressStandard = AddressStandard.builder().build();
        addressStandard.setId(addressStandardId);
        addressStandard.setAddressAreaName(poisV2.getName());
        addressStandard.setAddressAreaCode(poisV2.getId());
        addressStandard.setAddressLongitude(poisV2.getLocation().split(",")[0]);
        addressStandard.setAddressLatitude(poisV2.getLocation().split(",")[1]);
        addressStandard.setAddressProvinceName(poisV2.getPname());
        addressStandard.setAddressProvinceCode(poisV2.getPcode());
        addressStandard.setAddressCityName(poisV2.getCityname());
        addressStandard.setAddressCityCode(StringUtils.isEmpty(poisV2.getCitycode()) ? "" : poisV2.getAdcode().substring(0, 4) + "00");
        addressStandard.setAddressDistrictName(poisV2.getAdname());
        addressStandard.setAddressDistrictCode(poisV2.getAdcode());
        addressStandard.setAddressTownName(amapAddressDetailMapping.getAddressTownName());
        addressStandard.setAddressTownCode(amapAddressDetailMapping.getAddressTownCode());
        addressStandard.setPoiType(poisV2.getType());
        addressStandard.setCoordinatesType("AMAP_GCJ-02");
        addressStandard.setAddressName(poisV2.getName());
        addressStandard.setAddressDetailDesc(amapAddressDetailMapping.getAddressAreaName());
        addressStandard.setCreator(userCache.getId());
        addressStandard.setUpdator(userCache.getId());


        //手工匹配，则相似度置空
        addressDetailMapping.setSimilarity(null);

        addressStandardService.saveOrUpdateAddressStandard(addressStandard);
        addressDetailMappingService.saveOrUpdateAddressDetailMapping(addressDetailMapping);
    }

    /**
     * 地址智能解析分页: 远程分页调用高德 搜索POI 接口
     *
     * @param keywords 检索关键字
     * @param city     检索城市范围
     * @param pageNum  当前页数
     * @param pageSize 每页显示条数
     * @param types
     * @return
     */
    private PageData<AddressStandardLatestVo> searchLatestList(String keywords, String city, Integer pageNum, Integer pageSize, String types) {

        PageData<AddressStandardLatestVo> addressStandardLatestVoPageData = new PageData<>();
        addressStandardLatestVoPageData.setPageIndex(pageNum);
        addressStandardLatestVoPageData.setPageSize(pageSize);
        addressStandardLatestVoPageData.setPages(0);
        addressStandardLatestVoPageData.setData(new ArrayList<>());
        addressStandardLatestVoPageData.setTotal(0);
        if ("".equals(keywords)) {
            return addressStandardLatestVoPageData;
        }

        List<AddressStandardLatestVo> addressStandardLatestVoList = new ArrayList<>();

        String searchUrl = amapUrl + "/v3/place/text?keywords=" + keywords + "&city=" + city + "&output=json&offset=" + pageSize + "&page=" + pageNum + "&types=" + types + "&key=" + amapKey + "&extensions=all";

        ResponseEntity<AmapSearchJsonRootBeanLatest> amapSearchJsonRootBeanLatest;
        try {

            log.info("地址智能解析分页: 远程分页调用高德 搜索POI 接口,请求URL: {}", searchUrl);
            amapSearchJsonRootBeanLatest = restTemplate.postForEntity(searchUrl, "", AmapSearchJsonRootBeanLatest.class);
            log.info("地址智能解析分页: 远程分页调用高德 搜索POI 获取的返回值: {}", OBJECT_MAPPER.writeValueAsString(amapSearchJsonRootBeanLatest.getBody()));

            long total = Long.parseLong(Objects.requireNonNull(amapSearchJsonRootBeanLatest.getBody()).getCount());
            addressStandardLatestVoPageData.setTotal(total);


            List<PoisLatest> pois = Objects.requireNonNull(amapSearchJsonRootBeanLatest.getBody()).getPois();
            for (PoisLatest poisLatest : pois) {
                AddressStandardLatestVo addressStandardLatestVo = new AddressStandardLatestVo();
                // 地址名
                addressStandardLatestVo.setAddressName(poisLatest.getName());
                // 地址描述
                addressStandardLatestVo.setAddressDetailDesc(poisLatest.getAddress());
                // 地址编码
                addressStandardLatestVo.setAddressAreaCode(poisLatest.getId());
                /* 经度 */
                addressStandardLatestVo.setAddressLongitude(poisLatest.getLocation().split(",")[0]);
                /* 纬度 */
                addressStandardLatestVo.setAddressLatitude(poisLatest.getLocation().split(",")[1]);

                String pname = poisLatest.getPname();
                String cityname = poisLatest.getCityname();
                String adname = poisLatest.getAdname();

                addressStandardLatestVo.setAddressProvinceCode("");
                addressStandardLatestVo.setAddressProvinceName(Objects.requireNonNull(pname));
                addressStandardLatestVo.setAddressCityCode("");
                addressStandardLatestVo.setAddressCityName(Objects.requireNonNull(cityname));
                addressStandardLatestVo.setAddressDistrictCode("");
                addressStandardLatestVo.setAddressDistrictName(Objects.requireNonNull(adname));
                addressStandardLatestVo.setAddressTownCode("");
                addressStandardLatestVo.setAddressTownName("");

                addressStandardLatestVo.setAddressAreaName(
                        addressStandardLatestVo.getAddressProvinceName()
                                + addressStandardLatestVo.getAddressCityName()
                                + addressStandardLatestVo.getAddressDistrictName()
                                + addressStandardLatestVo.getAddressTownName()
                                + addressStandardLatestVo.getAddressDetailDesc()
                                + addressStandardLatestVo.getAddressName()
                );

                addressStandardLatestVoList.add(addressStandardLatestVo);
            }
            addressStandardLatestVoPageData.setData(addressStandardLatestVoList);
            addressStandardLatestVoPageData.setPages(
                    (int) (addressStandardLatestVoPageData.getTotal() / (addressStandardLatestVoPageData.getPageSize() == 0L ? 1 : addressStandardLatestVoPageData.getPageSize())) + 1
            );

        } catch (Exception e) {
            log.info("远程调用高德接口异常: {}", e.getMessage());
        }

        return addressStandardLatestVoPageData;
    }

    //@PostConstruct
    private void getRegionSet() {
        List<Map<String, String>> regionList = regionMapper.getRegionList();
        String lastProvince = "";
        String lastShortProvince = "";
        for (Map<String, String> region : regionList) {
            if (lastProvince.equals(region.get("province"))) {
                fullRegions.get(lastProvince).add(region.get("city"));
                shortRegions.get(lastShortProvince).add(region.get("short_city"));
            } else {
                lastProvince = region.get("province");
                lastShortProvince = region.get("short_province");
                fullRegions.put(lastProvince, new HashSet<>());
                shortRegions.put(lastShortProvince, new HashSet<>());
                regionMapping.put(lastProvince, lastShortProvince);
            }
        }
        log.info("full region: {}", fullRegions);
        log.info("short region: {}", shortRegions);
        log.info("region mapping: {}", regionMapping);
    }

    /**
     * 从地址中解析所在城市
     *
     * @param address      源地址
     * @param districtCode 机构所在区划
     * @return 地址中的城市信息
     */
    private String getCity(String address, String districtCode) {
        if (StringUtils.isBlank(address)) {
            return null;
        }

        Map<String, String> stAddress = getStructuredAddress(address);

        if (StringUtils.isNotEmpty(stAddress.get("city"))) {
            return stAddress.get("city");
        }

        if (StringUtils.isNotEmpty(stAddress.get("district"))) {
            return stAddress.get("district");
        }

        // xxx省xxx(市)xxx
        if (StringUtils.isNotEmpty(stAddress.get("province"))) {
            try {
                Set<String> cites = shortRegions.get(regionMapping.get(stAddress.get("province")));
                for (String city : cites) {
                    if (stAddress.get("place").startsWith(city)) {
                        return city;
                    }
                }
            } catch (Exception e) {
                log.error("地址标化，提取城市名称错误：" + stAddress.get("province"));
            }
            return StringUtils.isNotEmpty(districtCode) ? districtCode.substring(0, 4) + "00" : stAddress.get("province");
        }

        // xxx(省)xxx(市)xxx
        for (Map.Entry<String, Set<String>> entry : shortRegions.entrySet()) {
            if (address.startsWith(entry.getKey())) {
                for (String city : entry.getValue()) {
                    if (address.replaceFirst(entry.getKey(), "").trim().startsWith(city)) {
                        return city;
                    }
                }
                return StringUtils.isNotEmpty(districtCode) ? districtCode.substring(0, 4) + "00" : entry.getKey();
            }
        }

        // xxx(市)xxx 或其他 ：直接使用org的行政区划编码
        return StringUtils.isNotEmpty(districtCode) ? districtCode.substring(0, 4) + "00" : "";
    }

    private Map<String, String> getStructuredAddress(String address) {
        String regex = "(?<province>.+自治区|.[^省]+省|(北京|上海|天津|重庆)市)?" +
                "(?<city>.+自治州|[^市]+市|.+?地区|.+盟|.+县级行政区划)?" +
                "(?<district>[^县]+县|.+地区|[^区]+(?<!社)区|.+市|.+旗|.+海域|.+岛)?(?<place>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String province;
        String city;
        String district;
        String place;
        Map<String, String> row = new HashMap<>();
        if (m.find()) {
            row = new LinkedHashMap<>();
            province = m.group("province");
            row.put("province", province == null ? "" : province.trim());
            city = m.group("city");
            row.put("city", city == null ? "" : city.trim());
            district = m.group("district");
            row.put("district", district == null ? "" : district.trim());
            district = m.group("place");
            row.put("place", district == null ? "" : district.trim());
        }
        return row;
    }

}