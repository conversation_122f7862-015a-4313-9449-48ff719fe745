package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.dto.AreaInfoVO;
import com.iflytek.cdc.admin.entity.*;
import com.iflytek.cdc.admin.mapper.RegionMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrConfigMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrCustomizeDistrictMapper;
import com.iflytek.cdc.admin.mapper.TbCdcmrDistrictJsonDataMapper;
import com.iflytek.cdc.admin.service.CustomizeDistrictService;
import com.iflytek.cdc.admin.service.UapUserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CustomizeDistrictServiceImpl implements CustomizeDistrictService {

    @Resource
    TbCdcmrCustomizeDistrictMapper tbCdcmrCustomizeDistrictMapper;
    @Resource
    TbCdcmrDistrictJsonDataMapper tbCdcmrDistrictJsonDataMapper;
    @Resource
    TbCdcmrConfigMapper tbCdcmrConfigMapper;
    @Resource
    UapUserService uapUserService;
    @Resource
    RegionMapper regionMapper;

    public static final Integer DISTRICT = 1;
    public static final Integer CITY = 2;
    public static final Integer PROVINCE = 3;


    @Override
    public List<TbCdcmrCustomizeDistrict> getCustomizeDistrictWithoutData() {
        return tbCdcmrCustomizeDistrictMapper.selectListWithoutData();
    }

    @Override
    public String getCustomizeDistrictSwitch() {
        return tbCdcmrConfigMapper.selectByConfigTypeAndConfigKey(1, "customizeDistrictSwitch").getConfigValue();
    }

    @Override
    public void updateCustomizeDistrictSwitch(TbCdcmrConfig tbCdcmrConfig) {
        tbCdcmrConfig.setConfigType(1);
        tbCdcmrConfig.setConfigKey("customizeDistrictSwitch");
        tbCdcmrConfigMapper.updateByPrimaryKey(tbCdcmrConfig);
    }

    @Override
    public void deleteCustomizeDistrict(String districtCode) {
        tbCdcmrCustomizeDistrictMapper.deleteCustomizeDistrict(districtCode);
    }

    @Override
    public List<String> getJsonDataNameList() {
        return tbCdcmrDistrictJsonDataMapper.selectNameList();
    }

    @Override
    public String getJsonDataByName(String jsonName) {
        return tbCdcmrDistrictJsonDataMapper.selectByPrimaryKey(jsonName);
    }

    @Override
    public void saveCustomizeDistrict(TbCdcmrCustomizeDistrict tbCdcmrCustomizeDistrict) {
        tbCdcmrCustomizeDistrict.setIsDeleted(0);
        tbCdcmrCustomizeDistrictMapper.insert(tbCdcmrCustomizeDistrict);
    }

    @Override
    public TbCdcmrCustomizeDistrict getCustomizeDistrict(String districtCode) {
        return tbCdcmrCustomizeDistrictMapper.selectByPrimaryKey(districtCode);
    }

    @Override
    public AreaInfoVO findAreaAndLevelByUser(String loginUserId) {
        UapUserExtend userInfo = uapUserService.getUserInfo(loginUserId);
        //根据机构是否包含省市区编码来决定层级数
        AreaInfoVO areaInfoVO = new AreaInfoVO();
        List<CascadeVO> list;
        if (StringUtils.isNotBlank(userInfo.getDistrictCode())) {
            //区级不为空 则为区级用户
            areaInfoVO.setLevel(DISTRICT);
            //只查询该区的信息
            list = getEntity().stream().filter(a -> a.getCode().equals(userInfo.getDistrictCode())).
                    map(a -> CascadeVO.fromArea(a, a.getDistrict())).collect(Collectors.toList());

        } else if (StringUtils.isNotBlank(userInfo.getCityCode())) {
            areaInfoVO.setLevel(CITY);
            //先查询市 再查询区
            list = getEntity().stream().filter(a -> a.getCode().equals(userInfo.getCityCode())).map(a -> CascadeVO.fromArea(a, a.getCity()))
                    .collect(Collectors.toList());
            setDistricts(list);
        } else {
            areaInfoVO.setLevel(PROVINCE);
            //先查询省 然后市 然后区
            list = getEntity().stream().filter(a -> a.getCode().equals(userInfo.getProvinceCode())).map(
                    a -> CascadeVO.fromArea(a, a.getProvince())
            ).collect(Collectors.toList());

            list.forEach(areaVO -> {
                List<CascadeVO> cityList = getEntity().stream().filter(a -> a.getParentId().equals(Long.parseLong(areaVO.getValue())))
                        .map(a -> CascadeVO.fromArea(a, a.getCity())).collect(Collectors.toList());
                setDistricts(cityList);
                areaVO.setChildren(cityList);

            });
        }
        areaInfoVO.setList(list);

        return areaInfoVO;
    }

    private List<Area> getEntity() {
        List<Area> list = regionMapper.findAll();
        if ("0".equals(getCustomizeDistrictSwitch())) {
            List<String> codeList = regionMapper.getMappingDistrictCodeList();
            return list.stream()
                    .filter(area -> !codeList.contains(area.getCode()))
                    .collect(Collectors.toList());
        }
        return list;
    }

    private void setDistricts(List<CascadeVO> cityList) {
        //省级和市级用户都能看到完整的一个或若干个市下的所有区级行政规划，都会用到此方法来填充第三层数据
        cityList.forEach(city -> {
            List<CascadeVO> districtList = getEntity().stream().filter(c -> c.getParentId().equals(Long.parseLong(city.getValue())))
                    .map(d -> CascadeVO.fromArea(d, d.getDistrict())).collect(Collectors.toList());
            city.setChildren(districtList);
        });
    }
}
