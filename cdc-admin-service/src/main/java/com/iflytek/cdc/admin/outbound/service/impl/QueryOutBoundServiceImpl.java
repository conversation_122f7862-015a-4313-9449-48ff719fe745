package com.iflytek.cdc.admin.outbound.service.impl;

import com.google.gson.reflect.TypeToken;

import com.iflytek.cdc.admin.outbound.common.executor.OutboundExecutor;
import com.iflytek.cdc.admin.outbound.constant.OutboundConfig;
import com.iflytek.cdc.admin.outbound.model.dto.input.BatchList;
import com.iflytek.cdc.admin.outbound.model.dto.output.*;
import com.iflytek.cdc.admin.outbound.service.QueryOutBoundService;
import com.iflytek.cdc.admin.outbound.util.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 外呼结果、信息查询服务
 * <AUTHOR>
 */
@Service
public class QueryOutBoundServiceImpl implements QueryOutBoundService {

    @Resource
    private OutboundConfig outboundConfig;

    @Resource
    private OutboundExecutor outboundExecutor;

    @Override
    public Response<SpeechVariable> querySpeechVariable(String speechId) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getSpeechVariable(),
                SpeechId.builder().speechId(speechId).build(), new TypeToken<Response<SpeechVariable>>() {}.getType());
    }

    @Override
    public Response<BatchDetailRs> queryBatchDetail(String batchId) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getBatchDetail(),
                BatchId.builder().batch(batchId).build(), new TypeToken<Response<BatchDetailRs>>() {}.getType());
    }

    @Override
    public Response<BatchListRs> queryBatchList(BatchList batchList) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getBatchList(), batchList,
                new TypeToken<Response<BatchListRs>>() {}.getType());
    }

    @Override
    public Response<RecordDetailRs> queryRecordDetail(String recordId) {
        return outboundExecutor.invokeOutbound(outboundConfig.getPublicUrl() + outboundConfig.getRecordDetail(),
                RecordId.builder().recordId(recordId).build(), new TypeToken<Response<RecordDetailRs>>() {}.getType());
    }
}
