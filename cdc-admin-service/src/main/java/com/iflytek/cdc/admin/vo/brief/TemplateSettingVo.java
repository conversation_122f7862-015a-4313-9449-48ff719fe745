package com.iflytek.cdc.admin.vo.brief;

import com.iflytek.cdc.admin.entity.brief.IndicatorsEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 模板设置dto
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@ApiModel("模板设置dto")
public class TemplateSettingVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @ApiModelProperty("模板ID")
    @NotEmpty
    private String templateId;
    /**
     * 内容标题
     */
    @ApiModelProperty("内容标题")
    private String contentTitle;
    /**
     * 附件标题
     */
    @ApiModelProperty("附件标题")
    private String attachmentTitle;

    /**
     * 模板内容
     */
    @ApiModelProperty("模板内容")
    private String content;
    /**
     * 原始模板内容
     */
    @ApiModelProperty("原始模板内容")
    private String originalContent;
    /**
     * 修改者ID
     */
    @ApiModelProperty("修改者ID")
    private String updatorId;
    /**
     * 修改者
     */
    @ApiModelProperty("修改者")
    private String updatorName;
    /**
     * 维度指标集合
     */
    @ApiModelProperty("维度指标集合")
    private List<IndicatorsEntity> indicators;
    /**
     * 附件维度指标集合
     */
    @ApiModelProperty("附件维度指标集合")
    private List<IndicatorsEntity> attachmentIndicators;
    private String attachmentFlag;

}
