package com.iflytek.cdc.admin.capacity.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.iflytek.cdc.admin.capacity.api.algorithm.request.FeatureSelectionRequest;
import com.iflytek.cdc.admin.capacity.api.algorithm.response.InfluenceFactorResponse;
import com.iflytek.cdc.admin.capacity.service.AlgorithmApiService;
import com.iflytek.cdc.admin.capacity.service.DiseaseInfluenceFactorService;
import com.iflytek.cdc.admin.constant.DiseaseInfluenceFactorEnum;
import com.iflytek.cdc.admin.dto.InfluenceFactorDTO;
import com.iflytek.cdc.admin.dto.InfluenceFactorRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 疾病传播影响因素接口实现类
 */
@Service
public class DiseaseInfluenceFactorServiceImpl implements DiseaseInfluenceFactorService {
    private List<InfluenceFactorDTO> list;

    @Resource
    private AlgorithmApiService algorithmApiService;

    @Override
    public void importDataByFile(InputStream inputStream) throws IOException {
        if (ObjectUtil.isNotEmpty(inputStream)) {
            list = new ArrayList<>();
            list = EasyExcel.read(inputStream).head(InfluenceFactorDTO.class).sheet().doReadSync();
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(e -> {
                    System.out.println(e.toString());
                });
            }
        }
    }

    /**
     * 疾病发病影响因素分析
     *
     * @return
     */
    @Override
    public InfluenceFactorResponse.Result.InfluenceFactor influenceFactorAnalysis(InfluenceFactorRequest influence, String loginUserId) {
        if (CollectionUtil.isEmpty(list)) {
            throw new RuntimeException("样本数据为空，请先上传样本数据");
        }
        FeatureSelectionRequest request = new FeatureSelectionRequest();
        FeatureSelectionRequest.Data data = new FeatureSelectionRequest.Data();
        FeatureSelectionRequest.Model model = new FeatureSelectionRequest.Model();
        data.setX_cols(influence.getXCols());
        data.setY_cols(influence.getYCols());
        data.setDetailData(list);
        data.setFile_path("");
        model.setMethod("rfe");
        model.setMetric("roc_auc");
        model.setEstimator(influence.getEstimator());
        model.setThreshold(influence.getThreshold());
        model.setStep(1);
        int selSize = influence.getXCols().size();
        if (0 == influence.getFeaturesSelect() || influence.getFeaturesSelect() >= selSize) {
            influence.setFeaturesSelect(selSize - 1);
        }
        model.setN_features_to_select(influence.getFeaturesSelect());
        request.setData(data);
        request.setModel(model);
        System.out.println(JSONUtil.toJsonStr(request));
        return algorithmApiService.randomForest(request, loginUserId);

    }


    /**
     * 样本数据模版下载
     *
     * @return
     */
    @Override
    public byte[] downloadTemplate() {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        //Map<Integer, List<String>> selectMap = new HashMap<>();
        //selectMap.put(4, Arrays.stream(TableColumnTypeEnum.values()).map(TableColumnTypeEnum::getName).collect(Collectors.toList()));
        EasyExcel.write(outputStream, InfluenceFactorDTO.class).sheet().doWrite(new ArrayList<>());
        //.registerWriteHandler(new SelectDataSheetWriteHandler(selectMap)).sheet().doWrite(new ArrayList<>());
        return outputStream.toByteArray();
    }

    /**
     * 疾病影响因素集合
     *
     * @return
     */
    @Override
    public List<String> findDiseaseInfluenceFactor() {
        return Arrays.stream(DiseaseInfluenceFactorEnum.values()).map(DiseaseInfluenceFactorEnum::getName).collect(Collectors.toList());
    }


}
