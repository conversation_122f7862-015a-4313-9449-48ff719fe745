package com.iflytek.cdc.admin.service.province.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.entity.mr.TbCdcmrUserPageConfig;
import com.iflytek.cdc.admin.mapper.province.TbCdcmrUserPageConfigMapper;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.cdc.admin.service.province.UserPageConfigService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.iflytek.cdc.admin.common.interceptor.UserInfoInterceptor.USER_INFO;

@Service
public class UserPageConfigServiceImpl extends CdcServiceBaseImpl<TbCdcmrUserPageConfigMapper, TbCdcmrUserPageConfig> implements UserPageConfigService {
    @Override
    public void saveConfig(TbCdcmrUserPageConfig pageConfig) {
        if (StringUtils.isEmpty(pageConfig.getPageCode())){
            throw new MedicalBusinessException("页面编码不能为空");
        }
        UapUserPo uapUserPo = USER_INFO.get();
        String userId = uapUserPo.getId();
        TbCdcmrUserPageConfig curr = loadPageConfig(userId, pageConfig.getPageCode());
        if (curr != null){
            curr.setColumnConfig(pageConfig.getColumnConfig());
            curr.setFilterConfig(pageConfig.getFilterConfig());
            update(curr);
        }else {
            pageConfig.setUserId(userId);
            create(pageConfig);
        }

    }

    @Override
    public TbCdcmrUserPageConfig loadPageConfig(String userId, String pageCode) {
        LambdaQueryWrapper<TbCdcmrUserPageConfig> eq = lambdaQueryWrapper()
                .eq(TbCdcmrUserPageConfig::getUserId, userId)
                .eq(TbCdcmrUserPageConfig::getPageCode, pageCode);
        return getOne(eq);
    }

    @Override
    public void deleteUserConfig(String userId, String pageCode) {
        LambdaQueryWrapper<TbCdcmrUserPageConfig> eq = lambdaQueryWrapper()
                .eq(TbCdcmrUserPageConfig::getUserId, userId)
                .eq(TbCdcmrUserPageConfig::getPageCode, pageCode);
        remove(eq);
    }

}
