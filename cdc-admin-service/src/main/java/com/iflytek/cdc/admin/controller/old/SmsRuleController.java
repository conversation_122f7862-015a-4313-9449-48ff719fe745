package com.iflytek.cdc.admin.controller.old;

import com.iflytek.cdc.admin.dto.*;
import com.iflytek.cdc.admin.entity.CascadeVO;
import com.iflytek.cdc.admin.entity.TbCdcmrOutpatientSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrPreventionControlSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrUnknownReasonSmsRule;
import com.iflytek.cdc.admin.entity.TbCdcmrCustomizedSmsRule;
import com.iflytek.cdc.admin.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

;

@Api(tags = "预警短信规则配置")
@RestController
public class SmsRuleController {

    @Resource
    SmsRuleService smsRuleService;

    @Resource
    SyndromeService syndromeService;

    @Resource
    InfectiousDiseasesApiService infectiousDiseasesApiService;

    @Resource
    SchoolSymptomService schoolSymptomService;

    @Resource
    PoisoningWarnService poisoningWarnService;

    @Resource
    OutpatientWarnService outpatientWarnService;

    @Resource
    UnknownReasonWarnService unknownReasonWarnService;

    @Resource
    PreventionControlWarnService preventionControlWarnService;

    @Resource
    CustomizedWarningTaskService customizedWarningTaskService;

    @PostMapping("/pt/v1/smsRule/saveSymptomSmsRule")
    @ApiOperation("保存症候群预警短信规则")
    public void saveSymptomSmsRule(@RequestBody SymptomSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertSymptomSmsRule(rule, loginUserId);
    }


    @GetMapping("/pt/v1/smsRule/getSymptomSmsRuleList")
    @ApiOperation("查询症候群预警短信规则")
    public List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getSymptomSmsRuleList(businessPersonId);
    }

    @GetMapping("/pt/v1/smsRule/getSyndromeNameList")
    @ApiOperation("症候群列表")
    public List<CascadeVO> getSyndromeList(String userId) {
        return syndromeService.getSyndromeList(userId);
    }

    @PostMapping("/pt/v1/smsRule/getSymptomSmsRuleListByCodeList")
    @ApiOperation("根据症候群列表获取短信规则")
    public List<TbCdcmrSymptomSmsRule> getSymptomSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getSymptomSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deleteSymptomSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除症候群规则")
    public void deleteSymptomSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteSymptomSmsRuleByLoginUserId(loginUserId);
    }

    //----------------------------------------------------------------------------------------------------------------------------
    @GetMapping("/pt/v1/smsRule/getInfectedNameList")
    @ApiOperation("查询传染病列表")
    public List<CascadeVO> getInfectedNameList(String userId) {
        return infectiousDiseasesApiService.getInfectedNameList(userId);
    }

    @PostMapping("/pt/v1/smsRule/saveInfectedSmsRule")
    @ApiOperation("保存传染病预警短信规则")
    public void saveInfectedSmsRule(@RequestBody InfectedSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertInfectedSmsRule(rule, loginUserId);
    }

    @PostMapping("/pt/v1/smsRule/getInfectedSmsRuleListByCodeList")
    @ApiOperation("根据传染病列表获取短信规则")
    public List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getInfectedSmsRuleListByCodeList(codeList);
    }

    @GetMapping("/pt/v1/smsRule/getInfectedSmsRuleList")
    @ApiOperation("查询传染病预警短信规则")
    public List<TbCdcmrInfectedSmsRule> getInfectedSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getInfectedSmsRuleList(businessPersonId);
    }

    @PostMapping("/pt/v1/smsRule/deleteInfectedSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除传染病规则")
    public void deleteInfectedSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteInfectedSmsRuleByLoginUserId(loginUserId);
    }

    //----------------------------------------------------------------------------------------------------------------------------
    @GetMapping("/pt/v1/smsRule/getScSymptomNameList")
    @ApiOperation("学校症状列表")
    public List<CascadeVO> getScSymptomList(String userId) {
        return schoolSymptomService.getSymptoms(userId);
    }

    @PostMapping("/pt/v1/smsRule/saveScSymptomSmsRule")
    @ApiOperation("保存学校症状预警短信规则")
    public void saveSymptomSmsRule(@RequestBody SympSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertScSymptomSmsRule(rule, loginUserId);
    }

    @GetMapping("/pt/v1/smsRule/getScSymptomSmsRuleList")
    @ApiOperation("查询学校症状预警短信规则")
    public List<TbCdcmrSympSmsRule> getScSymptomSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getScSymptomSmsRuleList(businessPersonId);
    }

    @PostMapping("/pt/v1/smsRule/getScSymptomSmsRuleListByCodeList")
    @ApiOperation("根据学校症状列表获取短信规则")
    public List<TbCdcmrSympSmsRule> getScSymptomSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getScSymptomSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deleteScSymptomSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除学校症状规则")
    public void deleteScSymptomSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteScSymptomSmsRuleByLoginUserId(loginUserId);
    }

    //----------------------------------------------------------------------------------------------------------------------------

    @GetMapping("/pt/v1/smsRule/getPoisonNameList")
    @ApiOperation("中毒列表")
    public List<CascadeVO> getPoisonList(String userId) {
        return poisoningWarnService.getPoisonNameList(userId);
    }

    @GetMapping("/pt/v1/smsRule/getCustomizedNameList")
    @ApiOperation("自定义列表")
    public List<CascadeVO> getCustomizedNameList(String userId) {
        return customizedWarningTaskService.getCustomizedNameList(userId);
    }

    @PostMapping("/pt/v1/smsRule/savePoisonSmsRule")
    @ApiOperation("保存中毒预警短信规则")
    public void savePoisonSmsRule(@RequestBody PoisonSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertPoisonSmsRule(rule, loginUserId);
    }

    @PostMapping("/pt/v1/smsRule/getPoisonSmsRuleListByCodeList")
    @ApiOperation("根据中毒类列表获取短信规则")
    public List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getPoisonSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deletePoisonSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除中毒类规则")
    public void deletePoisonSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deletePoisonSmsRuleByLoginUserId(loginUserId);
    }

    @GetMapping("/pt/v1/smsRule/getPoisonSmsRuleList")
    @ApiOperation("查询中毒预警短信规则")
    public List<TbCdcmrPoisonSmsRule> getPoisonSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getPoisonSmsRuleList(businessPersonId);
    }

    //----------------------------------------------------------------------------------------------------------------------------


    @PostMapping("/pt/v1/smsRule/saveOutpatientSmsRule")
    @ApiOperation("保存门诊预警短信规则")
    public void saveOutpatientSmsRule(@RequestBody OutpatientSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertOutpatientSmsRule(rule, loginUserId);
    }

    @GetMapping("/pt/v1/smsRule/getOutpatientSmsRuleList")
    @ApiOperation("查询门诊预警短信规则")
    public List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getOutpatientSmsRuleList(businessPersonId);
    }

    @GetMapping("/pt/v1/smsRule/getOutpatientNameList")
    @ApiOperation("门诊列表")
    public List<CascadeVO> getOutpatientList(String userId) {
        return outpatientWarnService.getOutpatientTypeNameList(userId);
    }

    @PostMapping("/pt/v1/smsRule/getOutpatientSmsRuleListByCodeList")
    @ApiOperation("根据门诊类列表获取短信规则")
    public List<TbCdcmrOutpatientSmsRule> getOutpatientSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getOutpatientSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deleteOutpatientSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除门诊类规则")
    public void deleteOutpatientSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteOutpatientSmsRuleByLoginUserId(loginUserId);
    }
    //----------------------------------------------------------------------------------------------------------------------------
    @PostMapping("/pt/v1/smsRule/saveUnknownReasonSmsRule")
    @ApiOperation("保存不明原因预警短信规则")
    public void saveUnknownReasonSmsRule(@RequestBody UnknownReasonSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertUnknownReasonSmsRule(rule, loginUserId);
    }

    @GetMapping("/pt/v1/smsRule/getUnknownReasonSmsRuleList")
    @ApiOperation("查询不明原因预警短信规则")
    public List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getUnknownReasonSmsRuleList(businessPersonId);
    }

    @GetMapping("/pt/v1/smsRule/getUnknownReasonNameList")
    @ApiOperation("不明原因列表")
    public List<CascadeVO> getUnknownReasonList(String userId) {
        return unknownReasonWarnService.getUnknownReasonNameList(userId);
    }

    @PostMapping("/pt/v1/smsRule/getUnknownReasonSmsRuleListByCodeList")
    @ApiOperation("根据不明原因类列表获取短信规则")
    public List<TbCdcmrUnknownReasonSmsRule> getUnknownReasonSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getUnknownReasonSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deleteUnknownReasonSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除不明原因类规则")
    public void deleteUnknownReasonSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteUnknownReasonSmsRuleByLoginUserId(loginUserId);
    }

    //----------------------------------------------------------------------------------------------------------------------------


    @PostMapping("/pt/v1/smsRule/savePreventionControlSmsRule")
    @ApiOperation("保存联防联控预警短信规则")
    public void savePreventionControlSmsRule(@RequestBody PreventionControlSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertPreventionControlSmsRule(rule, loginUserId);
    }

    @GetMapping("/pt/v1/smsRule/getPreventionControlSmsRuleList")
    @ApiOperation("查询联防联控预警短信规则")
    public List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getPreventionControlSmsRuleList(businessPersonId);
    }

    @GetMapping("/pt/v1/smsRule/getPreventionControlNameList")
    @ApiOperation("联防联控列表")
    public List<CascadeVO> getPreventionControlList(String userId) {
        return preventionControlWarnService.getPreventionControlNameList(userId);
    }

    @PostMapping("/pt/v1/smsRule/getPreventionControlSmsRuleListByCodeList")
    @ApiOperation("根据联防联控类列表获取短信规则")
    public List<TbCdcmrPreventionControlSmsRule> getPreventionControlSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getPreventionControlSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deletePreventionControlSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除联防联控类规则")
    public void deletePreventionControlSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deletePreventionControlSmsRuleByLoginUserId(loginUserId);
    }


    @PostMapping("/pt/v1/smsRule/saveCustomizedSmsRule")
    @ApiOperation("保存自定义预警短信规则")
    public void saveCustomizedSmsRule(@RequestBody CustomizedSmsRuleParam rule, @RequestParam String loginUserId) {
        smsRuleService.upsertCustomizedSmsRule(rule, loginUserId);
    }


    @GetMapping("/pt/v1/smsRule/getCustomizedSmsRuleList")
    @ApiOperation("查询自定义预警短信规则")
    public List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleList(@RequestParam String businessPersonId) {
        return smsRuleService.getCustomizedSmsRuleList(businessPersonId);
    }


    @PostMapping("/pt/v1/smsRule/getCustomizedSmsRuleListByCodeList")
    @ApiOperation("根据自定义类列表获取短信规则")
    public List<TbCdcmrCustomizedSmsRule> getCustomizedSmsRuleListByCodeList(@RequestBody List<String> codeList) {
        return smsRuleService.getCustomizedSmsRuleListByCodeList(codeList);
    }


    @PostMapping("/pt/v1/smsRule/deleteCustomizedSmsRuleByLoginUserId")
    @ApiOperation("根据用户ID删除自定义类规则")
    public void deleteCustomizedSmsRuleByLoginUserId(@RequestParam String loginUserId) {
        smsRuleService.deleteCustomizedSmsRuleByLoginUserId(loginUserId);
    }

}
