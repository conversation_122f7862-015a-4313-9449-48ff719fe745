package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.apiservice.CdcDataServiceApi;
import com.iflytek.cdc.admin.dto.DictParams;
import com.iflytek.cdc.admin.dto.SearchParams;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.province.DimensionService;
import com.iflytek.cdc.admin.vo.DictValueVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "维度表处理")
@RequestMapping("/pt/{version}/dim")
public class DimensionController {

    @Resource
    private DimensionService dimensionService;

    @Resource
    private CdcDataServiceApi cdcDataServiceApi;

    @Value("${mdm.dictCode.diagnose:404003-CDC}")
    private String diagnoseDictCode;

    @PostMapping("/getInfectedInfo")
    @ApiOperation("传染病病种级联")
    public List<TreeNode> getInfectedInfo(@RequestParam(required = false) String infectedClassCode,
                                          @RequestParam(required = false) String infectedTypeCode) {

        return dimensionService.getInfectedInfo(infectedClassCode, infectedTypeCode,false);
    }
    @PostMapping("/getInfectedParentInfo")
    @ApiOperation("传染病父级诊断+病毒性肝炎子级")
    public List<TreeNode> getInfectedParentInfo(@RequestParam(required = false) String infectedClassCode,
                                          @RequestParam(required = false) String infectedTypeCode) {

        return dimensionService.getInfectedParentInfo(infectedClassCode, infectedTypeCode);
    }

    @PostMapping("/getOrgInfo")
    @ApiOperation("获取机构级联")
    public List<TreeNode> getOrgInfo() {

        return dimensionService.getOrgInfo();
    }

    @PostMapping("/getSyndromeInfo")
    @ApiOperation("获取症候群级联")
    public List<TreeNode> getSyndromeInfo() {

        return dimensionService.getSyndromeInfo();
    }

    @PostMapping("/getEmergingInfo")
    @ApiOperation("获取新发突发传染病级联")
    public List<TreeNode> getEmergingInfo() {

        return dimensionService.getEmergingInfo();
    }

    @PostMapping("/getEndemicInfo")
    @ApiOperation("获取地方病级联")
    public List<TreeNode> getEndemicInfo() {

        return dimensionService.getEndemicInfo();
    }

    @PostMapping("/getPathogenInfo")
    @ApiOperation("获取病原级联")
    public List<TreeNode> getPathogenInfo() {

        return dimensionService.getPathogenInfo();
    }


    @PostMapping("/getTagsByDataSource")
    @ApiOperation("根据数据模型字段过滤标签名称")
    public List<String> getTagsByDataSource(@RequestBody List<String> dataSources) {
        return cdcDataServiceApi.getTagsByDataSource(dataSources);
    }

    @PostMapping("/getTagValuesByTag")
    @ApiOperation("根据数据模型字段&标签名称过滤标签值")
    public List<String> getTagValuesBy(String tag, @RequestBody List<String> dataSources) {
        return cdcDataServiceApi.getTagValuesBy(tag, dataSources);
    }


    @PostMapping("/searchDictValueBy")
    @ApiOperation("根据字典代码获取字典值域")
    public PageInfo<DictValueVO> searchDictValueBy(@RequestBody DictParams dictParams) {
        return cdcDataServiceApi.searchDictValueBy(dictParams);
    }

    @PostMapping("/searchDiagnoseValues")
    @ApiOperation("诊断字典值查询")
    public PageInfo<DictValueVO> searchDiagnoseValues(@RequestBody SearchParams searchParams) {
        DictParams dictParams = DictParams.builder().dictCode(diagnoseDictCode).dictValue(searchParams.getSearchValue()).build();
        dictParams.setPageIndex(searchParams.getPageIndex());
        dictParams.setPageSize(searchParams.getPageSize());
        return cdcDataServiceApi.searchDictValueBy(dictParams);
    }


}
