package com.iflytek.cdc.admin.outbound.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.iflytek.cdc.admin.outbound.common.executor.ManualCallPhoneExecutor;
import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.model.vo.ManualCallResultVo;
import com.iflytek.cdc.admin.outbound.service.CallHelperService;
import com.iflytek.cdc.admin.outbound.service.PhoneCallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 软电话外呼服务实现类
 **/
@Slf4j
@Service
@RefreshScope
public class PhoneCallServiceImpl implements PhoneCallService {

    private RestTemplate restTemplate = new RestTemplate();


    @Value("${cdc.phone.url:http://172.30.32.235:9000/open-api}")
    private String phoneUrl;

    @Value("${cdc.phone.appId:1300545634336768}")
    private String appId;

    @Value("${cdc.phone.secretKey:wt5Oyxa53aj9OQRW}")
    private String secretKey;

    @Resource
    private CallHelperService callHelperService;

    @Resource
    private ManualCallPhoneExecutor manualCallPhoneExecutor;

    /**
     * 获取签名参数
     *
     * @return
     */
    private String getSignParams() {
        Long timestamp = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        sb.append(appId).append(timestamp).append(secretKey);
        String sign = SecureUtil.md5(sb.toString());
        return "appId=" + appId + "&timestamp=" + timestamp + "&sign=" + sign;
    }

    @Override
    public String getAccount(String name, String telephone, String relationId) {
       return manualCallPhoneExecutor.getAccount(name, telephone, relationId);
    }

    @Override
    public String updateTime(String recordId) {
        return manualCallPhoneExecutor.updateTime(recordId);
    }

    @Override
    public String updateResult(String recordId, String relationId, ManualCallResultVo vo) {
        return manualCallPhoneExecutor.updateResult(recordId, relationId, vo);
    }

    @Override
    public String getRecordUrl(String audioName, String relationId) {
        try {
            //获取电话音频
            String urlResult = manualCallPhoneExecutor.getRecordUrl(relationId);
            if (StringUtils.isEmpty(urlResult)){
                return urlResult;
            }
            //判断音频是否已经存在
            int num = callHelperService.checkIsExistCaseAudio(urlResult, relationId);
            if(num > 0){
                return StrUtil.EMPTY;
            }
            //保存音频
            callHelperService.updateAudioUrl(urlResult, relationId);
            //解析转义
            callHelperService.transformAudio(urlResult, audioName, relationId);
            return urlResult;
        } catch (Exception e) {
            log.error("获取软电话音频地址异常！", e);
        }
        return StrUtil.EMPTY;
    }

    @Override
    public AudioRecord getRecord(String relationId) {
        return callHelperService.getCallAudio(relationId, "call");
    }

    @Override
    public void saveAudio(String tempText, String audioName, String relationId) {
        //保存音频
        callHelperService.saveAudio(tempText,audioName, relationId);
    }

}
