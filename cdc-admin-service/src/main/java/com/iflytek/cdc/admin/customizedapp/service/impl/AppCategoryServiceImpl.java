package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsAppCategory;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsAppCategoryMapper;
import com.iflytek.cdc.admin.customizedapp.service.AppCategoryService;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用分类;(tb_cdccs_app_category)表服务实现类
 */
@Service
public class AppCategoryServiceImpl extends CdcServiceBaseImpl<TbCdccsAppCategoryMapper, TbCdccsAppCategory> implements AppCategoryService {
    @Resource
    private TbCdccsAppCategoryMapper tbCdccsAppCategoryMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private AppService appService;

    @Override
    public List<TbCdccsAppCategory> listAll() {
        LambdaQueryWrapper<TbCdccsAppCategory> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.orderByAsc(TbCdccsAppCategory::getOrderSeq);
        return list(lambdaQuery);
    }

    @Override
    public void beforeCreate(TbCdccsAppCategory entity) {
        duplicate(entity,  "分类编码重复", TbCdccsAppCategory::getCategoryCode);
        super.beforeCreate(entity);
    }

    @Override
    public void beforeUpdate(TbCdccsAppCategory entity) {
        duplicate(entity,  "分类编码重复", TbCdccsAppCategory::getCategoryCode);
        super.beforeUpdate(entity);
    }

    @Override
    public void afterDelete(String id) {
        super.afterDelete(id);
        appService.deleteByCategoryId(id);
    }
}