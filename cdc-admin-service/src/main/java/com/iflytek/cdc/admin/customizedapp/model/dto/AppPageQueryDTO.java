package com.iflytek.cdc.admin.customizedapp.model.dto;

import com.iflytek.cdc.admin.dto.PageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("应用页面查询条件")
public class AppPageQueryDTO extends PageInfoDTO {
    @ApiModelProperty("应用id")
    private String appId;
    @ApiModelProperty("关键词查询")
    private String queryKey;
}
