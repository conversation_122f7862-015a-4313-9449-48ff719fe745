package com.iflytek.cdc.admin.datamodel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcdmMetadataTableInfoMapper extends BaseMapper<TbCdcdmMetadataTableInfo> {

    TbCdcdmMetadataTableInfo getTableInfoById(@Param("tableId") String tableId);

    List<TbCdcdmMetadataTableInfo> getTableInfoByIds(@Param("ids") List<String> ids);

    List<TbCdcdmMetadataTableInfo> getTableInfoByTableName(@Param("tableName") String tableName);

    List<TbCdcdmMetadataTableInfo> getTableInfoByTableNames(@Param("tableNames") List<String> tableName);

    List<TbCdcdmMetadataTableInfo> getAllTableInfo();
}