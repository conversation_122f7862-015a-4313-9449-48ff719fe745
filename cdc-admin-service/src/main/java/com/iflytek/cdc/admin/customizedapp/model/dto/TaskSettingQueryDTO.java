package com.iflytek.cdc.admin.customizedapp.model.dto;

import com.iflytek.cdc.admin.dto.PageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("分页查询")
public class TaskSettingQueryDTO extends PageInfoDTO {
    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("任务名称")
    private String taskName;
}
