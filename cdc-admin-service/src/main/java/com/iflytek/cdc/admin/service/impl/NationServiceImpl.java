package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.entity.UapNation;
import com.iflytek.cdc.admin.entity.NationInfo;
import com.iflytek.cdc.admin.service.NationService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.cdc.admin.util.StringEmptyUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.zhyl.uap.usercenter.entity.TUapNation;
import com.iflytek.zhyl.uap.usercenter.service.UapNationApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/12 10:31
 **/
@Service
@Slf4j
public class NationServiceImpl implements NationService {

    private final UapNationApi uapNationApi;

    private final UapUserService uapUserService;

    public NationServiceImpl(UapNationApi uapNationApi,UapUserService uapUserService) {
        this.uapNationApi = uapNationApi;
        this.uapUserService = uapUserService;
    }

    @Override
    public NationInfo queryAllNationInfo(String code) {
        NationInfo nationInfo = new NationInfo();
        try {
            TUapNation uapNation = uapNationApi.getNationByCode(code);
            formatNation(nationInfo, uapNation);
            //判断当前区域等级
            //如果为省级
            if (StringUtils.isBlank(uapNation.getDistrict()) && StringUtils.isBlank(uapNation.getCity())) {
                //获取市级
                List<TUapNation> cityList = uapNationApi.getOrgType(code);
                //数据格式化形成新的list
                List<NationInfo> cityNation = new ArrayList<>();
                if (CollUtil.isNotEmpty(cityList)) {
                    cityList.forEach(e -> {
                        //格式转换
                        NationInfo n = new NationInfo();
                        n.setLabel(StringEmptyUtil.notEmpty(e.getProvince(), e.getCity(), e.getDistrict()));
                        n.setValue(e.getCode());
                        //获取区级
                        List<TUapNation> districtList = uapNationApi.getOrgType(e.getCode());
                        if (CollUtil.isNotEmpty(districtList)) {
                            //区级格式转换
                            formatChildList(n, districtList);
                        }
                        cityNation.add(n);
                    });
                }
                nationInfo.setChild(cityNation);
            }
            //如果是为市级
            if (StringUtils.isBlank(uapNation.getDistrict()) && StringUtils.isNotBlank(uapNation.getCity())) {
                //获取区级
                List<TUapNation> districtList = uapNationApi.getOrgType(code);
                //区级内容转换
                formatChildList(nationInfo, districtList);
            }
        } catch (Exception e) {
            log.error("查询下级行政区域异常:{}", e);
            throw new MedicalBusinessException("查询下级行政区域异常");
        }
        return nationInfo;
    }

    @Override
    public NationInfo queryAllNationInfoByUserId(String userId) {
       return queryAllNationInfo(uapUserService.getUserInfo(userId).getAreaCode());
    }

    @Override
    public UapNation getNations(String code, AreaLevelEnum targetLvlEnum) {

        TUapNation tUapNation = uapNationApi.getNationByCode(code);
        Queue<UapNation> queue = new LinkedList<>();

        if (tUapNation != null) {
            UapNation nation = UapNation.buildFrom(tUapNation);
            queue.offer(nation);

            while (!queue.isEmpty()) {
                UapNation currNation = queue.poll();
                if (currNation != null && targetLvlEnum.isBelow(currNation.getLevel())) {
                    List<TUapNation> childUapNations = uapNationApi.getChildUapNationByCode(currNation.getCode());
                    if (CollUtil.isNotEmpty(childUapNations)) {
                        for (TUapNation childUapNation : childUapNations) {
                            UapNation childNation = UapNation.buildFrom(currNation, childUapNation);
                            queue.offer(childNation);
                            currNation.addChildNation(childNation);
                        }
                    }
                }
            }
            return nation;
        }

        return new UapNation();
    }

    private void formatNation(NationInfo nationInfo, TUapNation uapNation) {
        if (uapNation != null) {
            //根据省，市，区哪个不为空，判断属于哪一级
            nationInfo.setLabel(StringEmptyUtil.notEmpty(uapNation.getProvince(), uapNation.getCity(), uapNation.getDistrict()));
            nationInfo.setValue(uapNation.getCode());
        }
    }

    private void formatChildList(NationInfo nationInfo, List<TUapNation> districtList) {
        nationInfo.setChild(districtList.stream().map(p -> {
            NationInfo ni = new NationInfo();
            //根据省，市，区哪个不为空，判断属于哪一级
            ni.setLabel(StringEmptyUtil.notEmpty(p.getProvince(), p.getCity(), p.getDistrict()));
            ni.setValue(p.getCode());
            return ni;
        }).collect(Collectors.toList()));
    }
}
