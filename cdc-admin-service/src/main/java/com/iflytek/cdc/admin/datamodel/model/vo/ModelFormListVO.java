package com.iflytek.cdc.admin.datamodel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ModelFormListVO {

    @ApiModelProperty(value = "表单id")
    private String modelFormId;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "是否可重复")
    private Integer isRepeat;

    @ApiModelProperty(value = "重复最大次数")
    private Integer maxRepeatCnt;

    @ApiModelProperty(value = "表单唯一标识")
    private String formIdentityColumn;

    @ApiModelProperty(value = "表单json")
    private String formJson;

    @ApiModelProperty(value = "引用模型的模型id，若没有引用为空")
    private String quoteModel;

    @ApiModelProperty(value = "表单是否已删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateTime;
     
}
