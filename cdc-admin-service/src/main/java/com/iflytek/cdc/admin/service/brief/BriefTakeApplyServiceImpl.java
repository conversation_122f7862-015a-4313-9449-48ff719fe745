package com.iflytek.cdc.admin.service.brief;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.common.vo.uap.UapOrgPo;
import com.iflytek.cdc.admin.common.vo.uap.UapUserPo;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyApproveDto;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyQueryDto;
import com.iflytek.cdc.admin.entity.brief.BriefTakeApply;
import com.iflytek.cdc.admin.mapper.brief.BriefTakeApplyMapper;
import com.iflytek.cdc.admin.vo.brief.BriefTakeApplyVo;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BriefTakeApplyServiceImpl extends ServiceImpl<BriefTakeApplyMapper, BriefTakeApply> implements BriefTakeApplyService {


    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private BriefTakeApplyMapper briefTakeApplyMapper;

    @Autowired
    public void setBriefTakeApplyMapper(BriefTakeApplyMapper briefTakeApplyMapper) {
        this.briefTakeApplyMapper = briefTakeApplyMapper;
    }

    @Override
    public BriefTakeApply create(BriefTakeApply briefTakeApply, String loginUserId) {
        if (briefTakeApply == null){
            log.error("缺少申请参数");
            return new BriefTakeApply();
        }
        String templateId = briefTakeApply.getTemplateId();
        if (StrUtil.isBlank(templateId)){
            log.error("缺少模板");
            return new BriefTakeApply();
        }


        LambdaQueryWrapper<BriefTakeApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BriefTakeApply::getCreatorId,loginUserId).eq(BriefTakeApply::getTemplateId,templateId);
        BriefTakeApply one = this.getOne(wrapper);
        if (one != null){
            log.error("申请已存在");
            return new BriefTakeApply();
        }

        UapUserPo user = uapServiceApi.getUser(loginUserId);
        briefTakeApply.setId(String.valueOf(batchUidService.getUid(BriefTakeApply.TB_NAME)));
        briefTakeApply.setCreatorId(user.getId());
        briefTakeApply.setCreator(user.getName());
        briefTakeApply.setCreateTime(new Date());
        briefTakeApply.setUpdatorId(user.getId());
        briefTakeApply.setUpdator(user.getName());
        briefTakeApply.setUpdateTime(new Date());

        briefTakeApply.setApproveStatus(0);
        briefTakeApply.setApplyTime(new Date());

        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            briefTakeApply.setDistrictName(district);
            briefTakeApply.setDistrictCode(userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            briefTakeApply.setCityName(city);
            briefTakeApply.setCityCode(userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            briefTakeApply.setProvinceName(province);
            briefTakeApply.setProvinceCode(userOrgpo.getProvinceCode());
        }
        // 判断
        if (StrUtil.isNotBlank(province) && StrUtil.isBlank(city)){
            log.error("省级账号不可申请");
            return new BriefTakeApply();
        }

        BriefTakeApply.UserData userData = new BriefTakeApply.UserData();
        userData.setUserId(user.getId());
        userData.setUserName(user.getName());
        userData.setPhoneNum(user.getPhone());
        briefTakeApply.setApplyUser(userData);

        try {
            this.save(briefTakeApply);
            return briefTakeApply;
        } catch (Exception e) {
            throw new MedicalBusinessException("新增报告订阅申请失败");
        }
    }

    @Override
    public BriefTakeApplyVo queryById(String id, String loginUserId) {
        if (StrUtil.isBlank(id)){
            log.error("缺少申请id");
            return new BriefTakeApplyVo();
        }
        return briefTakeApplyMapper.queryById(id);
    }

    @Override
    public PageInfo<BriefTakeApplyVo> queryList(BriefTakeApplyQueryDto dto, String loginUserId) {
        if (dto == null){
            return new PageInfo<>();
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());

        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            dto.setDistrictCode(userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            dto.setCityCode(userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            dto.setProvinceCode(userOrgpo.getProvinceCode());
        }
        List<BriefTakeApplyVo> list = briefTakeApplyMapper.queryList(dto);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return  new PageInfo<>(list);
    }

    @Override
    public Boolean updateApprove(BriefTakeApplyApproveDto dto, String loginUserId) {
        if (dto == null) {
            return false;
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)) {
            return false;
        }
        Integer approveStatus = dto.getApproveStatus();
        if (approveStatus == null) {
            return false;
        }
        if (approveStatus == 2){
            String approveMark = dto.getApproveMark();
            if (StrUtil.isBlank(approveMark)){
                throw new MedicalBusinessException("申请驳回时,填写驳回原因");
            }
        }
        BriefTakeApply briefTakeApply = this.getById(id);
        if (briefTakeApply.getApproveStatus() != 0){
            throw new MedicalBusinessException("申请已审批,不可重复审批");
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);

        // 判断账号
        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        String districtCode = userOrgpo.getDistrictCode();
        // 区账号
        if (StrUtil.isNotBlank(districtCode)) {
            throw new MedicalBusinessException("区级账号,不可审批");
        }
        String cityCode = userOrgpo.getCityCode();
        // 市账号
        if (StrUtil.isNotBlank(cityCode)){
            String districtCode1 = briefTakeApply.getDistrictCode();
            if (StrUtil.isBlank(districtCode1)){
                throw new MedicalBusinessException("市级账号,只可审批区级");
            }
        }
        briefTakeApply.setUpdateTime(new Date());
        briefTakeApply.setUpdatorId(user.getId());
        briefTakeApply.setUpdator(user.getName());

        briefTakeApply.setApproveTime(new Date());
        if (approveStatus == 2){
            briefTakeApply.setApproveMark(dto.getApproveMark());
        }
        briefTakeApply.setApproveStatus(approveStatus);

        BriefTakeApply.UserData userData = new BriefTakeApply.UserData();
        userData.setUserId(user.getId());
        userData.setUserName(user.getName());
        userData.setPhoneNum(user.getPhone());
        briefTakeApply.setApplyUser(userData);
        return this.updateById(briefTakeApply);
    }
}
