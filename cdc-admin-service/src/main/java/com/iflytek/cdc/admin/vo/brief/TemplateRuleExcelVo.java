package com.iflytek.cdc.admin.vo.brief;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;


@Data
public class TemplateRuleExcelVo {

	@ExcelProperty("简报生成规则ID")
	private String id;

	@ExcelProperty("区域分布")
	private String region;

	@ExcelProperty("统计周期")
	private String statisticsCycleDesc;

	@ExcelProperty("简报模板")
	private String title;


	@ExcelProperty("最近更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	@ExcelProperty("最近更新人")
	private String updatorName;

	@ExcelProperty("状态")
	private String status;

}
