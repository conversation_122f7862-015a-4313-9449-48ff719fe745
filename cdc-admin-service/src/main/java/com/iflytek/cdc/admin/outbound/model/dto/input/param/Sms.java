package com.iflytek.cdc.admin.outbound.model.dto.input.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 短信对象
 * <AUTHOR>
 */
@Data
public class Sms {

    @ApiModelProperty(value = "短信id（医疗提供）", required = true)
    private String smsId;

    @ApiModelProperty(value = "短信提醒条件1-未接通 2-接通 3-全部", required = true)
    private Integer smsCondition;

    @ApiModelProperty(value = "短信动态参数")
    private List<Var> smsVars;

}
