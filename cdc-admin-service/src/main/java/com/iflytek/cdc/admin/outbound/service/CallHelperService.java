package com.iflytek.cdc.admin.outbound.service;

import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.admin.outbound.entity.AudioRecord;
import com.iflytek.cdc.admin.outbound.model.CustomMappingJackson2HttpMessageConverter;
import com.iflytek.cdc.admin.outbound.model.ExtractWordRequest;
import com.iflytek.cdc.admin.outbound.model.ExtractWordResponse;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.*;

/**
 * @Description 电话流调辅助服务类
 * <AUTHOR> Lee
 * @Date 2022/5/13
 */
@Slf4j
@Service
@RefreshScope
public class CallHelperService {

    @Value("${extract.word.url:http://*************:10011/ies}")
    private String url;

    @Value("${extract.word.subParams:{\"nbest\":1,\"encode\":\"utf-8\",\"resType\":\"dadongqu\",\"type\":\"intent|semantic\"}}")
    private String subParams;

    @Value("${cdc.phone.oldUrl:https://bjb.openstorage.cn}")
    private String oldUrl;

    @Value("${cdc.phone.newUrl:https://bjb.openstorage.cn}")
    private String newUrl;

    @Resource
    private AudioService audioService;


    private RestTemplate restTemplate;

    public CallHelperService() {
        this.restTemplate = new RestTemplate();
        this.restTemplate.getMessageConverters().add(new CustomMappingJackson2HttpMessageConverter());
    }

//    @Transactional
//    public void uploadAudio(String caseId, String audioName, String tempResult, MultipartFile file) {
//        try {
//            if (null != file) {
//                if(!file.getContentType().contains("audio/wav")){
//                    // tempResult——音频实时解析文本
//                    InputStream in = file.getInputStream();
//                    file = new MockMultipartFile("file", UUID.randomUUID() + ".wav", "audio/wav", IOUtils.toByteArray(in));
//                }
//                audioService.uploadAudio(caseId, audioName, tempResult, "audio", file, null);
//            }
//        } catch (Exception e) {
//            log.error("上传语音流调音频失败", e);
//            throw new MedicalBusinessException("上传语音流调音频失败");
//        }
//    }


    public void saveAudio(String tempText, String audioName,  String relationId){
        audioService.saveAudio(tempText, audioName, relationId);
    }

    public void updateAudioUrl(String url, String relationId){
        audioService.updateAudioUrl(url,relationId);
    }

    public int checkIsExistCaseAudio(String url, String relationId){
        return audioService.checkIsExistCaseAudio(url,relationId);
    }

    @Async("asyncExecutor")
    public void transformAudio(String url, String audioName, String relationId) {
        try (InputStream in = new BufferedInputStream(new URL(url.replace(oldUrl, newUrl)).openStream())) {
            MultipartFile multipartFile = new MockMultipartFile("file", UUID.randomUUID() + ".wav", "audio/wav", IOUtils.toByteArray(in));
            //获取relationId对应的url的text
            String text = audioService.getTempTextByPathAndCaseId(url, relationId);
            audioService.uploadAudio(relationId, audioName,  text, "call", multipartFile, url);
            //删除当前url记录
            audioService.deleteByPathAndCaseId(url, relationId);
        } catch (Exception e) {
            log.error("上传电话流调音频失败", e);
            //更新个案已经获取到url
            //重置当前url记录
            audioService.updateByPathAndCaseId(url, relationId);
            throw new MedicalBusinessException("上传电话流调音频失败");
        }
    }


    public AudioRecord getCallAudio(String caseId, String audioAdress) {
        return audioService.getCallAudio(caseId,audioAdress);
    }

    private String changeKey(String oldKey) {
        if (StrUtil.equals(oldKey, "pname")) {
            return "province";
        }

        if (StrUtil.equals(oldKey, "cityKey")) {
            return "city";
        }

        if (StrUtil.equals(oldKey, "adname")) {
            return "district";
        }

        if (StrUtil.equals(oldKey, "town")) {
            return "street";
        }

        if (StrUtil.equals(oldKey, "address")) {
            return "curAddr";
        }

        return oldKey;
    }

    private String getSid() {
        String sid = null;
        try {
            ExtractWordRequest request = getRequest(1, "ssb");
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            ExtractWordResponse response = restTemplate.postForObject(url+"/", new HttpEntity<>(request, headers), ExtractWordResponse.class);
            sid = response.getResult().getSid();
        } catch (Exception e) {
            log.error("调用语义提取ssb接口失败", e);
        }
        return sid;
    }

    private ExtractWordRequest getRequest(int id, String cmd) {
        ExtractWordRequest request = new ExtractWordRequest();
        request.setId(id);
        request.setJsonrpc("2.0");
        request.setMethod("deal_request");
        ExtractWordRequest.Params params = new ExtractWordRequest.Params();
        params.setCmd(cmd);
        params.setSvc("ies");
        request.setParams(params);
        return request;
    }
}
