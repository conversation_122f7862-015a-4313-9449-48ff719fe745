package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.GaodeContourCacheInfoVO;
import com.iflytek.cdc.admin.entity.TbCdcewGaodeContourCache;
import com.iflytek.cdc.admin.mapper.TbCdcewGaodeContourCacheMapper;
import com.iflytek.cdc.admin.service.GaodeContourCacheService;
import com.iflytek.cdc.admin.util.HttpClientUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GaodeContourCacheServiceImpl extends ServiceImpl<TbCdcewGaodeContourCacheMapper, TbCdcewGaodeContourCache>
        implements GaodeContourCacheService {

    @Autowired
    private BatchUidService uidService;
    @Autowired
    private HttpClientUtils httpClientUtils;
    @Value("${amap.key}")
    private String key;
    @Value("${amap.url:https://restapi.amap.com}")
    private String amapUrl;

    @Override
    public GaodeContourCacheInfoVO getCacheData(String keywords, String subdistrict, String extensions) {
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("请配置高德key：" + key);
        }
        TbCdcewGaodeContourCache cache = baseMapper.selectOneByCondition(key, keywords, subdistrict, extensions);
        String jsonData;
        if (ObjectUtil.isNotEmpty(cache)) {
            jsonData = cache.getJsonData();
        } else {
            jsonData = getGaodeContourCache(key, keywords, subdistrict, extensions);
            if (StringUtils.isBlank(jsonData)) {
                return new GaodeContourCacheInfoVO();
            }
            TbCdcewGaodeContourCache cacheVo = buildEntity(key, keywords, subdistrict, extensions, jsonData);
            save(cacheVo);
        }
        GaodeContourCacheInfoVO resultVo = JSONObject.parseObject(jsonData, GaodeContourCacheInfoVO.class);
        resultVo.setSuggestionInfo(JSONObject.parseObject(resultVo.getSuggestion(), GaodeContourCacheInfoVO.SuggestionInfo.class));
        resultVo.setDistrictList(JSONObject.parseArray(resultVo.getDistricts(), GaodeContourCacheInfoVO.DistrictInfo.class));
        resultVo.setSuggestion("");
        resultVo.setDistricts("");
        return resultVo;
    }

    public TbCdcewGaodeContourCache buildEntity(String key, String keywords, String subdistrict, String extensions,
                                                String jsonData) {
        TbCdcewGaodeContourCache cacheVo = new TbCdcewGaodeContourCache();
        cacheVo.setId(String.valueOf(uidService.getUid("tb_cdcew_gaode_contour_cache")));
        cacheVo.setKey(key);
        cacheVo.setKeywords(keywords);
        cacheVo.setSubdistrict(subdistrict);
        cacheVo.setExtensions(extensions);
        cacheVo.setJsonData(jsonData);
        cacheVo.setCreateDatetime(LocalDateTimeUtil.now());
        cacheVo.setUpdateDatetime(LocalDateTimeUtil.now());
        cacheVo.setDeleteFlag(Constants.COMMON_STRNUM_ZERO);
        return cacheVo;
    }

    public String getGaodeContourCache(String key, String keywords, String subdistrict, String extensions) {
        String url = mergeString(amapUrl,"/v3/config/district", "?keywords=", keywords, "&key=",
                key, "&subdistrict=", subdistrict, "&extensions=", extensions);
        return httpClientUtils.httpGet(url);
    }

    public static String mergeString(Object... objs) {
        StringBuilder stringBuilder = new StringBuilder();
        for(int i = 0; i < objs.length; ++i) {
            if (objs[i] == null) {
                continue;
            }
            String key = String.valueOf(objs[i]);
            if (StringUtils.isNotEmpty(key)) {
                stringBuilder.append(key);
            }
        }
        return stringBuilder.toString();
    }
}
