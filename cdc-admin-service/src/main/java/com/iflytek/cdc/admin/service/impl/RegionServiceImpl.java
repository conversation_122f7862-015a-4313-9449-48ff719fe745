package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.constant.AreaLevelEnum;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.GroupRegionDTO;
import com.iflytek.cdc.admin.dto.GroupRegionInsertDTO;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.RegionParam;
import com.iflytek.cdc.admin.dto.UploadResultVO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressFuzzyNormalizationDTO;
import com.iflytek.cdc.admin.dto.addressstandardize.AddressStandardVo;
import com.iflytek.cdc.admin.dto.addressstandardize.RegionQueryDTO;
import com.iflytek.cdc.admin.entity.NationStructMapper;
import com.iflytek.cdc.admin.entity.TbCdcmrRegion;
import com.iflytek.cdc.admin.entity.UapNation;
import com.iflytek.cdc.admin.entity.UapUserExtend;
import com.iflytek.cdc.admin.mapper.TbCdcmrRegionMapper;
import com.iflytek.cdc.admin.service.AmapService;
import com.iflytek.cdc.admin.service.NationService;
import com.iflytek.cdc.admin.service.ParamConfigService;
import com.iflytek.cdc.admin.service.RegionService;
import com.iflytek.cdc.admin.service.UapUserService;
import com.iflytek.cdc.admin.util.DateUtils;
import com.iflytek.cdc.admin.util.ExcelUtils;
import com.iflytek.cdc.admin.vo.CascadeVO;
import com.iflytek.cdc.admin.vo.region.GroupRegionFileVO;
import com.iflytek.cdc.admin.vo.region.GroupRegionVO;
import com.iflytek.cdc.admin.vo.LabelValueVO;
import com.iflytek.cdc.admin.vo.region.RegionCascadeVO;
import com.iflytek.cdc.admin.vo.region.RegionStructMapper;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
@Service
@Slf4j
public class RegionServiceImpl implements RegionService {

    public static final int BATCH_SIZE = 1000;

    //    来源:1.uap, 2.built_in(系统内置), 3.edit(用户上传/编辑), 4.external(外部系统接口新增)
    private static final String DATA_SOURCE_UAP = "uap";
    private static final String DATA_SOURCE_EDIT = "edit";
    private static final String DATA_SOURCE_EXTERNAL = "external";

    @Resource
    BatchUidService batchUidService;

    @Resource
    NationService nationService;

    @Resource
    TbCdcmrRegionMapper tbCdcmrRegionMapper;

    @Resource
    UapUserService uapUserService;

    @Resource
    AmapService amapService;

    @Resource
    private ParamConfigService paramConfigService;

    private final Cache<String, UapUserExtend> USER_CACHE = CacheBuilder.newBuilder()
                                                                        .expireAfterWrite(12, TimeUnit.HOURS)
                                                                        .build();

    @Value("${localRegionCode:}")
    private String localRegionCode;
    @Value("${uap.nation.level:#{T(com.iflytek.cdc.admin.constant.AreaLevelEnum).VILLAGE.getLevel()}}")
    private int uapNationLevel;

    /**
     * 1. 后台增量同步UAP区划, 默认同步配置省份, 到区县Level; 未配置则同步全国区县
     * 2. 调用高德API解析区划数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncUapNationByTime(Date date) {

        UapNation currNation = nationService.getNations(localRegionCode, AreaLevelEnum.level(uapNationLevel));
        List<UapNation> nations = UapNation.flatToList(currNation);
        List<UapNation> updateNations = nations.stream()
                                               .filter(t -> DateUtils.isAfter(t.getLastUpdateTime(), date) ||
                                                            DateUtils.isAfter(t.getCreateTime(), date))
                                               .collect(Collectors.toList());

        List<TbCdcmrRegion> tbCdcmrRegions = this.buildRegionsFromUapNation(updateNations);

        this.processAmapApi(tbCdcmrRegions);

        this.batchUpsert(tbCdcmrRegions);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncUapNation(String code, int targetLevel) {
        AreaLevelEnum toLevelEnum = AreaLevelEnum.level(targetLevel);

        // 1. 获取UAP区划
        UapNation currNation = nationService.getNations(code, toLevelEnum);
        List<UapNation> nations = UapNation.flatToList(currNation);
        if (nations.isEmpty()) {
            log.info("没有需要同步的UAP区划数据, code:{}, toLevel:{}", code, targetLevel);
            return;
        }
        List<TbCdcmrRegion> tbCdcmrRegionList = buildRegionsFromUapNation(nations);

        this.processAmapApi(tbCdcmrRegionList);

        this.batchMaintain(code, currNation.getLevel(), targetLevel, tbCdcmrRegionList);
    }

    @Override
    public void amapAnalysis(String areaCode, int targetLevel, Date startTime, Date endTime) {
        TbCdcmrRegion currRegion = tbCdcmrRegionMapper.selectByCode(areaCode);
        if (currRegion == null) {
            return;
        }

        Integer currLevel = currRegion.getRegionLevel();
        List<TbCdcmrRegion> tbCdcmrRegions = tbCdcmrRegionMapper.selectByCodeLevelUpdateTime(areaCode, currLevel, targetLevel, startTime, endTime);

        // 调用高德API解析
        this.processAmapApi(tbCdcmrRegions);

        // 更新数据
        tbCdcmrRegionMapper.batchUpdateAmapInfos(tbCdcmrRegions);

    }

    @Override
    public PageData<GroupRegionVO> regionSearch(String loginUserId, RegionQueryDTO regionQueryDTO) {
//        regionQueryDTO.setSplitCityCodeList(getSplitList(regionQueryDTO.getCityCode()));
//        regionQueryDTO.setSplitDistrictCodeList(getSplitList(regionQueryDTO.getDistrictCode()));

        RegionQueryDTO userAuthQueryDTO = buildUserAuthQueryDTO(loginUserId, regionQueryDTO);

        PageMethod.startPage(userAuthQueryDTO.getPageIndex(), userAuthQueryDTO.getPageSize())
                  .setOrderBy("update_datetime desc");

        List<GroupRegionVO> result = tbCdcmrRegionMapper.regionSearch(userAuthQueryDTO);
        List<String> streetCodes = result.stream().map(GroupRegionVO::getStreetCode).distinct().collect(Collectors.toList());
        Map<String, TbCdcmrRegion> streetAliasMap = tbCdcmrRegionMapper.selectByCodesLevel(streetCodes, AreaLevelEnum.STREET.getLevel())
                                                                       .stream()
                                                                       .collect(Collectors.toMap(TbCdcmrRegion::getRegionCode, Function.identity()));
        Map<String, UapUserExtend> uapUserExtendMap = new HashMap<>();
        for (GroupRegionVO groupRegionVO : result) {
            String aliasName = Optional.ofNullable(streetAliasMap.get(groupRegionVO.getStreetCode())).map(TbCdcmrRegion::getAliasName).orElse("");
            groupRegionVO.setStreetAliasName(aliasName);
            groupRegionVO.setCreator(getUapUserName(groupRegionVO.getCreator()));
            groupRegionVO.setUpdater(getUapUserName(groupRegionVO.getUpdater()));
        }

        PageInfo<GroupRegionVO> pageInfo = new PageInfo<>(result);
        PageData<GroupRegionVO> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(result);
        return pageData;
    }

    private String getUapUserName(String loginUserId) {
        String uapUserName = "";
        try {
            UapUserExtend uapUserExtend = USER_CACHE.get(loginUserId, () -> uapUserService.getUserInfo(loginUserId));
            uapUserName = uapUserExtend.getName();
        } catch (Exception e) {
            log.error("获取UAP用户失败: ", e);
        }
        return uapUserName;
    }

    private RegionQueryDTO buildUserAuthQueryDTO(String loginUserId, RegionQueryDTO regionQueryDTO) {
        RegionParam userAuthParam = this.getUserAuthParam(loginUserId, regionQueryDTO.getRegionLevel());

        if (StringUtils.isNotEmpty(userAuthParam.getProvinceCode()) && StringUtils.isEmpty(regionQueryDTO.getProvinceCode())) {
            regionQueryDTO.setProvinceCode(userAuthParam.getProvinceCode());
        }
        if (StringUtils.isNotEmpty(userAuthParam.getCityCode()) && StringUtils.isEmpty(regionQueryDTO.getCityCode())) {
            regionQueryDTO.setCityCode(userAuthParam.getCityCode());
        }
        if (StringUtils.isNotEmpty(userAuthParam.getProvinceCode()) && StringUtils.isEmpty(regionQueryDTO.getDistrictCode())) {
            regionQueryDTO.setDistrictCode(userAuthParam.getDistrictCode());
        }
        return regionQueryDTO;
    }

    @Override
    public void insertGroupRegion(String loginUserId, GroupRegionInsertDTO groupRegionInsertDTO) {
        LabelValueVO villageLabel = groupRegionInsertDTO.getVillageLabel();
        String villageCode = villageLabel.getValue();
        TbCdcmrRegion villageRegion = tbCdcmrRegionMapper.selectByCode(villageCode);
        if (villageRegion == null) {
            throw new MedicalBusinessException("该社区/村信息不存在!");
        }
        String groupName = groupRegionInsertDTO.getGroupName();
        TbCdcmrRegion existedGroup = tbCdcmrRegionMapper.selectByParentCodeAndName(villageCode, groupName);
        if (existedGroup != null) {
            throw new MedicalBusinessException("该社区/村下已存在相同小区/组!");
        }


//        TbCdcmrRegion groupEntity = RegionStructMapper.INSTANCE.buildGroupRegionFrom(groupRegionDTO);
        TbCdcmrRegion tbCdcmrRegion = RegionStructMapper.INSTANCE.copy(villageRegion);
        tbCdcmrRegion.setRegionLevel(AreaLevelEnum.GROUP.getLevel());
        tbCdcmrRegion.setId(String.valueOf(batchUidService.getUid("tb_cdcmr_region")));

        tbCdcmrRegion.setRegionCode(tbCdcmrRegion.getId());
        tbCdcmrRegion.setRegionName(groupName);
        tbCdcmrRegion.setGroupCode(tbCdcmrRegion.getId());  // 用户编辑无code, 用ID填充
        tbCdcmrRegion.setGroupName(groupName);
        tbCdcmrRegion.setAliasName(groupRegionInsertDTO.getGroupAliasName());
        tbCdcmrRegion.setParentRegionCode(villageCode);
        tbCdcmrRegion.setAddressDetail(tbCdcmrRegion.generateAddressDetail());

        tbCdcmrRegion.setDataSource(DATA_SOURCE_EDIT);
        tbCdcmrRegion.setIsEnable(Constants.YES_USE);
        tbCdcmrRegion.setUpdater(loginUserId);
        tbCdcmrRegion.setCreator(loginUserId);
        tbCdcmrRegion.setCreateDatetime(new Date());
        tbCdcmrRegion.setUpdateDatetime(new Date());
        tbCdcmrRegion.setDeleteFlag(Constants.NO_DELETE);

        tbCdcmrRegionMapper.insert(tbCdcmrRegion);
    }

    @Override
    public void updateGroupRegion(String loginUserId, GroupRegionDTO groupRegionDTO) {
        TbCdcmrRegion tbCdcmrRegion = RegionStructMapper.INSTANCE.buildGroupRegionFrom(groupRegionDTO);

        tbCdcmrRegion.setDataSource(DATA_SOURCE_EDIT);
//        tbCdcmrRegion.setIsEnable(Constants.YES_USE);
        tbCdcmrRegion.setUpdater(loginUserId);
        tbCdcmrRegion.setDeleteFlag(Constants.NO_DELETE);
        tbCdcmrRegion.setUpdateDatetime(new Date());

        tbCdcmrRegionMapper.updateGroupRegion(tbCdcmrRegion);
    }

    @Override
    public void enableGroupRegion(String loginUserId, String id, String isEnable) {
        tbCdcmrRegionMapper.enableGroupRegion(loginUserId, id, isEnable);
    }

    @Override
    public void deleteGroupRegion(String loginUserId, String id) {
        tbCdcmrRegionMapper.softDeleteById(loginUserId, id);
    }


    @Override
    public ResponseEntity<byte[]> exportGroupRegionBy(String loginUserId, RegionQueryDTO regionQueryDTO) {

        RegionQueryDTO userAuthQueryDTO = this.buildUserAuthQueryDTO(loginUserId, regionQueryDTO);
        List<GroupRegionVO> result = tbCdcmrRegionMapper.regionSearch(userAuthQueryDTO);

        //校验是否超出文件导出最大值
        paramConfigService.checkExportMax(result);

        List<GroupRegionFileVO> regionFileVOs = result.stream().map(RegionStructMapper.INSTANCE::voToFileVO).collect(Collectors.toList());

        String fileName = Constants.GROUP_REGION_EXPORT_NAME + "-" + DateUtil.now() + ".xlsx";
        return ExcelUtils.exportExcel(regionFileVOs, GroupRegionFileVO.class, true, fileName);
    }

    @Override
    public RegionCascadeVO getUserRegionLabel(String loginUserId, int fromLevel, int toLevel) {

        RegionParam regionParam = getUserAuthParam(loginUserId, toLevel);

        List<TbCdcmrRegion> tbCdcmrRegions = tbCdcmrRegionMapper.selectRecurseByParam(regionParam, fromLevel, toLevel);

        List<CascadeVO> fromLevelList = tbCdcmrRegions.stream()
                                                      .filter(t -> fromLevel == t.getRegionLevel())
                                                      .map(t -> new CascadeVO(t.getRegionName(), t.getRegionCode()))
                                                      .sorted().collect(Collectors.toList());

        RegionCascadeVO regionCascadeVO = new RegionCascadeVO(fromLevel, toLevel, fromLevelList);

        // build cascade result
        Map<String, List<TbCdcmrRegion>> parentChildMap = tbCdcmrRegions.stream().collect(Collectors.groupingBy(TbCdcmrRegion::getParentRegionCode));
        LinkedList<CascadeVO> toBuildList = Lists.newLinkedList(fromLevelList);
        while (CollectionUtil.isNotEmpty(toBuildList)) {
            CascadeVO currVO = toBuildList.pop();
            List<TbCdcmrRegion> children = parentChildMap.get(currVO.getValue());
            if (CollectionUtil.isNotEmpty(children)) {
                List<CascadeVO> childrenVOs = children.stream().map(t -> new CascadeVO(t.getRegionName(), t.getRegionCode()))
                                                      .sorted().collect(Collectors.toList());

                toBuildList.addAll(childrenVOs);
                // build cascade
                currVO.setChildren(childrenVOs);
            }
        }


        return regionCascadeVO;
    }

    @Override
    public List<LabelValueVO> getChildLabelByUserCode(String loginUserId, String regionCode) {

        // regionCode为空, 查询省级
        int childLevel = AreaLevelEnum.PROVINCE.getLevel();

        if (StringUtils.isNotEmpty(regionCode)) {
            TbCdcmrRegion tbCdcmrRegion = tbCdcmrRegionMapper.selectByCode(regionCode);
            if (tbCdcmrRegion == null) {
                return new ArrayList<>();
            }
            childLevel = AreaLevelEnum.childLevel(tbCdcmrRegion.getRegionLevel()).getLevel();
        }

        RegionParam regionParam = this.getUserAuthParam(loginUserId, childLevel);
        regionParam.setParentRegionCode(regionCode);
        List<TbCdcmrRegion> tbCdcmrRegions = tbCdcmrRegionMapper.selectByParam(regionParam, childLevel);

        return tbCdcmrRegions.stream().map(t -> new LabelValueVO(t.getRegionName(), t.getRegionCode()))
                             .sorted().collect(Collectors.toList());
    }

    @Override
    public UploadResultVO importGroupRegion(String loginUserId, MultipartFile file) throws IOException {

        List<GroupRegionFileVO> groupRegionVOs = ExcelUtils.readExcel(GroupRegionFileVO.class, file);

//        groupRegionVOs
        List<List<String>> villageInfoList = groupRegionVOs.stream().map(t -> Lists.newArrayList(t.getProvinceName(),
                                                                                                      t.getCityName(),
                                                                                                      t.getDistrictName(),
                                                                                                      t.getStreetName(),
                                                                                                      t.getVillageName()))
                                                                .distinct().collect(Collectors.toList());

        Map<List<String>, TbCdcmrRegion> villageMap = tbCdcmrRegionMapper.selectVillageRegionsByNameInfo(villageInfoList)
                                                                         .stream()
                                                                         .collect(Collectors.toMap(t -> Lists.newArrayList(t.getProvinceName(),
                                                                                                                           t.getCityName(),
                                                                                                                           t.getDistrictName(),
                                                                                                                           t.getStreetName(),
                                                                                                                           t.getVillageName()),
                                                                                                   Function.identity()));

        for (GroupRegionFileVO groupRegionVO : groupRegionVOs) {
            List<String> regionNameInfo = Lists.newArrayList(groupRegionVO.getProvinceName(),
                                                             groupRegionVO.getCityName(),
                                                             groupRegionVO.getDistrictName(),
                                                             groupRegionVO.getStreetName(),
                                                             groupRegionVO.getVillageName());
            TbCdcmrRegion tbCdcmrRegion = villageMap.get(regionNameInfo);
            if (tbCdcmrRegion != null) {

            }
        }

        return null;
    }

    private List<TbCdcmrRegion> buildRegionsFromUapNation(List<UapNation> nations) {
        if (CollectionUtil.isEmpty(nations)) {
            return new ArrayList<>();
        }
        List<TbCdcmrRegion> tbCdcmrRegionList = new ArrayList<>();
        for (UapNation uapNation : nations) {
            TbCdcmrRegion tbCdcmrRegion = NationStructMapper.INSTANCE.nationToRegion(uapNation);
            tbCdcmrRegion.setDataSource(DATA_SOURCE_UAP);
            tbCdcmrRegion.setId(String.valueOf(uapNation.getId())); // uap主键
            if (StringUtils.isBlank(tbCdcmrRegion.getAddressDetail())) {
                tbCdcmrRegion.setAddressDetail(tbCdcmrRegion.generateAddressDetail());
            }

            tbCdcmrRegionList.add(tbCdcmrRegion);
        }
        return tbCdcmrRegionList;
    }

    private void processAmapApi(List<TbCdcmrRegion> tbCdcmrRegionList) {

        for (TbCdcmrRegion tbCdcmrRegion : tbCdcmrRegionList) {
            String provinceName = tbCdcmrRegion.getProvinceName();
            String cityName = tbCdcmrRegion.getCityName();
            String streetAddressDetail = tbCdcmrRegion.getAddressDetail();

            String type = "";
            if ((AreaLevelEnum.GROUP.is(tbCdcmrRegion.getRegionLevel()))) {
                type = "住宅小区;商务住宅;村组级地名;地名地址信息;科教文化服务;公司企业";
            } else if (AreaLevelEnum.VILLAGE.is(tbCdcmrRegion.getRegionLevel())) {
                type = "村庄级地名;乡镇以下级政府及事业单位;政府机构及社会团体;地名地址信息;医疗保健服务;科教文化服务;公司企业;";
            }

            AddressFuzzyNormalizationDTO dto = AddressFuzzyNormalizationDTO.builder()
                                                                           .provinceName(provinceName)
                                                                           .cityName(cityName)
                                                                           .districtCode(tbCdcmrRegion.getDistrictCode())
                                                                           .addressDetail(streetAddressDetail)
                                                                           .types(type)
                                                                           .refresh(false)
                                                                           .build();

            try {
                AddressStandardVo stdVo = amapService.fuzzyAddressNormalizationSimple(dto, "");
                String addressLongitude = stdVo.getAddressLongitude();
                String addressLatitude = stdVo.getAddressLatitude();

                if (!AddressStandardVo.isEmptyValue(stdVo)) {
                    tbCdcmrRegion.setAmapAreaCode(stdVo.getAddressAreaCode());
                    tbCdcmrRegion.setAmapAreaName(stdVo.getAddressAreaName());
                    tbCdcmrRegion.setLongitude(Optional.ofNullable(addressLongitude).map(Double::valueOf).orElse(null));
                    tbCdcmrRegion.setLatitude(Optional.ofNullable(addressLatitude).map(Double::valueOf).orElse(null));
                }
            } catch (Exception e) {
                log.error("街道经纬度调用API失败: {}", tbCdcmrRegion, e);
            }
        }

    }

    private void batchMaintain(String code, int currLevel, int toLevel, List<TbCdcmrRegion> tbCdcmrRegionList) {
        log.info("开始删除Region数据, code:{}, toLevel:{}", code, toLevel);
        int deleteCount = tbCdcmrRegionMapper.deleteByCodeLevel(code, currLevel, toLevel);
        log.info("结束删除Region数据, 总数:{}, code:{}, toLevel:{} ", deleteCount, code, toLevel);

        this.batchUpsert(tbCdcmrRegionList);
    }

    private void batchUpsert(List<TbCdcmrRegion> tbCdcmrRegionList) {
        log.info("开始插入/更新Region数据");
        List<List<TbCdcmrRegion>> partition = Lists.partition(tbCdcmrRegionList, BATCH_SIZE);
        for (List<TbCdcmrRegion> regions : partition) {
            regions.forEach(t -> t.setDeleteFlag(Constants.NO_DELETE));
            tbCdcmrRegionMapper.batchUpsert(regions);
        }
        log.info("插入/更新Region数据, 总数:{}", tbCdcmrRegionList.size());
    }


    private RegionParam getUserAuthParam(String loginUserId, int targetLevel) {
        UapUserExtend userInfo = uapUserService.getUserInfo(loginUserId);

        //根据机构是否包含省市区编码来决定层级数
        RegionParam usrAuthParam = new RegionParam();
        if (AreaLevelEnum.PROVINCE.aeq(targetLevel)) {
            usrAuthParam.setProvinceCode(userInfo.getProvinceCode());
        }
        if (AreaLevelEnum.CITY.aeq(targetLevel)) {
            usrAuthParam.setCityCode(userInfo.getCityCode());
        }
        if (AreaLevelEnum.DISTRICT.aeq(targetLevel)) {
            usrAuthParam.setDistrictCode(userInfo.getDistrictCode());
        }

        return usrAuthParam;
    }


}
