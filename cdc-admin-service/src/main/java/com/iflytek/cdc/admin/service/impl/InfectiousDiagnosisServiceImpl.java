package com.iflytek.cdc.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.iflytek.cdc.admin.constant.MdmDataSyncConstants;
import com.iflytek.cdc.admin.constant.TableName;
import com.iflytek.cdc.admin.dto.InfectiousDiseasesDto;
import com.iflytek.cdc.admin.dto.PageData;
import com.iflytek.cdc.admin.dto.SearchInfecInfoDTO;
import com.iflytek.cdc.admin.dto.UpdateInfecDTO;
import com.iflytek.cdc.admin.entity.DictMappingInfo;
import com.iflytek.cdc.admin.entity.InfectiousDiagnosis;
import com.iflytek.cdc.admin.mapper.InfectiousDiagnosisMapper;
import com.iflytek.cdc.admin.service.MdmDataSyncService;
import com.iflytek.cdc.admin.service.SyncMdmDataService;
import com.iflytek.cdc.admin.service.async.AsyncMdmDataService;
import com.iflytek.cdc.admin.thread.RedisHandlerService;
import com.iflytek.cdc.admin.thread.Status;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.MdmPageData;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.TermCodedValueInfoFilter;
import com.iflytek.zhyl.uap.usercenter.entity.TUapUser;
import com.iflytek.zhyl.uap.usercenter.service.UapUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InfectiousDiagnosisServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/2 22:37
 * @Version 1.0
 */

@Service("infecDiaService")
@Slf4j
public class InfectiousDiagnosisServiceImpl extends AbstractSyncMdmDataService implements SyncMdmDataService<InfectiousDiagnosis> {

    @Resource
    public InfectiousDiagnosisMapper infectiousDiagnosisMapper;

    @Resource
    public AsyncMdmDataService asyncMdmDataInfecDia;

    @Resource
    RedisHandlerService redisHandlerService;

    public InfectiousDiagnosisServiceImpl(UapUserApi uapUserApi, MdmDataSyncService mdmDataSyncService, BatchUidService batchUidService) {
        super(uapUserApi, mdmDataSyncService, batchUidService);
    }

    @Override
    public void addInfectiousDiseases(String loginUserId, InfectiousDiseasesDto record) {
        throw new MedicalBusinessException("暂不支持添加传染病诊断标准");
    }

    @Override
    public List<InfectiousDiagnosis> queryInfecInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        List<InfectiousDiagnosis> infectiousDiagnosis = infectiousDiagnosisMapper.queryInfecInfo(searchInfecInfoDTO);
        return infectiousDiagnosis;
    }

    @Override
    public List<TermCodedValueInfo> getMdmDataSync(String code) {
        List<TermCodedValueInfo> mdmCodeValues = mdmDataSyncService.mdmDictCodeSync(code,
                MdmDataSyncConstants.SYNDROME_PAGENUMBER, 1000);
        return mdmCodeValues;
    }

    @Override
    public PageData<InfectiousDiagnosis> queryInfecPageInfo(SearchInfecInfoDTO searchInfecInfoDTO) {
        PageMethod.startPage(searchInfecInfoDTO.getPageIndex(), searchInfecInfoDTO.getPageSize());
        List<InfectiousDiagnosis> infectiousDiseases = infectiousDiagnosisMapper.queryInfecInfo(searchInfecInfoDTO);
        PageInfo<InfectiousDiagnosis> pageInfo = new PageInfo<>(infectiousDiseases);
        PageData<InfectiousDiagnosis> pageData = PageData.fromPageInfo(pageInfo);
        pageData.setData(pageInfo.getList());
        return pageData;
    }

    @Override
    public void updateSyncMdmData(String  loginUserId, List<InfectiousDiagnosis> infectiousDiagnosis, List<TermCodedValueInfo> mdmCodeValues) {
        TUapUser uapUser = getUapUser(loginUserId);
        //取出本地库中存在而mdm不存在的数据
        List<InfectiousDiagnosis> localExistData = getUpdateIdList(mdmCodeValues,infectiousDiagnosis);
        if(CollectionUtils.isEmpty(localExistData)){
            return ;
        }
        //本地库中存在而mdm不存在的数据
        UpdateInfecDTO ud = new UpdateInfecDTO();
        List<String> updateIds = new ArrayList<>();
        localExistData.forEach(localData -> {
            updateIds.add(localData.getId());
        });
        ud.setUpdateUser(uapUser.getId());
        ud.setIsEnable(MdmDataSyncConstants.NO_USE);
        //批量更新mdm已删除的数据状态
        if (updateIds.size() > 0) {
            List<List<String>>  updateIdGroups = fixedGrouping2(updateIds,1000);
            for(List<String> updateIdGroup: updateIdGroups){
                ud.setIds(updateIdGroup);
                infectiousDiagnosisMapper.updateInfecInfo(ud);
            }
        }
    }

    @Override
    public void insertSyncMdmData(String loginUserId, List<InfectiousDiagnosis> infectiousDiagnosis, List<TermCodedValueInfo> mdmCodeValues) {
        //取出mdm库中存在而本地库中不存在的数据
        TUapUser uapUser = getUapUser(loginUserId);
        List<TermCodedValueInfo> mdmExistData = getAddIdList(infectiousDiagnosis,mdmCodeValues);
        //如果mdm库中存在而本地库中不存在的数据，则直接插入到本地库中
        List<InfectiousDiagnosis> insertInfos = new ArrayList<InfectiousDiagnosis>();
        //格式化需要插入数据库中的数据
        mdmExistData.forEach(mdmData -> {
            InfectiousDiagnosis si = new InfectiousDiagnosis();
            si.setId(String.valueOf(batchUidService.getUid(TableName.INFECTIOUS_DIAGSIS)));
            si.setDiagnosisCode(mdmData.getCodedValue());
            si.setDiagnosisName(mdmData.getDescription());
            si.setCreateUser(uapUser.getId());
            si.setIsEnable(MdmDataSyncConstants.YES_USE);
            insertInfos.add(si);
        });
        List<List<InfectiousDiagnosis>>  insertInfosGroup = fixedGrouping2(insertInfos,1000);
        if(!CollectionUtils.isEmpty(insertInfosGroup)&&insertInfosGroup.size()>0){
            for(List<InfectiousDiagnosis>insertInfosGroups: insertInfosGroup){
                infectiousDiagnosisMapper.insertInfectiousDiagnosis(insertInfosGroups);
            }
        }

    }

    private  List<TermCodedValueInfo> getAddIdList(List<InfectiousDiagnosis> infectiousDiagnosis, List<TermCodedValueInfo> mdmCodeValues) {
        if (infectiousDiagnosis.size()>0 && !mdmCodeValues.isEmpty()) {
            Map<String, String> dataMap = new HashMap<String, String>();
            for (InfectiousDiagnosis  param : infectiousDiagnosis) {
                dataMap.put(param.getDiagnosisCode(), param.getDiagnosisCode());
            }

            List<TermCodedValueInfo> newList = new ArrayList<TermCodedValueInfo>();
            for (TermCodedValueInfo value : mdmCodeValues) {
                if (!dataMap.containsKey(value.getCodedValue())) {
                    newList.add(value);
                }
            }
            return newList;
        } else {
            return mdmCodeValues;
        }
    }

    private  List<InfectiousDiagnosis> getUpdateIdList(List<TermCodedValueInfo> mdmCodeValues,List<InfectiousDiagnosis> infectiousDiagnosis) {
        if (infectiousDiagnosis != null && !mdmCodeValues.isEmpty()) {
            Map<String, String> dataMap = new HashMap<String, String>();
            for (TermCodedValueInfo  mdmCodeValue : mdmCodeValues) {
                dataMap.put(mdmCodeValue.getCodedValue(), mdmCodeValue.getCodedValue());
            }
            List<InfectiousDiagnosis> newList = new ArrayList<InfectiousDiagnosis>();
            for (InfectiousDiagnosis value : infectiousDiagnosis) {
                if (!dataMap.containsKey(value.getDiagnosisCode())) {
                    newList.add(value);
                }
            }
            return newList;
        }
        return null;
    }

    @Override
    public void updateInfecInfo(String loginUserId, InfectiousDiagnosis infectiousDiagnosis) {
        try {
            //获取登录人员信息
            TUapUser uapUser = getUapUser(loginUserId);
            infectiousDiagnosis.setUpdateUser(uapUser.getId());
            infectiousDiagnosisMapper.updateinfectiousDiagnosis(infectiousDiagnosis);
        } catch (Exception e) {
            log.error("传染病诊断信息维护异常：{}", e);
            throw new MedicalBusinessException("传染病诊断信息维护异常");
        }
    }

    @Override
    public void syncMdmData(String batchId,String loginUserId) {
        //todo 等待mdm修改字典目录接口，获取字典目录状态，如果被删除或被禁用，则直接将所有本地数据状态修改为禁用。
        //        //先查询本地数据表所有的编码数据
        if (isCanUse(MdmDataSyncConstants.INFECDIA_CODE)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            throw new MedicalBusinessException("该字典目录被删除或已被禁用，无法进行数据同步");
        }
        if (isSyncMdm(MdmDataSyncConstants.INFECDIA_STRATEGY)) {
            log.error("正在有诊断任务同步中,不可再次进行同步");
            throw new MedicalBusinessException("正在有诊断任务同步中,不可再次进行同步");
        }
        asyncMdmDataInfecDia.executeAsyncMdmThread(MdmDataSyncConstants.INFECDIA_STRATEGY,batchId,loginUserId);
    }

    private boolean isSyncMdm(String code) {
        String status = redisHandlerService.getStatus(code);
        if (Status.TODO.toString().equals(status)) {
            return true;
        }
        redisHandlerService.setStatus(code, Status.TODO);
        return false;
    }

    public void  updateCodeNme(TUapUser uapUser, List<InfectiousDiagnosis> infectiousDiagnosis, List<TermCodedValueInfo> mdmCodeValues){
        //取出本地库中存在而mdm也存在的数据
        List<TermCodedValueInfo> allExistData=mdmCodeValues.stream().filter(mdm->infectiousDiagnosis.stream().map(InfectiousDiagnosis::getDiagnosisCode).anyMatch(
                code->Objects.equals(mdm.getCodedValue(),code))).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(allExistData)){
            allExistData.forEach(allData->
                    infectiousDiagnosisMapper.updateCodeName(allData.getCodedValue(),allData.getDescription(),uapUser.getId()));
        }
    }

    @Override
    public boolean judgeIsUse(String id) {
        //根据id查询该字典值域的详细
        InfectiousDiagnosis si = infectiousDiagnosisMapper.queryInfoById(id);
        DictMappingInfo di = mdmDataSyncService.getMappingInfo(MdmDataSyncConstants.INFECDIA_CODE);
        //判断字典目录是否存在
        if (isCanUse(MdmDataSyncConstants.INFECDIA_CODE)) {
            log.error("未查询mdm该字典目录，或者该字典目录已被禁用");
            return false;
        }
        //查询字典值域
        String[] codeValue = {si.getDiagnosisCode()};
        //获取字典值域数据
        MdmPageData<TermCodedValueInfo, TermCodedValueInfoFilter> mdmData = mdmDataSyncService.getMdmCodeInfo(di, MdmDataSyncConstants.SYNDROME_PAGENUMBER,
                MdmDataSyncConstants.SYNDROME_PAGESIZE, codeValue);
        if (CollUtil.isEmpty(mdmData.getEntities())) {
            return false;
        }
        return !StringUtils.isBlank(mdmData.getEntities().get(0).getId().toString());
    }

}
