package com.iflytek.cdc.admin.service.brief;

import java.util.List;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.constant.Constants;
import com.iflytek.cdc.admin.dto.brief.OverviewSearchDto;
import com.iflytek.cdc.admin.entity.brief.BriefIndicatorsEntity;
import com.iflytek.cdc.admin.entity.brief.BriefInfoEntity;
import com.iflytek.cdc.admin.entity.brief.PushRecordEntity;
import com.iflytek.cdc.admin.mapper.brief.BriefIndicatorsMapper;
import com.iflytek.cdc.admin.mapper.brief.OverviewMapper;
import com.iflytek.cdc.admin.util.brief.ApiResult;
import com.iflytek.cdc.admin.vo.brief.BriefInfoVo;

import lombok.RequiredArgsConstructor;

/**
 * 简报概况服务impl
 *
 * <AUTHOR>
 * @date 2025/01/06
 */
@Service
@RequiredArgsConstructor
public class OverviewServiceImpl implements OverviewService {

    private final OverviewMapper overviewMapper;

    private final BriefIndicatorsMapper briefIndicatorsMapper;

    @Override
    public ApiResult<PageInfo<BriefInfoEntity>> list(OverviewSearchDto searchDto) {
        PageHelper.startPage(searchDto.getPageIndex(), searchDto.getPageSize());
        List<BriefInfoEntity> list = overviewMapper.queryBriefList(searchDto);
        PageHelper.clearPage();
        return ApiResult.ok(new PageInfo<>(list));
    }

    @Override
    public ApiResult<PageInfo<PushRecordEntity>> pushList(OverviewSearchDto searchDto) {
        PageHelper.startPage(searchDto.getPageIndex(), searchDto.getPageSize());
        List<PushRecordEntity> list = overviewMapper.queryPushList(searchDto);
        PageHelper.clearPage();
        return ApiResult.ok(new PageInfo<>(list));
    }

    @Override
    public BriefInfoVo getById(String id) {
        BriefInfoVo briefInfoVo = overviewMapper.getById(id);
        
        // 获取附件维度指标集合
        List<BriefIndicatorsEntity> fjIndicators = briefIndicatorsMapper.selectList(new LambdaQueryWrapper<BriefIndicatorsEntity>()
                .eq(BriefIndicatorsEntity::getBriefId, id)
                .eq(BriefIndicatorsEntity::getIndexScope, Constants.BriefReport.INDEX_SCOPE_FJ));
        briefInfoVo.setAttachmentIndicators(fjIndicators);

        return briefInfoVo;
    }

    @Override
    public void deleteInfoById(String id) {
        if (StringUtils.isBlank(id)) {
            throw new MedicalBusinessException("简报ID不能为空");
        }
        overviewMapper.deleteInfoById(id);
    }
}
