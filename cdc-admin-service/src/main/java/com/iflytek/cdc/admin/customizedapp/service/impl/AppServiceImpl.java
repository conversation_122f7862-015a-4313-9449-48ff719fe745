package com.iflytek.cdc.admin.customizedapp.service.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.admin.common.apiservice.UapServiceApi;
import com.iflytek.cdc.admin.customizedapp.config.CustomizedAppConfig;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsApp;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.enums.AppStatusEnum;
import com.iflytek.cdc.admin.customizedapp.enums.VersionTypeEnum;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsAppMapper;
import com.iflytek.cdc.admin.customizedapp.model.dto.AppQueryDTO;
import com.iflytek.cdc.admin.customizedapp.service.AppService;
import com.iflytek.cdc.admin.customizedapp.service.MenuService;
import com.iflytek.cdc.admin.customizedapp.service.PageService;
import com.iflytek.cdc.admin.customizedapp.service.VersionService;
import com.iflytek.cdc.admin.model.mr.vo.TreeNode;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.cdc.admin.common.vo.uap.UapApp;
import com.iflytek.cdc.admin.common.vo.uap.UapAuthorityTree;
import com.iflytek.cdc.admin.common.vo.uap.UapMenu;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应用;(tb_cdccs_app)表服务实现类
 */
@Service
public class AppServiceImpl extends CdcServiceBaseImpl<TbCdccsAppMapper, TbCdccsApp> implements AppService {
    @Resource
    private TbCdccsAppMapper tbCdccsAppMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private CustomizedAppConfig customizedAppConfig;

    @Resource
    private PageService pageService;

    @Resource
    private VersionService versionService;

    @Resource
    private MenuService menuService;

    @Override
    public TbCdccsApp loadByCode(String code) {
        LambdaQueryWrapper<TbCdccsApp> tbCdccsAppLambdaQueryWrapper = lambdaQueryWrapper();
        tbCdccsAppLambdaQueryWrapper.eq(TbCdccsApp::getAppCode, code);
        return getOne(tbCdccsAppLambdaQueryWrapper);
    }

    @Override
    public List<TbCdccsApp> listAll(AppQueryDTO queryDTO) {
        LambdaQueryWrapper<TbCdccsApp> lambdaQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDTO.getAppName())){
            lambdaQuery.like(TbCdccsApp::getAppName, queryDTO.getAppName());
        }
        if (StringUtils.isNotEmpty(queryDTO.getCategoryId())){
            lambdaQuery.eq(TbCdccsApp::getCategoryId, queryDTO.getCategoryId() );
        }
        if (StringUtils.isNotEmpty(queryDTO.getStatus())){
            lambdaQuery.eq(TbCdccsApp::getStatus, queryDTO.getStatus());
        }
        lambdaQuery.orderByAsc(TbCdccsApp::getOrderSeq);
        return list(lambdaQuery);
    }

    @Override
    public List<TbCdccsApp> listByCategoryId(String categoryId, String appName) {
        LambdaQueryWrapper<TbCdccsApp> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(TbCdccsApp::getCategoryId, categoryId)
                .orderByAsc(TbCdccsApp::getOrderSeq);
        if (StringUtils.isNotEmpty(appName)){
            lambdaQuery.like(TbCdccsApp::getAppName, appName);
        }
        return list(lambdaQuery);
    }


    @Override
    @Transactional
    public void published(String groupId, String loginUserId) {
        TbCdccsVersion tbCdccsVersion = versionService.loadByBusinessId(groupId, VersionTypeEnum.APP_MENU);
        versionService.published(groupId, VersionTypeEnum.APP_MENU);
        published(tbCdccsVersion.getBusinessKey(), menuService.listByGroupId(groupId), loginUserId);
    }

    private void published(String id, List<TbCdccsMenu> tbCdccsMenus, String loginUserId) {
        TbCdccsApp byId = getById(id);
        byId.setStatus(AppStatusEnum.PUBLISHED.getCode());
        if (StringUtils.isEmpty(byId.getUapAppId())){
            UapAuthorityTree uapAuthorityTrees = uapServiceApi.listUapAuthorityTree(loginUserId);
            UapAuthorityTree authorityTree = uapAuthorityTrees.getNodes().stream().filter(u -> u.getAppName().equals(byId.getAppName())).findFirst().orElse(null);
            if (authorityTree == null) {
                UapApp app = new UapApp();
                app.setCode(byId.getAppCode());
                app.setName(byId.getAppName());
                app.setIsShow(1);
                app.setUrl(customizedAppConfig.getAppRoot() + "?appCode=" + byId.getAppCode());
                String uapAppId = uapServiceApi.addUapApp(app);
                byId.setUapAppId(uapAppId);
            }else {
                byId.setUapAppId(authorityTree.getAppId());
            }
        }
        syncToUap(byId, tbCdccsMenus, loginUserId );
        update(byId);
    }

    @Override
    @Transactional
    public void deleteByCategoryId(String categoryId) {
        List<TbCdccsApp> tbCdccsApps = listByCategoryId(categoryId, null);
        tbCdccsApps.forEach(t -> {
            pageService.deleteByAppId(t.getId());
        });
        remove(lambdaQueryWrapper().eq(TbCdccsApp::getCategoryId, categoryId));
    }

    @Override
    public void beforeCreate(TbCdccsApp entity) {
        super.beforeCreate(entity);
        entity.setStatus(AppStatusEnum.WAITING_PUBLISH.getCode());
        duplicate(entity,  "应用编码重复", TbCdccsApp::getAppCode);
    }

    @Override
    public void beforeUpdate(TbCdccsApp entity) {
        super.beforeUpdate(entity);
        duplicate(entity,  "应用编码重复", TbCdccsApp::getAppCode);
    }

    @Override
    public void afterDelete(String id) {
        super.afterDelete(id);
        pageService.deleteByAppId(id);
    }

    /**
     * 同步至uap
     */
    private void syncToUap(TbCdccsApp app, List<TbCdccsMenu> tbCdccsMenus, String loginUserId){
        List<TreeNode> treeNodes = TreeNode.buildTreeByNodeList(tbCdccsMenus, TbCdccsMenu::getId, TbCdccsMenu::getParentId, TbCdccsMenu::getMenuName, TbCdccsMenu::getMenuCode, null, true);
        List<UapAuthorityTree> uapAuthorityTrees = uapServiceApi.listUapAuthorityTree(loginUserId).getNodes();
        UapAuthorityTree authorityTree = uapAuthorityTrees.stream().filter(u -> u.getAppId().equals(app.getUapAppId())).findFirst().orElse(null);
        if (authorityTree == null){
            throw new MedicalBusinessException("数据错误，请联系管理员");
        }
        List<UapAuthorityTree> toUpdates = new ArrayList<>();
        List<Pair<UapAuthorityTree, List<TreeNode>>> toInserts = new ArrayList<>();
        List<UapAuthorityTree> toDeletes = new ArrayList<>();
        compareToSave(treeNodes, authorityTree.getNodes(), authorityTree, toUpdates, toInserts, toDeletes);
        if (toDeletes.size() > 0){
            //只删除code 在应用这边的菜单
            List<String> deletedCodes = menuService.listAllContainsDeletedCode(app.getId());
            toDeletes.forEach(d -> {
                if (deletedCodes.contains(d.getCode())){
                    uapServiceApi.deleteMenu(d.getId());
                }
            });
        }
        if (toUpdates.size() > 0){
            toUpdates.forEach(u -> {
                UapMenu uapMenu = new UapMenu();
                uapMenu.setId(u.getId());
                uapMenu.setName(u.getName());
                uapMenu.setUrl(u.getUrl());
                uapMenu.setUrlType(u.getUrlType());
                uapMenu.setRemark(u.getRemark());
                uapMenu.setSort(String.valueOf(u.getSort()));
                uapServiceApi.updateMenu(uapMenu);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        if (toInserts.size() > 0){
            toInserts.forEach(pair -> {
                createToUap(app.getUapAppId(), app.getAppName(), pair.getKey().getId(), pair.getValue());
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        }

    }

    /**
     * 在uap创建
     */
    private void createToUap(String appId, String appName, String parentId, List<TreeNode> treeNodes){
        treeNodes.forEach(t -> {
            TbCdccsMenu originalData = (TbCdccsMenu) t.getOriginalData();
            UapMenu menu = new UapMenu();
            menu.setParentId(parentId);
            menu.setCode(t.getValue());
            menu.setName(t.getLabel());
            menu.setAppId(appId);
            menu.setAppName(appName);
            menu.setStatus(0);
            menu.setStatus(1);
            menu.setUrl(originalData.getUrl());
            menu.setUrlType(originalData.getUrlType() == null ? 0 : originalData.getUrlType());
            menu.setRemark(originalData.getRemark());
            menu.setType(originalData.getType() == null ? 0 : originalData.getType());
            menu.setSort(String.valueOf(originalData.getSort()));
            String menuId = uapServiceApi.addUapMenu(menu);
            if (!CollectionUtils.isEmpty(t.getChildren())){
                createToUap(appId, appName, menuId, t.getChildren());
            }
        });
    }

    /**
     * 与uap比较
     * @param treeNodes 系统树节点
     * @param uapAuthorityTrees uap权限节点
     * @param toUpdates 待更新
     * @param toInserts 待插入
     * @param toDeletes 待删除
     */
    private void compareToSave(List<TreeNode> treeNodes,
                               List<UapAuthorityTree> uapAuthorityTrees,
                               UapAuthorityTree parentTree,
                               List<UapAuthorityTree> toUpdates,
                               List<Pair<UapAuthorityTree, List<TreeNode>>> toInserts,
                               List<UapAuthorityTree> toDeletes){
        if (uapAuthorityTrees == null){
            uapAuthorityTrees = new ArrayList<>();
        }
        Map<String, UapAuthorityTree> treeMap = uapAuthorityTrees.stream().collect(Collectors.toMap(UapAuthorityTree::getCode, u -> u));
        toDeletes.addAll(treeMap.values());
        for (TreeNode treeNode : treeNodes){
            toInserts.add(Pair.of(parentTree, Collections.singletonList(treeNode)));
        }
    }
}