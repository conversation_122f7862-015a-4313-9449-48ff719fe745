package com.iflytek.cdc.admin.datamodel.service.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.common.constants.CommonConstants;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.admin.datamodel.entity.TbCdcdmMetadataTableSqlLog;
import com.iflytek.cdc.admin.datamodel.mapper.TbCdcdmMetadataTableInfoMapper;
import com.iflytek.cdc.admin.datamodel.model.dto.MetadataTableInfoQueryDTO;
import com.iflytek.cdc.admin.datamodel.model.vo.TableColumnInfoExcelVO;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableColumnInfoService;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableInfoService;
import com.iflytek.cdc.admin.datamodel.service.MetadataTableSqlLogService;
import com.iflytek.cdc.admin.enums.TableColumnTypeEnum;
import com.iflytek.cdc.admin.enums.TableSqlStatusEnum;
import com.iflytek.cdc.admin.enums.TableStatusEnum;
import com.iflytek.cdc.admin.excel.SelectDataSheetWriteHandler;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MetadataTableInfoServiceImpl extends CdcServiceBaseImpl<TbCdcdmMetadataTableInfoMapper, TbCdcdmMetadataTableInfo> implements MetadataTableInfoService {

    @Resource
    private MetadataTableSqlLogService metadataTableSqlLogService;

    @Resource
    private MetadataTableColumnInfoService metadataTableColumnInfoService;



    @Override
    public void fulfillChildProperty(TbCdcdmMetadataTableInfo tbCdcdmMetadataTableInfo) {
        super.fulfillChildProperty(tbCdcdmMetadataTableInfo);
        tbCdcdmMetadataTableInfo.setColumnInfoList(metadataTableColumnInfoService.listByTableId(tbCdcdmMetadataTableInfo.getId()));
    }

    @Override
    public void createEntityTable(String id) {
        TbCdcdmMetadataTableInfo tableInfo = loadCascade(id);
        if (!tableInfo.getTableStatus().equals(TableStatusEnum.WAITING_CREATE.getCode())) {
            throw new MedicalBusinessException("表状态错误，无法创建");
        }
        metadataTableSqlLogService.createTableSql(tableInfo);
        tableInfo.setTableStatus(TableStatusEnum.CREATING.getCode());
        updateById(tableInfo);
    }

    @Override
    public void syncEntityTable(String id) {
        TbCdcdmMetadataTableInfo tableInfo = getById(id);
        if (!tableInfo.getTableStatus().equals(TableStatusEnum.WAIT_SYNC.getCode())) {
            throw new MedicalBusinessException("表状态错误，无法同步");
        }
        metadataTableSqlLogService.startToExecute(tableInfo);
        tableInfo.setTableStatus(TableStatusEnum.SYNCING.getCode());
        updateById(tableInfo);
    }

    @Override
    public byte[] downloadTemplate() {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Map<Integer, List<String>> selectMap = new HashMap<>();
        selectMap.put(4, Arrays.stream(TableColumnTypeEnum.values()).map(TableColumnTypeEnum::getName).collect(Collectors.toList()));
        EasyExcel.write(outputStream, TableColumnInfoExcelVO.class)
                .registerWriteHandler(new SelectDataSheetWriteHandler(selectMap)).sheet().doWrite(new ArrayList<>());
        return outputStream.toByteArray();
    }

    @Override
    public List<TableColumnInfoExcelVO> readExcel(InputStream inputStream) {
        List<TableColumnInfoExcelVO> columnInfoExcelVOS = EasyExcel.read(inputStream).head(TableColumnInfoExcelVO.class).sheet().doReadSync();
        columnInfoExcelVOS.forEach(c -> {
            c.setIsRequired("是".equals(c.getIsRequired()) ? "1" : "0");
            c.setDataType(TableColumnTypeEnum.getCodeByName(c.getDataType()));
        });
        return columnInfoExcelVOS;
    }

    @Override
    @Transactional
    public void importByFile(InputStream inputStream) {
        List<TableColumnInfoExcelVO> columnInfoExcelVOS = readExcel(inputStream);
        if (CollectionUtils.isEmpty(columnInfoExcelVOS)){
            throw new MedicalBusinessException("未填写信息");
        }
        check(columnInfoExcelVOS);
        Map<String, List<TableColumnInfoExcelVO>> excelMap = columnInfoExcelVOS.stream()
                .collect(Collectors.groupingBy(TableColumnInfoExcelVO::getTableName, LinkedHashMap::new, Collectors.toList()));
        excelMap.forEach((tableName, columns) -> {
            TbCdcdmMetadataTableInfo tableInfo = new TbCdcdmMetadataTableInfo();
            tableInfo.setTableName(tableName);
            tableInfo.setTableDesc(columns.get(0).getTableDesc());
            tableInfo.setSchema("kd");
            List<TbCdcdmMetadataTableColumnInfo> convert = convert(columns);
            mergeSystemField(convert);
            tableInfo.setColumnInfoList(convert);
            create(tableInfo);
        });
    }

    @Override
    public PageInfo<TbCdcdmMetadataTableInfo> pageList(MetadataTableInfoQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        LambdaQueryWrapper<TbCdcdmMetadataTableInfo> queryWrapper = lambdaQueryWrapper();
        if (StringUtils.isNotEmpty(queryDTO.getSchema())){
            queryWrapper.eq(TbCdcdmMetadataTableInfo::getSchema, queryDTO.getSchema());
        }
        if (StringUtils.isNotEmpty(queryDTO.getTableCode())){
            queryWrapper.like(TbCdcdmMetadataTableInfo::getTableCode, queryDTO.getTableCode());
        }
        if (StringUtils.isNotEmpty(queryDTO.getTableName())){
            queryWrapper.like(TbCdcdmMetadataTableInfo::getTableName, queryDTO.getTableName());
        }
        if (StringUtils.isNotEmpty(queryDTO.getCustomizedEnable())){
            queryWrapper.eq(TbCdcdmMetadataTableInfo::getCustomizedEnable, queryDTO.getCustomizedEnable());
        }

        return new PageInfo<>(list(queryWrapper));
    }

    @Override
    public void updateSyncStatus(List<TbCdcdmMetadataTableSqlLog> sqlLogs, String status) {
        String sqlExecuteStatus;
        if (TableStatusEnum.SYNC_FAILED.getCode().equals(status)){
            sqlExecuteStatus = TableSqlStatusEnum.EXECUTE_FAILED.getCode();
        } else {
            sqlExecuteStatus = TableSqlStatusEnum.EXECUTED.getCode();
        }
        lambdaUpdate().set(TbCdcdmMetadataTableInfo::getTableStatus, status).in(TbCdcdmMetadataTableInfo::getId,
                sqlLogs.stream().map(TbCdcdmMetadataTableSqlLog::getTableId).collect(Collectors.toList())).update();
        sqlLogs.forEach(s -> {
            s.setStatus(sqlExecuteStatus);
        });
        metadataTableSqlLogService.update(sqlLogs);
    }

    @Override
    public void afterCreate(TbCdcdmMetadataTableInfo entity) {
        super.afterCreate(entity);
        if (!CollectionUtils.isEmpty(entity.getColumnInfoList())){
            metadataTableColumnInfoService.saveByTable(Pair.of(null, entity), entity.getColumnInfoList());
        }
    }

    @Override
    public void beforeCreate(TbCdcdmMetadataTableInfo entity) {
        super.beforeCreate(entity);
        entity.setTableStatus(TableStatusEnum.WAITING_CREATE.getCode());
        entity.setCustomizedEnable(String.valueOf(CommonConstants.STATUS_ON));
        if (StringUtils.isEmpty(entity.getTableCode())){
            entity.setTableCode(entity.getTableName());
        }
        duplicate(entity, "table_code 重复", TbCdcdmMetadataTableInfo::getTableCode);
    }

    @Override
    public void beforeUpdate(TbCdcdmMetadataTableInfo entity) {
        super.beforeUpdate(entity);
        duplicate(entity, "table_code 重复", TbCdcdmMetadataTableInfo::getTableCode);
        TbCdcdmMetadataTableInfo oldTable = getById(entity.getId());
        if (oldTable.getTableStatus().equals(TableStatusEnum.CREATING.getCode())
                || oldTable.getTableStatus().equals(TableStatusEnum.SYNCING.getCode())){
            throw new MedicalBusinessException("表正在创建或同步中，请稍后修改");
        }
        if (oldTable.getTableStatus().equals(TableStatusEnum.CREATED.getCode())
                || oldTable.getTableStatus().equals(TableStatusEnum.SYNCED.getCode())){
            entity.setTableStatus(TableStatusEnum.WAIT_SYNC.getCode());
        }
        if (!CollectionUtils.isEmpty(entity.getColumnInfoList())){
            metadataTableColumnInfoService.saveByTable(Pair.of(oldTable, entity), entity.getColumnInfoList());
        }
    }
    
    private List<TbCdcdmMetadataTableColumnInfo> convert(List<TableColumnInfoExcelVO> columnInfoExcelVOS){
        return columnInfoExcelVOS.stream().map(c -> {
            TbCdcdmMetadataTableColumnInfo columnInfo = new TbCdcdmMetadataTableColumnInfo();
            BeanUtils.copyProperties(c, columnInfo);
            return columnInfo;
        }).collect(Collectors.toList());
    }
    
    private void mergeSystemField(List<TbCdcdmMetadataTableColumnInfo> inputs){
        Map<String, TbCdcdmMetadataTableColumnInfo> columnInfoMap = inputs.stream().collect(Collectors.toMap(TbCdcdmMetadataTableColumnInfo::getColumnName, v -> v));
        systemField().forEach(s -> {
            if (!columnInfoMap.containsKey(s.getColumnName())){
                inputs.add(s);
            }
        });
    }
    
    private List<TbCdcdmMetadataTableColumnInfo> systemField(){
        List<TbCdcdmMetadataTableColumnInfo> columnInfoList = new ArrayList<>();
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("id", "主键", TableColumnTypeEnum.VARCHAR.getCode(), "1"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("creatorId", "创建者id", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("creator", "创建者名称", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("createTime", "创建时间", TableColumnTypeEnum.TIMESTAMP.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("updaterId", "修改者id", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("updater", "修改者名称", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("updateTime", "修改时间", TableColumnTypeEnum.TIMESTAMP.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("deleteFlag", "删除标识", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        columnInfoList.add(TbCdcdmMetadataTableColumnInfo.of("refId", "外键", TableColumnTypeEnum.VARCHAR.getCode(), "0"));
        return columnInfoList;
    }
    
    private void check(List<TableColumnInfoExcelVO> columnInfoExcelVOS){
        int i = 2;
        List<String> errorMessages = new ArrayList<>();
        for (TableColumnInfoExcelVO columnInfoExcelVO : columnInfoExcelVOS) {
            if (StringUtils.isEmpty(columnInfoExcelVO.getTableName())){
                errorMessages.add("第" + i +"行表名不能为空");
            }
            if (StringUtils.isEmpty(columnInfoExcelVO.getTableDesc())){
                errorMessages.add("第" + i +"行表描述不能为空");
            }
            if (StringUtils.isEmpty(columnInfoExcelVO.getColumnName())){
                errorMessages.add("第" + i +"行列名不能为空");
            }
            if (StringUtils.isEmpty(columnInfoExcelVO.getColumnDesc())){
                errorMessages.add("第" + i +"行列描述不能为空");
            }
            if (StringUtils.isEmpty(columnInfoExcelVO.getDataType())){
                errorMessages.add("第" + i +"行数据类型不能为空");
            }
            if (columnInfoExcelVO.getColumnLength() == null){
                errorMessages.add("第" + i +"行列的长度不能为空");
            }
            i++;
        }
        if (errorMessages.size() > 0){
            throw new MedicalBusinessException(String.join("\n", errorMessages));
        }
    }
}
