package com.iflytek.cdc.admin.customizedapp.service;

import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsMenu;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsVersion;
import com.iflytek.cdc.admin.customizedapp.model.dto.MenuGroupDTO;
import com.iflytek.cdc.admin.service.ICdcService;

import java.util.List;

public interface MenuService extends ICdcService<TbCdccsMenu> {

    /**
     * 通过菜单分组创建
     * @param menuGroup 菜单分组
     */
    void create(MenuGroupDTO menuGroup);

    /**
     * 通过菜单分组创建
     * @param menuGroupDTO 菜单分组
     */
    void update(MenuGroupDTO menuGroupDTO);

    /**
     * 获取id
     * @return id
     */
    String getId();

    /**
     * 根据应用id查询
     * @param appId 应用id
     * @return 菜单集合
     */
    List<TbCdccsMenu> listByAppId(String appId);

    /**
     * 根据分组id查询
     * @param groupId 分组id
     * @return 菜单集合
     */
    List<TbCdccsMenu> listByGroupId(String groupId);

    /**
     * 查询分组
     * @param appId 应用id
     * @return 版本数据
     */
    List<TbCdccsVersion> listGroup(String appId);

    /**
     * 删除分组
     */
    void deleteGroup(String groupId);

    /**
     * 获取已删除的菜单的编码
     */
    List<String> listAllContainsDeletedCode(String appId);


}
