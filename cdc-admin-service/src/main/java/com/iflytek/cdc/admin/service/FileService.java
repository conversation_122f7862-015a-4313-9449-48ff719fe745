package com.iflytek.cdc.admin.service;

import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 文件服务类
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 文件上传-单文件
     *
     * @param file
     * @return
     */
    List<String> upload(MultipartFile file);

    String upload(String url, String businessId);

    String upload(InputStream inputStream, String businessId, String fileName);

    void uploadFileAsync(MultipartFile file, String businessId, String fileName);

    /**
     * 文件上传
     *
     * @param bytes
     * @param fileName
     * @return
     */
    String upload(byte[] bytes, String fileName);

    /**
     * 文件上传-多文件
     *
     * @param files
     * @return
     */
    List<String> uploads(MultipartFile[] files);

    /**
     * 文件下载
     *
     * @param id
     * @return
     */
    ResponseEntity<byte[]> download(String id);


    TbCdcAttachment saveAttachmentRecord(TbCdcAttachment attachment);

    String getPathByAttachmentId(String id);

    /**
     * 文件上传——获取文件路径
     * @param file
     * @return
     */
    String fileUploadGetUrl(MultipartFile file);
}
