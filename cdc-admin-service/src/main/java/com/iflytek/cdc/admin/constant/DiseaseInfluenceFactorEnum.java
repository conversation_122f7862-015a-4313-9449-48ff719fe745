package com.iflytek.cdc.admin.constant;

public enum DiseaseInfluenceFactorEnum {



    ROUTE_TRANSMISSION("传播途径", "传播途径"),
    affected_area("发病地区", "发病地区"),
    AGE("年龄", "年龄"),
    SEX("性别", "性别"),
    CAREER("职业", "职业"),
    TEMPERATURE("温度", "温度"),
    HUMIDITY("湿度", "湿度"),
    UNDERLYING_DISEASE("基础疾病", "基础疾病"),
    POLITICAL_ECONOMY("政治经济", "政治经济"),
    Humane_Religion("人文宗教", "人文宗教"),
    LIFESTYLE_CUSTOMS("生活风俗", "生活风俗"),
    VACCINATION("疫苗接种", "疫苗接种");

    private String code;
    private String name;
    
    
    DiseaseInfluenceFactorEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
}
