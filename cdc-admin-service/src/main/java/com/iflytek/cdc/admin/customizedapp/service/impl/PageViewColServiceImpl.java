package com.iflytek.cdc.admin.customizedapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPage;
import com.iflytek.cdc.admin.customizedapp.entity.TbCdccsPageViewCol;
import com.iflytek.cdc.admin.customizedapp.mapper.TbCdccsPageViewColMapper;
import com.iflytek.cdc.admin.customizedapp.service.PageViewColService;
import com.iflytek.cdc.admin.enums.DeleteFlagEnum;
import com.iflytek.cdc.admin.service.impl.CdcServiceBaseImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PageViewColServiceImpl extends CdcServiceBaseImpl<TbCdccsPageViewColMapper, TbCdccsPageViewCol> implements PageViewColService {
    @Override
    public void saveByPage(TbCdccsPage page, List<TbCdccsPageViewCol> pageViewColList) {
        int i = 0;
        for (TbCdccsPageViewCol viewCol : pageViewColList){
            viewCol.setSort(i++);
        }
        batchSaveByForeignKey(page, TbCdccsPage::getId,
                TbCdccsPageViewCol::getPageId,
                (p, c) -> {
                    c.setPageName(p.getPageName());
                    c.setPageId(p.getId());
                },
                pageViewColList);
    }

    @Override
    public List<TbCdccsPageViewCol> listByPageId(String pageId) {
        LambdaQueryWrapper<TbCdccsPageViewCol> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbCdccsPageViewCol::getPageId, pageId);
        queryWrapper.orderByAsc(TbCdccsPageViewCol::getSort);
        return list(queryWrapper);
    }

    @Override
    public void deleteByPageId(String pageId) {
        LambdaUpdateChainWrapper<TbCdccsPageViewCol> updateChainWrapper = lambdaUpdate();
        updateChainWrapper.set(TbCdccsPageViewCol::getDeleteFlag, DeleteFlagEnum.YES.getCode())
                .eq(TbCdccsPageViewCol::getPageId, pageId)
                .update();
    }
}
