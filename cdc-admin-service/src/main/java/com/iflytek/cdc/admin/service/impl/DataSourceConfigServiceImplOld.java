package com.iflytek.cdc.admin.service.impl;

import com.iflytek.cdc.admin.dto.DataSourceConfigVO;
import com.iflytek.cdc.admin.entity.TbCdcmrDataSourceConfig;
import com.iflytek.cdc.admin.enums.BusinessTypeEnum;
import com.iflytek.cdc.admin.enums.DataSourceValueEnum;
import com.iflytek.cdc.admin.enums.SignalTypeEnum;
import com.iflytek.cdc.admin.mapper.TbCdcmrDataSourceConfigMapper;
import com.iflytek.cdc.admin.service.DataSourceConfigService;
import com.iflytek.cdc.admin.service.UapUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class DataSourceConfigServiceImplOld implements DataSourceConfigService {
    @Resource
    TbCdcmrDataSourceConfigMapper tbCdcmrDataSourceConfigMapper;
    @Resource
    UapUserService uapUserService;

    @Override
    public TbCdcmrDataSourceConfig getByBusinessTypeAndSignalType(String businessType, String signalType) {
        return tbCdcmrDataSourceConfigMapper.selectByBusinessTypeAndSignalType(businessType, signalType);
    }

    @Override
    public List<TbCdcmrDataSourceConfig> listByBusinessType(String businessType) {
        return tbCdcmrDataSourceConfigMapper.listByBusinessType(businessType);
    }

    @Override
    public List<DataSourceConfigVO> getList() {
        List<DataSourceConfigVO> resultList = tbCdcmrDataSourceConfigMapper.getList();
        if (!resultList.isEmpty()) {
            resultList.forEach(e -> {
                e.setBusinessTypeName(BusinessTypeEnum.getNameByCode(e.getBusinessType()));
                e.setSignalTypeName(SignalTypeEnum.getNameByCode(e.getSignalType()));
                e.setDataSourceName(DataSourceValueEnum.getNameByCode(e.getDataSource()));
            });
        }
        return resultList;
    }

    @Override
    public void updateConfig(TbCdcmrDataSourceConfig record) {
        record.setUpdateTime(new Date());
        record.setUpdateName(uapUserService.getUserInfo(record.getUpdateId()).getName());
        tbCdcmrDataSourceConfigMapper.updateByPrimaryKey(record);
    }
}
