package com.iflytek.cdc.admin.controller.old;


import com.iflytek.cdc.admin.annotation.LogExportAnnotation;
import com.iflytek.cdc.admin.entity.TbCdcAttachment;
import com.iflytek.cdc.admin.service.FileService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件处理控制类
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "文件处理")
public class FileController {

    @Autowired
    private FileService fileService;

    @PostMapping(value = "/pt/v1/fileManage/upload")
    @ApiOperation("上传附件")
    public List<String> analysisUpload(@RequestParam(value = "files") MultipartFile[] files) {
        if (null == files || files.length == 0) {
            throw new MedicalBusinessException("上传文件为空！");
        }
        return fileService.uploads(files);
    }

    @PostMapping(value = "/pt/v1/fileManage/testUpload")
    @ApiOperation("上传附件")
    public List<String> testUpload(@RequestParam(value = "file") MultipartFile file) {
        return fileService.uploads(new MultipartFile[]{file});
    }


    @GetMapping("/pt/v1/fileManage/download")
    @ApiOperation("下载文件")
    @LogExportAnnotation
    public ResponseEntity<byte[]> download(@RequestParam(value = "id") String id , @RequestParam("loginUserId") String loginUserId) {
        return fileService.download(id);
    }

    @PostMapping(value = "/pt/{version}/fileManage/saveAttachmentRecord")
    @ApiOperation("保存附件信息到数据库中")
    public TbCdcAttachment saveAttachmentRecord(@RequestBody TbCdcAttachment attachment) {

        return fileService.saveAttachmentRecord(attachment);
    }

    @PostMapping(value = "/pt/v1/fileManage/fileUploadGetUrl")
    @ApiOperation("上传附件病返回文件路径")
    public String fileUploadGetUrl(@RequestParam(value = "file") MultipartFile file) {
        return fileService.fileUploadGetUrl(file);
    }

}
