package com.iflytek.cdc.admin.controller.province;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.RiskReportPushDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordDto;
import com.iflytek.cdc.admin.dto.RiskReportPushRecordQueryDto;
import com.iflytek.cdc.admin.service.RiskReportPushService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 风险报告推送
 */
@RestController
@Api(tags = "风险报告推送")
@RequestMapping("/pt/{version}/riskReportPush")
public class RiskReportPushController {
    
    private RiskReportPushService riskReportPushService;

    @Autowired
    public void setRiskReportPushService(RiskReportPushService riskReportPushService) {
        this.riskReportPushService = riskReportPushService;
    }

    @PostMapping("/push")
    @ApiOperation("报告推送")
    public ResponseEntity<Boolean> pushReport(@RequestParam String loginUserId,
                                              @RequestBody RiskReportPushDto dto){
        return ResponseEntity.ok(riskReportPushService.pushReport(loginUserId, dto));
    }

    @PostMapping("/queryList")
    @ApiOperation("报告推送记录")
    public PageInfo<RiskReportPushRecordDto> queryList(@RequestParam String loginUserId,
                                                       @RequestBody RiskReportPushRecordQueryDto dto){
        return riskReportPushService.queryList(loginUserId,dto);
    }
}
