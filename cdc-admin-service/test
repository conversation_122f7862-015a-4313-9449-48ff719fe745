{
  "appId": "string",
  "appName": "string",
  "groupId": "string"
}
  "buttonSettingList": [
    {
      "buttonName": "string",
      "buttonType": "string",
      "deleteFlag": "string",
      "extendJson": "string",
      "id": "string",
      "openType": "string",
      "outParameter": "string",
      "pageId": "string",
      "pageName": "string",
      "relationCode": "string",
      "relationType": "string",
      "showCondition": "string"
    }
  ],
  "dataModelId": "string",
  "deleteFlag": "string",
  "extendJson": "string",
  "inParameter": "string",
  "isProcess": "string",
  "pageCode": "string",
  "pageName": "string",
  "pageViewColList": [
    {
      "colCode": "string",
      "colName": "string",
      "deleteFlag": "string",
      "extendJson": "string",
      "id": "string",
      "isHidden": "string",
      "isQuery": "string",
      "isSort": "string",
      "pageId": "string",
      "pageName": "string",
      "queryDefaultValue": "string",
      "queryOp": "string",
      "showType": "string",
      "tableKey": "string"
    }
  ],
  "processId": "string"
}