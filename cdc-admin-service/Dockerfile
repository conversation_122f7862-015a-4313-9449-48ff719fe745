FROM --platform=$TARGETPLATFORM artifacts.iflytek.com/docker-private/zhyl/os/euler:20.03_jdk1.8
ARG TARGETOS
ARG TARGETARCH
RUN echo "TARGETOS: $TARGETOS, TARGETARCH: $TARGETARCH"

ARG APP_PATH=cdc-admin-service/target
ARG APP_NAME=cdc-admin-service.jar

# RUN yum install kde-l10n-Chinese glibc-common -y
# RUN localedef -c -f UTF-8 -i zh_CN zh_CN.utf8
#ENV LC_ALL zh_CN.UTF-8
ADD ${APP_PATH}/${APP_NAME} /usr/local/
RUN chmod 755 /usr/local/${APP_NAME}