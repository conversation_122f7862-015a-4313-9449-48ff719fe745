<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.fpva</groupId>
        <artifactId>cdc-admin</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>cdc-admin-service</artifactId>

    <properties>
        <elasticsearch-client.version>7.17.24</elasticsearch-client.version>
        <jackson.version>2.14.1</jackson.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>cdc-admin-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.cloud</groupId>-->
<!--                    <artifactId>spring-cloud-function-context</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>spring-boot-starter-tomcat</artifactId>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.iflytek.medicalboot</groupId>
            <artifactId>starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.xuxueli</groupId>
			        <artifactId>xxl-job-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.iflytek.zhyl</groupId>
            <artifactId>zyzl-mdm-api</artifactId>
            <version>3.0.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.3.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.3.Final</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>common</artifactId>
            <version>1.1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-ldap-core</artifactId>
                    <groupId>org.springframework.ldap</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--redisson-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>uap-service-ext-api</artifactId>
            <version>5.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--            <version>42.2.20</version>-->
<!--        </dependency>-->
        <!-- 瀚高数据库JDBC -->
        <dependency>
            <groupId>com.highgo</groupId>
            <artifactId>hgdb-pgjdbc</artifactId>
            <version>42.5.0</version>
          </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>7.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>carrier</artifactId>
            <version>1.11</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>geocoder</artifactId>
            <version>2.12</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek</groupId>
            <artifactId>seclib</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.storage</groupId>
            <artifactId>storage-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>cdc-admin-rule</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>cdc-admin-workbench</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
        <groupId>com.iflytek.fpva</groupId>
        <artifactId>cdc-admin-expert</artifactId>
        <version>1.0-SNAPSHOT</version>
        <scope>compile</scope>
    </dependency>

        <!-- https://mvnrepository.com/artifact/com.xuxueli/xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>cdc-outcall-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--整合Knife4j-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.6</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.mpkorstanje/simmetrics-core -->
        <dependency>
            <groupId>com.github.mpkorstanje</groupId>
            <artifactId>simmetrics-core</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.3.70</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>

        <dependency>
            <groupId>org</groupId>
            <artifactId>cas-result-core</artifactId>
            <version>3.6.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/cas-result-core.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>

        <dependency>
            <groupId>org</groupId>
            <artifactId>jaudiotagger</artifactId>
            <version>2.0.3</version>
        </dependency>

         <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-all-deps</artifactId>
            <version>2.6.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.java-websocket/Java-WebSocket -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.3.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
            <version>${elasticsearch-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${elasticsearch-client.version}</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>tomcat</id>
            <!-- 默认使用tomcat -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profiles.active>tomcat</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <!-- 东方通web容器 -->
            <id>tongWeb</id>
            <properties>
                <profiles.active>tongWeb</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.tongweb.springboot</groupId>
                    <artifactId>tongweb-spring-boot-starter-2.x</artifactId>
                    <version>7.0.E.6</version>
                </dependency>
            </dependencies>

            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>

        <profile>
            <!-- 中创web容器 -->
            <id>inforsuite</id>
            <properties>
                <profiles.active>inforsuite</profiles.active>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>com.cvicse.embedded</groupId>
                    <artifactId>spring-boot-starter-inforsuite</artifactId>
                    <version>********.IFP01</version>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <build>
        <finalName>cdc-admin-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
