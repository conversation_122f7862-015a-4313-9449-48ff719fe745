package com.iflytek.cdc.admin.common.vo.uap;

import lombok.Data;

import java.util.Date;

@Data
public class UapUserPo {
    private String id;
    private String name;
    private String loginName;
    private Integer userType;
    private Integer userSource;
    private String phone;
    private String address;
    private String email;
    private Integer status;
    private String orgId;
    private String orgName;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Date pwdUpdateTime;
    private String orgProvinceCode;
    private String orgProvince;
    private String orgCityCode;
    private String orgCity;
    private String orgDistrict;
    private String orgDistrictCode;
}
