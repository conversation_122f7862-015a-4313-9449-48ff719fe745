package com.iflytek.cdc.admin.common.vo.uap;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("机构用户信息")
@Data
public class UapOrgUser {

    @ApiModelProperty("用户id")
    private String id;

    @ApiModelProperty("用户名称")
    private String name;

    @ApiModelProperty("登录名")
    private String loginName;

    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("机构id")
    private String orgId;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("职工工号")
    private String employeeNo;

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区编码")
    private String districtCode;

    @ApiModelProperty("区名称")
    private String districtName;


}
