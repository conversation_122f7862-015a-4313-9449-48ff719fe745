package com.iflytek.cdc.admin.common.vo.uap;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

/**
 * @description: 新增/修改用户信息
 * @author: shenghuang
 * @create: 2023/7/26 11:07
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UapUserVo {

    @ApiModelProperty(value = "用户ID")
    private String id;

    @ApiModelProperty(value = "姓名")
    @Length(min = 1,max = 50,message = "姓名非空,长度最大50")
    private String name;

    @ApiModelProperty(value = "电话")
    @Length(min = 1,max = 50,message = "电话非空,长度最大50")
    private String phone;

    @ApiModelProperty(value = "性别编码")
    @Length(min = 1,max = 10,message = "性别编码非空,长度最大10")
    private String sexCode;

    @ApiModelProperty(value = "角色IDs")
    private String[] roleIds;

    @ApiModelProperty(value = "邮件")
    @Length(min = 0,max = 64,message = "邮件长度最大64")
    private String email;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构id列表")
    private List<String> orgIdList;
}
