package com.iflytek.cdc.admin.common.vo.uap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class UserInfoDto {

    @JsonIgnore
    private String loginUserId;

    private String loginUserName;

    private String username;

    private String phone;

    private String email;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String orgId;

    private String orgName;

    private String departmentCode;

    private String departmentName;

    private Integer userLevel;

    private List<UapAuthNode> menu;

    private String buildId;

    private Boolean isAdmin;

    private String appName;

    private List<String> orgIds;
    
    private String roleName;

    private Boolean isCdcRole;
}
