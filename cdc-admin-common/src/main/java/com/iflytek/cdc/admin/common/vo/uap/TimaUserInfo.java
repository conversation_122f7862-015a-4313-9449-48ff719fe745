package com.iflytek.cdc.admin.common.vo.uap;

import lombok.Data;

import java.util.List;

/**
 * @description: 新版用户信息
 * @author: shenghuang
 * @create: 2023-03-29 15:55
 **/
@Data
public class TimaUserInfo {
    private String aptitudeType;
    private Integer auditStatus;
    private String birthday;
    private String careerStartDate;
    private String careerType;
    private String careerTypeName;
    private String createDatetime;
    private String degree;
    private String degreeName;
    private String deptCode;
    private String deptId;
    private String deptIndex;
    private String deptName;
    private String doctorCertificateNumber;
    private String doctorCertificateTime;
    private String doctorIndex;
    private String doctorQualificationCertificateCode;
    private String doctorQualificationCertificateDate;
    private String doctorQualificationCertificateName;
    private String education;
    private String educationName;
    private String ehrId;
    private String email;
    private String empIdCard;
    private String empIdType;
    private String empIdTypeName;
    private String empName;
    private String employeeNo;
    private String employeeStatusCode;
    private String employeeStatusName;
    private String employeeType;
    private String employeeTypeName;
    private String esaddrDetail;
    private String expertNum;
    //0脱敏，1-不脱敏
    private Integer sensitiveInfo;
    private List<TimaUserInfoExtand> extands;
    private Integer gbtZoneIndex;
    private String graduationDate;
    private String graduationSchool;
    private String headImage;
    private String id;
    private String jobRank;
    private String jobTitle;
    private String jobTitleName;
    private String jobskill;
    private String landline;
    private String loginName;
    private String major;
    private String marital;
    private String maritalName;
    private String name;
    private String national;
    private String nationalName;
    private String nationality;
    private String nationalityName;
    private String officeTel;
    private String orgCode;
    private String orgId;
    private String orgIndex;
    private String orgName;
    private String otherIdCard;
    private String party;
    private String partyName;
    private String phone;
    private String physicianCategoryCode;
    private String physicianCategoryName;
    private String professionalName;
    private String professionalNameValue;
    private String sex;
    private String sexName;
    private Integer status;
    private String technicalType;
    private String technicalTypeName;
    private String telephone;
    private String titleDate;
    private String titleLevel;
    private String titleLevelName;
    private String titleName;
    private String updateDatetime;
    private String updateIdNumber;
    private String updateName;
    private String updateOrgCode;
    private String updateOrgName;
    private String userAttribute;
    private String userAttributeName;
    private String userCode;
    private String userSourceId;
    private Integer userType;
    private String userTypeText;
    private String workDate;
    private String roleNames;
    private List<TnewUapRole> userRoleInfoVos;

    //对接简版uap属性
    private String mainDeptId;

    private String mainDeptName;

    private List<SimpleUapDept> deptList;
}
