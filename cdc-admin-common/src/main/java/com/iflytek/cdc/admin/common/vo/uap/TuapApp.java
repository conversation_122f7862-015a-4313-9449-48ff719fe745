package com.iflytek.cdc.admin.common.vo.uap;

import com.iflytek.medicalboot.core.validation.Group;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: shenghuang
 * @create: 2023/7/24 16:41
 **/
@Data
public class TuapApp implements Serializable {
    private String id;
    private @Size(
            min = 2,
            max = 32,
            groups = {Group.Crud.Create.class}
    ) String name;
    private String code;
    private String orgId;
    private String orgName;
    private String contacts;
    private String contactsMobile;
    private String securityCode;
    private String authCode;
    private Integer status;
    private String version;
    private @Size(
            min = 2,
            max = 100,
            groups = {Group.Crud.Create.class}
    ) String url;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private @NotNull(
            groups = {Group.Crud.Create.class}
    ) Integer isShow;
}
