package com.iflytek.cdc.admin.common.vo.uap;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TuapExtand implements Serializable {
    private String id;
    private String name;
    private String nameCode;
    private String code;
    private Integer type;
    private Integer catalog;
    private Integer mandatory;
    private String componentType;
    private String componentData;
    private Integer isUnique;
    private Integer status;
    private Integer sort;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String apiUrl;
}
